// src/node/fs.ts
import { existsSync, readFileSync } from "fs";
import { dirname, join } from "path";
import path from "path";

// src/utils.ts
import { sha256 } from "js-sha256";
var ifInBrowser = typeof window !== "undefined";
function assert(condition, message) {
  if (!condition) {
    throw new Error(message || "Asser<PERSON> failed");
  }
}

// src/node/fs.ts
var pkgCacheMap = {};
function getRunningPkgInfo(dir) {
  if (ifInBrowser) {
    return null;
  }
  const dirToCheck = dir || process.cwd();
  if (pkgCacheMap[dirToCheck]) {
    return pkgCacheMap[dirToCheck];
  }
  const pkgDir = findNearestPackageJson(dirToCheck);
  const pkgJsonFile = pkgDir ? join(pkgDir, "package.json") : null;
  if (pkgDir && pkgJsonFile) {
    const { name, version } = JSON.parse(readFileSync(pkgJsonFile, "utf-8"));
    pkgCacheMap[dirToCheck] = {
      name: name || "midscene-unknown-package-name",
      version: version || "0.0.0",
      dir: pkgDir
    };
    return pkgCacheMap[dirToCheck];
  }
  return {
    name: "midscene-unknown-package-name",
    version: "0.0.0",
    dir: dirToCheck
  };
}
function findNearestPackageJson(dir) {
  const packageJsonPath = join(dir, "package.json");
  if (existsSync(packageJsonPath)) {
    return dir;
  }
  const parentDir = dirname(dir);
  if (parentDir === dir) {
    return null;
  }
  return findNearestPackageJson(parentDir);
}
function getElementInfosScriptContent() {
  const currentFilePath = __filename;
  const pathDir = findNearestPackageJson(dirname(currentFilePath));
  assert(pathDir, `can't find pathDir, with ${dirname}`);
  const scriptPath = path.join(pathDir, "./dist/script/htmlElement.js");
  const elementInfosScriptContent = readFileSync(scriptPath, "utf-8");
  return elementInfosScriptContent;
}
async function getExtraReturnLogic(tree = false) {
  if (ifInBrowser) {
    return null;
  }
  const elementInfosScriptContent = `${getElementInfosScriptContent()}midscene_element_inspector.setNodeHashCacheListOnWindow();`;
  if (tree) {
    return `${elementInfosScriptContent}midscene_element_inspector.webExtractNodeTree()`;
  }
  return `${elementInfosScriptContent}midscene_element_inspector.webExtractTextWithPosition()`;
}
export {
  findNearestPackageJson,
  getElementInfosScriptContent,
  getExtraReturnLogic,
  getRunningPkgInfo
};
