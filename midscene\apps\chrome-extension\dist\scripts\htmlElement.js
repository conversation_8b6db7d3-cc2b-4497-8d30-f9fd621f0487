/*! For license information please see htmlElement.js.LICENSE.txt */
"use strict";var midscene_element_inspector=(()=>{var e=Object.create,t=Object.defineProperty,n=Object.defineProperties,o=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertyNames,a=Object.getOwnPropertySymbols,s=Object.getPrototypeOf,h=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,d=(e,n,o)=>n in e?t(e,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[n]=o,f=(e,t)=>{for(var n in t||(t={}))h.call(t,n)&&d(e,n,t[n]);if(a)for(var n of a(t))l.call(t,n)&&d(e,n,t[n]);return e},u=(e,t)=>n(e,i(t)),c=(e,t)=>function(){return t||(0,e[r(e)[0]])((t={exports:{}}).exports,t),t.exports},p=(e,n,i,a)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let s of r(n))h.call(e,s)||s===i||t(e,s,{get:()=>n[s],enumerable:!(a=o(n,s))||a.enumerable});return e},m=c({"resolve-false:\\empty-stub"(e,t){t.exports={}}}),b=c({"../../node_modules/.pnpm/js-sha256@0.11.0/node_modules/js-sha256/src/sha256.js"(e,t){!function(){var e="input is invalid type",n="object"==typeof window,o=n?window:{};o.JS_SHA256_NO_WINDOW&&(n=!1);var i=!n&&"object"==typeof self,r=!o.JS_SHA256_NO_NODE_JS&&"object"==typeof process&&process.versions&&process.versions.node;r?o=global:i&&(o=self);var a=!o.JS_SHA256_NO_COMMON_JS&&"object"==typeof t&&t.exports,s="function"==typeof define&&define.amd,h=!o.JS_SHA256_NO_ARRAY_BUFFER&&"undefined"!=typeof ArrayBuffer,l="0123456789abcdef".split(""),d=[-0x80000000,8388608,32768,128],f=[24,16,8,0],u=[0x428a2f98,0x71374491,0xb5c0fbcf,0xe9b5dba5,0x3956c25b,0x59f111f1,0x923f82a4,0xab1c5ed5,0xd807aa98,0x12835b01,0x243185be,0x550c7dc3,0x72be5d74,0x80deb1fe,0x9bdc06a7,0xc19bf174,0xe49b69c1,0xefbe4786,0xfc19dc6,0x240ca1cc,0x2de92c6f,0x4a7484aa,0x5cb0a9dc,0x76f988da,0x983e5152,0xa831c66d,0xb00327c8,0xbf597fc7,0xc6e00bf3,0xd5a79147,0x6ca6351,0x14292967,0x27b70a85,0x2e1b2138,0x4d2c6dfc,0x53380d13,0x650a7354,0x766a0abb,0x81c2c92e,0x92722c85,0xa2bfe8a1,0xa81a664b,0xc24b8b70,0xc76c51a3,0xd192e819,0xd6990624,0xf40e3585,0x106aa070,0x19a4c116,0x1e376c08,0x2748774c,0x34b0bcb5,0x391c0cb3,0x4ed8aa4a,0x5b9cca4f,0x682e6ff3,0x748f82ee,0x78a5636f,0x84c87814,0x8cc70208,0x90befffa,0xa4506ceb,0xbef9a3f7,0xc67178f2],c=["hex","array","digest","arrayBuffer"],p=[];(o.JS_SHA256_NO_NODE_JS||!Array.isArray)&&(Array.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)}),h&&(o.JS_SHA256_NO_ARRAY_BUFFER_IS_VIEW||!ArrayBuffer.isView)&&(ArrayBuffer.isView=function(e){return"object"==typeof e&&e.buffer&&e.buffer.constructor===ArrayBuffer});var b=function(e,t){return function(n){return new N(t,!0).update(n)[e]()}},y=function(e){var t=b("hex",e);r&&(t=g(t,e)),t.create=function(){return new N(e)},t.update=function(e){return t.create().update(e)};for(var n=0;n<c.length;++n){var o=c[n];t[o]=b(o,e)}return t},g=function(t,n){var i,r=m(),a=m().Buffer,s=n?"sha224":"sha256";return i=a.from&&!o.JS_SHA256_NO_BUFFER_FROM?a.from:function(e){return new a(e)},function(n){if("string"==typeof n)return r.createHash(s).update(n,"utf8").digest("hex");if(null==n)throw Error(e);return n.constructor===ArrayBuffer&&(n=new Uint8Array(n)),Array.isArray(n)||ArrayBuffer.isView(n)||n.constructor===a?r.createHash(s).update(i(n)).digest("hex"):t(n)}},x=function(e,t){return function(n,o){return new T(n,t,!0).update(o)[e]()}},w=function(e){var t=x("hex",e);t.create=function(t){return new T(t,e)},t.update=function(e,n){return t.create(e).update(n)};for(var n=0;n<c.length;++n){var o=c[n];t[o]=x(o,e)}return t};function N(e,t){t?(p[0]=p[16]=p[1]=p[2]=p[3]=p[4]=p[5]=p[6]=p[7]=p[8]=p[9]=p[10]=p[11]=p[12]=p[13]=p[14]=p[15]=0,this.blocks=p):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],e?(this.h0=0xc1059ed8,this.h1=0x367cd507,this.h2=0x3070dd17,this.h3=0xf70e5939,this.h4=0xffc00b31,this.h5=0x68581511,this.h6=0x64f98fa7,this.h7=0xbefa4fa4):(this.h0=0x6a09e667,this.h1=0xbb67ae85,this.h2=0x3c6ef372,this.h3=0xa54ff53a,this.h4=0x510e527f,this.h5=0x9b05688c,this.h6=0x1f83d9ab,this.h7=0x5be0cd19),this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0,this.is224=e}function T(t,n,o){var i,r=typeof t;if("string"===r){var a,s=[],l=t.length,d=0;for(i=0;i<l;++i)(a=t.charCodeAt(i))<128?s[d++]=a:(a<2048?s[d++]=192|a>>>6:(a<55296||a>=57344?s[d++]=224|a>>>12:(a=65536+((1023&a)<<10|1023&t.charCodeAt(++i)),s[d++]=240|a>>>18,s[d++]=128|a>>>12&63),s[d++]=128|a>>>6&63),s[d++]=128|63&a);t=s}else if("object"===r){if(null===t)throw Error(e);else if(h&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!Array.isArray(t)&&(!h||!ArrayBuffer.isView(t)))throw Error(e)}else throw Error(e);t.length>64&&(t=new N(n,!0).update(t).array());var f=[],u=[];for(i=0;i<64;++i){var c=t[i]||0;f[i]=92^c,u[i]=54^c}N.call(this,n,o),this.update(u),this.oKeyPad=f,this.inner=!0,this.sharedMemory=o}N.prototype.update=function(t){if(!this.finalized){var n,o=typeof t;if("string"!==o){if("object"===o){if(null===t)throw Error(e);else if(h&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!Array.isArray(t)&&(!h||!ArrayBuffer.isView(t)))throw Error(e)}else throw Error(e);n=!0}for(var i,r,a=0,s=t.length,l=this.blocks;a<s;){if(this.hashed&&(this.hashed=!1,l[0]=this.block,this.block=l[16]=l[1]=l[2]=l[3]=l[4]=l[5]=l[6]=l[7]=l[8]=l[9]=l[10]=l[11]=l[12]=l[13]=l[14]=l[15]=0),n)for(r=this.start;a<s&&r<64;++a)l[r>>>2]|=t[a]<<f[3&r++];else for(r=this.start;a<s&&r<64;++a)(i=t.charCodeAt(a))<128?l[r>>>2]|=i<<f[3&r++]:(i<2048?l[r>>>2]|=(192|i>>>6)<<f[3&r++]:(i<55296||i>=57344?l[r>>>2]|=(224|i>>>12)<<f[3&r++]:(i=65536+((1023&i)<<10|1023&t.charCodeAt(++a)),l[r>>>2]|=(240|i>>>18)<<f[3&r++],l[r>>>2]|=(128|i>>>12&63)<<f[3&r++]),l[r>>>2]|=(128|i>>>6&63)<<f[3&r++]),l[r>>>2]|=(128|63&i)<<f[3&r++]);this.lastByteIndex=r,this.bytes+=r-this.start,r>=64?(this.block=l[16],this.start=r-64,this.hash(),this.hashed=!0):this.start=r}return this.bytes>0xffffffff&&(this.hBytes+=this.bytes/0x100000000<<0,this.bytes=this.bytes%0x100000000),this}},N.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[16]=this.block,e[t>>>2]|=d[3&t],this.block=e[16],t>=56&&(this.hashed||this.hash(),e[0]=this.block,e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.hBytes<<3|this.bytes>>>29,e[15]=this.bytes<<3,this.hash()}},N.prototype.hash=function(){var e,t,n,o,i,r,a,s,h,l,d,f=this.h0,c=this.h1,p=this.h2,m=this.h3,b=this.h4,y=this.h5,g=this.h6,x=this.h7,w=this.blocks;for(e=16;e<64;++e)t=((i=w[e-15])>>>7|i<<25)^(i>>>18|i<<14)^i>>>3,n=((i=w[e-2])>>>17|i<<15)^(i>>>19|i<<13)^i>>>10,w[e]=w[e-16]+t+w[e-7]+n<<0;for(e=0,d=c&p;e<64;e+=4)this.first?(this.is224?(s=300032,x=(i=w[0]-0x543c9a5b)-0x8f1a6c7<<0,m=i+0x170e9b5<<0):(s=0x2a01a605,x=(i=w[0]-0xc881298)-0x5ab00ac6<<0,m=i+0x8909ae5<<0),this.first=!1):(t=(f>>>2|f<<30)^(f>>>13|f<<19)^(f>>>22|f<<10),n=(b>>>6|b<<26)^(b>>>11|b<<21)^(b>>>25|b<<7),o=(s=f&c)^f&p^d,i=x+n+(b&y^~b&g)+u[e]+w[e],r=t+o,x=m+i<<0,m=i+r<<0),t=(m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10),n=(x>>>6|x<<26)^(x>>>11|x<<21)^(x>>>25|x<<7),o=(h=m&f)^m&c^s,i=g+n+(x&b^~x&y)+u[e+1]+w[e+1],r=t+o,g=p+i<<0,t=((p=i+r<<0)>>>2|p<<30)^(p>>>13|p<<19)^(p>>>22|p<<10),n=(g>>>6|g<<26)^(g>>>11|g<<21)^(g>>>25|g<<7),o=(l=p&m)^p&f^h,i=y+n+(g&x^~g&b)+u[e+2]+w[e+2],r=t+o,y=c+i<<0,t=((c=i+r<<0)>>>2|c<<30)^(c>>>13|c<<19)^(c>>>22|c<<10),n=(y>>>6|y<<26)^(y>>>11|y<<21)^(y>>>25|y<<7),o=(d=c&p)^c&m^l,i=b+n+(y&g^~y&x)+u[e+3]+w[e+3],r=t+o,b=f+i<<0,f=i+r<<0,this.chromeBugWorkAround=!0;this.h0=this.h0+f<<0,this.h1=this.h1+c<<0,this.h2=this.h2+p<<0,this.h3=this.h3+m<<0,this.h4=this.h4+b<<0,this.h5=this.h5+y<<0,this.h6=this.h6+g<<0,this.h7=this.h7+x<<0},N.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,n=this.h2,o=this.h3,i=this.h4,r=this.h5,a=this.h6,s=this.h7,h=l[e>>>28&15]+l[e>>>24&15]+l[e>>>20&15]+l[e>>>16&15]+l[e>>>12&15]+l[e>>>8&15]+l[e>>>4&15]+l[15&e]+l[t>>>28&15]+l[t>>>24&15]+l[t>>>20&15]+l[t>>>16&15]+l[t>>>12&15]+l[t>>>8&15]+l[t>>>4&15]+l[15&t]+l[n>>>28&15]+l[n>>>24&15]+l[n>>>20&15]+l[n>>>16&15]+l[n>>>12&15]+l[n>>>8&15]+l[n>>>4&15]+l[15&n]+l[o>>>28&15]+l[o>>>24&15]+l[o>>>20&15]+l[o>>>16&15]+l[o>>>12&15]+l[o>>>8&15]+l[o>>>4&15]+l[15&o]+l[i>>>28&15]+l[i>>>24&15]+l[i>>>20&15]+l[i>>>16&15]+l[i>>>12&15]+l[i>>>8&15]+l[i>>>4&15]+l[15&i]+l[r>>>28&15]+l[r>>>24&15]+l[r>>>20&15]+l[r>>>16&15]+l[r>>>12&15]+l[r>>>8&15]+l[r>>>4&15]+l[15&r]+l[a>>>28&15]+l[a>>>24&15]+l[a>>>20&15]+l[a>>>16&15]+l[a>>>12&15]+l[a>>>8&15]+l[a>>>4&15]+l[15&a];return this.is224||(h+=l[s>>>28&15]+l[s>>>24&15]+l[s>>>20&15]+l[s>>>16&15]+l[s>>>12&15]+l[s>>>8&15]+l[s>>>4&15]+l[15&s]),h},N.prototype.toString=N.prototype.hex,N.prototype.digest=function(){this.finalize();var e=this.h0,t=this.h1,n=this.h2,o=this.h3,i=this.h4,r=this.h5,a=this.h6,s=this.h7,h=[e>>>24&255,e>>>16&255,e>>>8&255,255&e,t>>>24&255,t>>>16&255,t>>>8&255,255&t,n>>>24&255,n>>>16&255,n>>>8&255,255&n,o>>>24&255,o>>>16&255,o>>>8&255,255&o,i>>>24&255,i>>>16&255,i>>>8&255,255&i,r>>>24&255,r>>>16&255,r>>>8&255,255&r,a>>>24&255,a>>>16&255,a>>>8&255,255&a];return this.is224||h.push(s>>>24&255,s>>>16&255,s>>>8&255,255&s),h},N.prototype.array=N.prototype.digest,N.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(this.is224?28:32),t=new DataView(e);return t.setUint32(0,this.h0),t.setUint32(4,this.h1),t.setUint32(8,this.h2),t.setUint32(12,this.h3),t.setUint32(16,this.h4),t.setUint32(20,this.h5),t.setUint32(24,this.h6),this.is224||t.setUint32(28,this.h7),e},T.prototype=new N,T.prototype.finalize=function(){if(N.prototype.finalize.call(this),this.inner){this.inner=!1;var e=this.array();N.call(this,this.is224,this.sharedMemory),this.update(this.oKeyPad),this.update(e),N.prototype.finalize.call(this)}};var E=y();E.sha256=E,E.sha224=y(!0),E.sha256.hmac=w(),E.sha224.hmac=w(!0),a?t.exports=E:(o.sha256=E.sha256,o.sha224=E.sha224,s&&define(function(){return E}))}()}}),y={};function g(e,t=150){return void 0===e?"":("object"==typeof e&&(e=JSON.stringify(e)),"number"==typeof e)?e.toString():"string"==typeof e&&e.length>t?`${e.slice(0,t)}...`:"string"==typeof e?e.trim():""}function x(e,t){return Object.keys(e).reduce((n,o)=>{let i=e[o];return"style"===o||"htmlTagName"===o||"nodeType"===o||(n[o]=g(i,t)),n},{})}function w(e,t,n=!1,o=!0){let i=e=>Object.entries(e).map(([e,n])=>`${e}="${g(n,t)}"`).join(" ");return(function e(o,r=0,a=!0){var s;let h="",l="",d="",f=!0,u="  ".repeat(r),c="";for(let t=0;t<(o.children||[]).length;t++){let n=e(o.children[t],r+1,a);n&&(c+=`
${n}`)}if(o.node&&o.node.rect.width>4&&o.node.rect.height>4&&(!n||n&&o.node.content)&&(!a||a&&o.node.isVisible)){let e;f=!1,e=(null==(s=o.node.attributes)?void 0:s.htmlTagName)?o.node.attributes.htmlTagName.replace(/[<>]/g,""):o.node.attributes.nodeType.replace(/\sNode$/,"").toLowerCase();let n=o.node.indexId,r=n?`markerId="${n}"`:"",a=o.node.rect?{left:o.node.rect.left,top:o.node.rect.top,width:o.node.rect.width,height:o.node.rect.height}:{};h=`<${e} id="${o.node.id}" ${r} ${i(x(o.node.attributes||{},t))} ${i(a)}>`;let c=g(o.node.content,t);l=c?`
${u}  ${c}`:"",d=`</${e}>`}else!n&&(c.trim().startsWith("<>")||(h="<>",l="",d="</>"));if(f&&!c.trim())return"";let p=`${u}${h}${l}${c}
${u}${d}`;return p.trim()?p:""})(e,0,o).replace(/^\s*\n/gm,"")}function N(e){let t=[];return!function e(n){for(let o of(n.node&&t.push(n.node),n.children))e(o)}(e),t}function T(e,t){return!function e(n){for(let o of(n.node&&(n.node=t(n.node)),n.children))e(o)}(e),e}((e,n)=>{for(var o in n)t(e,o,{get:n[o],enumerable:!0})})(y,{descriptionOfTree:()=>w,generateElementByPosition:()=>A,getElementInfoByXpath:()=>ee,getNodeFromCacheList:()=>D,getNodeInfoByXpath:()=>Q,getXpathsById:()=>Z,setNodeHashCacheListOnWindow:()=>R,traverseTree:()=>T,treeToList:()=>N,trimAttributes:()=>x,truncateText:()=>g,webExtractNodeTree:()=>G,webExtractNodeTreeAsString:()=>X,webExtractTextWithPosition:()=>J});var E=((n,o,i)=>(i=null!=n?e(s(n)):{},p(n&&n.__esModule?i:t(i,"default",{value:n,enumerable:!0}),n)))(b()),v={};function M(e,t=""){let n=JSON.stringify({content:t,rect:e}),o=5,i="",r=E.sha256.create().update(n).hex().split("").map(e=>String.fromCharCode(97+Number.parseInt(e,16)%26)).join("");for(;o<r.length-1;){if(v[i=r.slice(0,o)]&&v[i]!==n){o++;continue}v[i]=n;break}return i}function O(e){return e instanceof Element&&(window.getComputedStyle(e).fontFamily||"").toLowerCase().indexOf("iconfont")>=0}function C(e){if(!(e instanceof HTMLElement))return!1;if(e.innerText)return!0;for(let t of["svg","button","input","textarea","select","option","img","a"])if(e.querySelectorAll(t).length>0)return!0;return!1}function A(e){let t={left:Math.max(e.x-4,0),top:Math.max(e.y-4,0),width:8,height:8};return{id:M(t),attributes:{nodeType:"POSITION Node"},rect:t,content:"",center:[e.x,e.y]}}var _=!1;function L(...e){_&&console.log(...e)}var S="_midscene_retrieve_task_id";function H(e,t,n,o){if(!(e instanceof o.HTMLElement))return"";if(!S)return console.error("No task id found"),"";let i=`[${S}='${t}']`;return _&&(n?e.parentNode instanceof o.HTMLElement&&e.parentNode.setAttribute(S,t.toString()):e.setAttribute(S,t.toString())),i}function $(e,t,n,o=2/3){let i=e.height,r=e.width,a=B(e,{left:0,top:0,width:t.innerWidth||n.documentElement.clientWidth,height:t.innerHeight||n.documentElement.clientHeight,right:t.innerWidth||n.documentElement.clientWidth,bottom:t.innerHeight||n.documentElement.clientHeight,x:0,y:0,zoom:1});return!!a&&a.width*a.height/(i*r)>=o}function I(e,t){if(!(e instanceof t.HTMLElement))return{before:"",after:""};let n=t.getComputedStyle(e,"::before").getPropertyValue("content"),o=t.getComputedStyle(e,"::after").getPropertyValue("content");return{before:"none"===n?"":n.replace(/"/g,""),after:"none"===o?"":o.replace(/"/g,"")}}function B(e,t){let n=Math.max(e.left,t.left),o=Math.max(e.top,t.top),i=Math.min(e.right,t.right),r=Math.min(e.bottom,t.bottom);return n<i&&o<r?{left:n,top:o,right:i,bottom:r,width:i-n,height:r-o,x:n,y:o,zoom:1}:null}function j(e,t,n){let o,i=1;if(e instanceof n.HTMLElement)o=e.getBoundingClientRect(),"currentCSSZoom"in e||(i=Number.parseFloat(n.getComputedStyle(e).zoom)||1);else{let t=n.document.createRange();t.selectNodeContents(e),o=t.getBoundingClientRect()}let r=i*t;return{width:o.width*r,height:o.height*r,left:o.left*r,top:o.top*r,right:o.right*r,bottom:o.bottom*r,x:o.x*r,y:o.y*r,zoom:r}}var z=(e,t,n)=>{let o=t.left+t.width/2,i=t.top+t.height/2,r=n.document.elementFromPoint(o,i);return!(!r||r===e||(null==e?void 0:e.contains(r))||(null==r?void 0:r.contains(e)))&&!!B(t,j(r,1,n))&&(L(e,"Element is covered by another element",{topElement:r,el:e,rect:t,x:o,y:i}),!0)};function V(e,t,n,o=1){if(!e||!(e instanceof t.HTMLElement)&&e.nodeType!==Node.TEXT_NODE&&"svg"!==e.nodeName.toLowerCase())return L(e,"Element is not in the DOM hierarchy"),!1;if(e instanceof t.HTMLElement){let n=t.getComputedStyle(e);if("none"===n.display||"hidden"===n.visibility||"0"===n.opacity&&"INPUT"!==e.tagName)return L(e,"Element is hidden"),!1}let i=j(e,o,t);if(0===i.width&&0===i.height)return L(e,"Element has no size"),!1;if(1===o&&z(e,i,t))return!1;let r=$(i,t,n),a=e,s=e=>{let n=null==e?void 0:e.parentElement;for(;n;){if("static"!==t.getComputedStyle(n).position)return n;n=n.parentElement}return null};for(;a&&a!==n.body;){if(!(a instanceof t.HTMLElement)){a=a.parentElement;continue}let n=t.getComputedStyle(a);if("hidden"===n.overflow){let n=j(a,1,t);if(i.right<n.left-10||i.left>n.right+10||i.bottom<n.top-10||i.top>n.bottom+10)return L(e,"element is partially or totally hidden by an ancestor",{rect:i,parentRect:n}),!1}if("fixed"===n.position||"sticky"===n.position)break;a="absolute"===n.position?s(a):a.parentElement}return{left:Math.round(i.left),top:Math.round(i.top),width:Math.round(i.width),height:Math.round(i.height),zoom:i.zoom,isVisible:r}}function k(e,t){return e&&e instanceof t.HTMLElement&&e.attributes?Object.fromEntries(Array.from(e.attributes).map(e=>{if("class"===e.name)return[e.name,`.${e.value.split(" ").join(".")}`];if(!e.value)return[];let t=e.value;return t.startsWith("data:image")&&(t="image"),t.length>300&&(t=`${t.slice(0,300)}...`),[e.name,t]})):{}}function P(e,t,n){let o=M(n,t);return e&&(window.midsceneNodeHashCacheList||R(),function(e,t){var n;if("undefined"!=typeof window){if(D(t))return;null==(n=window.midsceneNodeHashCacheList)||n.push({node:e,id:t})}}(e,o)),o}function R(){"undefined"!=typeof window&&(window.midsceneNodeHashCacheList=[])}function D(e){var t,n;return"undefined"!=typeof window?null==(n=null==(t=window.midsceneNodeHashCacheList)?void 0:t.find(t=>t.id===e))?void 0:n.node:null}var U=0;function W(e){let t="";if(e instanceof HTMLElement)t=e.tagName.toLowerCase();else{let n=e.parentElement;n&&n instanceof HTMLElement&&(t=n.tagName.toLowerCase())}return t?`<${t}>`:""}function F(e,t,n,o=1,i={left:0,top:0}){var r;let a=V(e,t,n,o);if(!a||a.width<3||a.height<3||((0!==i.left||0!==i.top)&&(a.left+=i.left,a.top+=i.top),a.height>=window.innerHeight&&a.width>=window.innerWidth))return null;if(e instanceof HTMLElement&&("input"===e.tagName.toLowerCase()||"textarea"===e.tagName.toLowerCase()||"select"===e.tagName.toLowerCase()||"option"===e.tagName.toLowerCase())){let n=k(e,t),o=n.value||n.placeholder||e.textContent||"",i=P(e,o,a),r=H(e,i,!1,t),s=e.tagName.toLowerCase();return"select"===e.tagName.toLowerCase()&&(o=e.options[e.selectedIndex].textContent||""),("input"===e.tagName.toLowerCase()||"textarea"===e.tagName.toLowerCase())&&e.value&&(o=e.value),{id:i,nodeHashId:i,locator:r,nodeType:"FORM_ITEM Node",indexId:U++,attributes:u(f({},n),{htmlTagName:`<${s}>`,nodeType:"FORM_ITEM Node"}),content:o.trim(),rect:a,center:[Math.round(a.left+a.width/2),Math.round(a.top+a.height/2)],zoom:a.zoom,isVisible:a.isVisible}}if(e instanceof HTMLElement&&"button"===e.tagName.toLowerCase()){let i=function(e,t,n,o=1){let i=V(e,t,n,o);if(!i)return null;let r=i.left,a=i.top,s=i.left+i.width,h=i.top+i.height;return!function e(i){for(let l=0;l<i.childNodes.length;l++){let d=i.childNodes[l];if(1===d.nodeType){let i=V(d,t,n,o);i&&(r=Math.min(r,i.left),a=Math.min(a,i.top),s=Math.max(s,i.left+i.width),h=Math.max(h,i.top+i.height)),e(d)}}}(e),u(f({},i),{left:r,top:a,width:s-r,height:h-a})}(e,t,n,o);if(!i)return null;let r=k(e,t),a=I(e,t),s=e.innerText||a.before||a.after||"",h=P(e,s,i),l=H(e,h,!1,t);return{id:h,indexId:U++,nodeHashId:h,nodeType:"BUTTON Node",locator:l,attributes:u(f({},r),{htmlTagName:W(e),nodeType:"BUTTON Node"}),content:s,rect:i,center:[Math.round(i.left+i.width/2),Math.round(i.top+i.height/2)],zoom:i.zoom,isVisible:i.isVisible}}if(!C(e)&&e instanceof Element&&"none"!==window.getComputedStyle(e).getPropertyValue("background-image")||O(e)||e instanceof HTMLElement&&"img"===e.tagName.toLowerCase()||e instanceof SVGElement&&"svg"===e.tagName.toLowerCase()){let n=k(e,t),o=P(e,"",a),i=H(e,o,!1,t);return{id:o,indexId:U++,nodeHashId:o,locator:i,attributes:u(f(f({},n),"svg"===e.nodeName.toLowerCase()?{svgContent:"true"}:{}),{nodeType:"IMG Node",htmlTagName:W(e)}),nodeType:"IMG Node",content:"",rect:a,center:[Math.round(a.left+a.width/2),Math.round(a.top+a.height/2)],zoom:a.zoom,isVisible:a.isVisible}}if("#text"===e.nodeName.toLowerCase()&&!O(e)){let n=null==(r=e.textContent)?void 0:r.trim().replace(/\n+/g," ");if(!n)return null;let o=k(e,t),i=Object.keys(o);if(!n.trim()&&0===i.length)return null;let s=P(e,n,a),h=H(e,s,!0,t);return{id:s,indexId:U++,nodeHashId:s,nodeType:"TEXT Node",locator:h,attributes:u(f({},o),{nodeType:"TEXT Node",htmlTagName:W(e)}),center:[Math.round(a.left+a.width/2),Math.round(a.top+a.height/2)],content:n,rect:a,zoom:a.zoom,isVisible:a.isVisible}}if(e instanceof HTMLElement&&"a"===e.tagName.toLowerCase()){let n=k(e,t),o=I(e,t),i=e.innerText||o.before||o.after||"",r=P(e,i,a),s=H(e,r,!1,t);return{id:r,indexId:U++,nodeHashId:r,nodeType:"Anchor Node",locator:s,attributes:u(f({},n),{htmlTagName:W(e),nodeType:"Anchor Node"}),content:i,rect:a,center:[Math.round(a.left+a.width/2),Math.round(a.top+a.height/2)],zoom:a.zoom,isVisible:a.isVisible}}if(!(!(e instanceof HTMLElement)||C(e))&&window.getComputedStyle(e).getPropertyValue("background-color")){let n=k(e,t),o=P(e,"",a),i=H(e,o,!1,t);return{id:o,nodeHashId:o,indexId:U++,nodeType:"CONTAINER Node",locator:i,attributes:u(f({},n),{nodeType:"CONTAINER Node",htmlTagName:W(e)}),content:"",rect:a,center:[Math.round(a.left+a.width/2),Math.round(a.top+a.height/2)],zoom:a.zoom,isVisible:a.isVisible}}return null}function J(e,t=!1){let n=G(e,t),o=[];return!function e(t){t.node&&o.push(t.node);for(let n=0;n<t.children.length;n++)e(t.children[n])}({children:n.children,node:n.node}),o}function X(e,t=!1,n=!1){return w(G(e,n),void 0,!1,t)}function G(e,t=!1){_=t,U=0;let n=document.body||document,o=e||n,i=[];function r(e,t,n,o=1,i={left:0,top:0}){if(!e||e.nodeType&&10===e.nodeType)return null;let a=F(e,t,n,o,i);if(e instanceof t.HTMLIFrameElement&&e.contentWindow&&e.contentWindow)return null;let s={node:a,children:[]};if((null==a?void 0:a.nodeType)==="BUTTON Node"||(null==a?void 0:a.nodeType)==="IMG Node"||(null==a?void 0:a.nodeType)==="TEXT Node"||(null==a?void 0:a.nodeType)==="FORM_ITEM Node"||(null==a?void 0:a.nodeType)==="CONTAINER Node")return s;let h=j(e,o,t);for(let o=0;o<e.childNodes.length;o++){L("will dfs",e.childNodes[o]);let a=r(e.childNodes[o],t,n,h.zoom,i);a&&s.children.push(a)}return s}let a=r(o,window,document,1,{left:0,top:0});if(a&&i.push(a),o===n){let e=document.querySelectorAll("iframe");for(let t=0;t<e.length;t++){let n=e[t];if(n.contentDocument&&n.contentWindow){let e=F(n,window,document,1);if(e){let t=r(n.contentDocument.body,n.contentWindow,n.contentDocument,1,{left:e.rect.left,top:e.rect.top});t&&i.push(t)}}}}return{node:null,children:i}}var Y=e=>{let t=1,n=e.previousElementSibling;for(;n;)n.nodeName.toLowerCase()===e.nodeName.toLowerCase()&&t++,n=n.previousElementSibling;return t},q=e=>{let t=1,n=e.previousSibling;for(;n;)n.nodeType===Node.TEXT_NODE&&t++,n=n.previousSibling;return t},K=e=>{var t;if(e.nodeType===Node.TEXT_NODE){let n=e.parentNode;if(n&&n.nodeType===Node.ELEMENT_NODE){let o=K(n),i=q(e),r=null==(t=e.textContent)?void 0:t.trim();return r?`${o}/text()[${i}][normalize-space()="${r}"]`:`${o}/text()[${i}]`}return""}if(e.nodeType!==Node.ELEMENT_NODE)return"";if(e===document.documentElement)return"/html";if(e===document.body)return"/html/body";if(!e.parentNode)return`/${e.nodeName.toLowerCase()}`;let n=Y(e),o=e.nodeName.toLowerCase();if(e.parentNode){let t=K(e.parentNode);return`${t}/${o}[${n}]`}return`/${o}[${n}]`};function Z(e){let t=D(e);return t?t?[K(t)]:[]:null}function Q(e){let t=document.evaluate(e,document,null,XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,null);return 1!==t.snapshotLength?null:t.snapshotItem(0)}function ee(e){let t=Q(e);return t?(t instanceof HTMLElement&&($(j(t,1,window),window,document,1)||t.scrollIntoView({behavior:"instant",block:"center"})),F(t,window,document,1,{left:0,top:0})):null}return p(t({},"__esModule",{value:!0}),y)})();