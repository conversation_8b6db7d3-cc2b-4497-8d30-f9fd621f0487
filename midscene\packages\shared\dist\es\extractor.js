// src/extractor/tree.ts
function truncateText(text, maxLength = 150) {
  if (typeof text === "undefined") {
    return "";
  }
  if (typeof text === "object") {
    text = JSON.stringify(text);
  }
  if (typeof text === "number") {
    return text.toString();
  }
  if (typeof text === "string" && text.length > maxLength) {
    return `${text.slice(0, maxLength)}...`;
  }
  if (typeof text === "string") {
    return text.trim();
  }
  return "";
}
function trimAttributes(attributes, truncateTextLength) {
  const tailorAttributes = Object.keys(attributes).reduce(
    (res, currentKey) => {
      const attributeVal = attributes[currentKey];
      if (currentKey === "style" || currentKey === "htmlTagName" || currentKey === "nodeType") {
        return res;
      }
      res[currentKey] = truncateText(attributeVal, truncateTextLength);
      return res;
    },
    {}
  );
  return tailorAttributes;
}
var nodeSizeThreshold = 4;
function descriptionOfTree(tree, truncateTextLength, filterNonTextContent = false, visibleOnly = true) {
  const attributesString = (kv) => {
    return Object.entries(kv).map(
      ([key, value]) => `${key}="${truncateText(value, truncateTextLength)}"`
    ).join(" ");
  };
  function buildContentTree(node, indent = 0, visibleOnly2 = true) {
    let before = "";
    let contentWithIndent = "";
    let after = "";
    let emptyNode = true;
    const indentStr = "  ".repeat(indent);
    let children = "";
    for (let i = 0; i < (node.children || []).length; i++) {
      const childContent = buildContentTree(
        node.children[i],
        indent + 1,
        visibleOnly2
      );
      if (childContent) {
        children += `
${childContent}`;
      }
    }
    if (node.node && node.node.rect.width > nodeSizeThreshold && node.node.rect.height > nodeSizeThreshold && (!filterNonTextContent || filterNonTextContent && node.node.content) && (!visibleOnly2 || visibleOnly2 && node.node.isVisible)) {
      emptyNode = false;
      let nodeTypeString;
      if (node.node.attributes?.htmlTagName) {
        nodeTypeString = node.node.attributes.htmlTagName.replace(/[<>]/g, "");
      } else {
        nodeTypeString = node.node.attributes.nodeType.replace(/\sNode$/, "").toLowerCase();
      }
      const markerId = node.node.indexId;
      const markerIdString = markerId ? `markerId="${markerId}"` : "";
      const rectAttribute = node.node.rect ? {
        left: node.node.rect.left,
        top: node.node.rect.top,
        width: node.node.rect.width,
        height: node.node.rect.height
      } : {};
      before = `<${nodeTypeString} id="${node.node.id}" ${markerIdString} ${attributesString(trimAttributes(node.node.attributes || {}, truncateTextLength))} ${attributesString(rectAttribute)}>`;
      const content = truncateText(node.node.content, truncateTextLength);
      contentWithIndent = content ? `
${indentStr}  ${content}` : "";
      after = `</${nodeTypeString}>`;
    } else if (!filterNonTextContent) {
      if (!children.trim().startsWith("<>")) {
        before = "<>";
        contentWithIndent = "";
        after = "</>";
      }
    }
    if (emptyNode && !children.trim()) {
      return "";
    }
    const result2 = `${indentStr}${before}${contentWithIndent}${children}
${indentStr}${after}`;
    if (result2.trim()) {
      return result2;
    }
    return "";
  }
  const result = buildContentTree(tree, 0, visibleOnly);
  return result.replace(/^\s*\n/gm, "");
}
function treeToList(tree) {
  const result = [];
  function dfs(node) {
    if (node.node) {
      result.push(node.node);
    }
    for (const child of node.children) {
      dfs(child);
    }
  }
  dfs(tree);
  return result;
}
function traverseTree(tree, onNode) {
  function dfs(node) {
    if (node.node) {
      node.node = onNode(node.node);
    }
    for (const child of node.children) {
      dfs(child);
    }
  }
  dfs(tree);
  return tree;
}

// src/constants/index.ts
var CONTAINER_MINI_HEIGHT = 3;
var CONTAINER_MINI_WIDTH = 3;

// src/utils.ts
import { sha256 } from "js-sha256";
var hashMap = {};
function generateHashId(rect, content = "") {
  const combined = JSON.stringify({
    content,
    rect
  });
  let sliceLength = 5;
  let slicedHash = "";
  const hashHex = sha256.create().update(combined).hex();
  const toLetters = (hex) => {
    return hex.split("").map((char) => {
      const code = Number.parseInt(char, 16);
      return String.fromCharCode(97 + code % 26);
    }).join("");
  };
  const hashLetters = toLetters(hashHex);
  while (sliceLength < hashLetters.length - 1) {
    slicedHash = hashLetters.slice(0, sliceLength);
    if (hashMap[slicedHash] && hashMap[slicedHash] !== combined) {
      sliceLength++;
      continue;
    }
    hashMap[slicedHash] = combined;
    break;
  }
  return slicedHash;
}

// src/extractor/dom-util.ts
function isFormElement(node) {
  return node instanceof HTMLElement && (node.tagName.toLowerCase() === "input" || node.tagName.toLowerCase() === "textarea" || node.tagName.toLowerCase() === "select" || node.tagName.toLowerCase() === "option");
}
function isButtonElement(node) {
  return node instanceof HTMLElement && node.tagName.toLowerCase() === "button";
}
function isAElement(node) {
  return node instanceof HTMLElement && node.tagName.toLowerCase() === "a";
}
function isImgElement(node) {
  if (!includeBaseElement(node) && node instanceof Element) {
    const computedStyle = window.getComputedStyle(node);
    const backgroundImage = computedStyle.getPropertyValue("background-image");
    if (backgroundImage !== "none") {
      return true;
    }
  }
  if (isIconfont(node)) {
    return true;
  }
  return node instanceof HTMLElement && node.tagName.toLowerCase() === "img" || node instanceof SVGElement && node.tagName.toLowerCase() === "svg";
}
function isIconfont(node) {
  if (node instanceof Element) {
    const computedStyle = window.getComputedStyle(node);
    const fontFamilyValue = computedStyle.fontFamily || "";
    return fontFamilyValue.toLowerCase().indexOf("iconfont") >= 0;
  }
  return false;
}
function isTextElement(node) {
  return node.nodeName.toLowerCase() === "#text" && !isIconfont(node);
}
function isContainerElement(node) {
  if (!(node instanceof HTMLElement))
    return false;
  if (includeBaseElement(node)) {
    return false;
  }
  const computedStyle = window.getComputedStyle(node);
  const backgroundColor = computedStyle.getPropertyValue("background-color");
  if (backgroundColor) {
    return true;
  }
  return false;
}
function includeBaseElement(node) {
  if (!(node instanceof HTMLElement))
    return false;
  if (node.innerText) {
    return true;
  }
  const includeList = [
    "svg",
    "button",
    "input",
    "textarea",
    "select",
    "option",
    "img",
    "a"
  ];
  for (const tagName of includeList) {
    const element = node.querySelectorAll(tagName);
    if (element.length > 0) {
      return true;
    }
  }
  return false;
}
function generateElementByPosition(position) {
  const rect = {
    left: Math.max(position.x - 4, 0),
    top: Math.max(position.y - 4, 0),
    width: 8,
    height: 8
  };
  const id = generateHashId(rect);
  const element = {
    id,
    attributes: { nodeType: "POSITION Node" /* POSITION */ },
    rect,
    content: "",
    center: [position.x, position.y]
  };
  return element;
}

// src/extractor/util.ts
var MAX_VALUE_LENGTH = 300;
var debugMode = false;
function setDebugMode(mode) {
  debugMode = mode;
}
function getDebugMode() {
  return debugMode;
}
function logger(..._msg) {
  if (!debugMode) {
    return;
  }
  console.log(..._msg);
}
var taskIdKey = "_midscene_retrieve_task_id";
function selectorForValue(val) {
  return `[${taskIdKey}='${val}']`;
}
function setDataForNode(node, nodeHash, setToParentNode, currentWindow) {
  const taskId = taskIdKey;
  if (!(node instanceof currentWindow.HTMLElement)) {
    return "";
  }
  if (!taskId) {
    console.error("No task id found");
    return "";
  }
  const selector = selectorForValue(nodeHash);
  if (getDebugMode()) {
    if (setToParentNode) {
      if (node.parentNode instanceof currentWindow.HTMLElement) {
        node.parentNode.setAttribute(taskIdKey, nodeHash.toString());
      }
    } else {
      node.setAttribute(taskIdKey, nodeHash.toString());
    }
  }
  return selector;
}
function isElementPartiallyInViewport(rect, currentWindow, currentDocument, visibleAreaRatio = 2 / 3) {
  const elementHeight = rect.height;
  const elementWidth = rect.width;
  const viewportRect = {
    left: 0,
    top: 0,
    width: currentWindow.innerWidth || currentDocument.documentElement.clientWidth,
    height: currentWindow.innerHeight || currentDocument.documentElement.clientHeight,
    right: currentWindow.innerWidth || currentDocument.documentElement.clientWidth,
    bottom: currentWindow.innerHeight || currentDocument.documentElement.clientHeight,
    x: 0,
    y: 0,
    zoom: 1
  };
  const overlapRect = overlappedRect(rect, viewportRect);
  if (!overlapRect) {
    return false;
  }
  const visibleArea = overlapRect.width * overlapRect.height;
  const totalArea = elementHeight * elementWidth;
  return visibleArea / totalArea >= visibleAreaRatio;
}
function getPseudoElementContent(element, currentWindow) {
  if (!(element instanceof currentWindow.HTMLElement)) {
    return { before: "", after: "" };
  }
  const beforeContent = currentWindow.getComputedStyle(element, "::before").getPropertyValue("content");
  const afterContent = currentWindow.getComputedStyle(element, "::after").getPropertyValue("content");
  return {
    before: beforeContent === "none" ? "" : beforeContent.replace(/"/g, ""),
    after: afterContent === "none" ? "" : afterContent.replace(/"/g, "")
  };
}
function overlappedRect(rect1, rect2) {
  const left = Math.max(rect1.left, rect2.left);
  const top = Math.max(rect1.top, rect2.top);
  const right = Math.min(rect1.right, rect2.right);
  const bottom = Math.min(rect1.bottom, rect2.bottom);
  if (left < right && top < bottom) {
    return {
      left,
      top,
      right,
      bottom,
      width: right - left,
      height: bottom - top,
      x: left,
      y: top,
      zoom: 1
    };
  }
  return null;
}
function getRect(el, baseZoom, currentWindow) {
  let originalRect;
  let newZoom = 1;
  if (!(el instanceof currentWindow.HTMLElement)) {
    const range = currentWindow.document.createRange();
    range.selectNodeContents(el);
    originalRect = range.getBoundingClientRect();
  } else {
    originalRect = el.getBoundingClientRect();
    if (!("currentCSSZoom" in el)) {
      newZoom = Number.parseFloat(currentWindow.getComputedStyle(el).zoom) || 1;
    }
  }
  const zoom = newZoom * baseZoom;
  return {
    width: originalRect.width * zoom,
    height: originalRect.height * zoom,
    left: originalRect.left * zoom,
    top: originalRect.top * zoom,
    right: originalRect.right * zoom,
    bottom: originalRect.bottom * zoom,
    x: originalRect.x * zoom,
    y: originalRect.y * zoom,
    zoom
  };
}
var isElementCovered = (el, rect, currentWindow) => {
  const x = rect.left + rect.width / 2;
  const y = rect.top + rect.height / 2;
  const topElement = currentWindow.document.elementFromPoint(x, y);
  if (!topElement) {
    return false;
  }
  if (topElement === el) {
    return false;
  }
  if (el?.contains(topElement)) {
    return false;
  }
  if (topElement?.contains(el)) {
    return false;
  }
  const rectOfTopElement = getRect(topElement, 1, currentWindow);
  const overlapRect = overlappedRect(rect, rectOfTopElement);
  if (!overlapRect) {
    return false;
  }
  logger(el, "Element is covered by another element", {
    topElement,
    el,
    rect,
    x,
    y
  });
  return true;
};
function elementRect(el, currentWindow, currentDocument, baseZoom = 1) {
  if (!el) {
    logger(el, "Element is not in the DOM hierarchy");
    return false;
  }
  if (!(el instanceof currentWindow.HTMLElement) && el.nodeType !== Node.TEXT_NODE && el.nodeName.toLowerCase() !== "svg") {
    logger(el, "Element is not in the DOM hierarchy");
    return false;
  }
  if (el instanceof currentWindow.HTMLElement) {
    const style = currentWindow.getComputedStyle(el);
    if (style.display === "none" || style.visibility === "hidden" || style.opacity === "0" && el.tagName !== "INPUT") {
      logger(el, "Element is hidden");
      return false;
    }
  }
  const rect = getRect(el, baseZoom, currentWindow);
  if (rect.width === 0 && rect.height === 0) {
    logger(el, "Element has no size");
    return false;
  }
  if (baseZoom === 1 && isElementCovered(el, rect, currentWindow)) {
    return false;
  }
  const isVisible = isElementPartiallyInViewport(
    rect,
    currentWindow,
    currentDocument
  );
  let parent = el;
  const parentUntilNonStatic = (currentNode) => {
    let parent2 = currentNode?.parentElement;
    while (parent2) {
      const style = currentWindow.getComputedStyle(parent2);
      if (style.position !== "static") {
        return parent2;
      }
      parent2 = parent2.parentElement;
    }
    return null;
  };
  while (parent && parent !== currentDocument.body) {
    if (!(parent instanceof currentWindow.HTMLElement)) {
      parent = parent.parentElement;
      continue;
    }
    const parentStyle = currentWindow.getComputedStyle(parent);
    if (parentStyle.overflow === "hidden") {
      const parentRect = getRect(parent, 1, currentWindow);
      const tolerance = 10;
      if (rect.right < parentRect.left - tolerance || rect.left > parentRect.right + tolerance || rect.bottom < parentRect.top - tolerance || rect.top > parentRect.bottom + tolerance) {
        logger(el, "element is partially or totally hidden by an ancestor", {
          rect,
          parentRect
        });
        return false;
      }
    }
    if (parentStyle.position === "fixed" || parentStyle.position === "sticky") {
      break;
    }
    if (parentStyle.position === "absolute") {
      parent = parentUntilNonStatic(parent);
    } else {
      parent = parent.parentElement;
    }
  }
  return {
    left: Math.round(rect.left),
    top: Math.round(rect.top),
    width: Math.round(rect.width),
    height: Math.round(rect.height),
    zoom: rect.zoom,
    isVisible
  };
}
function getNodeAttributes(node, currentWindow) {
  if (!node || !(node instanceof currentWindow.HTMLElement) || !node.attributes) {
    return {};
  }
  const attributesList = Array.from(node.attributes).map((attr) => {
    if (attr.name === "class") {
      return [attr.name, `.${attr.value.split(" ").join(".")}`];
    }
    if (!attr.value) {
      return [];
    }
    let value = attr.value;
    if (value.startsWith("data:image")) {
      value = "image";
    }
    if (value.length > MAX_VALUE_LENGTH) {
      value = `${value.slice(0, MAX_VALUE_LENGTH)}...`;
    }
    return [attr.name, value];
  });
  return Object.fromEntries(attributesList);
}
function midsceneGenerateHash(node, content, rect) {
  const slicedHash = generateHashId(rect, content);
  if (node) {
    if (!window.midsceneNodeHashCacheList) {
      setNodeHashCacheListOnWindow();
    }
    setNodeToCacheList(node, slicedHash);
  }
  return slicedHash;
}
function setNodeHashCacheListOnWindow() {
  if (typeof window !== "undefined") {
    window.midsceneNodeHashCacheList = [];
  }
}
function setNodeToCacheList(node, id) {
  if (typeof window !== "undefined") {
    if (getNodeFromCacheList(id)) {
      return;
    }
    window.midsceneNodeHashCacheList?.push({ node, id });
  }
}
function getNodeFromCacheList(id) {
  if (typeof window !== "undefined") {
    return window.midsceneNodeHashCacheList?.find(
      (item) => item.id === id
    )?.node;
  }
  return null;
}
function getTopDocument() {
  const container = document.body || document;
  return container;
}

// src/extractor/web-extractor.ts
var indexId = 0;
function tagNameOfNode(node) {
  let tagName = "";
  if (node instanceof HTMLElement) {
    tagName = node.tagName.toLowerCase();
  } else {
    const parentElement = node.parentElement;
    if (parentElement && parentElement instanceof HTMLElement) {
      tagName = parentElement.tagName.toLowerCase();
    }
  }
  return tagName ? `<${tagName}>` : "";
}
function collectElementInfo(node, currentWindow, currentDocument, baseZoom = 1, basePoint = { left: 0, top: 0 }) {
  const rect = elementRect(node, currentWindow, currentDocument, baseZoom);
  if (!rect) {
    return null;
  }
  if (rect.width < CONTAINER_MINI_WIDTH || rect.height < CONTAINER_MINI_HEIGHT) {
    return null;
  }
  if (basePoint.left !== 0 || basePoint.top !== 0) {
    rect.left += basePoint.left;
    rect.top += basePoint.top;
  }
  if (rect.height >= window.innerHeight && rect.width >= window.innerWidth) {
    return null;
  }
  if (isFormElement(node)) {
    const attributes = getNodeAttributes(node, currentWindow);
    let valueContent = attributes.value || attributes.placeholder || node.textContent || "";
    const nodeHashId = midsceneGenerateHash(node, valueContent, rect);
    const selector = setDataForNode(node, nodeHashId, false, currentWindow);
    const tagName = node.tagName.toLowerCase();
    if (node.tagName.toLowerCase() === "select") {
      const selectedOption = node.options[node.selectedIndex];
      valueContent = selectedOption.textContent || "";
    }
    if ((node.tagName.toLowerCase() === "input" || node.tagName.toLowerCase() === "textarea") && node.value) {
      valueContent = node.value;
    }
    const elementInfo = {
      id: nodeHashId,
      nodeHashId,
      locator: selector,
      nodeType: "FORM_ITEM Node" /* FORM_ITEM */,
      indexId: indexId++,
      attributes: {
        ...attributes,
        htmlTagName: `<${tagName}>`,
        nodeType: "FORM_ITEM Node" /* FORM_ITEM */
      },
      content: valueContent.trim(),
      rect,
      center: [
        Math.round(rect.left + rect.width / 2),
        Math.round(rect.top + rect.height / 2)
      ],
      zoom: rect.zoom,
      isVisible: rect.isVisible
    };
    return elementInfo;
  }
  if (isButtonElement(node)) {
    const rect2 = mergeElementAndChildrenRects(
      node,
      currentWindow,
      currentDocument,
      baseZoom
    );
    if (!rect2) {
      return null;
    }
    const attributes = getNodeAttributes(node, currentWindow);
    const pseudo = getPseudoElementContent(node, currentWindow);
    const content = node.innerText || pseudo.before || pseudo.after || "";
    const nodeHashId = midsceneGenerateHash(node, content, rect2);
    const selector = setDataForNode(node, nodeHashId, false, currentWindow);
    const elementInfo = {
      id: nodeHashId,
      indexId: indexId++,
      nodeHashId,
      nodeType: "BUTTON Node" /* BUTTON */,
      locator: selector,
      attributes: {
        ...attributes,
        htmlTagName: tagNameOfNode(node),
        nodeType: "BUTTON Node" /* BUTTON */
      },
      content,
      rect: rect2,
      center: [
        Math.round(rect2.left + rect2.width / 2),
        Math.round(rect2.top + rect2.height / 2)
      ],
      zoom: rect2.zoom,
      isVisible: rect2.isVisible
    };
    return elementInfo;
  }
  if (isImgElement(node)) {
    const attributes = getNodeAttributes(node, currentWindow);
    const nodeHashId = midsceneGenerateHash(node, "", rect);
    const selector = setDataForNode(node, nodeHashId, false, currentWindow);
    const elementInfo = {
      id: nodeHashId,
      indexId: indexId++,
      nodeHashId,
      locator: selector,
      attributes: {
        ...attributes,
        ...node.nodeName.toLowerCase() === "svg" ? {
          svgContent: "true"
        } : {},
        nodeType: "IMG Node" /* IMG */,
        htmlTagName: tagNameOfNode(node)
      },
      nodeType: "IMG Node" /* IMG */,
      content: "",
      rect,
      center: [
        Math.round(rect.left + rect.width / 2),
        Math.round(rect.top + rect.height / 2)
      ],
      zoom: rect.zoom,
      isVisible: rect.isVisible
    };
    return elementInfo;
  }
  if (isTextElement(node)) {
    const text = node.textContent?.trim().replace(/\n+/g, " ");
    if (!text) {
      return null;
    }
    const attributes = getNodeAttributes(node, currentWindow);
    const attributeKeys = Object.keys(attributes);
    if (!text.trim() && attributeKeys.length === 0) {
      return null;
    }
    const nodeHashId = midsceneGenerateHash(node, text, rect);
    const selector = setDataForNode(node, nodeHashId, true, currentWindow);
    const elementInfo = {
      id: nodeHashId,
      indexId: indexId++,
      nodeHashId,
      nodeType: "TEXT Node" /* TEXT */,
      locator: selector,
      attributes: {
        ...attributes,
        nodeType: "TEXT Node" /* TEXT */,
        htmlTagName: tagNameOfNode(node)
      },
      center: [
        Math.round(rect.left + rect.width / 2),
        Math.round(rect.top + rect.height / 2)
      ],
      content: text,
      rect,
      zoom: rect.zoom,
      isVisible: rect.isVisible
    };
    return elementInfo;
  }
  if (isAElement(node)) {
    const attributes = getNodeAttributes(node, currentWindow);
    const pseudo = getPseudoElementContent(node, currentWindow);
    const content = node.innerText || pseudo.before || pseudo.after || "";
    const nodeHashId = midsceneGenerateHash(node, content, rect);
    const selector = setDataForNode(node, nodeHashId, false, currentWindow);
    const elementInfo = {
      id: nodeHashId,
      indexId: indexId++,
      nodeHashId,
      nodeType: "Anchor Node" /* A */,
      locator: selector,
      attributes: {
        ...attributes,
        htmlTagName: tagNameOfNode(node),
        nodeType: "Anchor Node" /* A */
      },
      content,
      rect,
      center: [
        Math.round(rect.left + rect.width / 2),
        Math.round(rect.top + rect.height / 2)
      ],
      zoom: rect.zoom,
      isVisible: rect.isVisible
    };
    return elementInfo;
  }
  if (isContainerElement(node)) {
    const attributes = getNodeAttributes(node, currentWindow);
    const nodeHashId = midsceneGenerateHash(node, "", rect);
    const selector = setDataForNode(node, nodeHashId, false, currentWindow);
    const elementInfo = {
      id: nodeHashId,
      nodeHashId,
      indexId: indexId++,
      nodeType: "CONTAINER Node" /* CONTAINER */,
      locator: selector,
      attributes: {
        ...attributes,
        nodeType: "CONTAINER Node" /* CONTAINER */,
        htmlTagName: tagNameOfNode(node)
      },
      content: "",
      rect,
      center: [
        Math.round(rect.left + rect.width / 2),
        Math.round(rect.top + rect.height / 2)
      ],
      zoom: rect.zoom,
      isVisible: rect.isVisible
    };
    return elementInfo;
  }
  return null;
}
function extractTextWithPosition(initNode, debugMode2 = false) {
  const elementNode = extractTreeNode(initNode, debugMode2);
  const elementInfoArray = [];
  function dfsTopChildren(node) {
    if (node.node) {
      elementInfoArray.push(node.node);
    }
    for (let i = 0; i < node.children.length; i++) {
      dfsTopChildren(node.children[i]);
    }
  }
  dfsTopChildren({ children: elementNode.children, node: elementNode.node });
  return elementInfoArray;
}
function extractTreeNodeAsString(initNode, visibleOnly = false, debugMode2 = false) {
  const elementNode = extractTreeNode(initNode, debugMode2);
  return descriptionOfTree(elementNode, void 0, false, visibleOnly);
}
function extractTreeNode(initNode, debugMode2 = false) {
  setDebugMode(debugMode2);
  indexId = 0;
  const topDocument = getTopDocument();
  const startNode = initNode || topDocument;
  const topChildren = [];
  function dfs(node, currentWindow, currentDocument, baseZoom = 1, basePoint = { left: 0, top: 0 }) {
    if (!node) {
      return null;
    }
    if (node.nodeType && node.nodeType === 10) {
      return null;
    }
    const elementInfo = collectElementInfo(
      node,
      currentWindow,
      currentDocument,
      baseZoom,
      basePoint
    );
    if (node instanceof currentWindow.HTMLIFrameElement) {
      if (node.contentWindow && node.contentWindow) {
        return null;
      }
    }
    const nodeInfo = {
      node: elementInfo,
      children: []
    };
    if (elementInfo?.nodeType === "BUTTON Node" /* BUTTON */ || elementInfo?.nodeType === "IMG Node" /* IMG */ || elementInfo?.nodeType === "TEXT Node" /* TEXT */ || elementInfo?.nodeType === "FORM_ITEM Node" /* FORM_ITEM */ || elementInfo?.nodeType === "CONTAINER Node" /* CONTAINER */) {
      return nodeInfo;
    }
    const rect = getRect(node, baseZoom, currentWindow);
    for (let i = 0; i < node.childNodes.length; i++) {
      logger("will dfs", node.childNodes[i]);
      const childNodeInfo = dfs(
        node.childNodes[i],
        currentWindow,
        currentDocument,
        rect.zoom,
        basePoint
      );
      if (childNodeInfo) {
        nodeInfo.children.push(childNodeInfo);
      }
    }
    return nodeInfo;
  }
  const rootNodeInfo = dfs(startNode, window, document, 1, {
    left: 0,
    top: 0
  });
  if (rootNodeInfo) {
    topChildren.push(rootNodeInfo);
  }
  if (startNode === topDocument) {
    const iframes = document.querySelectorAll("iframe");
    for (let i = 0; i < iframes.length; i++) {
      const iframe = iframes[i];
      if (iframe.contentDocument && iframe.contentWindow) {
        const iframeInfo = collectElementInfo(iframe, window, document, 1);
        if (iframeInfo) {
          const iframeChildren = dfs(
            iframe.contentDocument.body,
            iframe.contentWindow,
            iframe.contentDocument,
            1,
            {
              left: iframeInfo.rect.left,
              top: iframeInfo.rect.top
            }
          );
          if (iframeChildren) {
            topChildren.push(iframeChildren);
          }
        }
      }
    }
  }
  return {
    node: null,
    children: topChildren
  };
}
function mergeElementAndChildrenRects(node, currentWindow, currentDocument, baseZoom = 1) {
  const selfRect = elementRect(node, currentWindow, currentDocument, baseZoom);
  if (!selfRect)
    return null;
  let minLeft = selfRect.left;
  let minTop = selfRect.top;
  let maxRight = selfRect.left + selfRect.width;
  let maxBottom = selfRect.top + selfRect.height;
  function traverse(child) {
    for (let i = 0; i < child.childNodes.length; i++) {
      const sub = child.childNodes[i];
      if (sub.nodeType === 1) {
        const rect = elementRect(sub, currentWindow, currentDocument, baseZoom);
        if (rect) {
          minLeft = Math.min(minLeft, rect.left);
          minTop = Math.min(minTop, rect.top);
          maxRight = Math.max(maxRight, rect.left + rect.width);
          maxBottom = Math.max(maxBottom, rect.top + rect.height);
        }
        traverse(sub);
      }
    }
  }
  traverse(node);
  return {
    ...selfRect,
    left: minLeft,
    top: minTop,
    width: maxRight - minLeft,
    height: maxBottom - minTop
  };
}

// src/extractor/locator.ts
var getElementIndex = (element) => {
  let index = 1;
  let prev = element.previousElementSibling;
  while (prev) {
    if (prev.nodeName.toLowerCase() === element.nodeName.toLowerCase()) {
      index++;
    }
    prev = prev.previousElementSibling;
  }
  return index;
};
var getTextNodeIndex = (textNode) => {
  let index = 1;
  let current = textNode.previousSibling;
  while (current) {
    if (current.nodeType === Node.TEXT_NODE) {
      index++;
    }
    current = current.previousSibling;
  }
  return index;
};
var getElementXPath = (element) => {
  if (element.nodeType === Node.TEXT_NODE) {
    const parentNode = element.parentNode;
    if (parentNode && parentNode.nodeType === Node.ELEMENT_NODE) {
      const parentXPath = getElementXPath(parentNode);
      const textIndex = getTextNodeIndex(element);
      const textContent = element.textContent?.trim();
      if (textContent) {
        return `${parentXPath}/text()[${textIndex}][normalize-space()="${textContent}"]`;
      }
      return `${parentXPath}/text()[${textIndex}]`;
    }
    return "";
  }
  if (element.nodeType !== Node.ELEMENT_NODE)
    return "";
  const el = element;
  if (el === document.documentElement) {
    return "/html";
  }
  if (el === document.body) {
    return "/html/body";
  }
  if (!el.parentNode) {
    return `/${el.nodeName.toLowerCase()}`;
  }
  const index = getElementIndex(el);
  const tagName = el.nodeName.toLowerCase();
  if (el.parentNode) {
    const parentXPath = getElementXPath(el.parentNode);
    return `${parentXPath}/${tagName}[${index}]`;
  }
  return `/${tagName}[${index}]`;
};
function generateXPaths(node) {
  if (!node)
    return [];
  const fullXPath = getElementXPath(node);
  return [fullXPath];
}
function getXpathsById(id) {
  const node = getNodeFromCacheList(id);
  if (!node) {
    return null;
  }
  return generateXPaths(node);
}
function getNodeInfoByXpath(xpath) {
  const xpathResult = document.evaluate(
    xpath,
    document,
    null,
    XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
    null
  );
  if (xpathResult.snapshotLength !== 1) {
    return null;
  }
  const node = xpathResult.snapshotItem(0);
  return node;
}
function getElementInfoByXpath(xpath) {
  const node = getNodeInfoByXpath(xpath);
  if (!node) {
    return null;
  }
  if (node instanceof HTMLElement) {
    const rect = getRect(node, 1, window);
    const isVisible = isElementPartiallyInViewport(rect, window, document, 1);
    if (!isVisible) {
      node.scrollIntoView({ behavior: "instant", block: "center" });
    }
  }
  return collectElementInfo(node, window, document, 1, {
    left: 0,
    top: 0
  });
}
export {
  descriptionOfTree,
  generateElementByPosition,
  getElementInfoByXpath,
  getNodeFromCacheList,
  getNodeInfoByXpath,
  getXpathsById,
  setNodeHashCacheListOnWindow,
  traverseTree,
  treeToList,
  trimAttributes,
  truncateText,
  extractTreeNode as webExtractNodeTree,
  extractTreeNodeAsString as webExtractNodeTreeAsString,
  extractTextWithPosition as webExtractTextWithPosition
};
