/*! For license information please see htmlElementDebug.js.LICENSE.txt */
"use strict";var midscene_element_inspector=(()=>{var e=Object.create,t=Object.defineProperty,n=Object.defineProperties,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertyNames,a=Object.getOwnPropertySymbols,s=Object.getPrototypeOf,h=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,f=(e,n,i)=>n in e?t(e,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[n]=i,d=(e,t)=>{for(var n in t||(t={}))h.call(t,n)&&f(e,n,t[n]);if(a)for(var n of a(t))l.call(t,n)&&f(e,n,t[n]);return e},c=(e,t)=>n(e,o(t)),u=(e,t)=>function(){return t||(0,e[r(e)[0]])((t={exports:{}}).exports,t),t.exports},p=(e,n,o,a)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let s of r(n))h.call(e,s)||s===o||t(e,s,{get:()=>n[s],enumerable:!(a=i(n,s))||a.enumerable});return e},m=u({"resolve-false:\\empty-stub"(e,t){t.exports={}}}),y=((n,i,o)=>(o=null!=n?e(s(n)):{},p(n&&n.__esModule?o:t(o,"default",{value:n,enumerable:!0}),n)))(u({"../../node_modules/.pnpm/js-sha256@0.11.0/node_modules/js-sha256/src/sha256.js"(e,t){!function(){var e="input is invalid type",n="object"==typeof window,i=n?window:{};i.JS_SHA256_NO_WINDOW&&(n=!1);var o=!n&&"object"==typeof self,r=!i.JS_SHA256_NO_NODE_JS&&"object"==typeof process&&process.versions&&process.versions.node;r?i=global:o&&(i=self);var a=!i.JS_SHA256_NO_COMMON_JS&&"object"==typeof t&&t.exports,s="function"==typeof define&&define.amd,h=!i.JS_SHA256_NO_ARRAY_BUFFER&&"undefined"!=typeof ArrayBuffer,l="0123456789abcdef".split(""),f=[-0x80000000,8388608,32768,128],d=[24,16,8,0],c=[0x428a2f98,0x71374491,0xb5c0fbcf,0xe9b5dba5,0x3956c25b,0x59f111f1,0x923f82a4,0xab1c5ed5,0xd807aa98,0x12835b01,0x243185be,0x550c7dc3,0x72be5d74,0x80deb1fe,0x9bdc06a7,0xc19bf174,0xe49b69c1,0xefbe4786,0xfc19dc6,0x240ca1cc,0x2de92c6f,0x4a7484aa,0x5cb0a9dc,0x76f988da,0x983e5152,0xa831c66d,0xb00327c8,0xbf597fc7,0xc6e00bf3,0xd5a79147,0x6ca6351,0x14292967,0x27b70a85,0x2e1b2138,0x4d2c6dfc,0x53380d13,0x650a7354,0x766a0abb,0x81c2c92e,0x92722c85,0xa2bfe8a1,0xa81a664b,0xc24b8b70,0xc76c51a3,0xd192e819,0xd6990624,0xf40e3585,0x106aa070,0x19a4c116,0x1e376c08,0x2748774c,0x34b0bcb5,0x391c0cb3,0x4ed8aa4a,0x5b9cca4f,0x682e6ff3,0x748f82ee,0x78a5636f,0x84c87814,0x8cc70208,0x90befffa,0xa4506ceb,0xbef9a3f7,0xc67178f2],u=["hex","array","digest","arrayBuffer"],p=[];(i.JS_SHA256_NO_NODE_JS||!Array.isArray)&&(Array.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)}),h&&(i.JS_SHA256_NO_ARRAY_BUFFER_IS_VIEW||!ArrayBuffer.isView)&&(ArrayBuffer.isView=function(e){return"object"==typeof e&&e.buffer&&e.buffer.constructor===ArrayBuffer});var y=function(e,t){return function(n){return new N(t,!0).update(n)[e]()}},b=function(e){var t=y("hex",e);r&&(t=x(t,e)),t.create=function(){return new N(e)},t.update=function(e){return t.create().update(e)};for(var n=0;n<u.length;++n){var i=u[n];t[i]=y(i,e)}return t},x=function(t,n){var o,r=m(),a=m().Buffer,s=n?"sha224":"sha256";return o=a.from&&!i.JS_SHA256_NO_BUFFER_FROM?a.from:function(e){return new a(e)},function(n){if("string"==typeof n)return r.createHash(s).update(n,"utf8").digest("hex");if(null==n)throw Error(e);return n.constructor===ArrayBuffer&&(n=new Uint8Array(n)),Array.isArray(n)||ArrayBuffer.isView(n)||n.constructor===a?r.createHash(s).update(o(n)).digest("hex"):t(n)}},g=function(e,t){return function(n,i){return new T(n,t,!0).update(i)[e]()}},w=function(e){var t=g("hex",e);t.create=function(t){return new T(t,e)},t.update=function(e,n){return t.create(e).update(n)};for(var n=0;n<u.length;++n){var i=u[n];t[i]=g(i,e)}return t};function N(e,t){t?(p[0]=p[16]=p[1]=p[2]=p[3]=p[4]=p[5]=p[6]=p[7]=p[8]=p[9]=p[10]=p[11]=p[12]=p[13]=p[14]=p[15]=0,this.blocks=p):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],e?(this.h0=0xc1059ed8,this.h1=0x367cd507,this.h2=0x3070dd17,this.h3=0xf70e5939,this.h4=0xffc00b31,this.h5=0x68581511,this.h6=0x64f98fa7,this.h7=0xbefa4fa4):(this.h0=0x6a09e667,this.h1=0xbb67ae85,this.h2=0x3c6ef372,this.h3=0xa54ff53a,this.h4=0x510e527f,this.h5=0x9b05688c,this.h6=0x1f83d9ab,this.h7=0x5be0cd19),this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0,this.is224=e}function T(t,n,i){var o,r=typeof t;if("string"===r){var a,s=[],l=t.length,f=0;for(o=0;o<l;++o)(a=t.charCodeAt(o))<128?s[f++]=a:(a<2048?s[f++]=192|a>>>6:(a<55296||a>=57344?s[f++]=224|a>>>12:(a=65536+((1023&a)<<10|1023&t.charCodeAt(++o)),s[f++]=240|a>>>18,s[f++]=128|a>>>12&63),s[f++]=128|a>>>6&63),s[f++]=128|63&a);t=s}else if("object"===r){if(null===t)throw Error(e);else if(h&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!Array.isArray(t)&&(!h||!ArrayBuffer.isView(t)))throw Error(e)}else throw Error(e);t.length>64&&(t=new N(n,!0).update(t).array());var d=[],c=[];for(o=0;o<64;++o){var u=t[o]||0;d[o]=92^u,c[o]=54^u}N.call(this,n,i),this.update(c),this.oKeyPad=d,this.inner=!0,this.sharedMemory=i}N.prototype.update=function(t){if(!this.finalized){var n,i=typeof t;if("string"!==i){if("object"===i){if(null===t)throw Error(e);else if(h&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!Array.isArray(t)&&(!h||!ArrayBuffer.isView(t)))throw Error(e)}else throw Error(e);n=!0}for(var o,r,a=0,s=t.length,l=this.blocks;a<s;){if(this.hashed&&(this.hashed=!1,l[0]=this.block,this.block=l[16]=l[1]=l[2]=l[3]=l[4]=l[5]=l[6]=l[7]=l[8]=l[9]=l[10]=l[11]=l[12]=l[13]=l[14]=l[15]=0),n)for(r=this.start;a<s&&r<64;++a)l[r>>>2]|=t[a]<<d[3&r++];else for(r=this.start;a<s&&r<64;++a)(o=t.charCodeAt(a))<128?l[r>>>2]|=o<<d[3&r++]:(o<2048?l[r>>>2]|=(192|o>>>6)<<d[3&r++]:(o<55296||o>=57344?l[r>>>2]|=(224|o>>>12)<<d[3&r++]:(o=65536+((1023&o)<<10|1023&t.charCodeAt(++a)),l[r>>>2]|=(240|o>>>18)<<d[3&r++],l[r>>>2]|=(128|o>>>12&63)<<d[3&r++]),l[r>>>2]|=(128|o>>>6&63)<<d[3&r++]),l[r>>>2]|=(128|63&o)<<d[3&r++]);this.lastByteIndex=r,this.bytes+=r-this.start,r>=64?(this.block=l[16],this.start=r-64,this.hash(),this.hashed=!0):this.start=r}return this.bytes>0xffffffff&&(this.hBytes+=this.bytes/0x100000000<<0,this.bytes=this.bytes%0x100000000),this}},N.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[16]=this.block,e[t>>>2]|=f[3&t],this.block=e[16],t>=56&&(this.hashed||this.hash(),e[0]=this.block,e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.hBytes<<3|this.bytes>>>29,e[15]=this.bytes<<3,this.hash()}},N.prototype.hash=function(){var e,t,n,i,o,r,a,s,h,l,f,d=this.h0,u=this.h1,p=this.h2,m=this.h3,y=this.h4,b=this.h5,x=this.h6,g=this.h7,w=this.blocks;for(e=16;e<64;++e)t=((o=w[e-15])>>>7|o<<25)^(o>>>18|o<<14)^o>>>3,n=((o=w[e-2])>>>17|o<<15)^(o>>>19|o<<13)^o>>>10,w[e]=w[e-16]+t+w[e-7]+n<<0;for(e=0,f=u&p;e<64;e+=4)this.first?(this.is224?(s=300032,g=(o=w[0]-0x543c9a5b)-0x8f1a6c7<<0,m=o+0x170e9b5<<0):(s=0x2a01a605,g=(o=w[0]-0xc881298)-0x5ab00ac6<<0,m=o+0x8909ae5<<0),this.first=!1):(t=(d>>>2|d<<30)^(d>>>13|d<<19)^(d>>>22|d<<10),n=(y>>>6|y<<26)^(y>>>11|y<<21)^(y>>>25|y<<7),i=(s=d&u)^d&p^f,o=g+n+(y&b^~y&x)+c[e]+w[e],r=t+i,g=m+o<<0,m=o+r<<0),t=(m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10),n=(g>>>6|g<<26)^(g>>>11|g<<21)^(g>>>25|g<<7),i=(h=m&d)^m&u^s,o=x+n+(g&y^~g&b)+c[e+1]+w[e+1],r=t+i,x=p+o<<0,t=((p=o+r<<0)>>>2|p<<30)^(p>>>13|p<<19)^(p>>>22|p<<10),n=(x>>>6|x<<26)^(x>>>11|x<<21)^(x>>>25|x<<7),i=(l=p&m)^p&d^h,o=b+n+(x&g^~x&y)+c[e+2]+w[e+2],r=t+i,b=u+o<<0,t=((u=o+r<<0)>>>2|u<<30)^(u>>>13|u<<19)^(u>>>22|u<<10),n=(b>>>6|b<<26)^(b>>>11|b<<21)^(b>>>25|b<<7),i=(f=u&p)^u&m^l,o=y+n+(b&x^~b&g)+c[e+3]+w[e+3],r=t+i,y=d+o<<0,d=o+r<<0,this.chromeBugWorkAround=!0;this.h0=this.h0+d<<0,this.h1=this.h1+u<<0,this.h2=this.h2+p<<0,this.h3=this.h3+m<<0,this.h4=this.h4+y<<0,this.h5=this.h5+b<<0,this.h6=this.h6+x<<0,this.h7=this.h7+g<<0},N.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,n=this.h2,i=this.h3,o=this.h4,r=this.h5,a=this.h6,s=this.h7,h=l[e>>>28&15]+l[e>>>24&15]+l[e>>>20&15]+l[e>>>16&15]+l[e>>>12&15]+l[e>>>8&15]+l[e>>>4&15]+l[15&e]+l[t>>>28&15]+l[t>>>24&15]+l[t>>>20&15]+l[t>>>16&15]+l[t>>>12&15]+l[t>>>8&15]+l[t>>>4&15]+l[15&t]+l[n>>>28&15]+l[n>>>24&15]+l[n>>>20&15]+l[n>>>16&15]+l[n>>>12&15]+l[n>>>8&15]+l[n>>>4&15]+l[15&n]+l[i>>>28&15]+l[i>>>24&15]+l[i>>>20&15]+l[i>>>16&15]+l[i>>>12&15]+l[i>>>8&15]+l[i>>>4&15]+l[15&i]+l[o>>>28&15]+l[o>>>24&15]+l[o>>>20&15]+l[o>>>16&15]+l[o>>>12&15]+l[o>>>8&15]+l[o>>>4&15]+l[15&o]+l[r>>>28&15]+l[r>>>24&15]+l[r>>>20&15]+l[r>>>16&15]+l[r>>>12&15]+l[r>>>8&15]+l[r>>>4&15]+l[15&r]+l[a>>>28&15]+l[a>>>24&15]+l[a>>>20&15]+l[a>>>16&15]+l[a>>>12&15]+l[a>>>8&15]+l[a>>>4&15]+l[15&a];return this.is224||(h+=l[s>>>28&15]+l[s>>>24&15]+l[s>>>20&15]+l[s>>>16&15]+l[s>>>12&15]+l[s>>>8&15]+l[s>>>4&15]+l[15&s]),h},N.prototype.toString=N.prototype.hex,N.prototype.digest=function(){this.finalize();var e=this.h0,t=this.h1,n=this.h2,i=this.h3,o=this.h4,r=this.h5,a=this.h6,s=this.h7,h=[e>>>24&255,e>>>16&255,e>>>8&255,255&e,t>>>24&255,t>>>16&255,t>>>8&255,255&t,n>>>24&255,n>>>16&255,n>>>8&255,255&n,i>>>24&255,i>>>16&255,i>>>8&255,255&i,o>>>24&255,o>>>16&255,o>>>8&255,255&o,r>>>24&255,r>>>16&255,r>>>8&255,255&r,a>>>24&255,a>>>16&255,a>>>8&255,255&a];return this.is224||h.push(s>>>24&255,s>>>16&255,s>>>8&255,255&s),h},N.prototype.array=N.prototype.digest,N.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(this.is224?28:32),t=new DataView(e);return t.setUint32(0,this.h0),t.setUint32(4,this.h1),t.setUint32(8,this.h2),t.setUint32(12,this.h3),t.setUint32(16,this.h4),t.setUint32(20,this.h5),t.setUint32(24,this.h6),this.is224||t.setUint32(28,this.h7),e},T.prototype=new N,T.prototype.finalize=function(){if(N.prototype.finalize.call(this),this.inner){this.inner=!1;var e=this.array();N.call(this,this.is224,this.sharedMemory),this.update(this.oKeyPad),this.update(e),N.prototype.finalize.call(this)}};var v=b();v.sha256=v,v.sha224=b(!0),v.sha256.hmac=w(),v.sha224.hmac=w(!0),a?t.exports=v:(i.sha256=v.sha256,i.sha224=v.sha224,s&&define(function(){return v}))}()}})()),b={};function x(e){return e instanceof Element&&(window.getComputedStyle(e).fontFamily||"").toLowerCase().indexOf("iconfont")>=0}function g(e){if(!(e instanceof HTMLElement))return!1;if(e.innerText)return!0;for(let t of["svg","button","input","textarea","select","option","img","a"])if(e.querySelectorAll(t).length>0)return!0;return!1}var w=!1;function N(...e){w&&console.log(...e)}var T="_midscene_retrieve_task_id";function v(e,t,n,i){if(!(e instanceof i.HTMLElement))return"";if(!T)return console.error("No task id found"),"";let o=`[${T}='${t}']`;return w&&(n?e.parentNode instanceof i.HTMLElement&&e.parentNode.setAttribute(T,t.toString()):e.setAttribute(T,t.toString())),o}function E(e,t){if(!(e instanceof t.HTMLElement))return{before:"",after:""};let n=t.getComputedStyle(e,"::before").getPropertyValue("content"),i=t.getComputedStyle(e,"::after").getPropertyValue("content");return{before:"none"===n?"":n.replace(/"/g,""),after:"none"===i?"":i.replace(/"/g,"")}}function M(e,t){let n=Math.max(e.left,t.left),i=Math.max(e.top,t.top),o=Math.min(e.right,t.right),r=Math.min(e.bottom,t.bottom);return n<o&&i<r?{left:n,top:i,right:o,bottom:r,width:o-n,height:r-i,x:n,y:i,zoom:1}:null}function A(e,t,n){let i,o=1;if(e instanceof n.HTMLElement)i=e.getBoundingClientRect(),"currentCSSZoom"in e||(o=Number.parseFloat(n.getComputedStyle(e).zoom)||1);else{let t=n.document.createRange();t.selectNodeContents(e),i=t.getBoundingClientRect()}let r=o*t;return{width:i.width*r,height:i.height*r,left:i.left*r,top:i.top*r,right:i.right*r,bottom:i.bottom*r,x:i.x*r,y:i.y*r,zoom:r}}var C=(e,t,n)=>{let i=t.left+t.width/2,o=t.top+t.height/2,r=n.document.elementFromPoint(i,o);return!(!r||r===e||(null==e?void 0:e.contains(r))||(null==r?void 0:r.contains(e)))&&!!M(t,A(r,1,n))&&(N(e,"Element is covered by another element",{topElement:r,el:e,rect:t,x:i,y:o}),!0)};function O(e,t,n,i=1){if(!e||!(e instanceof t.HTMLElement)&&e.nodeType!==Node.TEXT_NODE&&"svg"!==e.nodeName.toLowerCase())return N(e,"Element is not in the DOM hierarchy"),!1;if(e instanceof t.HTMLElement){let n=t.getComputedStyle(e);if("none"===n.display||"hidden"===n.visibility||"0"===n.opacity&&"INPUT"!==e.tagName)return N(e,"Element is hidden"),!1}let o=A(e,i,t);if(0===o.width&&0===o.height)return N(e,"Element has no size"),!1;if(1===i&&C(e,o,t))return!1;let r=function(e,t,n,i=2/3){let o=e.height,r=e.width,a=M(e,{left:0,top:0,width:t.innerWidth||n.documentElement.clientWidth,height:t.innerHeight||n.documentElement.clientHeight,right:t.innerWidth||n.documentElement.clientWidth,bottom:t.innerHeight||n.documentElement.clientHeight,x:0,y:0,zoom:1});return!!a&&a.width*a.height/(o*r)>=i}(o,t,n),a=e,s=e=>{let n=null==e?void 0:e.parentElement;for(;n;){if("static"!==t.getComputedStyle(n).position)return n;n=n.parentElement}return null};for(;a&&a!==n.body;){if(!(a instanceof t.HTMLElement)){a=a.parentElement;continue}let n=t.getComputedStyle(a);if("hidden"===n.overflow){let n=A(a,1,t);if(o.right<n.left-10||o.left>n.right+10||o.bottom<n.top-10||o.top>n.bottom+10)return N(e,"element is partially or totally hidden by an ancestor",{rect:o,parentRect:n}),!1}if("fixed"===n.position||"sticky"===n.position)break;a="absolute"===n.position?s(a):a.parentElement}return{left:Math.round(o.left),top:Math.round(o.top),width:Math.round(o.width),height:Math.round(o.height),zoom:o.zoom,isVisible:r}}function _(e,t){return e&&e instanceof t.HTMLElement&&e.attributes?Object.fromEntries(Array.from(e.attributes).map(e=>{if("class"===e.name)return[e.name,`.${e.value.split(" ").join(".")}`];if(!e.value)return[];let t=e.value;return t.startsWith("data:image")&&(t="image"),t.length>300&&(t=`${t.slice(0,300)}...`),[e.name,t]})):{}}function S(e,t,n){let i=function(e,t=""){let n=JSON.stringify({content:t,rect:e}),i=5,o="",r=y.sha256.create().update(n).hex().split("").map(e=>String.fromCharCode(97+Number.parseInt(e,16)%26)).join("");for(;i<r.length-1;){if(b[o=r.slice(0,i)]&&b[o]!==n){i++;continue}b[o]=n;break}return o}(n,t);return e&&(window.midsceneNodeHashCacheList||"undefined"!=typeof window&&(window.midsceneNodeHashCacheList=[]),function(e,t){var n,i,o,r;if("undefined"!=typeof window){if(i=t,"undefined"!=typeof window?null==(r=null==(o=window.midsceneNodeHashCacheList)?void 0:o.find(e=>e.id===i))?void 0:r.node:null)return;null==(n=window.midsceneNodeHashCacheList)||n.push({node:e,id:t})}}(e,i)),i}var L=0;function H(e){let t="";if(e instanceof HTMLElement)t=e.tagName.toLowerCase();else{let n=e.parentElement;n&&n instanceof HTMLElement&&(t=n.tagName.toLowerCase())}return t?`<${t}>`:""}function z(e,t,n,i=1,o={left:0,top:0}){var r;let a=O(e,t,n,i);if(!a||a.width<3||a.height<3||((0!==o.left||0!==o.top)&&(a.left+=o.left,a.top+=o.top),a.height>=window.innerHeight&&a.width>=window.innerWidth))return null;if(e instanceof HTMLElement&&("input"===e.tagName.toLowerCase()||"textarea"===e.tagName.toLowerCase()||"select"===e.tagName.toLowerCase()||"option"===e.tagName.toLowerCase())){let n=_(e,t),i=n.value||n.placeholder||e.textContent||"",o=S(e,i,a),r=v(e,o,!1,t),s=e.tagName.toLowerCase();return"select"===e.tagName.toLowerCase()&&(i=e.options[e.selectedIndex].textContent||""),("input"===e.tagName.toLowerCase()||"textarea"===e.tagName.toLowerCase())&&e.value&&(i=e.value),{id:o,nodeHashId:o,locator:r,nodeType:"FORM_ITEM Node",indexId:L++,attributes:c(d({},n),{htmlTagName:`<${s}>`,nodeType:"FORM_ITEM Node"}),content:i.trim(),rect:a,center:[Math.round(a.left+a.width/2),Math.round(a.top+a.height/2)],zoom:a.zoom,isVisible:a.isVisible}}if(e instanceof HTMLElement&&"button"===e.tagName.toLowerCase()){let o=function(e,t,n,i=1){let o=O(e,t,n,i);if(!o)return null;let r=o.left,a=o.top,s=o.left+o.width,h=o.top+o.height;return!function e(o){for(let l=0;l<o.childNodes.length;l++){let f=o.childNodes[l];if(1===f.nodeType){let o=O(f,t,n,i);o&&(r=Math.min(r,o.left),a=Math.min(a,o.top),s=Math.max(s,o.left+o.width),h=Math.max(h,o.top+o.height)),e(f)}}}(e),c(d({},o),{left:r,top:a,width:s-r,height:h-a})}(e,t,n,i);if(!o)return null;let r=_(e,t),a=E(e,t),s=e.innerText||a.before||a.after||"",h=S(e,s,o),l=v(e,h,!1,t);return{id:h,indexId:L++,nodeHashId:h,nodeType:"BUTTON Node",locator:l,attributes:c(d({},r),{htmlTagName:H(e),nodeType:"BUTTON Node"}),content:s,rect:o,center:[Math.round(o.left+o.width/2),Math.round(o.top+o.height/2)],zoom:o.zoom,isVisible:o.isVisible}}if(!g(e)&&e instanceof Element&&"none"!==window.getComputedStyle(e).getPropertyValue("background-image")||x(e)||e instanceof HTMLElement&&"img"===e.tagName.toLowerCase()||e instanceof SVGElement&&"svg"===e.tagName.toLowerCase()){let n=_(e,t),i=S(e,"",a),o=v(e,i,!1,t);return{id:i,indexId:L++,nodeHashId:i,locator:o,attributes:c(d(d({},n),"svg"===e.nodeName.toLowerCase()?{svgContent:"true"}:{}),{nodeType:"IMG Node",htmlTagName:H(e)}),nodeType:"IMG Node",content:"",rect:a,center:[Math.round(a.left+a.width/2),Math.round(a.top+a.height/2)],zoom:a.zoom,isVisible:a.isVisible}}if("#text"===e.nodeName.toLowerCase()&&!x(e)){let n=null==(r=e.textContent)?void 0:r.trim().replace(/\n+/g," ");if(!n)return null;let i=_(e,t),o=Object.keys(i);if(!n.trim()&&0===o.length)return null;let s=S(e,n,a),h=v(e,s,!0,t);return{id:s,indexId:L++,nodeHashId:s,nodeType:"TEXT Node",locator:h,attributes:c(d({},i),{nodeType:"TEXT Node",htmlTagName:H(e)}),center:[Math.round(a.left+a.width/2),Math.round(a.top+a.height/2)],content:n,rect:a,zoom:a.zoom,isVisible:a.isVisible}}if(e instanceof HTMLElement&&"a"===e.tagName.toLowerCase()){let n=_(e,t),i=E(e,t),o=e.innerText||i.before||i.after||"",r=S(e,o,a),s=v(e,r,!1,t);return{id:r,indexId:L++,nodeHashId:r,nodeType:"Anchor Node",locator:s,attributes:c(d({},n),{htmlTagName:H(e),nodeType:"Anchor Node"}),content:o,rect:a,center:[Math.round(a.left+a.width/2),Math.round(a.top+a.height/2)],zoom:a.zoom,isVisible:a.isVisible}}if(!(!(e instanceof HTMLElement)||g(e))&&window.getComputedStyle(e).getPropertyValue("background-color")){let n=_(e,t),i=S(e,"",a),o=v(e,i,!1,t);return{id:i,nodeHashId:i,indexId:L++,nodeType:"CONTAINER Node",locator:o,attributes:c(d({},n),{nodeType:"CONTAINER Node",htmlTagName:H(e)}),content:"",rect:a,center:[Math.round(a.left+a.width/2),Math.round(a.top+a.height/2)],zoom:a.zoom,isVisible:a.isVisible}}return null}function B(e,t=!1){let n=function(e,t=!1){w=t,L=0;let n=document.body||document,i=e||n,o=[];function r(e,t,n,i=1,o={left:0,top:0}){if(!e||e.nodeType&&10===e.nodeType)return null;let a=z(e,t,n,i,o);if(e instanceof t.HTMLIFrameElement&&e.contentWindow&&e.contentWindow)return null;let s={node:a,children:[]};if((null==a?void 0:a.nodeType)==="BUTTON Node"||(null==a?void 0:a.nodeType)==="IMG Node"||(null==a?void 0:a.nodeType)==="TEXT Node"||(null==a?void 0:a.nodeType)==="FORM_ITEM Node"||(null==a?void 0:a.nodeType)==="CONTAINER Node")return s;let h=A(e,i,t);for(let i=0;i<e.childNodes.length;i++){N("will dfs",e.childNodes[i]);let a=r(e.childNodes[i],t,n,h.zoom,o);a&&s.children.push(a)}return s}let a=r(i,window,document,1,{left:0,top:0});if(a&&o.push(a),i===n){let e=document.querySelectorAll("iframe");for(let t=0;t<e.length;t++){let n=e[t];if(n.contentDocument&&n.contentWindow){let e=z(n,window,document,1);if(e){let t=r(n.contentDocument.body,n.contentWindow,n.contentDocument,1,{left:e.rect.left,top:e.rect.top});t&&o.push(t)}}}}return{node:null,children:o}}(e,t),i=[];return!function e(t){t.node&&i.push(t.node);for(let n=0;n<t.children.length;n++)e(t.children[n])}({children:n.children,node:n.node}),i}console.log(B(document.body,!0)),console.log(JSON.stringify(B(document.body,!0))),"undefined"!=typeof window&&(window.extractTextWithPosition=B),"undefined"!=typeof window&&(window.midsceneVisibleRect=O)})();