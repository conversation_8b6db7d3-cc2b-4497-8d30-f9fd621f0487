
> @midscene/core@0.17.5 build E:\development\level-up\midscene\packages\core
> modern build

[1m[38;2;189;255;243m [39m[38;2;189;255;243m [39m[38;2;184;252;239mM[39m[38;2;179;249;235mo[39m[38;2;173;247;231md[39m[38;2;168;244;227me[39m[38;2;163;241;223mr[39m[38;2;158;238;219mn[39m[38;2;152;236;215m.[39m[38;2;147;233;211mj[39m[38;2;142;230;207ms[39m[38;2;142;230;207m [39m[38;2;137;227;203mM[39m[38;2;132;224;199mo[39m[38;2;126;222;194md[39m[38;2;121;219;190mu[39m[38;2;116;216;186ml[39m[38;2;111;213;182me[39m[38;2;111;213;182m [39m[38;2;105;211;178mv[39m[38;2;100;208;174m2[39m[38;2;95;205;170m.[39m[38;2;90;202;166m6[39m[38;2;84;200;162m0[39m[38;2;79;197;158m.[39m[38;2;74;194;154m6[39m[38;2;74;194;154m
[39m[22m
[1m[36minfo   [39m[22m Build succeed in [36m8.0s[39m
[1m[36minfo   [39m[22m Bundle generated 30 files

[1m[32mBundle Files                           Size[39m[22m
dist\lib\tree.d.ts                     94.0 B
dist\lib\utils.d.ts                    1.5 KB
dist\lib\index.d.ts                    5.8 KB
dist\lib\ai-model.d.ts                 1.5 KB
dist\lib\types-00c9086f.d.ts           17.3 KB
dist\lib\llm-planning-453ffce5.d.ts    3.6 KB
dist\es\tree.d.ts                      94.0 B
dist\es\utils.d.ts                     1.5 KB
dist\es\index.d.ts                     5.8 KB
dist\es\ai-model.d.ts                  1.5 KB
dist\es\types-00c9086f.d.ts            17.3 KB
dist\es\llm-planning-453ffce5.d.ts     3.6 KB
dist\types\tree.d.ts                   94.0 B
dist\types\utils.d.ts                  1.5 KB
dist\types\index.d.ts                  5.8 KB
dist\types\ai-model.d.ts               1.5 KB
dist\types\types-00c9086f.d.ts         17.3 KB
dist\types\llm-planning-453ffce5.d.ts  3.6 KB
dist\es\index.js                       16.2 KB
dist\es\utils.js                       561.0 B
dist\es\chunk-USEZKJPF.js              8.1 KB
dist\es\tree.js                        181.0 B
dist\es\ai-model.js                    573.0 B
dist\es\chunk-OAYGM2Z6.js              73.5 KB
dist\lib\index.js                      18.0 KB
dist\lib\utils.js                      954.0 B
dist\lib\chunk-USEZKJPF.js             9.9 KB
dist\lib\tree.js                       310.0 B
dist\lib\ai-model.js                   966.0 B
dist\lib\chunk-OAYGM2Z6.js             76.3 KB
