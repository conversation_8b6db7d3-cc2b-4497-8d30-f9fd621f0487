"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/node/fs.ts
var fs_exports = {};
__export(fs_exports, {
  findNearestPackageJson: () => findNearestPackageJson,
  getElementInfosScriptContent: () => getElementInfosScriptContent,
  getExtraReturnLogic: () => getExtraReturnLogic,
  getRunningPkgInfo: () => getRunningPkgInfo
});
module.exports = __toCommonJS(fs_exports);
var import_node_fs = require("fs");
var import_node_path = require("path");
var import_node_path2 = __toESM(require("path"));

// src/utils.ts
var import_js_sha256 = require("js-sha256");
var ifInBrowser = typeof window !== "undefined";
function assert(condition, message) {
  if (!condition) {
    throw new Error(message || "Assertion failed");
  }
}

// src/node/fs.ts
var pkgCacheMap = {};
function getRunningPkgInfo(dir) {
  if (ifInBrowser) {
    return null;
  }
  const dirToCheck = dir || process.cwd();
  if (pkgCacheMap[dirToCheck]) {
    return pkgCacheMap[dirToCheck];
  }
  const pkgDir = findNearestPackageJson(dirToCheck);
  const pkgJsonFile = pkgDir ? (0, import_node_path.join)(pkgDir, "package.json") : null;
  if (pkgDir && pkgJsonFile) {
    const { name, version } = JSON.parse((0, import_node_fs.readFileSync)(pkgJsonFile, "utf-8"));
    pkgCacheMap[dirToCheck] = {
      name: name || "midscene-unknown-package-name",
      version: version || "0.0.0",
      dir: pkgDir
    };
    return pkgCacheMap[dirToCheck];
  }
  return {
    name: "midscene-unknown-package-name",
    version: "0.0.0",
    dir: dirToCheck
  };
}
function findNearestPackageJson(dir) {
  const packageJsonPath = (0, import_node_path.join)(dir, "package.json");
  if ((0, import_node_fs.existsSync)(packageJsonPath)) {
    return dir;
  }
  const parentDir = (0, import_node_path.dirname)(dir);
  if (parentDir === dir) {
    return null;
  }
  return findNearestPackageJson(parentDir);
}
function getElementInfosScriptContent() {
  const currentFilePath = __filename;
  const pathDir = findNearestPackageJson((0, import_node_path.dirname)(currentFilePath));
  assert(pathDir, `can't find pathDir, with ${import_node_path.dirname}`);
  const scriptPath = import_node_path2.default.join(pathDir, "./dist/script/htmlElement.js");
  const elementInfosScriptContent = (0, import_node_fs.readFileSync)(scriptPath, "utf-8");
  return elementInfosScriptContent;
}
async function getExtraReturnLogic(tree = false) {
  if (ifInBrowser) {
    return null;
  }
  const elementInfosScriptContent = `${getElementInfosScriptContent()}midscene_element_inspector.setNodeHashCacheListOnWindow();`;
  if (tree) {
    return `${elementInfosScriptContent}midscene_element_inspector.webExtractNodeTree()`;
  }
  return `${elementInfosScriptContent}midscene_element_inspector.webExtractTextWithPosition()`;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  findNearestPackageJson,
  getElementInfosScriptContent,
  getExtraReturnLogic,
  getRunningPkgInfo
});
