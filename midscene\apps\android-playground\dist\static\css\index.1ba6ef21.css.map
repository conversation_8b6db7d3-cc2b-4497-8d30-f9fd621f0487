{"version": 3, "file": "static/css/index.1ba6ef21.css", "sources": ["webpack://android-playground/./src/App.less", "webpack://android-playground/./src/adb-device/index.less", "webpack://android-playground/./src/scrcpy-player/index.less", "webpack://android-playground/../../packages/visualizer/dist/index.css"], "sourcesContent": ["body {\n  margin: 0;\n  padding: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji';\n  font-size: 14px;\n}\n.app-container {\n  width: 100%;\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: #f5f5f5;\n}\n.app-content {\n  height: 100vh;\n  overflow: hidden;\n}\n.app-grid-layout {\n  height: 100%;\n  display: flex;\n}\n.app-grid-layout .ant-row {\n  flex: 1;\n  height: 100%;\n  display: flex;\n  flex-wrap: nowrap;\n  width: 100%;\n}\n.app-panel {\n  height: 100%;\n  background-color: #fff;\n  border-radius: 0;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n  transition: box-shadow 0.3s;\n  overflow: hidden;\n}\n.app-panel:hover {\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);\n}\n.app-panel.left-panel {\n  width: 480px;\n  flex: none;\n  overflow: hidden;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n.app-panel.right-panel {\n  border-radius: 0;\n  flex: 1;\n  overflow: hidden;\n  box-shadow: -4px 0px 20px 0px #0000000A;\n}\n.panel-content {\n  padding: 12px 24px 24px 24px;\n  height: 100%;\n  overflow: auto;\n  display: flex;\n  flex-direction: column;\n  border-left: 1px solid rgba(0, 0, 0, 0.08);\n}\n.panel-content.left-panel-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  overflow: hidden;\n}\n.panel-content.right-panel-content {\n  border-radius: 0;\n}\n.panel-content h2 {\n  color: #000;\n  font-size: 18px;\n  margin-top: 16px;\n  margin-bottom: 12px;\n}\n.panel-content canvas {\n  max-width: 100%;\n  margin-top: 16px;\n  border: 1px solid #f0f0f0;\n  border-radius: 4px;\n}\n.command-form {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  overflow: hidden;\n}\n.command-form .form-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  gap: 24px;\n}\n.command-form .command-input-wrapper {\n  margin-top: 8px;\n}\n.result-container {\n  flex: 1;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  min-height: 0;\n  position: relative;\n  height: 100%;\n}\n@media (max-width: 768px) {\n  .app-container {\n    height: auto;\n    min-height: 100vh;\n  }\n  .app-grid-layout .ant-row {\n    flex-wrap: wrap !important;\n  }\n  .app-panel {\n    margin-bottom: 16px;\n    height: auto;\n    min-height: 200px;\n    width: 100% !important;\n    flex: 0 0 100% !important;\n  }\n  .app-panel:first-child {\n    border-radius: 20px;\n  }\n  .app-panel:first-child .panel-content {\n    border-radius: 20px;\n  }\n  .panel-content {\n    padding: 12px;\n  }\n  .panel-content h2 {\n    font-size: 16px;\n    margin-bottom: 12px;\n    padding-bottom: 6px;\n  }\n  .panel-content textarea {\n    min-height: 100px;\n  }\n}\n@media (min-width: 769px) and (max-width: 992px) {\n  .app-panel {\n    margin-bottom: 16px;\n    min-height: 300px;\n  }\n}\n.resize-handle {\n  width: 2px;\n  background-color: #f0f0f0;\n  transition: background-color 0.2s;\n}\n.resize-handle:hover {\n  background-color: #1677ff;\n}\n", ".device-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16px;\n}\n.device-header .device-title-container {\n  display: flex;\n  align-items: center;\n}\n.device-header .device-title {\n  margin: 0;\n  font-size: 18px;\n  color: #000;\n  margin-right: 12px;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n.device-dropdown-button {\n  border: none;\n  padding: 4px 12px 4px 8px;\n  display: flex;\n  align-items: center;\n  background: #f0f0f0;\n  border-radius: 20px;\n  box-shadow: none;\n  height: 32px;\n}\n.device-dropdown-button .device-icon-container {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background: #3b82f6;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n.device-dropdown-button .device-icon-container .device-icon {\n  font-size: 14px;\n  color: white;\n}\n.device-dropdown-button .device-icon-container .status-indicator {\n  position: absolute;\n  right: -5px;\n  bottom: -5px;\n  display: flex;\n  align-items: center;\n}\n.device-dropdown-button .device-name {\n  font-weight: bold;\n  font-size: 16px;\n  color: #333;\n  max-width: 120px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  margin-left: 8px;\n  margin-right: 4px;\n}\n.device-dropdown-button .device-name.no-device {\n  color: #999;\n  font-weight: normal;\n  font-style: italic;\n}\n.device-dropdown-button .dropdown-arrow {\n  color: #666;\n  font-size: 12px;\n  transform: scaleY(0.6);\n  font-weight: bold;\n}\n.device-dropdown {\n  width: 430px;\n  background: #fff;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0px 10px 20px 0px #00000005;\n  border: 1px solid #EAEDF1;\n}\n.device-dropdown .dropdown-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 16px 12px;\n}\n.device-dropdown .dropdown-header .dropdown-title {\n  font-weight: bold;\n  font-size: 16px;\n  color: #333;\n}\n.device-dropdown .device-list-item {\n  padding: 5px 17px 9px 6px;\n  cursor: pointer;\n}\n.device-dropdown .device-list-item.selected {\n  background: #2B83FF14;\n}\n.device-dropdown .device-list-item.offline {\n  cursor: not-allowed;\n  opacity: 0.5;\n}\n.device-dropdown .device-list-item .device-item-content {\n  display: flex;\n  align-items: center;\n}\n.device-dropdown .device-list-item .device-item-content .device-item-icon-container {\n  width: 34px;\n  height: 34px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 12px;\n}\n.device-dropdown .device-list-item .device-item-content .device-item-icon-container .device-item-icon {\n  font-size: 22px;\n  color: #666;\n}\n.device-dropdown .device-list-item .device-item-content .device-item-info {\n  flex: 1;\n}\n.device-dropdown .device-list-item .device-item-content .device-item-info .device-item-name {\n  font-weight: bold;\n  font-size: 15px;\n  color: #333;\n}\n.device-dropdown .device-list-item .device-item-content .device-item-info .device-item-status {\n  display: flex;\n  align-items: center;\n  margin-top: 4px;\n}\n.device-dropdown .device-list-item .device-item-content .device-item-info .device-item-status .status-badge {\n  display: flex;\n  align-items: center;\n  margin-right: 12px;\n}\n.device-dropdown .device-list-item .device-item-content .device-item-info .device-item-status .status-badge .status-text {\n  color: #666;\n  font-size: 12px;\n}\n.device-dropdown .device-list-item .device-item-content .device-item-info .device-item-status .device-id-container {\n  color: #999;\n  font-size: 12px;\n}\n.device-dropdown .device-list-item .device-item-content .current-device-indicator {\n  margin-left: auto;\n  color: #1890ff;\n  font-weight: bold;\n  font-size: 13px;\n}\n.device-dropdown .device-list-empty {\n  padding: 20px;\n  text-align: center;\n  color: #999;\n}\n.status-dot {\n  margin-right: 4px;\n  font-size: 12px;\n}\n.status-divider {\n  margin: 0 4px;\n}\n", ".scrcpy-container {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n.scrcpy-container .ant-card {\n  background: transparent;\n  border: none;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n.scrcpy-container .ant-card .ant-card-head {\n  display: none;\n}\n.scrcpy-container .ant-card .ant-card-body {\n  padding: 0;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n.scrcpy-container .ant-card .ant-card-body .ant-row {\n  flex: 1;\n  margin: 0 !important;\n}\n.scrcpy-container .ant-card .ant-card-body .ant-row .ant-col {\n  padding: 0 !important;\n}\n.scrcpy-container .ant-card .ant-card-body .ant-row .ant-col:first-child {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n.scrcpy-container .header-bar {\n  display: grid;\n  grid-template-columns: 1fr auto 1fr;\n  align-items: center;\n  padding: 8px 16px;\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(10px);\n  border-top-left-radius: 12px;\n  border-top-right-radius: 12px;\n  border: 1px solid #F5F5F5;\n}\n.scrcpy-container .header-bar .header-left {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n.scrcpy-container .header-bar .header-left .ant-typography {\n  color: #333;\n  font-size: 14px;\n  margin: 0;\n}\n.scrcpy-container .header-bar .header-left .anticon {\n  color: #999;\n  font-size: 14px;\n  cursor: pointer;\n}\n.scrcpy-container .header-bar .header-left .anticon:hover {\n  color: #666;\n}\n.scrcpy-container .header-bar .screen-info {\n  text-align: center;\n}\n.scrcpy-container .header-bar .screen-info .ant-typography {\n  color: #666;\n  font-size: 14px;\n  margin: 0;\n}\n.scrcpy-container .header-bar .header-right {\n  justify-self: end;\n  display: flex;\n  gap: 8px;\n  align-items: center;\n}\n.scrcpy-container .header-bar .header-right .ant-btn {\n  width: 16px;\n  height: 16px;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.scrcpy-container .header-bar .header-right .ant-btn .link-icon {\n  font-size: 16px;\n  line-height: 1;\n}\n.scrcpy-container .video-section {\n  position: relative;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n.scrcpy-container .video-section .video-container {\n  width: 100%;\n  flex: 1;\n  background-color: #F5F5F5;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  border-bottom-left-radius: 12px;\n  border-bottom-right-radius: 12px;\n  overflow: hidden;\n  position: relative;\n}\n.scrcpy-container .video-section .video-container .canvas-wrapper {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n.scrcpy-container .video-section .video-container canvas {\n  border: none !important;\n  box-shadow: none !important;\n}\n.scrcpy-container .video-section .video-container .empty-state {\n  position: relative;\n  z-index: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16px;\n  padding: 24px;\n  text-align: center;\n}\n.scrcpy-container .video-section .video-container .empty-state .empty-state-icon {\n  font-size: 48px;\n  line-height: 1;\n}\n.scrcpy-container .video-section .video-container .empty-state .empty-state-text {\n  color: #666;\n  font-size: 16px;\n  margin-bottom: 8px;\n}\n.scrcpy-container .video-section .video-container .empty-state .loading-spinner {\n  margin-top: 16px;\n}\n.scrcpy-container .video-section .video-container .empty-state .loading-spinner .ant-spin {\n  color: #1677ff;\n}\n.scrcpy-container .video-section .video-container .empty-state .ant-btn {\n  min-width: 120px;\n  height: 40px;\n  font-size: 16px;\n  border-radius: 20px;\n}\n.scrcpy-container .video-section .screen-info {\n  position: absolute;\n  bottom: 8px;\n  left: 8px;\n  background: rgba(0, 0, 0, 0.5);\n  padding: 4px 8px;\n  border-radius: 4px;\n}\n.scrcpy-container .video-section .screen-info .ant-typography {\n  color: white;\n  margin: 0;\n}\n/* 确保响应式布局 */\n@media (max-width: 576px) {\n  .scrcpy-container .header-bar {\n    grid-template-columns: 1fr;\n    grid-template-rows: auto auto auto;\n    padding: 8px;\n    gap: 8px;\n  }\n  .scrcpy-container .header-bar .header-left {\n    text-align: center;\n  }\n  .scrcpy-container .header-bar .screen-info {\n    text-align: center;\n  }\n  .scrcpy-container .header-bar .header-right {\n    justify-self: center;\n    width: 100%;\n    justify-content: flex-end;\n  }\n}\n", "/* src/component/playground/index.less */\nbody {\n  margin: 0;\n  font-family:\n    -apple-system,\n    BlinkMacSystemFont,\n    \"Segoe UI\",\n    \"Noto Sans\",\n    Helvetica,\n    Arial,\n    sans-serif,\n    \"Apple Color Emoji\",\n    \"Segoe UI Emoji\";\n  font-size: 14px;\n}\n.playground-container,\n.image-describer {\n  width: 100%;\n  height: 100%;\n}\n.playground-container.vertical-mode,\n.image-describer.vertical-mode {\n  height: inherit;\n}\n.playground-container.vertical-mode .form-part h3,\n.image-describer.vertical-mode .form-part h3 {\n  font-size: 14px;\n  line-height: 1.6;\n}\n.playground-container .playground-header,\n.image-describer .playground-header {\n  padding: 10px 10px 30px;\n}\n.playground-container .playground-left-panel,\n.image-describer .playground-left-panel {\n  width: 100%;\n  background-color: #FFF;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  overflow-y: auto !important;\n}\n.playground-container .playground-left-panel .ant-form,\n.image-describer .playground-left-panel .ant-form {\n  flex-grow: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n}\n.playground-container .form-part h3,\n.image-describer .form-part h3 {\n  margin-top: 0;\n  margin-bottom: 12px;\n  font-size: 18px;\n}\n.playground-container .form-part .form-sub-title,\n.image-describer .form-part .form-sub-title {\n  margin-bottom: 12px;\n  font-size: 14px;\n}\n.playground-container .form-part .switch-btn-wrapper .ant-btn,\n.image-describer .form-part .switch-btn-wrapper .ant-btn {\n  padding: 0;\n}\n.playground-container .loading-container,\n.image-describer .loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  box-sizing: border-box;\n  padding: 20px 20px;\n}\n.playground-container .loading-container .loading-progress-text,\n.image-describer .loading-container .loading-progress-text {\n  text-align: center;\n  width: 100%;\n  color: #777;\n  margin-top: 16px;\n}\n.playground-container .loading-container .loading-progress-text-tab-info,\n.image-describer .loading-container .loading-progress-text-tab-info {\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n.playground-container .loading-container .loading-progress-text-progress,\n.image-describer .loading-container .loading-progress-text-progress {\n  height: 60px;\n}\n.playground-container .result-wrapper,\n.image-describer .result-wrapper {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  box-sizing: border-box;\n  border-radius: 4px;\n  flex: 1 1 auto;\n  min-height: 0;\n  overflow: auto;\n}\n.playground-container .result-wrapper.result-wrapper-compact,\n.image-describer .result-wrapper.result-wrapper-compact {\n  padding: 0;\n}\n.playground-container .result-wrapper.vertical-mode-result,\n.image-describer .result-wrapper.vertical-mode-result {\n  height: inherit;\n  min-height: 300px;\n  display: flex;\n  flex-direction: column;\n  flex: 1 1;\n}\n.playground-container .result-wrapper pre,\n.image-describer .result-wrapper pre {\n  display: block;\n  word-wrap: break-word;\n  white-space: pre-wrap;\n}\n.playground-container .result-wrapper .result-empty-tip,\n.image-describer .result-wrapper .result-empty-tip {\n  flex: 1 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 200px;\n  width: 100%;\n  height: 100%;\n  border: 1px solid rgba(0, 0, 0, 0.12);\n  border-radius: 8px;\n  color: rgba(0, 0, 0, 0.45);\n  font-size: 14px;\n  box-sizing: border-box;\n}\n.input-wrapper {\n  box-sizing: border-box;\n}\n.input-wrapper .mode-radio-group-wrapper {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n.input-wrapper .mode-radio-group {\n  display: flex;\n  align-items: center;\n  height: 100%;\n}\n.input-wrapper .mode-radio-group .ant-radio-button-wrapper {\n  height: 24px;\n  padding: 0 8px;\n  line-height: 24px;\n  border-radius: 11px;\n  margin-right: 8px;\n  box-shadow: none;\n  border: none;\n  background-color: #F7F7F7;\n  font-size: 12px;\n}\n.input-wrapper .mode-radio-group .ant-radio-button-wrapper:before {\n  display: none;\n}\n.input-wrapper .mode-radio-group .ant-radio-button-wrapper:focus-within {\n  outline: none;\n}\n.input-wrapper .mode-radio-group .ant-radio-button-wrapper.ant-radio-button-wrapper-checked {\n  background-color: #2B83FF;\n  border-color: #2B83FF;\n  color: white;\n}\n.input-wrapper .mode-radio-group .ant-radio-button-wrapper.ant-radio-button-wrapper-checked:hover {\n  color: #fff;\n}\n.input-wrapper .mode-radio-group .ant-radio-button-wrapper:hover {\n  color: #2B83FF;\n}\n.input-wrapper .main-side-console-input {\n  position: relative;\n  margin-top: 10px;\n}\n.input-wrapper .main-side-console-input .main-side-console-input-textarea {\n  border-radius: 12px;\n  border: 1px solid rgba(0, 0, 0, 0.12);\n  padding: 12px 16px 52px 16px;\n  transition: background-color 0.2s ease;\n  overflow-y: auto;\n  white-space: pre-wrap;\n  line-height: 21px;\n  scrollbar-width: thin;\n}\n.input-wrapper .main-side-console-input .main-side-console-input-textarea::-webkit-scrollbar {\n  width: 6px;\n}\n.input-wrapper .main-side-console-input .main-side-console-input-textarea::-webkit-scrollbar-thumb {\n  background-color: rgba(0, 0, 0, 0.2);\n  border-radius: 3px;\n}\n.input-wrapper .main-side-console-input.disabled .form-controller-wrapper {\n  background-color: transparent;\n}\n.input-wrapper .ant-input {\n  padding-bottom: 40px;\n}\n.input-wrapper .form-controller-wrapper {\n  position: absolute;\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  width: calc(100% - 32px);\n  box-sizing: border-box;\n  align-items: flex-end;\n  gap: 8px;\n  background-color: #FFF;\n  left: 16px;\n  bottom: 0.5px;\n  padding: 8px 0;\n  line-height: 32px;\n  transition: background-color 0.2s ease;\n}\n.input-wrapper .settings-wrapper {\n  display: flex;\n  flex-direction: row;\n  gap: 2px;\n  color: #777;\n  flex-wrap: wrap;\n}\n.input-wrapper .settings-wrapper.settings-wrapper-hover {\n  color: #3b3b3b;\n}\n\n/* src/component/player.less */\n.player-container {\n  width: 100%;\n  height: 100%;\n  max-width: 100%;\n  max-height: 100%;\n  padding: 8px;\n  background: #fff;\n  box-sizing: border-box;\n  border: 1px solid #0000000F;\n  border-radius: 8px;\n  line-height: 100%;\n  margin: 0 auto;\n  display: flex;\n  flex-direction: column;\n  overflow: visible;\n  min-height: 300px;\n  position: relative;\n}\n.player-container .canvas-container {\n  flex: 1 1;\n  min-height: 200px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  overflow: hidden;\n  padding: 8px;\n  margin-bottom: 12px;\n  position: relative;\n  background-color: #F5F5F5;\n  border-radius: inherit;\n}\n.player-container .canvas-container canvas {\n  max-width: 100%;\n  max-height: 100%;\n  box-sizing: border-box;\n  display: block;\n  margin: 0 auto;\n  object-fit: contain;\n}\n.player-container .player-timeline-wrapper {\n  width: 100%;\n  height: 4px;\n  flex: none;\n  margin-bottom: 2px;\n  position: relative;\n}\n.player-container .player-timeline {\n  width: 100%;\n  height: 4px;\n  background: #666;\n  position: relative;\n  flex-shrink: 0;\n}\n.player-container .player-timeline .player-timeline-progress {\n  transition-timing-function: linear;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 4px;\n  background: #2B83FF;\n}\n.player-container .player-tools-wrapper {\n  width: 100%;\n  height: 72px;\n  flex: none;\n  position: relative;\n  padding-top: 15px;\n  padding-bottom: 15px;\n  box-sizing: border-box;\n}\n.player-container .player-tools {\n  width: 100%;\n  height: 42px;\n  max-width: 100%;\n  overflow: hidden;\n  color: #000;\n  font-size: 14px;\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  flex-shrink: 0;\n}\n.player-container .player-tools .ant-spin {\n  color: #333;\n}\n.player-container .player-tools .player-control {\n  flex-grow: 1;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  overflow: hidden;\n}\n.player-container .player-tools .status-icon {\n  transition: 0.2s;\n  width: 32px;\n  height: 32px;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  margin-left: 10px;\n}\n.player-container .player-tools .status-icon:hover {\n  cursor: pointer;\n  background: #F0f0f0;\n}\n.player-container .player-tools .status-text {\n  flex-grow: 1;\n  flex-shrink: 1;\n  min-width: 0;\n  overflow: hidden;\n  position: relative;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  width: 0;\n}\n.player-container .player-tools .title {\n  font-weight: 600;\n}\n.player-container .player-tools .title,\n.player-container .player-tools .subtitle {\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n  width: 100%;\n}\n.player-container .player-tools .player-tools-item {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n}\n\n/* src/component/logo.less */\n.logo img {\n  height: 30px;\n  line-height: 30px;\n  vertical-align: baseline;\n  vertical-align: -webkit-baseline-middle;\n}\n.logo-with-star-wrapper {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n}\n\n/* src/component/blackboard.less */\n.blackboard .footer {\n  color: #aaa;\n}\n.blackboard ul {\n  padding-left: 0px;\n}\n.blackboard li {\n  list-style: none;\n}\n.blackboard .bottom-tip {\n  height: 30px;\n}\n.blackboard .bottom-tip-item {\n  max-width: 500px;\n  color: #AAA;\n  text-overflow: ellipsis;\n  word-wrap: break-word;\n}\n.blackboard-filter {\n  margin: 10px 0;\n}\n.blackboard-main-content canvas {\n  width: 100%;\n  border: 1px solid #888;\n  box-sizing: border-box;\n}\n\n/* src/component/shiny-text.less */\n.shiny-text {\n  position: relative;\n  display: inline-block;\n  background-size: 300% auto;\n  -webkit-background-clip: text;\n  background-clip: text;\n  color: transparent;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n  overflow: hidden;\n  padding: 0 5px;\n  animation: textGradient 8s ease infinite;\n  background-image: linear-gradient(45deg, #2b83ff, #6a11cb, #2575fc, #4481eb);\n}\n.shiny-text::after {\n  content: \"\";\n  position: absolute;\n  top: -10%;\n  left: -150%;\n  width: 120%;\n  height: 120%;\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 10%, rgba(255, 255, 255, 0.6) 50%, rgba(255, 255, 255, 0.1) 90%, rgba(255, 255, 255, 0) 100%);\n  transform: skewX(-20deg) translateY(0);\n  animation: shine var(--animation-duration, 5s) cubic-bezier(0.25, 0.1, 0.25, 1) infinite;\n  z-index: 1;\n  pointer-events: none;\n}\n.shiny-text.disabled {\n  animation: none;\n  background: #2b83ff;\n  -webkit-background-clip: text;\n  background-clip: text;\n}\n.shiny-text.disabled::after {\n  animation: none;\n  display: none;\n}\n@keyframes shine {\n  0% {\n    left: -150%;\n    opacity: 0.7;\n  }\n  20% {\n    opacity: 1;\n  }\n  80% {\n    opacity: 1;\n  }\n  100% {\n    left: 250%;\n    opacity: 0.7;\n  }\n}\n@keyframes textGradient {\n  0% {\n    background-position: 0% 50%;\n  }\n  50% {\n    background-position: 100% 50%;\n  }\n  100% {\n    background-position: 0% 50%;\n  }\n}\n\n/* src/component/github-star.less */\n.github-star {\n  height: 22px;\n}\n\n/* src/component/describer.less */\n.image-describer {\n  position: relative;\n}\n.image-describer .describe-text {\n  box-sizing: border-box;\n  position: absolute;\n  background: #000;\n  width: 100%;\n  height: 30px;\n  left: 0;\n  bottom: 0;\n  color: #FFF;\n  font-size: 12px;\n  padding: 10px;\n}\n.image-describer .describe-text.success {\n  background: #047704;\n}\n.image-describer .describe-text.error {\n  background: #870707;\n}\n"], "names": [], "mappings": "AADA,mKAQA,mGAQA,0CAKA,0CAKA,sFAQA,sIASA,gDAIA,2GASA,+FAOA,mIASA,iGAOA,mDAIA,+EAOA,gGAOA,6EAOA,oFAOA,oDAIA,uHAUA,yBACE,4CAKA,mDAIA,wGAQA,gFAIA,4BAIA,uEAMA,0CAKF,+CACE,gDAMF,kFAMA,8CC/KA,kEAOA,uEAKA,qHASA,+JAWA,oLAWA,sFAKA,0IAQA,0LAYA,4FAMA,uGAOA,yIASA,uHAOA,4FAMA,2EAKA,iEAIA,yEAKA,wFAKA,oLASA,gIAKA,iFAIA,sIAMA,6IAMA,8JAMA,mJAKA,6IAKA,gJAOA,8EAMA,4CAKA,6BC1LA,4EAQA,iGAQA,wDAIA,+FAOA,8EAKA,iFAIA,mHAMA,uRAaA,mFAMA,8FAMA,6FAMA,qEAIA,6DAIA,8FAMA,qGAOA,+IASA,6FAKA,6FAOA,qPAaA,+KAWA,yGAKA,yLAWA,8GAKA,6HAMA,gGAIA,wGAIA,sIAOA,2IASA,kFAKA,yBACE,8GAOA,wGAIA,qGC9MF,yJAMA,8DAKA,kFAIA,8HAKA,oGAIA,iNASA,+KAOA,kHAMA,+HAKA,iIAIA,sKAQA,mLAOA,uMAMA,yJAIA,qOAaA,+HAIA,2LAQA,uIAMA,mTAeA,qCAIA,uGAMA,6EAMA,8MAYA,+EAIA,qFAIA,qJAMA,6GAIA,+EAIA,0EAKA,0PAWA,uGAIA,4IAKA,iGAIA,8CAIA,8SAiBA,gFAOA,sEAIA,6RAmBA,mOAcA,+IASA,+GAQA,yGAQA,4JASA,sJAUA,kNAcA,qDAIA,+HAQA,+LAYA,qFAKA,0MAaA,uDAIA,sJAOA,yHAOA,sGAOA,sFAMA,+BAIA,8BAIA,+BAIA,oCAIA,oGAOA,iCAIA,uFAMA,mVAgBA,uUAcA,yGAOA,uDAKA,+FAoBA,0GAcA,yBAIA,mCAIA,sKAaA,2DAIA"}