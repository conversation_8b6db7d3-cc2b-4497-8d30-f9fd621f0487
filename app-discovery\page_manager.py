"""
页面节点管理器 - 负责页面节点的存储、检索和管理
"""
import os
import json
import sqlite3
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
import pickle

from models import PageTreeNode
from simple_vectorizer import SimpleTextVectorizer, cosine_similarity_simple


class PageNodeManager:
    """页面节点管理器"""
    
    def __init__(self, db_path: str = "page_nodes.db", vector_cache_path: str = "vectors.pkl"):
        self.db_path = db_path
        self.vector_cache_path = vector_cache_path
        self.vectorizer = SimpleTextVectorizer(max_features=1000)
        self.nodes_cache = {}  # 内存缓存
        self.vectors_cache = {}  # 向量缓存

        self._init_database()
        self._load_vectorizer()
    
    def _init_database(self):
        """初始化SQLite数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建页面节点表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS page_nodes (
                id TEXT PRIMARY KEY,
                data TEXT NOT NULL,
                semantic_text TEXT,
                parent_id TEXT,
                action_from_parent TEXT,
                depth INTEGER,
                is_goal BOOLEAN,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建关系表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS page_relationships (
                parent_id TEXT,
                child_id TEXT,
                action TEXT,
                PRIMARY KEY (parent_id, child_id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def _load_vectorizer(self):
        """加载向量化器和缓存"""
        if os.path.exists(self.vector_cache_path):
            try:
                with open(self.vector_cache_path, 'rb') as f:
                    cache_data = pickle.load(f)
                    # 只加载向量缓存，不加载向量化器（因为类型不同）
                    self.vectors_cache = cache_data.get('vectors', {})
                    # 如果有缓存的向量化器数据，重新训练
                    if 'texts' in cache_data and cache_data['texts']:
                        self.vectorizer.fit(cache_data['texts'])
            except Exception as e:
                print(f"Warning: Could not load vector cache: {e}")
    
    def _save_vectorizer(self):
        """保存向量化器和缓存"""
        try:
            cache_data = {
                'vectorizer': self.vectorizer,
                'vectors': self.vectors_cache
            }
            with open(self.vector_cache_path, 'wb') as f:
                pickle.dump(cache_data, f)
        except Exception as e:
            print(f"Warning: Could not save vector cache: {e}")
    
    def _get_text_embedding(self, text: str) -> np.ndarray:
        """获取文本的向量表示"""
        if not text.strip():
            return np.zeros(1000)  # 返回零向量

        # 检查缓存
        if text in self.vectors_cache:
            return self.vectors_cache[text]

        try:
            # 如果向量化器还没有训练，先用当前文本训练
            if not self.vectorizer.fitted:
                # 获取所有已存储的文本来训练向量化器
                all_texts = self._get_all_semantic_texts()
                all_texts.append(text)
                if all_texts:  # 确保有文本可以训练
                    self.vectorizer.fit(all_texts)

            # 生成向量
            vector = self.vectorizer._document_to_vector(text)
            self.vectors_cache[text] = vector
            return vector
        except Exception as e:
            print(f"Error generating embedding for text: {e}")
            return np.zeros(1000)
    
    def _get_all_semantic_texts(self) -> List[str]:
        """获取所有已存储的语义文本"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT semantic_text FROM page_nodes WHERE semantic_text IS NOT NULL")
        texts = [row[0] for row in cursor.fetchall() if row[0]]
        conn.close()
        return texts
    
    def add_page_node(self, node: PageTreeNode) -> bool:
        """添加页面节点"""
        try:
            # 生成语义文本和向量
            semantic_text = node.get_semantic_text()
            embedding = self._get_text_embedding(semantic_text)
            node.state_data["embedding"] = embedding.tolist()
            
            # 存储到数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            node_data = json.dumps(node.to_dict())
            cursor.execute('''
                INSERT OR REPLACE INTO page_nodes 
                (id, data, semantic_text, parent_id, action_from_parent, depth, is_goal)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                node.id,
                node_data,
                semantic_text,
                node.parent.id if node.parent else None,
                node.action_from_parent,
                node.state_data["depth"],
                node.state_data["is_goal"]
            ))
            
            # 如果有父节点，添加关系
            if node.parent:
                cursor.execute('''
                    INSERT OR REPLACE INTO page_relationships 
                    (parent_id, child_id, action) VALUES (?, ?, ?)
                ''', (node.parent.id, node.id, node.action_from_parent))
            
            conn.commit()
            conn.close()
            
            # 更新缓存
            self.nodes_cache[node.id] = node
            self._save_vectorizer()
            
            return True
        except Exception as e:
            print(f"Error adding page node: {e}")
            return False
    
    def find_similar_pages(self, query_text: str, top_k: int = 5) -> List[Tuple[PageTreeNode, float]]:
        """根据查询文本查找相似页面"""
        try:
            query_vector = self._get_text_embedding(query_text)
            
            # 获取所有节点的向量
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT id, data, semantic_text FROM page_nodes")
            
            similarities = []
            for row in cursor.fetchall():
                node_id, node_data, semantic_text = row
                if semantic_text:
                    node_vector = self._get_text_embedding(semantic_text)
                    similarity = cosine_similarity_simple(query_vector, node_vector)
                    
                    # 从缓存或数据库加载节点
                    if node_id in self.nodes_cache:
                        node = self.nodes_cache[node_id]
                    else:
                        node_dict = json.loads(node_data)
                        node = PageTreeNode.from_dict(node_dict)
                        self.nodes_cache[node_id] = node
                    
                    similarities.append((node, similarity))
            
            conn.close()
            
            # 按相似度排序并返回top_k
            similarities.sort(key=lambda x: x[1], reverse=True)
            return similarities[:top_k]
            
        except Exception as e:
            print(f"Error finding similar pages: {e}")
            return []
    
    def get_page_by_id(self, page_id: str) -> Optional[PageTreeNode]:
        """根据ID获取页面节点"""
        if page_id in self.nodes_cache:
            return self.nodes_cache[page_id]
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT data FROM page_nodes WHERE id = ?", (page_id,))
            row = cursor.fetchone()
            conn.close()
            
            if row:
                node_dict = json.loads(row[0])
                node = PageTreeNode.from_dict(node_dict)
                self.nodes_cache[page_id] = node
                return node
            return None
        except Exception as e:
            print(f"Error getting page by ID: {e}")
            return None
    
    def get_children(self, parent_id: str) -> List[PageTreeNode]:
        """获取指定节点的所有子节点"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("""
                SELECT child_id FROM page_relationships WHERE parent_id = ?
            """, (parent_id,))
            
            children = []
            for row in cursor.fetchall():
                child_node = self.get_page_by_id(row[0])
                if child_node:
                    children.append(child_node)
            
            conn.close()
            return children
        except Exception as e:
            print(f"Error getting children: {e}")
            return []
    
    def get_page_path(self, page_id: str) -> List[PageTreeNode]:
        """获取从根节点到指定页面的路径"""
        path = []
        current_node = self.get_page_by_id(page_id)
        
        while current_node:
            path.insert(0, current_node)
            if current_node.parent:
                current_node = self.get_page_by_id(current_node.parent.id)
            else:
                break
        
        return path
    
    def get_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM page_nodes")
            total_nodes = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM page_relationships")
            total_relationships = cursor.fetchone()[0]
            
            cursor.execute("SELECT MAX(depth) FROM page_nodes")
            max_depth = cursor.fetchone()[0] or 0
            
            conn.close()
            
            return {
                "total_nodes": total_nodes,
                "total_relationships": total_relationships,
                "max_depth": max_depth,
                "cached_nodes": len(self.nodes_cache),
                "cached_vectors": len(self.vectors_cache)
            }
        except Exception as e:
            print(f"Error getting stats: {e}")
            return {}
