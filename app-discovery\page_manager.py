"""
页面节点管理器 - 负责页面节点的存储、检索和管理
使用ChromaDB作为向量数据库
"""

import json
from typing import List, Dict, Any, Optional, Tuple
import chromadb

from models import PageTreeNode


class PageNodeManager:
    """页面节点管理器 - 使用ChromaDB"""

    def __init__(
        self,
        collection_name: str = "page_nodes",
        persist_directory: str = "chroma_db",
    ):
        self.collection_name = collection_name
        self.persist_directory = "./db/" + persist_directory
        self.nodes_cache = {}  # 内存缓存

        # 初始化ChromaDB客户端
        self.client = chromadb.PersistentClient(path=persist_directory)

        # 获取或创建集合
        try:
            self.collection = self.client.get_collection(name=collection_name)
        except Exception:
            # 集合不存在，创建新集合
            self.collection = self.client.create_collection(
                name=collection_name,
                metadata={"description": "App page navigation nodes"},
            )

    def _prepare_node_data(self, node: PageTreeNode) -> Dict[str, Any]:
        """准备节点数据用于存储"""
        return {
            "id": node.id,
            "screenshot": node.state_data.get("screenshot", ""),
            "elements": json.dumps(
                [elem.__dict__ for elem in node.state_data.get("elements", [])]
            ),
            "semantic_desc": node.state_data.get("semantic_desc", ""),
            "depth": node.state_data.get("depth", 0),
            "is_goal": node.state_data.get("is_goal", False),
            "parent_id": node.parent.id if node.parent else "",
            "action_from_parent": node.action_from_parent or "",
            "children_ids": json.dumps([child.id for child in node.children]),
        }

    def add_page_node(self, node: PageTreeNode) -> bool:
        """添加页面节点到ChromaDB"""
        try:
            # 获取语义文本
            semantic_text = node.get_semantic_text()

            # 准备元数据
            metadata = self._prepare_node_data(node)

            # 添加到ChromaDB集合
            self.collection.add(
                documents=[semantic_text], metadatas=[metadata], ids=[node.id]
            )

            # 更新缓存
            self.nodes_cache[node.id] = node

            return True
        except Exception as e:
            print(f"Error adding page node: {e}")
            return False

    def find_similar_pages(
        self, query_text: str, top_k: int = 5
    ) -> List[Tuple[PageTreeNode, float]]:
        """根据查询文本查找相似页面"""
        try:
            # 使用ChromaDB的查询功能
            results = self.collection.query(query_texts=[query_text], n_results=top_k)

            similarities = []
            if results["ids"] and results["ids"][0]:
                for i, node_id in enumerate(results["ids"][0]):
                    distance = results["distances"][0][i] if results["distances"] else 0
                    # 将距离转换为相似度 (距离越小，相似度越高)
                    similarity = 1.0 / (1.0 + distance)

                    # 从缓存或重建节点
                    node = self._get_node_from_metadata(
                        node_id, results["metadatas"][0][i]
                    )
                    if node:
                        similarities.append((node, similarity))

            return similarities

        except Exception as e:
            print(f"Error finding similar pages: {e}")
            return []

    def _get_node_from_metadata(
        self, node_id: str, metadata: Dict[str, Any]
    ) -> Optional[PageTreeNode]:
        """从元数据重建节点"""
        if node_id in self.nodes_cache:
            return self.nodes_cache[node_id]

        try:
            # 从元数据重建节点
            elements_data = json.loads(metadata.get("elements", "[]"))
            state_data = {
                "screenshot": metadata.get("screenshot", ""),
                "elements": elements_data,
                "semantic_desc": metadata.get("semantic_desc", ""),
                "depth": metadata.get("depth", 0),
                "is_goal": metadata.get("is_goal", False),
            }

            node = PageTreeNode(node_id, state_data)
            node.action_from_parent = metadata.get("action_from_parent", "")

            # 缓存节点
            self.nodes_cache[node_id] = node
            return node

        except Exception as e:
            print(f"Error rebuilding node from metadata: {e}")
            return None

    def get_page_by_id(self, page_id: str) -> Optional[PageTreeNode]:
        """根据ID获取页面节点"""
        if page_id in self.nodes_cache:
            return self.nodes_cache[page_id]

        try:
            # 从ChromaDB获取节点
            results = self.collection.get(ids=[page_id])

            if results["ids"] and results["metadatas"]:
                metadata = results["metadatas"][0]
                node = self._get_node_from_metadata(page_id, metadata)
                return node

            return None
        except Exception as e:
            print(f"Error getting page by ID: {e}")
            return None

    def get_children(self, parent_id: str) -> List[PageTreeNode]:
        """获取指定节点的所有子节点"""
        try:
            # 查询所有节点，筛选出父节点为指定ID的节点
            results = self.collection.get()

            children = []
            if results["ids"] and results["metadatas"]:
                for i, node_id in enumerate(results["ids"]):
                    metadata = results["metadatas"][i]
                    if metadata.get("parent_id") == parent_id:
                        child_node = self._get_node_from_metadata(node_id, metadata)
                        if child_node:
                            children.append(child_node)

            return children
        except Exception as e:
            print(f"Error getting children: {e}")
            return []

    def get_page_path(self, page_id: str) -> List[PageTreeNode]:
        """获取从根节点到指定页面的路径"""
        path = []
        current_node = self.get_page_by_id(page_id)

        while current_node:
            path.insert(0, current_node)
            # 查找父节点
            parent_id = None
            try:
                results = self.collection.get(ids=[current_node.id])
                if results["metadatas"]:
                    parent_id = results["metadatas"][0].get("parent_id")
            except:
                pass

            if parent_id:
                current_node = self.get_page_by_id(parent_id)
            else:
                break

        return path

    def get_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        try:
            # 获取所有节点
            results = self.collection.get()
            total_nodes = len(results["ids"]) if results["ids"] else 0

            # 计算关系数量和最大深度
            total_relationships = 0
            max_depth = 0

            if results["metadatas"]:
                for metadata in results["metadatas"]:
                    if metadata.get("parent_id"):
                        total_relationships += 1
                    depth = metadata.get("depth", 0)
                    max_depth = max(max_depth, depth)

            return {
                "total_nodes": total_nodes,
                "total_relationships": total_relationships,
                "max_depth": max_depth,
                "cached_nodes": len(self.nodes_cache),
                "collection_name": self.collection_name,
            }
        except Exception as e:
            print(f"Error getting stats: {e}")
            return {}
