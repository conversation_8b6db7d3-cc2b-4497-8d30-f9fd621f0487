@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\development\level-up\midscene\node_modules\.pnpm\vitest@3.0.5_@types+debug@4.1.12_@types+node@18.19.62_jsdom@26.1.0_less@4.3.0_sass-embedded@1.86.3_terser@5.39.0\node_modules\vitest\node_modules;E:\development\level-up\midscene\node_modules\.pnpm\vitest@3.0.5_@types+debug@4.1.12_@types+node@18.19.62_jsdom@26.1.0_less@4.3.0_sass-embedded@1.86.3_terser@5.39.0\node_modules;E:\development\level-up\midscene\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\development\level-up\midscene\node_modules\.pnpm\vitest@3.0.5_@types+debug@4.1.12_@types+node@18.19.62_jsdom@26.1.0_less@4.3.0_sass-embedded@1.86.3_terser@5.39.0\node_modules\vitest\node_modules;E:\development\level-up\midscene\node_modules\.pnpm\vitest@3.0.5_@types+debug@4.1.12_@types+node@18.19.62_jsdom@26.1.0_less@4.3.0_sass-embedded@1.86.3_terser@5.39.0\node_modules;E:\development\level-up\midscene\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\vitest\vitest.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\vitest\vitest.mjs" %*
)
