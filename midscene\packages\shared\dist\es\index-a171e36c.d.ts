import { NodeType } from './constants.js';

declare function truncateText(text: string | number | object | undefined, maxLength?: number): string;
declare function trimAttributes(attributes: Record<string, any>, truncateTextLength?: number): {
    [key: string]: string;
    nodeType: NodeType;
};
declare function descriptionOfTree<ElementType extends BaseElement = BaseElement>(tree: ElementTreeNode<ElementType>, truncateTextLength?: number, filterNonTextContent?: boolean, visibleOnly?: boolean): string;
declare function treeToList<T extends BaseElement>(tree: ElementTreeNode<T>): T[];
declare function traverseTree<T extends BaseElement, ReturnNodeType extends BaseElement>(tree: ElementTreeNode<T>, onNode: (node: T) => ReturnNodeType): ElementTreeNode<ReturnNodeType>;

interface WebElementNode {
    node: WebElementInfo | null;
    children: WebElementNode[];
}
declare function extractTextWithPosition(initNode: globalThis.Node, debugMode?: boolean): WebElementInfo[];
declare function extractTreeNodeAsString(initNode: globalThis.Node, visibleOnly?: boolean, debugMode?: boolean): string;
declare function extractTreeNode(initNode: globalThis.Node, debugMode?: boolean): WebElementNode;

declare function setNodeHashCacheListOnWindow(): void;
declare function getNodeFromCacheList(id: string): any;

declare function getXpathsById(id: string): string[] | null;
declare function getNodeInfoByXpath(xpath: string): Node | null;
declare function getElementInfoByXpath(xpath: string): ElementInfo | null;

declare function generateElementByPosition(position: {
    x: number;
    y: number;
}): {
    id: string;
    attributes: {
        nodeType: NodeType;
    };
    rect: {
        left: number;
        top: number;
        width: number;
        height: number;
    };
    content: string;
    center: number[];
};

interface ElementInfo {
    id: string;
    indexId: number;
    nodeHashId: string;
    locator: string;
    xpaths?: string[];
    attributes: {
        nodeType: NodeType;
        [key: string]: string;
    };
    nodeType: NodeType;
    content: string;
    rect: {
        left: number;
        top: number;
        width: number;
        height: number;
    };
    center: [number, number];
    isVisible: boolean;
}
interface ElementNode {
    node: ElementInfo | null;
    children: ElementNode[];
}

interface Point {
    left: number;
    top: number;
}
interface Size {
    width: number;
    height: number;
    dpr?: number;
}
type Rect = Point & Size & {
    zoom?: number;
};
declare abstract class BaseElement {
    abstract id: string;
    abstract indexId?: number;
    abstract attributes: {
        nodeType: NodeType;
        [key: string]: string;
    };
    abstract content: string;
    abstract rect: Rect;
    abstract center: [number, number];
    abstract locator?: string;
    abstract xpaths?: string[];
    abstract isVisible: boolean;
}
interface ElementTreeNode<ElementType extends BaseElement = BaseElement> {
    node: ElementType | null;
    children: ElementTreeNode<ElementType>[];
}
interface WebElementInfo extends ElementInfo {
    zoom: number;
}

export { BaseElement as B, type ElementInfo as E, type Point as P, type Rect as R, type Size as S, type WebElementInfo as W, type ElementNode as a, treeToList as b, truncateText as c, descriptionOfTree as d, trimAttributes as e, extractTextWithPosition as f, extractTreeNode as g, extractTreeNodeAsString as h, getNodeFromCacheList as i, getXpathsById as j, getNodeInfoByXpath as k, getElementInfoByXpath as l, generateElementByPosition as m, type ElementTreeNode as n, setNodeHashCacheListOnWindow as s, traverseTree as t };
