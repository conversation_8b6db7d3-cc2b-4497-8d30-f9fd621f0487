"""
高级ChromaDB页面管理演示
展示更复杂的页面关系和搜索功能
"""
from models import PageTreeNode
from page_manager import PageNodeManager


def create_complex_app_structure():
    """创建一个复杂的应用页面结构"""
    manager = PageNodeManager(collection_name="complex_app", persist_directory="chroma_complex")
    
    # 创建更多样化的页面
    pages = []
    
    # 1. 启动页面
    splash_page = PageTreeNode("splash_001", {
        "screenshot": "screenshots/splash.png",
        "elements": [
            {"type": "image", "text": "应用Logo", "bounds": [100, 100, 200, 200]},
            {"type": "text", "text": "欢迎使用我们的应用", "bounds": [50, 250, 350, 280]}
        ],
        "semantic_desc": "应用启动页面，显示Logo和欢迎信息",
        "depth": 0,
        "is_goal": False
    })
    pages.append(splash_page)
    
    # 2. 登录页面
    login_page = PageTreeNode("login_001", {
        "screenshot": "screenshots/login.png",
        "elements": [
            {"type": "input", "hint": "请输入手机号", "bounds": [50, 150, 350, 180]},
            {"type": "input", "hint": "请输入密码", "bounds": [50, 200, 350, 230]},
            {"type": "button", "text": "登录", "bounds": [50, 260, 350, 300]},
            {"type": "button", "text": "忘记密码", "bounds": [50, 320, 150, 350]},
            {"type": "button", "text": "注册账号", "bounds": [250, 320, 350, 350]}
        ],
        "semantic_desc": "用户登录界面，包含手机号密码输入框和登录注册按钮",
        "depth": 1,
        "is_goal": False
    })
    pages.append(login_page)
    
    # 3. 注册页面
    register_page = PageTreeNode("register_001", {
        "screenshot": "screenshots/register.png",
        "elements": [
            {"type": "input", "hint": "手机号", "bounds": [50, 100, 350, 130]},
            {"type": "input", "hint": "验证码", "bounds": [50, 150, 250, 180]},
            {"type": "button", "text": "获取验证码", "bounds": [260, 150, 350, 180]},
            {"type": "input", "hint": "设置密码", "bounds": [50, 200, 350, 230]},
            {"type": "button", "text": "注册", "bounds": [50, 260, 350, 300]}
        ],
        "semantic_desc": "用户注册页面，需要填写手机号验证码和密码",
        "depth": 2,
        "is_goal": False
    })
    pages.append(register_page)
    
    # 4. 主页
    home_page = PageTreeNode("home_001", {
        "screenshot": "screenshots/home.png",
        "elements": [
            {"type": "text", "text": "首页", "bounds": [50, 50, 100, 80]},
            {"type": "button", "text": "商品分类", "bounds": [50, 100, 150, 140]},
            {"type": "button", "text": "搜索商品", "bounds": [200, 100, 300, 140]},
            {"type": "button", "text": "购物车", "bounds": [50, 160, 150, 200]},
            {"type": "button", "text": "个人中心", "bounds": [200, 160, 300, 200]}
        ],
        "semantic_desc": "应用主页，提供商品浏览、搜索、购物车和个人中心入口",
        "depth": 2,
        "is_goal": False
    })
    pages.append(home_page)
    
    # 5. 商品搜索页面
    search_page = PageTreeNode("search_001", {
        "screenshot": "screenshots/search.png",
        "elements": [
            {"type": "input", "hint": "搜索商品", "bounds": [50, 50, 300, 80]},
            {"type": "button", "text": "搜索", "bounds": [310, 50, 350, 80]},
            {"type": "text", "text": "热门搜索", "bounds": [50, 100, 150, 130]},
            {"type": "button", "text": "手机", "bounds": [50, 140, 100, 170]},
            {"type": "button", "text": "电脑", "bounds": [110, 140, 160, 170]}
        ],
        "semantic_desc": "商品搜索页面，可以输入关键词搜索或选择热门商品",
        "depth": 3,
        "is_goal": False
    })
    pages.append(search_page)
    
    # 6. 购物车页面
    cart_page = PageTreeNode("cart_001", {
        "screenshot": "screenshots/cart.png",
        "elements": [
            {"type": "text", "text": "购物车", "bounds": [50, 50, 150, 80]},
            {"type": "text", "text": "商品列表", "bounds": [50, 100, 150, 130]},
            {"type": "button", "text": "全选", "bounds": [50, 140, 100, 170]},
            {"type": "button", "text": "删除", "bounds": [110, 140, 160, 170]},
            {"type": "button", "text": "结算", "bounds": [250, 300, 350, 340]}
        ],
        "semantic_desc": "购物车页面，显示已选商品列表，可以管理商品和结算",
        "depth": 3,
        "is_goal": True  # 这是一个目标页面
    })
    pages.append(cart_page)
    
    # 7. 个人中心页面
    profile_page = PageTreeNode("profile_001", {
        "screenshot": "screenshots/profile.png",
        "elements": [
            {"type": "text", "text": "个人中心", "bounds": [50, 50, 150, 80]},
            {"type": "button", "text": "我的订单", "bounds": [50, 100, 150, 140]},
            {"type": "button", "text": "收货地址", "bounds": [200, 100, 300, 140]},
            {"type": "button", "text": "账户设置", "bounds": [50, 160, 150, 200]},
            {"type": "button", "text": "退出登录", "bounds": [200, 160, 300, 200]}
        ],
        "semantic_desc": "个人中心页面，包含订单管理、地址管理和账户设置",
        "depth": 3,
        "is_goal": False
    })
    pages.append(profile_page)
    
    # 建立页面关系
    splash_page.add_child(login_page, "点击进入登录")
    login_page.add_child(register_page, "点击注册账号")
    login_page.add_child(home_page, "登录成功")
    home_page.add_child(search_page, "点击搜索商品")
    home_page.add_child(cart_page, "点击购物车")
    home_page.add_child(profile_page, "点击个人中心")
    
    # 添加所有页面到管理器
    print("正在创建复杂应用结构...")
    for page in pages:
        success = manager.add_page_node(page)
        if success:
            print(f"  ✓ 添加页面: {page.id}")
        else:
            print(f"  ✗ 添加页面失败: {page.id}")
    
    return manager


def test_advanced_search(manager):
    """测试高级搜索功能"""
    print("\n=== 高级搜索测试 ===")
    
    search_queries = [
        "用户登录注册",
        "商品购买购物",
        "个人信息管理",
        "搜索查找商品",
        "订单管理",
        "密码验证码"
    ]
    
    for query in search_queries:
        print(f"\n搜索: '{query}'")
        results = manager.find_similar_pages(query, top_k=3)
        for i, (page, similarity) in enumerate(results, 1):
            goal_mark = " [目标页面]" if page.state_data.get('is_goal') else ""
            print(f"  {i}. {page.id} (相似度: {similarity:.3f}){goal_mark}")
            print(f"     {page.state_data['semantic_desc']}")


def test_navigation_paths(manager):
    """测试导航路径功能"""
    print("\n=== 导航路径测试 ===")
    
    target_pages = ["cart_001", "profile_001", "search_001"]
    
    for target in target_pages:
        print(f"\n到达 {target} 的路径:")
        path = manager.get_page_path(target)
        for i, node in enumerate(path):
            indent = "  " * i
            action = f" (通过: {node.action_from_parent})" if node.action_from_parent else ""
            goal_mark = " [目标]" if node.state_data.get('is_goal') else ""
            print(f"  {indent}{node.id}: {node.state_data['semantic_desc']}{action}{goal_mark}")


def main():
    """主函数"""
    try:
        # 创建复杂应用结构
        manager = create_complex_app_structure()
        
        # 显示统计信息
        stats = manager.get_stats()
        print(f"\n应用结构统计:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # 测试高级搜索
        test_advanced_search(manager)
        
        # 测试导航路径
        test_navigation_paths(manager)
        
        print("\n=== 演示完成 ===")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
