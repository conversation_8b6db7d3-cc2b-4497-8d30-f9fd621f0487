"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/img/index.ts
var img_exports = {};
__export(img_exports, {
  base64Encoded: () => base64Encoded,
  bufferFromBase64: () => bufferFromBase64,
  compositeElementInfoImg: () => compositeElementInfoImg,
  cropByRect: () => cropByRect,
  drawBoxOnImage: () => drawBoxOnImage,
  imageInfo: () => imageInfo,
  imageInfoOfBase64: () => imageInfoOfBase64,
  isValidPNGImageBuffer: () => isValidPNGImageBuffer,
  jimpFromBase64: () => jimpFromBase64,
  jimpToBase64: () => jimpToBase64,
  paddingToMatchBlock: () => paddingToMatchBlock,
  paddingToMatchBlockByBase64: () => paddingToMatchBlockByBase64,
  processImageElementInfo: () => processImageElementInfo,
  resizeImg: () => resizeImg,
  resizeImgBase64: () => resizeImgBase64,
  saveBase64Image: () => saveBase64Image,
  savePositionImg: () => savePositionImg,
  transformImgPathToBase64: () => transformImgPathToBase64,
  trimImage: () => trimImage,
  zoomForGPT4o: () => zoomForGPT4o
});
module.exports = __toCommonJS(img_exports);

// src/img/info.ts
var import_node_assert = __toESM(require("assert"));
var import_node_buffer = require("buffer");
var import_node_fs = require("fs");

// src/img/get-jimp.ts
var ifInBrowser = typeof window !== "undefined";
async function getJimp() {
  if (ifInBrowser) {
    await import("jimp/browser/lib/jimp.js");
    return window.Jimp;
  }
  return (await import("jimp")).default;
}

// src/img/info.ts
async function imageInfo(image) {
  const Jimp = await getJimp();
  let jimpImage;
  if (typeof image === "string") {
    jimpImage = await Jimp.read(image);
  } else if (import_node_buffer.Buffer.isBuffer(image)) {
    jimpImage = await Jimp.read(image);
  } else if (image instanceof Jimp) {
    jimpImage = image;
  } else {
    throw new Error("Invalid image input: must be a string path or a Buffer");
  }
  const { width, height } = jimpImage.bitmap;
  (0, import_node_assert.default)(
    width && height,
    `Invalid image: ${typeof image === "string" ? image : "Buffer"}`
  );
  return { width, height, jimpImage };
}
async function imageInfoOfBase64(imageBase64) {
  const buffer = await bufferFromBase64(imageBase64);
  return imageInfo(buffer);
}
async function bufferFromBase64(imageBase64) {
  const base64Data = imageBase64.replace(/^data:image\/\w+;base64,/, "");
  return import_node_buffer.Buffer.from(base64Data, "base64");
}
function base64Encoded(image, withHeader = true) {
  const imageBuffer = (0, import_node_fs.readFileSync)(image);
  if (!withHeader) {
    return imageBuffer.toString("base64");
  }
  if (image.endsWith("png")) {
    return `data:image/png;base64,${imageBuffer.toString("base64")}`;
  }
  if (image.endsWith("jpg") || image.endsWith("jpeg")) {
    return `data:image/jpeg;base64,${imageBuffer.toString("base64")}`;
  }
  throw new Error("unsupported image type");
}
function isValidPNGImageBuffer(buffer) {
  if (!buffer || buffer.length < 8) {
    return false;
  }
  const isPNG = buffer[0] === 137 && buffer[1] === 80 && buffer[2] === 78 && buffer[3] === 71;
  return isPNG;
}

// src/img/transform.ts
var import_node_assert2 = __toESM(require("assert"));
var import_node_buffer2 = require("buffer");
var import_debug = __toESM(require("debug"));
var debugImg = (0, import_debug.default)("img");
async function saveBase64Image(options) {
  debugImg(`saveBase64Image start: ${options.outputPath}`);
  const { base64Data, outputPath } = options;
  const base64Image = base64Data.split(";base64,").pop() || base64Data;
  const imageBuffer = import_node_buffer2.Buffer.from(base64Image, "base64");
  const Jimp = await getJimp();
  const image = await Jimp.read(imageBuffer);
  await image.writeAsync(outputPath);
  debugImg(`saveBase64Image done: ${options.outputPath}`);
}
async function transformImgPathToBase64(inputPath) {
  debugImg(`transformImgPathToBase64 start: ${inputPath}`);
  const Jimp = await getJimp();
  const image = await Jimp.read(inputPath);
  const buffer = await image.getBufferAsync(Jimp.MIME_JPEG);
  const res = buffer.toString("base64");
  debugImg(`transformImgPathToBase64 done: ${inputPath}`);
  return res;
}
async function resizeImg(inputData, newSize) {
  if (typeof inputData === "string")
    throw Error("inputData is base64, use resizeImgBase64 instead");
  (0, import_node_assert2.default)(
    newSize && newSize.width > 0 && newSize.height > 0,
    "newSize must be positive"
  );
  debugImg(`resizeImg start, target size: ${newSize.width}x${newSize.height}`);
  const Jimp = await getJimp();
  const image = await Jimp.read(inputData);
  const { width, height } = image.bitmap;
  if (!width || !height) {
    throw Error("Undefined width or height from the input image.");
  }
  if (newSize.width === width && newSize.height === height) {
    return inputData;
  }
  image.resize(newSize.width, newSize.height, Jimp.RESIZE_BICUBIC);
  image.quality(90);
  const resizedBuffer = await image.getBufferAsync(Jimp.MIME_JPEG);
  debugImg(`resizeImg done, target size: ${newSize.width}x${newSize.height}`);
  return resizedBuffer;
}
async function bufferFromBase642(base64) {
  const splitFlag = ";base64,";
  const dataSplitted = base64.split(splitFlag);
  if (dataSplitted.length !== 2) {
    throw Error("Invalid base64 data");
  }
  debugImg(`bufferFromBase64 start: ${base64}`);
  const res = import_node_buffer2.Buffer.from(dataSplitted[1], "base64");
  debugImg(`bufferFromBase64 done: ${base64}`);
  return res;
}
async function resizeImgBase64(inputBase64, newSize) {
  debugImg(`resizeImgBase64 start: ${inputBase64}`);
  const splitFlag = ";base64,";
  const dataSplitted = inputBase64.split(splitFlag);
  if (dataSplitted.length !== 2) {
    throw Error("Invalid base64 data");
  }
  const imageBuffer = import_node_buffer2.Buffer.from(dataSplitted[1], "base64");
  const buffer = await resizeImg(imageBuffer, newSize);
  const content = buffer.toString("base64");
  const res = `${dataSplitted[0]}${splitFlag}${content}`;
  debugImg(`resizeImgBase64 done: ${inputBase64}`);
  return res;
}
function zoomForGPT4o(originalWidth, originalHeight) {
  const maxWidth = 2048;
  const maxHeight = 768;
  let newWidth = originalWidth;
  let newHeight = originalHeight;
  const aspectRatio = originalWidth / originalHeight;
  if (originalWidth > maxWidth) {
    newWidth = maxWidth;
    newHeight = newWidth / aspectRatio;
  }
  if (newHeight > maxHeight) {
    newHeight = maxHeight;
    newWidth = newHeight * aspectRatio;
  }
  return {
    width: Math.round(newWidth),
    height: Math.round(newHeight)
  };
}
async function trimImage(image) {
  const Jimp = await getJimp();
  const jimpImage = await Jimp.read(
    import_node_buffer2.Buffer.isBuffer(image) ? image : import_node_buffer2.Buffer.from(image)
  );
  const { width, height } = jimpImage.bitmap;
  if (width <= 3 || height <= 3) {
    return null;
  }
  const trimmedImage = jimpImage.autocrop();
  const { width: trimmedWidth, height: trimmedHeight } = trimmedImage.bitmap;
  const trimOffsetLeft = (width - trimmedWidth) / 2;
  const trimOffsetTop = (height - trimmedHeight) / 2;
  if (trimOffsetLeft === 0 && trimOffsetTop === 0) {
    return null;
  }
  return {
    trimOffsetLeft: -trimOffsetLeft,
    trimOffsetTop: -trimOffsetTop,
    width: trimmedWidth,
    height: trimmedHeight
  };
}
async function jimpFromBase64(base64) {
  const Jimp = await getJimp();
  const imageBuffer = await bufferFromBase642(base64);
  return Jimp.read(imageBuffer);
}
async function paddingToMatchBlock(image, blockSize = 28) {
  debugImg("paddingToMatchBlock start");
  const { width, height } = image.bitmap;
  const targetWidth = Math.ceil(width / blockSize) * blockSize;
  const targetHeight = Math.ceil(height / blockSize) * blockSize;
  if (targetWidth === width && targetHeight === height) {
    return image;
  }
  const Jimp = await getJimp();
  const paddedImage = new Jimp(targetWidth, targetHeight, 4294967295);
  paddedImage.composite(image, 0, 0);
  return paddedImage;
}
async function paddingToMatchBlockByBase64(imageBase64, blockSize = 28) {
  const jimpImage = await jimpFromBase64(imageBase64);
  const paddedImage = await paddingToMatchBlock(jimpImage, blockSize);
  return jimpToBase64(paddedImage);
}
async function cropByRect(imageBase64, rect, paddingImage) {
  const jimpImage = await jimpFromBase64(imageBase64);
  const { left, top, width, height } = rect;
  jimpImage.crop(left, top, width, height);
  if (paddingImage) {
    const paddedImage = await paddingToMatchBlock(jimpImage);
    return jimpToBase64(paddedImage);
  }
  return jimpToBase64(jimpImage);
}
async function jimpToBase64(image) {
  const Jimp = await getJimp();
  return image.getBase64Async(Jimp.MIME_JPEG);
}

// src/img/box-select.ts
var import_node_assert3 = __toESM(require("assert"));
var cachedFont = null;
var loadFonts = async () => {
  const Jimp = await getJimp();
  try {
    const fonts = await Jimp.loadFont(Jimp.FONT_SANS_16_WHITE);
    return fonts;
  } catch (error) {
    console.warn("Error loading font, will try to load online fonts", error);
    const onlineFonts = "https://cdn.jsdelivr.net/npm/jimp-compact@0.16.1-2/fonts/open-sans/open-sans-16-white/open-sans-16-white.fnt";
    const fonts = await Jimp.loadFont(onlineFonts);
    return fonts;
  }
};
var createSvgOverlay = async (elements, imageWidth, imageHeight, boxPadding = 5, borderThickness = 2, prompt) => {
  const Jimp = await getJimp();
  const image = new Jimp(imageWidth, imageHeight, 0);
  const colors = [
    { rect: 3324182783, text: 4294967295 },
    // red, white
    { rect: 65535, text: 4294967295 },
    // blue, white
    { rect: 2336560127, text: 4294967295 },
    // brown, white
    { rect: 1048258559, text: 4294967295 },
    // green, white
    { rect: 1342206975, text: 4294967295 }
    // purple, white
  ];
  if (prompt) {
    try {
      cachedFont = cachedFont || await loadFonts();
      const promptPadding = 10;
      const promptMargin = 20;
      const promptHeight = 30;
      const promptY = imageHeight - promptHeight - promptMargin;
      image.scan(
        0,
        promptY,
        imageWidth,
        promptHeight,
        (x, y, idx) => {
          image.bitmap.data[idx + 0] = 0;
          image.bitmap.data[idx + 1] = 0;
          image.bitmap.data[idx + 2] = 0;
          image.bitmap.data[idx + 3] = 204;
        }
      );
      image.print(
        cachedFont,
        promptPadding,
        promptY,
        {
          text: prompt,
          alignmentX: Jimp.HORIZONTAL_ALIGN_LEFT,
          alignmentY: Jimp.VERTICAL_ALIGN_MIDDLE
        },
        imageWidth - promptPadding * 2,
        promptHeight
      );
    } catch (error) {
      console.error("Error drawing prompt text", error);
    }
  }
  for (let index = 0; index < elements.length; index++) {
    const element = elements[index];
    const color = colors[index % colors.length];
    const paddedLeft = Math.max(0, element.rect.left - boxPadding);
    const paddedTop = Math.max(0, element.rect.top - boxPadding);
    const paddedWidth = Math.min(
      imageWidth - paddedLeft,
      element.rect.width + boxPadding * 2
    );
    const paddedHeight = Math.min(
      imageHeight - paddedTop,
      element.rect.height + boxPadding * 2
    );
    const paddedRect = {
      left: paddedLeft,
      top: paddedTop,
      width: paddedWidth,
      height: paddedHeight
    };
    image.scan(
      paddedRect.left,
      paddedRect.top,
      paddedRect.width,
      paddedRect.height,
      (x, y, idx) => {
        if (x >= paddedRect.left && x < paddedRect.left + borderThickness || // Left border
        x <= paddedRect.left + paddedRect.width - 1 && x > paddedRect.left + paddedRect.width - borderThickness || // Right border
        y >= paddedRect.top && y < paddedRect.top + borderThickness || // Top border
        y <= paddedRect.top + paddedRect.height - 1 && y > paddedRect.top + paddedRect.height - borderThickness) {
          image.bitmap.data[idx + 0] = color.rect >> 24 & 255;
          image.bitmap.data[idx + 1] = color.rect >> 16 & 255;
          image.bitmap.data[idx + 2] = color.rect >> 8 & 255;
          image.bitmap.data[idx + 3] = color.rect & 255;
        }
      }
    );
    const indexId = element.indexId;
    if (typeof indexId !== "number") {
      continue;
    }
    const textWidth = indexId.toString().length * 8;
    const textHeight = 12;
    const rectWidth = textWidth + 5;
    const rectHeight = textHeight + 4;
    let rectX = paddedRect.left - rectWidth;
    let rectY = paddedRect.top + paddedRect.height / 2 - textHeight / 2 - 2;
    const checkOverlap = (x, y) => {
      return elements.slice(0, index).some((otherElement) => {
        return x < otherElement.rect.left + otherElement.rect.width && x + rectWidth > otherElement.rect.left && y < otherElement.rect.top + otherElement.rect.height && y + rectHeight > otherElement.rect.top;
      });
    };
    const isWithinBounds = (x, y) => {
      return x >= 0 && x + rectWidth <= imageWidth && y >= 0 && y + rectHeight <= imageHeight;
    };
    if (checkOverlap(rectX, rectY) || !isWithinBounds(rectX, rectY)) {
      if (!checkOverlap(paddedRect.left, paddedRect.top - rectHeight - 2) && isWithinBounds(paddedRect.left, paddedRect.top - rectHeight - 2)) {
        rectX = paddedRect.left;
        rectY = paddedRect.top - rectHeight - 2;
      } else if (!checkOverlap(
        paddedRect.left,
        paddedRect.top + paddedRect.height + 2
      ) && isWithinBounds(paddedRect.left, paddedRect.top + paddedRect.height + 2)) {
        rectX = paddedRect.left;
        rectY = paddedRect.top + paddedRect.height + 2;
      } else if (!checkOverlap(paddedRect.left + paddedRect.width + 2, paddedRect.top) && isWithinBounds(paddedRect.left + paddedRect.width + 2, paddedRect.top)) {
        rectX = paddedRect.left + paddedRect.width + 2;
        rectY = paddedRect.top;
      } else {
        rectX = paddedRect.left;
        rectY = paddedRect.top + 2;
      }
    }
    image.scan(
      rectX,
      rectY,
      rectWidth,
      rectHeight,
      (x, y, idx) => {
        image.bitmap.data[idx + 0] = color.rect >> 24 & 255;
        image.bitmap.data[idx + 1] = color.rect >> 16 & 255;
        image.bitmap.data[idx + 2] = color.rect >> 8 & 255;
        image.bitmap.data[idx + 3] = color.rect & 255;
      }
    );
    try {
      cachedFont = cachedFont || await loadFonts();
    } catch (error) {
      console.error("Error loading font", error);
    }
    image.print(
      cachedFont,
      rectX,
      rectY,
      {
        text: indexId.toString(),
        alignmentX: Jimp.HORIZONTAL_ALIGN_CENTER,
        alignmentY: Jimp.VERTICAL_ALIGN_MIDDLE
      },
      rectWidth,
      rectHeight
    );
  }
  return image;
};
var compositeElementInfoImg = async (options) => {
  (0, import_node_assert3.default)(options.inputImgBase64, "inputImgBase64 is required");
  let width = 0;
  let height = 0;
  let jimpImage;
  const Jimp = await getJimp();
  if (options.size) {
    width = options.size.width;
    height = options.size.height;
  }
  if (!width || !height) {
    const info = await imageInfoOfBase64(options.inputImgBase64);
    width = info.width;
    height = info.height;
    jimpImage = info.jimpImage;
  } else {
    const imageBuffer = await bufferFromBase64(options.inputImgBase64);
    jimpImage = await Jimp.read(imageBuffer);
    const imageBitmap = jimpImage.bitmap;
    if (imageBitmap.width !== width || imageBitmap.height !== height) {
      jimpImage.resize(width, height, Jimp.RESIZE_NEAREST_NEIGHBOR);
    }
  }
  if (!width || !height) {
    throw Error("Image processing failed because width or height is undefined");
  }
  const { elementsPositionInfo, prompt } = options;
  const result = await Promise.resolve(jimpImage).then(async (image) => {
    const svgOverlay = await createSvgOverlay(
      elementsPositionInfo,
      width,
      height,
      options.annotationPadding,
      options.borderThickness,
      prompt
    );
    const svgImage = await Jimp.read(svgOverlay);
    const compositeImage = await image.composite(svgImage, 0, 0, {
      mode: Jimp.BLEND_SOURCE_OVER,
      opacitySource: 1,
      opacityDest: 1
    });
    return compositeImage;
  }).then(async (compositeImage) => {
    compositeImage.quality(90);
    const base64 = await compositeImage.getBase64Async(Jimp.MIME_JPEG);
    return base64;
  }).catch((error) => {
    throw error;
  });
  return result;
};
var processImageElementInfo = async (options) => {
  const base64Image = options.inputImgBase64.split(";base64,").pop();
  (0, import_node_assert3.default)(base64Image, "base64Image is undefined");
  const [
    compositeElementInfoImgBase64,
    compositeElementInfoImgWithoutTextBase64
  ] = await Promise.all([
    compositeElementInfoImg({
      inputImgBase64: options.inputImgBase64,
      elementsPositionInfo: options.elementsPositionInfo
    }),
    compositeElementInfoImg({
      inputImgBase64: options.inputImgBase64,
      elementsPositionInfo: options.elementsPositionInfoWithoutText
    })
  ]);
  return {
    compositeElementInfoImgBase64,
    compositeElementInfoImgWithoutTextBase64
  };
};

// src/img/draw-box.ts
async function drawBoxOnImage(options) {
  const { inputImgBase64, rect } = options;
  const color = { r: 255, g: 0, b: 0, a: 255 };
  const Jimp = await getJimp();
  const imageBuffer = await bufferFromBase64(inputImgBase64);
  const image = await Jimp.read(imageBuffer);
  const centerX = rect.x;
  const centerY = rect.y;
  const radius = 5;
  image.scan(
    Math.floor(centerX - radius),
    Math.floor(centerY - radius),
    radius * 2,
    radius * 2,
    (x, y, idx) => {
      const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
      if (distance <= radius) {
        image.bitmap.data[idx + 0] = color.r;
        image.bitmap.data[idx + 1] = color.g;
        image.bitmap.data[idx + 2] = color.b;
        image.bitmap.data[idx + 3] = color.a;
      }
    }
  );
  image.quality(90);
  const resultBase64 = await image.getBase64Async(Jimp.MIME_JPEG);
  return resultBase64;
}
async function savePositionImg(options) {
  const { inputImgBase64, rect, outputPath } = options;
  const imgBase64 = await drawBoxOnImage({ inputImgBase64, rect });
  await saveBase64Image({
    base64Data: imgBase64,
    outputPath
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  base64Encoded,
  bufferFromBase64,
  compositeElementInfoImg,
  cropByRect,
  drawBoxOnImage,
  imageInfo,
  imageInfoOfBase64,
  isValidPNGImageBuffer,
  jimpFromBase64,
  jimpToBase64,
  paddingToMatchBlock,
  paddingToMatchBlockByBase64,
  processImageElementInfo,
  resizeImg,
  resizeImgBase64,
  saveBase64Image,
  savePositionImg,
  transformImgPathToBase64,
  trimImage,
  zoomForGPT4o
});
