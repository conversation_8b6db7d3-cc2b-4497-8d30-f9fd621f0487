Metadata-Version: 2.4
Name: joblib
Version: 1.5.1
Summary: Lightweight pipelining with Python functions
Author-email: <PERSON><PERSON> <<EMAIL>>
License: BSD 3-Clause
Project-URL: Homepage, https://joblib.readthedocs.io
Project-URL: Source, https://github.com/joblib/joblib
Platform: any
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: Intended Audience :: Education
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: Utilities
Classifier: Topic :: Software Development :: Libraries
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE.txt
Dynamic: license-file

|PyPi| |CIStatus| |ReadTheDocs| |Codecov|

.. |PyPi| image:: https://badge.fury.io/py/joblib.svg
   :target: https://badge.fury.io/py/joblib
   :alt: Joblib version

.. |CIStatus| image:: https://github.com/joblib/joblib/actions/workflows/test.yml/badge.svg
   :target: https://github.com/joblib/joblib/actions/workflows/test.yml?query=branch%3Amain
   :alt: CI status

.. |ReadTheDocs| image:: https://readthedocs.org/projects/joblib/badge/?version=latest
    :target: https://joblib.readthedocs.io/en/latest/?badge=latest
    :alt: Documentation Status

.. |Codecov| image:: https://codecov.io/gh/joblib/joblib/branch/main/graph/badge.svg
   :target: https://codecov.io/gh/joblib/joblib
   :alt: Codecov coverage


The homepage of joblib with user documentation is located on:

https://joblib.readthedocs.io

Getting the latest code
=======================

To get the latest code using git, simply type::

    git clone https://github.com/joblib/joblib.git

If you don't have git installed, you can download a zip
of the latest code: https://github.com/joblib/joblib/archive/refs/heads/main.zip

Installing
==========

You can use `pip` to install joblib from any directory::

    pip install joblib

or install it in editable mode from the source directory::

    pip install -e .

Dependencies
============

- Joblib has no mandatory dependencies besides Python (supported versions are
  3.9+).
- Joblib has an optional dependency on Numpy (at least version 1.6.1) for array
  manipulation.
- Joblib includes its own vendored copy of
  `loky <https://github.com/tomMoral/loky>`_ for process management.
- Joblib can efficiently dump and load numpy arrays but does not require numpy
  to be installed.
- Joblib has an optional dependency on
  `python-lz4 <https://pypi.python.org/pypi/lz4>`_ as a faster alternative to
  zlib and gzip for compressed serialization.
- Joblib has an optional dependency on psutil to mitigate memory leaks in
  parallel worker processes.
- Some examples require external dependencies such as pandas. See the
  instructions in the `Building the docs`_ section for details.

Workflow to contribute
======================

To contribute to joblib, first create an account on `github
<https://github.com/>`_. Once this is done, fork the `joblib repository
<https://github.com/joblib/joblib>`_ to have your own repository,
clone it using ``git clone``. Make your changes in a branch of your clone, push
them to your github account, test them locally, and when you are happy with
them, send a pull request to the main repository.

You can use `pre-commit <https://pre-commit.com/#install>`_ to run code style checks
before each commit::

    pip install pre-commit
    pre-commit install

pre-commit checks can be disabled for a single commit with::

    git commit -n

Running the test suite
======================

To run the test suite, you need the pytest (version >= 3) and coverage modules.
Run the test suite using::

    pytest joblib

from the root of the project.

Building the docs
=================

To build the docs you need to have sphinx (>=1.4) and some dependencies
installed::

    pip install -U -r .readthedocs-requirements.txt

The docs can then be built with the following command::

    make doc

The html docs are located in the ``doc/_build/html`` directory.


Making a source tarball
=======================

To create a source tarball, eg for packaging or distributing, run the
following command::

    pip install build
    python -m build --sdist

The tarball will be created in the `dist` directory. This command will create
the resulting tarball that can be installed with no extra dependencies than the
Python standard library.

Making a release and uploading it to PyPI
=========================================

This command is only run by project manager, to make a release, and
upload in to PyPI::

    pip install build
    python -m build --sdist --wheel
    twine upload dist/*


Note that the documentation should automatically get updated at each git
push. If that is not the case, try building th doc locally and resolve
any doc build error (in particular when running the examples).

Updating the changelog
======================

Changes are listed in the CHANGES.rst file. They must be manually updated
but, the following git command may be used to generate the lines::

    git log --abbrev-commit --date=short --no-merges --sparse
