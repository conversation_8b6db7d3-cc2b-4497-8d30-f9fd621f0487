"""
页面树节点模型定义
"""
from typing import List, Dict, Any, Optional
import json
from dataclasses import dataclass, asdict


@dataclass
class UIElement:
    """UI元素数据结构"""
    type: str  # button, input, text, image等
    text: Optional[str] = None
    hint: Optional[str] = None
    bounds: Optional[List[int]] = None  # [x1, y1, x2, y2]
    attributes: Optional[Dict[str, Any]] = None


class PageTreeNode:
    """页面树节点类"""
    
    def __init__(self, page_id: str, state_data: Dict[str, Any]):
        self.id = page_id
        self.state_data = {
            "screenshot": state_data.get("screenshot", ""),
            "elements": self._parse_elements(state_data.get("elements", [])),
            "semantic_desc": state_data.get("semantic_desc", ""),
            "embedding": state_data.get("embedding", None),
            "depth": state_data.get("depth", 0),
            "is_goal": state_data.get("is_goal", False)
        }
        self.parent = None
        self.action_from_parent = None
        self.children = []
    
    def _parse_elements(self, elements_data: List[Dict]) -> List[UIElement]:
        """解析UI元素数据"""
        elements = []
        for elem_data in elements_data:
            element = UIElement(
                type=elem_data.get("type", "unknown"),
                text=elem_data.get("text"),
                hint=elem_data.get("hint"),
                bounds=elem_data.get("bounds"),
                attributes=elem_data.get("attributes", {})
            )
            elements.append(element)
        return elements
    
    def add_child(self, child_node: 'PageTreeNode', action: str):
        """添加子节点"""
        child_node.parent = self
        child_node.action_from_parent = action
        child_node.state_data["depth"] = self.state_data["depth"] + 1
        self.children.append(child_node)
    
    def get_semantic_text(self) -> str:
        """获取用于向量化的语义文本"""
        # 组合语义描述和UI元素文本
        texts = [self.state_data["semantic_desc"]]
        
        for element in self.state_data["elements"]:
            if element.text:
                texts.append(element.text)
            if element.hint:
                texts.append(element.hint)
        
        return " ".join(filter(None, texts))
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，用于存储"""
        return {
            "id": self.id,
            "state_data": {
                "screenshot": self.state_data["screenshot"],
                "elements": [asdict(elem) for elem in self.state_data["elements"]],
                "semantic_desc": self.state_data["semantic_desc"],
                "embedding": self.state_data["embedding"],
                "depth": self.state_data["depth"],
                "is_goal": self.state_data["is_goal"]
            },
            "parent_id": self.parent.id if self.parent else None,
            "action_from_parent": self.action_from_parent,
            "children_ids": [child.id for child in self.children]
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PageTreeNode':
        """从字典创建节点"""
        node = cls(data["id"], data["state_data"])
        node.action_from_parent = data.get("action_from_parent")
        return node
    
    def __str__(self):
        return f"PageTreeNode(id={self.id}, depth={self.state_data['depth']}, children={len(self.children)})"
    
    def __repr__(self):
        return self.__str__()
