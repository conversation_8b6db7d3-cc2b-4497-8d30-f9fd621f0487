"""
App Discovery - 页面节点管理示例
"""
from models import PageTreeNode
from page_manager import PageNodeManager


def create_sample_page(page_id: str, desc: str, elements_data: list, depth: int = 0) -> PageTreeNode:
    """创建示例页面节点"""
    state_data = {
        "screenshot": f"screenshots/{page_id}.png",
        "elements": elements_data,
        "semantic_desc": desc,
        "depth": depth,
        "is_goal": False
    }
    return PageTreeNode(page_id, state_data)


def demo_page_management():
    """演示页面管理功能"""
    print("=== 页面节点管理演示 ===\n")

    # 初始化管理器
    manager = PageNodeManager()

    # 创建示例页面
    # 1. 登录页面
    login_elements = [
        {"type": "input", "hint": "用户名", "bounds": [100, 200, 300, 240]},
        {"type": "input", "hint": "密码", "bounds": [100, 260, 300, 300]},
        {"type": "button", "text": "登录", "bounds": [150, 320, 250, 360]}
    ]
    login_page = create_sample_page("login_001", "登录页面，包含用户名和密码输入框", login_elements)

    # 2. 主页
    home_elements = [
        {"type": "text", "text": "欢迎回来", "bounds": [50, 50, 200, 80]},
        {"type": "button", "text": "设置", "bounds": [300, 50, 350, 80]},
        {"type": "button", "text": "个人资料", "bounds": [50, 100, 150, 130]}
    ]
    home_page = create_sample_page("home_001", "应用主页，显示欢迎信息和导航按钮", home_elements, 1)

    # 3. 设置页面
    settings_elements = [
        {"type": "text", "text": "设置", "bounds": [50, 50, 100, 80]},
        {"type": "button", "text": "账户设置", "bounds": [50, 100, 150, 130]},
        {"type": "button", "text": "隐私设置", "bounds": [50, 140, 150, 170]}
    ]
    settings_page = create_sample_page("settings_001", "设置页面，包含各种配置选项", settings_elements, 2)

    # 建立页面关系
    login_page.add_child(home_page, "点击登录按钮")
    home_page.add_child(settings_page, "点击设置按钮")

    # 添加页面到管理器
    print("1. 添加页面节点...")
    manager.add_page_node(login_page)
    manager.add_page_node(home_page)
    manager.add_page_node(settings_page)
    print("   ✓ 已添加3个页面节点\n")

    # 查看统计信息
    stats = manager.get_stats()
    print("2. 存储统计信息:")
    for key, value in stats.items():
        print(f"   {key}: {value}")
    print()

    # 测试相似性搜索
    print("3. 相似性搜索测试:")

    # 搜索登录相关页面
    print("   搜索 '用户登录':")
    similar_pages = manager.find_similar_pages("用户登录", top_k=3)
    for i, (page, similarity) in enumerate(similar_pages, 1):
        print(f"     {i}. {page.id} (相似度: {similarity:.3f}) - {page.state_data['semantic_desc']}")
    print()

    # 搜索设置相关页面
    print("   搜索 '配置选项':")
    similar_pages = manager.find_similar_pages("配置选项", top_k=3)
    for i, (page, similarity) in enumerate(similar_pages, 1):
        print(f"     {i}. {page.id} (相似度: {similarity:.3f}) - {page.state_data['semantic_desc']}")
    print()

    # 测试根据ID获取页面
    print("4. 根据ID获取页面:")
    page = manager.get_page_by_id("home_001")
    if page:
        print(f"   找到页面: {page.id} - {page.state_data['semantic_desc']}")
        print(f"   深度: {page.state_data['depth']}")
        print(f"   UI元素数量: {len(page.state_data['elements'])}")
    print()

    # 测试获取子页面
    print("5. 获取子页面:")
    children = manager.get_children("home_001")
    print(f"   home_001的子页面数量: {len(children)}")
    for child in children:
        print(f"     - {child.id}: {child.state_data['semantic_desc']}")
    print()

    # 测试获取页面路径
    print("6. 获取页面路径:")
    path = manager.get_page_path("settings_001")
    print("   从根到settings_001的路径:")
    for i, node in enumerate(path):
        indent = "  " * i
        action = f" (通过: {node.action_from_parent})" if node.action_from_parent else ""
        print(f"     {indent}{node.id}: {node.state_data['semantic_desc']}{action}")


def main():
    """主函数"""
    try:
        demo_page_management()
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
