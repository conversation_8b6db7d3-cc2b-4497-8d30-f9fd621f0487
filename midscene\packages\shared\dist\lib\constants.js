"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/constants/index.ts
var constants_exports = {};
__export(constants_exports, {
  CONTAINER_MINI_HEIGHT: () => CONTAINER_MINI_HEIGHT,
  CONTAINER_MINI_WIDTH: () => CONTAINER_MINI_WIDTH,
  DEFAULT_WAIT_FOR_NAVIGATION_TIMEOUT: () => DEFAULT_WAIT_FOR_NAVIGATION_TIMEOUT,
  DEFAULT_WAIT_FOR_NETWORK_IDLE_CONCURRENCY: () => DEFAULT_WAIT_FOR_NETWORK_IDLE_CONCURRENCY,
  DEFAULT_WAIT_FOR_NETWORK_IDLE_TIME: () => DEFAULT_WAIT_FOR_NETWORK_IDLE_TIME,
  DEFAULT_WAIT_FOR_NETWORK_IDLE_TIMEOUT: () => DEFAULT_WAIT_FOR_NETWORK_IDLE_TIMEOUT,
  NodeType: () => NodeType,
  PLAYGROUND_SERVER_PORT: () => PLAYGROUND_SERVER_PORT,
  SCRCPY_SERVER_PORT: () => SCRCPY_SERVER_PORT,
  TEXT_MAX_SIZE: () => TEXT_MAX_SIZE,
  TEXT_SIZE_THRESHOLD: () => TEXT_SIZE_THRESHOLD
});
module.exports = __toCommonJS(constants_exports);
var TEXT_SIZE_THRESHOLD = 9;
var TEXT_MAX_SIZE = 40;
var CONTAINER_MINI_HEIGHT = 3;
var CONTAINER_MINI_WIDTH = 3;
var NodeType = /* @__PURE__ */ ((NodeType2) => {
  NodeType2["CONTAINER"] = "CONTAINER Node";
  NodeType2["FORM_ITEM"] = "FORM_ITEM Node";
  NodeType2["BUTTON"] = "BUTTON Node";
  NodeType2["A"] = "Anchor Node";
  NodeType2["IMG"] = "IMG Node";
  NodeType2["TEXT"] = "TEXT Node";
  NodeType2["POSITION"] = "POSITION Node";
  return NodeType2;
})(NodeType || {});
var PLAYGROUND_SERVER_PORT = 5800;
var SCRCPY_SERVER_PORT = 5700;
var DEFAULT_WAIT_FOR_NAVIGATION_TIMEOUT = 5e3;
var DEFAULT_WAIT_FOR_NETWORK_IDLE_TIMEOUT = 2e3;
var DEFAULT_WAIT_FOR_NETWORK_IDLE_TIME = 300;
var DEFAULT_WAIT_FOR_NETWORK_IDLE_CONCURRENCY = 2;
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  CONTAINER_MINI_HEIGHT,
  CONTAINER_MINI_WIDTH,
  DEFAULT_WAIT_FOR_NAVIGATION_TIMEOUT,
  DEFAULT_WAIT_FOR_NETWORK_IDLE_CONCURRENCY,
  DEFAULT_WAIT_FOR_NETWORK_IDLE_TIME,
  DEFAULT_WAIT_FOR_NETWORK_IDLE_TIMEOUT,
  NodeType,
  PLAYGROUND_SERVER_PORT,
  SCRCPY_SERVER_PORT,
  TEXT_MAX_SIZE,
  TEXT_SIZE_THRESHOLD
});
