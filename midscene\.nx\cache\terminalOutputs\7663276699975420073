
> @midscene/mcp@0.17.5 build E:\development\level-up\midscene\packages\mcp
> rslib build

[1m[38;2;189;255;243m [39m[38;2;189;255;243m [39m[38;2;179;249;235mR[39m[38;2;168;244;227ms[39m[38;2;158;238;219ml[39m[38;2;147;233;211mi[39m[38;2;137;227;203mb[39m[38;2;137;227;203m [39m[38;2;126;222;194mv[39m[38;2;116;216;186m0[39m[38;2;105;211;178m.[39m[38;2;95;205;170m6[39m[38;2;84;200;162m.[39m[38;2;74;194;154m3[39m[38;2;74;194;154m
[39m[22m
[1m[36mstart  [39m[22m generating declaration files... [90m(esm)[39m
[1m[32mready  [39m[22m built in [1m3.90[22m s[90m (esm)[39m
[1m[32mready  [39m[22m built in [1m3.90[22m s[90m (cjs)[39m
[1m[33mwarn   [39m[22m [1m[33mCompile warning: 
[39m[22mFile: [36mE:\development\level-up\midscene\node_modules\.pnpm\ws@8.17.1\node_modules\ws\lib\buffer-util.js[39m:1:1
  ⚠ Module not found: Can't resolve 'bufferutil' in 'E:\development\level-up\midscene\node_modules\.pnpm\ws@8.17.1\node_modules\ws\lib'
     ╭─[117:23]
 115 │ if (!process.env.WS_NO_BUFFER_UTIL) {
 116 │   try {
 117 │     const bufferUtil = require('bufferutil');
     ·                        ─────────────────────
 118 │ 
 119 │     module.exports.mask = function (source, mask, output, offset, length) {
     ╰────

 @ ../../node_modules/.pnpm/ws@8.17.1/node_modules/ws/lib/receiver.js
 @ ../../node_modules/.pnpm/ws@8.17.1/node_modules/ws/index.js
 @ ../../node_modules/.pnpm/socket.io-adapter@2.5.5/node_modules/socket.io-adapter/dist/in-memory-adapter.js
 @ ../../node_modules/.pnpm/socket.io-adapter@2.5.5/node_modules/socket.io-adapter/dist/index.js
 @ ../../node_modules/.pnpm/socket.io@4.8.1/node_modules/socket.io/dist/index.js
 @ ../web-integration/dist/lib/bridge-mode.js
 @ ./src/midscene.ts
 @ ./src/index.ts

File: [36mE:\development\level-up\midscene\node_modules\.pnpm\ws@8.17.1\node_modules\ws\lib\validation.js[39m:1:1
  ⚠ Module not found: Can't resolve 'utf-8-validate' in 'E:\development\level-up\midscene\node_modules\.pnpm\ws@8.17.1\node_modules\ws\lib'
     ╭─[122:24]
 120 │ } /* istanbul ignore else  */ else if (!process.env.WS_NO_UTF_8_VALIDATE) {
 121 │   try {
 122 │     const isValidUTF8 = require('utf-8-validate');
     ·                         ─────────────────────────
 123 │ 
 124 │     module.exports.isValidUTF8 = function (buf) {
     ╰────

 @ ../../node_modules/.pnpm/ws@8.17.1/node_modules/ws/lib/receiver.js
 @ ../../node_modules/.pnpm/ws@8.17.1/node_modules/ws/index.js
 @ ../../node_modules/.pnpm/socket.io-adapter@2.5.5/node_modules/socket.io-adapter/dist/in-memory-adapter.js
 @ ../../node_modules/.pnpm/socket.io-adapter@2.5.5/node_modules/socket.io-adapter/dist/index.js
 @ ../../node_modules/.pnpm/socket.io@4.8.1/node_modules/socket.io/dist/index.js
 @ ../web-integration/dist/lib/bridge-mode.js
 @ ./src/midscene.ts
 @ ./src/index.ts

File: [36mE:\development\level-up\midscene\node_modules\.pnpm\ws@8.17.1\node_modules\ws\lib\buffer-util.js[39m:1:1
  ⚠ Module not found: Can't resolve 'bufferutil' in 'E:\development\level-up\midscene\node_modules\.pnpm\ws@8.17.1\node_modules\ws\lib'
     ╭─[117:23]
 115 │ if (!process.env.WS_NO_BUFFER_UTIL) {
 116 │   try {
 117 │     const bufferUtil = require('bufferutil');
     ·                        ─────────────────────
 118 │ 
 119 │     module.exports.mask = function (source, mask, output, offset, length) {
     ╰────

 @ ../../node_modules/.pnpm/ws@8.17.1/node_modules/ws/lib/receiver.js
 @ ../../node_modules/.pnpm/ws@8.17.1/node_modules/ws/index.js
 @ ../../node_modules/.pnpm/engine.io-client@6.6.2/node_modules/engine.io-client/build/cjs/transports/websocket.node.js
 @ ../../node_modules/.pnpm/engine.io-client@6.6.2/node_modules/engine.io-client/build/cjs/index.js
 @ ../../node_modules/.pnpm/socket.io-client@4.8.1/node_modules/socket.io-client/build/cjs/index.js
 @ ../web-integration/dist/lib/bridge-mode.js
 @ ./src/midscene.ts
 @ ./src/index.ts

File: [36mE:\development\level-up\midscene\node_modules\.pnpm\ws@8.17.1\node_modules\ws\lib\validation.js[39m:1:1
  ⚠ Module not found: Can't resolve 'utf-8-validate' in 'E:\development\level-up\midscene\node_modules\.pnpm\ws@8.17.1\node_modules\ws\lib'
     ╭─[122:24]
 120 │ } /* istanbul ignore else  */ else if (!process.env.WS_NO_UTF_8_VALIDATE) {
 121 │   try {
 122 │     const isValidUTF8 = require('utf-8-validate');
     ·                         ─────────────────────────
 123 │ 
 124 │     module.exports.isValidUTF8 = function (buf) {
     ╰────

 @ ../../node_modules/.pnpm/ws@8.17.1/node_modules/ws/lib/receiver.js
 @ ../../node_modules/.pnpm/ws@8.17.1/node_modules/ws/index.js
 @ ../../node_modules/.pnpm/engine.io-client@6.6.2/node_modules/engine.io-client/build/cjs/transports/websocket.node.js
 @ ../../node_modules/.pnpm/engine.io-client@6.6.2/node_modules/engine.io-client/build/cjs/index.js
 @ ../../node_modules/.pnpm/socket.io-client@4.8.1/node_modules/socket.io-client/build/cjs/index.js
 @ ../web-integration/dist/lib/bridge-mode.js
 @ ./src/midscene.ts
 @ ./src/index.ts

[1m[32mready  [39m[22m declaration files generated in [1m2.82[22m s [90m(esm)[39m

[34m  File (esm)                     Size          [39m
  [2mdist\[22m[35mplaywright-example.txt[39m    1.8 kB    
  [2mdist\[22m[36m450.js[39m                    13.4 kB   
  [2mdist\[22m[35mAPI.mdx[39m                   22.8 kB   
  [2mdist\[22m[36m18.js[39m                     549.1 kB  
  [2mdist\[22m[36m952.js[39m                    1490.1 kB 
  [2mdist\[22m[36m251.js[39m                    1543.4 kB 
  [2mdist\[22m[36mindex.js[39m                  12949.8 kB

  [34mTotal:[39m 16570.5 kB

[2m  -----[22m

[34m  File (cjs)                     Size          [39m
  [2mdist\[22m[35mplaywright-example.txt[39m    1.8 kB    
  [2mdist\[22m[36m450.cjs[39m                   13.3 kB   
  [2mdist\[22m[35mAPI.mdx[39m                   22.8 kB   
  [2mdist\[22m[36m18.cjs[39m                    549.1 kB  
  [2mdist\[22m[36m952.cjs[39m                   1492.1 kB 
  [2mdist\[22m[36m251.cjs[39m                   1543.4 kB 
  [2mdist\[22m[36mindex.cjs[39m                 12971.8 kB

  [34mTotal:[39m 16594.3 kB

