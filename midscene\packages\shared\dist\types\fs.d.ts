interface PkgInfo {
    name: string;
    version: string;
    dir: string;
}
declare function getRunningPkgInfo(dir?: string): PkgInfo | null;
/**
 * Find the nearest package.json file recursively
 * @param {string} dir - Home directory
 * @returns {string|null} - The most recent package.json file path or null
 */
declare function findNearestPackageJson(dir: string): string | null;
declare function getElementInfosScriptContent(): string;
declare function getExtraReturnLogic(tree?: boolean): Promise<string | null>;

export { findNearestPackageJson, getElementInfosScriptContent, getExtraReturnLogic, getRunningPkgInfo };
