/*! For license information please see 18.f4dc3b35.js.LICENSE.txt */
"use strict";(self.webpackChunkchrome_extension=self.webpackChunkchrome_extension||[]).push([["18"],{18567:function(e,t,a){a.d(t,{r:()=>r});function r(e,t){return e.lc_error_code=t,e.message=`${e.message}

Troubleshooting URL: https://js.langchain.com/docs/troubleshooting/errors/${t}/
`,e}},56393:function(e,t,a){a.d(t,{j:()=>s,i:()=>o});var r=a(14979);function i(e,t){return t?.[e]||r(e)}function n(e){return Array.isArray(e)?[...e]:{...e}}function s(e){let t=Object.getPrototypeOf(e);return"function"==typeof e.lc_name&&("function"!=typeof t.lc_name||e.lc_name()!==t.lc_name())?e.lc_name():e.name}a(54933);class o{static lc_name(){return this.name}get lc_id(){return[...this.lc_namespace,s(this.constructor)]}get lc_secrets(){}get lc_attributes(){}get lc_aliases(){}constructor(e,...t){Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.lc_kwargs=e||{}}toJSON(){if(!this.lc_serializable||this.lc_kwargs instanceof o||"object"!=typeof this.lc_kwargs||Array.isArray(this.lc_kwargs))return this.toJSONNotImplemented();let e={},t={},a=Object.keys(this.lc_kwargs).reduce((e,t)=>(e[t]=t in this?this[t]:this.lc_kwargs[t],e),{});for(let r=Object.getPrototypeOf(this);r;r=Object.getPrototypeOf(r))Object.assign(e,Reflect.get(r,"lc_aliases",this)),Object.assign(t,Reflect.get(r,"lc_secrets",this)),Object.assign(a,Reflect.get(r,"lc_attributes",this));return Object.keys(t).forEach(e=>{let t=this,r=a,[i,...n]=e.split(".").reverse();for(let e of n.reverse()){if(!(e in t)||void 0===t[e])return;e in r&&void 0!==r[e]||("object"==typeof t[e]&&null!=t[e]?r[e]={}:Array.isArray(t[e])&&(r[e]=[])),t=t[e],r=r[e]}i in t&&void 0!==t[i]&&(r[i]=r[i]||t[i])}),{lc:1,type:"constructor",id:this.lc_id,kwargs:function(e,t,a){let r={};for(let i in e)Object.hasOwn(e,i)&&(r[t(i,a)]=e[i]);return r}(Object.keys(t).length?function(e,t){let a=n(e);for(let[e,r]of Object.entries(t)){let[t,...i]=e.split(".").reverse(),s=a;for(let e of i.reverse()){if(void 0===s[e])break;s[e]=n(s[e]),s=s[e]}void 0!==s[t]&&(s[t]={lc:1,type:"secret",id:[r]})}return a}(a,t):a,i,e)}}toJSONNotImplemented(){return{lc:1,type:"not_implemented",id:this.lc_id}}}},89590:function(e,t,a){a.d(t,{GC:()=>s,gY:()=>n});var r=a(7546),i=a(49156);class n extends r.ku{get lc_aliases(){return{...super.lc_aliases,tool_calls:"tool_calls",invalid_tool_calls:"invalid_tool_calls"}}constructor(e,t){let a;if("string"==typeof e)a={content:e,tool_calls:[],invalid_tool_calls:[],additional_kwargs:t??{}};else{a=e;let t=a.additional_kwargs?.tool_calls,r=a.tool_calls;null!=t&&t.length>0&&(void 0===r||0===r.length)&&console.warn("New LangChain packages are available that more efficiently handle tool calling.\n\nPlease upgrade your packages to versions that set message tool calls. e.g., `yarn add @langchain/anthropic`, yarn add @langchain/openai`, etc.");try{if(null!=t&&void 0===r){let[e,r]=(0,i.jC)(t);a.tool_calls=e??[],a.invalid_tool_calls=r??[]}else a.tool_calls=a.tool_calls??[],a.invalid_tool_calls=a.invalid_tool_calls??[]}catch(e){a.tool_calls=[],a.invalid_tool_calls=[]}}super(a),Object.defineProperty(this,"tool_calls",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"invalid_tool_calls",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"usage_metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),"string"!=typeof a&&(this.tool_calls=a.tool_calls??this.tool_calls,this.invalid_tool_calls=a.invalid_tool_calls??this.invalid_tool_calls),this.usage_metadata=a.usage_metadata}static lc_name(){return"AIMessage"}_getType(){return"ai"}get _printableFields(){return{...super._printableFields,tool_calls:this.tool_calls,invalid_tool_calls:this.invalid_tool_calls,usage_metadata:this.usage_metadata}}}class s extends r.$k{constructor(e){let t;if("string"==typeof e)t={content:e,tool_calls:[],invalid_tool_calls:[],tool_call_chunks:[]};else if(void 0===e.tool_call_chunks)t={...e,tool_calls:e.tool_calls??[],invalid_tool_calls:[],tool_call_chunks:[],usage_metadata:void 0!==e.usage_metadata?e.usage_metadata:void 0};else{let a=[],r=[];for(let t of e.tool_call_chunks){let e={};try{if(e=function(e){if(void 0===e)return null;try{return JSON.parse(e)}catch(e){}let t="",a=[],r=!1,i=!1;for(let n of e){if(r)'"'!==n||i?"\n"!==n||i?i="\\"===n&&!i:n="\\n":r=!1;else if('"'===n)r=!0,i=!1;else if("{"===n)a.push("}");else if("["===n)a.push("]");else if("}"===n||"]"===n)if(!a||a[a.length-1]!==n)return null;else a.pop();t+=n}r&&(t+='"');for(let e=a.length-1;e>=0;e-=1)t+=a[e];try{return JSON.parse(t)}catch(e){return null}}(t.args||"{}"),null===e||"object"!=typeof e||Array.isArray(e))throw Error("Malformed tool call chunk args.");a.push({name:t.name??"",args:e,id:t.id,type:"tool_call"})}catch(e){r.push({name:t.name,args:t.args,id:t.id,error:"Malformed args.",type:"invalid_tool_call"})}}t={...e,tool_calls:a,invalid_tool_calls:r,usage_metadata:void 0!==e.usage_metadata?e.usage_metadata:void 0}}super(t),Object.defineProperty(this,"tool_calls",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"invalid_tool_calls",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"tool_call_chunks",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"usage_metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.tool_call_chunks=t.tool_call_chunks??this.tool_call_chunks,this.tool_calls=t.tool_calls??this.tool_calls,this.invalid_tool_calls=t.invalid_tool_calls??this.invalid_tool_calls,this.usage_metadata=t.usage_metadata}get lc_aliases(){return{...super.lc_aliases,tool_calls:"tool_calls",invalid_tool_calls:"invalid_tool_calls",tool_call_chunks:"tool_call_chunks"}}static lc_name(){return"AIMessageChunk"}_getType(){return"ai"}get _printableFields(){return{...super._printableFields,tool_calls:this.tool_calls,tool_call_chunks:this.tool_call_chunks,invalid_tool_calls:this.invalid_tool_calls,usage_metadata:this.usage_metadata}}concat(e){let t={content:(0,r.Wf)(this.content,e.content),additional_kwargs:(0,r.wv)(this.additional_kwargs,e.additional_kwargs),response_metadata:(0,r.wv)(this.response_metadata,e.response_metadata),tool_call_chunks:[],id:this.id??e.id};if(void 0!==this.tool_call_chunks||void 0!==e.tool_call_chunks){let a=(0,r.eL)(this.tool_call_chunks,e.tool_call_chunks);void 0!==a&&a.length>0&&(t.tool_call_chunks=a)}if(void 0!==this.usage_metadata||void 0!==e.usage_metadata){let a={...(this.usage_metadata?.input_token_details?.audio!==void 0||e.usage_metadata?.input_token_details?.audio!==void 0)&&{audio:(this.usage_metadata?.input_token_details?.audio??0)+(e.usage_metadata?.input_token_details?.audio??0)},...(this.usage_metadata?.input_token_details?.cache_read!==void 0||e.usage_metadata?.input_token_details?.cache_read!==void 0)&&{cache_read:(this.usage_metadata?.input_token_details?.cache_read??0)+(e.usage_metadata?.input_token_details?.cache_read??0)},...(this.usage_metadata?.input_token_details?.cache_creation!==void 0||e.usage_metadata?.input_token_details?.cache_creation!==void 0)&&{cache_creation:(this.usage_metadata?.input_token_details?.cache_creation??0)+(e.usage_metadata?.input_token_details?.cache_creation??0)}},r={...(this.usage_metadata?.output_token_details?.audio!==void 0||e.usage_metadata?.output_token_details?.audio!==void 0)&&{audio:(this.usage_metadata?.output_token_details?.audio??0)+(e.usage_metadata?.output_token_details?.audio??0)},...(this.usage_metadata?.output_token_details?.reasoning!==void 0||e.usage_metadata?.output_token_details?.reasoning!==void 0)&&{reasoning:(this.usage_metadata?.output_token_details?.reasoning??0)+(e.usage_metadata?.output_token_details?.reasoning??0)}},i=this.usage_metadata??{input_tokens:0,output_tokens:0,total_tokens:0},n=e.usage_metadata??{input_tokens:0,output_tokens:0,total_tokens:0};t.usage_metadata={input_tokens:i.input_tokens+n.input_tokens,output_tokens:i.output_tokens+n.output_tokens,total_tokens:i.total_tokens+n.total_tokens,...Object.keys(a).length>0&&{input_token_details:a},...Object.keys(r).length>0&&{output_token_details:r}}}return new s(t)}}},7546:function(e,t,a){a.d(t,{$k:()=>u,QW:()=>h,Wf:()=>i,eL:()=>l,gQ:()=>c,ku:()=>s,n4:()=>n,wv:()=>o,x:()=>d});var r=a(56393);function i(e,t){if("string"==typeof e)if("string"==typeof t)return e+t;else return[{type:"text",text:e},...t];return Array.isArray(t)?l(e,t)??[...e,...t]:[...e,{type:"text",text:t}]}function n(e,t){return"error"===e||"error"===t?"error":"success"}class s extends r.i{get lc_aliases(){return{additional_kwargs:"additional_kwargs",response_metadata:"response_metadata"}}get text(){return"string"==typeof this.content?this.content:""}getType(){return this._getType()}constructor(e,t){"string"==typeof e&&(e={content:e,additional_kwargs:t,response_metadata:{}}),e.additional_kwargs||(e.additional_kwargs={}),e.response_metadata||(e.response_metadata={}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","messages"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"content",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"additional_kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"response_metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=e.name,this.content=e.content,this.additional_kwargs=e.additional_kwargs,this.response_metadata=e.response_metadata,this.id=e.id}toDict(){return{type:this._getType(),data:this.toJSON().kwargs}}static lc_name(){return"BaseMessage"}get _printableFields(){return{id:this.id,content:this.content,name:this.name,additional_kwargs:this.additional_kwargs,response_metadata:this.response_metadata}}_updateId(e){this.id=e,this.lc_kwargs.id=e}get[Symbol.toStringTag](){return this.constructor.lc_name()}[Symbol.for("nodejs.util.inspect.custom")](e){var t,a;if(null===e)return this;let r=(t=this._printableFields,a=Math.max(4,e),JSON.stringify(function e(t,r){if("object"!=typeof t||null==t)return t;if(r>=a)return Array.isArray(t)?"[Array]":"[Object]";if(Array.isArray(t))return t.map(t=>e(t,r+1));let i={};for(let a of Object.keys(t))i[a]=e(t[a],r+1);return i}(t,0),null,2));return`${this.constructor.lc_name()} ${r}`}}function o(e,t){let a={...e};for(let[e,r]of Object.entries(t))if(null==a[e])a[e]=r;else if(null==r)continue;else if(typeof a[e]!=typeof r||Array.isArray(a[e])!==Array.isArray(r))throw Error(`field[${e}] already exists in the message chunk, but with a different type.`);else if("string"==typeof a[e]){if("type"===e)continue;a[e]+=r}else if("object"!=typeof a[e]||Array.isArray(a[e]))if(Array.isArray(a[e]))a[e]=l(a[e],r);else{if(a[e]===r)continue;console.warn(`field[${e}] already exists in this message chunk and value has unsupported type.`)}else a[e]=o(a[e],r);return a}function l(e,t){if(void 0!==e||void 0!==t){if(void 0===e||void 0===t)return e||t;let a=[...e];for(let e of t)if("object"==typeof e&&"index"in e&&"number"==typeof e.index){let t=a.findIndex(t=>t.index===e.index);-1!==t?a[t]=o(a[t],e):a.push(e)}else{if("object"==typeof e&&"text"in e&&""===e.text)continue;a.push(e)}return a}}function d(e,t){if(!e&&!t)throw Error("Cannot merge two undefined objects.");if(!e||!t)return e||t;if(typeof e!=typeof t)throw Error(`Cannot merge objects of different types.
Left ${typeof e}
Right ${typeof t}`);if("string"==typeof e&&"string"==typeof t)return e+t;if(Array.isArray(e)&&Array.isArray(t))return l(e,t);if("object"==typeof e&&"object"==typeof t)return o(e,t);else if(e===t)return e;else throw Error(`Can not merge objects of different types.
Left ${e}
Right ${t}`)}class u extends s{}function c(e){return"string"==typeof e.role}function h(e){return"function"==typeof e?._getType}},42407:function(e,t,a){a.d(t,{J:()=>i});var r=a(7546);class i extends r.ku{static lc_name(){return"ChatMessage"}static _chatMessageClass(){return i}constructor(e,t){"string"==typeof e&&(e={content:e,role:t}),super(e),Object.defineProperty(this,"role",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.role=e.role}_getType(){return"generic"}static isInstance(e){return"generic"===e._getType()}get _printableFields(){return{...super._printableFields,role:this.role}}}class n extends r.$k{static lc_name(){return"ChatMessageChunk"}constructor(e,t){"string"==typeof e&&(e={content:e,role:t}),super(e),Object.defineProperty(this,"role",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.role=e.role}_getType(){return"generic"}concat(e){return new n({content:(0,r.Wf)(this.content,e.content),additional_kwargs:(0,r.wv)(this.additional_kwargs,e.additional_kwargs),response_metadata:(0,r.wv)(this.response_metadata,e.response_metadata),role:this.role,id:this.id??e.id})}get _printableFields(){return{...super._printableFields,role:this.role}}}},16899:function(e,t,a){a(7546).ku},76937:function(e,t,a){a.d(t,{xk:()=>i});var r=a(7546);class i extends r.ku{static lc_name(){return"HumanMessage"}_getType(){return"human"}}},40549:function(e,t,a){a.d(t,{jN:()=>i});var r=a(7546);class i extends r.ku{static lc_name(){return"SystemMessage"}_getType(){return"system"}}},49156:function(e,t,a){a.d(t,{Cq:()=>i,jC:()=>s});var r=a(7546);class i extends r.ku{static lc_name(){return"ToolMessage"}get lc_aliases(){return{tool_call_id:"tool_call_id"}}constructor(e,t,a){"string"==typeof e&&(e={content:e,name:a,tool_call_id:t}),super(e),Object.defineProperty(this,"lc_direct_tool_output",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"status",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tool_call_id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"artifact",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.tool_call_id=e.tool_call_id,this.artifact=e.artifact,this.status=e.status}_getType(){return"tool"}static isInstance(e){return"tool"===e._getType()}get _printableFields(){return{...super._printableFields,tool_call_id:this.tool_call_id,artifact:this.artifact}}}class n extends r.$k{constructor(e){super(e),Object.defineProperty(this,"tool_call_id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"status",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"artifact",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.tool_call_id=e.tool_call_id,this.artifact=e.artifact,this.status=e.status}static lc_name(){return"ToolMessageChunk"}_getType(){return"tool"}concat(e){return new n({content:(0,r.Wf)(this.content,e.content),additional_kwargs:(0,r.wv)(this.additional_kwargs,e.additional_kwargs),response_metadata:(0,r.wv)(this.response_metadata,e.response_metadata),artifact:(0,r.x)(this.artifact,e.artifact),tool_call_id:this.tool_call_id,id:this.id??e.id,status:(0,r.n4)(this.status,e.status)})}get _printableFields(){return{...super._printableFields,tool_call_id:this.tool_call_id,artifact:this.artifact}}}function s(e){let t=[],a=[];for(let r of e)if(!r.function)continue;else{let e=r.function.name;try{let a=JSON.parse(r.function.arguments),i={name:e||"",args:a||{},id:r.id};t.push(i)}catch(t){a.push({name:e,args:r.function.arguments,id:r.id,error:"Malformed args."})}}return[t,a]}},94509:function(e,t,a){a.d(t,{E1:()=>h,zs:()=>p});var r=a(18567),i=a(33702),n=a(89590),s=a(7546);a(42407),a(16899);var o=a(76937),l=a(40549),d=a(49156);function u(e){return(0,i.u)(e)?e:"string"==typeof e.id&&"function"===e.type&&"object"==typeof e.function&&null!==e.function&&"arguments"in e.function&&"string"==typeof e.function.arguments&&"name"in e.function&&"string"==typeof e.function.name?{id:e.id,args:JSON.parse(e.function.arguments),name:e.function.name,type:"tool_call"}:e}function c(e){let t,a;if("object"==typeof e&&null!=e&&1===e.lc&&Array.isArray(e.id)&&null!=e.kwargs&&"object"==typeof e.kwargs){let r=e.id.at(-1);t="HumanMessage"===r||"HumanMessageChunk"===r?"user":"AIMessage"===r||"AIMessageChunk"===r?"assistant":"SystemMessage"===r||"SystemMessageChunk"===r?"system":"unknown",a=e.kwargs}else{let{type:r,...i}=e;t=r,a=i}if("human"===t||"user"===t)return new o.xk(a);if("ai"===t||"assistant"===t){let{tool_calls:e,...t}=a;if(!Array.isArray(e))return new n.gY(a);let r=e.map(u);return new n.gY({...t,tool_calls:r})}if("system"===t)return new l.jN(a);if("developer"===t)return new l.jN({...a,additional_kwargs:{...a.additional_kwargs,__openai_role__:"developer"}});if("tool"===t&&"tool_call_id"in a)return new d.Cq({...a,content:a.content,tool_call_id:a.tool_call_id,name:a.name});throw(0,r.r)(Error(`Unable to coerce message from array: only human, AI, system, developer, or tool message coercion is currently supported.

Received: ${JSON.stringify(e,null,2)}`),"MESSAGE_COERCION_FAILURE")}function h(e){if("string"==typeof e)return new o.xk(e);if((0,s.QW)(e))return e;if(Array.isArray(e)){let[t,a]=e;return c({type:t,content:a})}if(!(0,s.gQ)(e))return c(e);{let{role:t,...a}=e;return c({...a,type:t})}}function p(e,t="Human",a="AI"){let r=[];for(let i of e){let e;if("human"===i._getType())e=t;else if("ai"===i._getType())e=a;else if("system"===i._getType())e="System";else if("function"===i._getType())e="Function";else if("tool"===i._getType())e="Tool";else if("generic"===i._getType())e=i.role;else throw Error(`Got unsupported message type: ${i._getType()}`);let n=i.name?`${i.name}, `:"",s="string"==typeof i.content?i.content:JSON.stringify(i.content,null,2);r.push(`${e}: ${n}${s}`)}return r.join("\n")}},44282:function(e,t,a){a.d(t,{GU:()=>l,Nn:()=>d,nw:()=>o});var r=a(56393),i=a(76937),n=a(94509);class s extends r.i{}class o extends s{static lc_name(){return"StringPromptValue"}constructor(e){super({value:e}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompt_values"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.value=e}toString(){return this.value}toChatMessages(){return[new i.xk(this.value)]}}class l extends s{static lc_name(){return"ChatPromptValue"}constructor(e){Array.isArray(e)&&(e={messages:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompt_values"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"messages",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.messages=e.messages}toString(){return(0,n.zs)(this.messages)}toChatMessages(){return this.messages}}class d extends s{static lc_name(){return"ImagePromptValue"}constructor(e){"imageUrl"in e||(e={imageUrl:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompt_values"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"imageUrl",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.imageUrl=e.imageUrl}toString(){return this.imageUrl.url}toChatMessages(){return[new i.xk({content:[{type:"image_url",image_url:{detail:this.imageUrl.detail,url:this.imageUrl.url}}]})]}}},13247:function(e,t,a){a.d(t,{d:()=>i});var r=a(58746);class i extends r.eq{get lc_attributes(){return{partialVariables:void 0}}constructor(e){super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompts",this._getPromptType()]}),Object.defineProperty(this,"inputVariables",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"outputParser",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"partialVariables",{enumerable:!0,configurable:!0,writable:!0,value:void 0});let{inputVariables:t}=e;if(t.includes("stop"))throw Error("Cannot have an input variable named 'stop', as it is used internally, please rename.");Object.assign(this,e)}async mergePartialAndUserVariables(e){let t=this.partialVariables??{},a={};for(let[e,r]of Object.entries(t))"string"==typeof r?a[e]=r:a[e]=await r();return{...a,...e}}async invoke(e,t){return this._callWithConfig(e=>this.formatPromptValue(e),e,{...t,runType:"prompt"})}serialize(){throw Error("Use .toJSON() instead")}static async deserialize(e){switch(e._type){case"prompt":{let{PromptTemplate:t}=await Promise.resolve().then(a.bind(a,49068));return t.deserialize(e)}case void 0:{let{PromptTemplate:t}=await Promise.resolve().then(a.bind(a,49068));return t.deserialize({...e,_type:"prompt"})}case"few_shot":{let{FewShotPromptTemplate:t}=await a.e("450").then(a.bind(a,32317));return t.deserialize(e)}default:throw Error(`Invalid prompt type in config: ${e._type}`)}}}},49068:function(e,t,a){a.d(t,{PromptTemplate:()=>n});var r=a(9548),i=a(603);class n extends r.A{static lc_name(){return"PromptTemplate"}constructor(e){if(super(e),Object.defineProperty(this,"template",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"templateFormat",{enumerable:!0,configurable:!0,writable:!0,value:"f-string"}),Object.defineProperty(this,"validateTemplate",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"additionalContentFields",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),"mustache"===e.templateFormat&&void 0===e.validateTemplate&&(this.validateTemplate=!1),Object.assign(this,e),this.validateTemplate){if("mustache"===this.templateFormat)throw Error("Mustache templates cannot be validated.");let e=this.inputVariables;this.partialVariables&&(e=e.concat(Object.keys(this.partialVariables))),(0,i.af)(this.template,this.templateFormat,e)}}_getPromptType(){return"prompt"}async format(e){let t=await this.mergePartialAndUserVariables(e);return(0,i.SM)(this.template,this.templateFormat,t)}static fromExamples(e,t,a,r="\n\n",i=""){return new n({inputVariables:a,template:[i,...e,t].join(r)})}static fromTemplate(e,t){let{templateFormat:a="f-string",...r}=t??{},s=new Set;return(0,i.$M)(e,a).forEach(e=>{"variable"===e.type&&s.add(e.name)}),new n({inputVariables:[...s],templateFormat:a,template:e,...r})}async partial(e){let t=this.inputVariables.filter(t=>!(t in e)),a={...this.partialVariables??{},...e};return new n({...this,inputVariables:t,partialVariables:a})}serialize(){if(void 0!==this.outputParser)throw Error("Cannot serialize a prompt template with an output parser");return{_type:this._getPromptType(),input_variables:this.inputVariables,template:this.template,template_format:this.templateFormat}}static async deserialize(e){if(!e.template)throw Error("Prompt template must have a template");return new n({inputVariables:e.input_variables,template:e.template,templateFormat:e.template_format})}}},9548:function(e,t,a){a.d(t,{A:()=>n});var r=a(44282),i=a(13247);class n extends i.d{async formatPromptValue(e){let t=await this.format(e);return new r.nw(t)}}},603:function(e,t,a){a.d(t,{af:()=>A,_O:()=>k,$M:()=>P,SM:()=>T,PZ:()=>S});var r=Object.prototype.toString,i=Array.isArray||function(e){return"[object Array]"===r.call(e)};function n(e){return"function"==typeof e}function s(e){return e.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function o(e,t){return null!=e&&"object"==typeof e&&t in e}var l=RegExp.prototype.test,d=/\S/,u={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;","`":"&#x60;","=":"&#x3D;"},c=/\s*/,h=/\s+/,p=/\s*=/,m=/\s*\}/,f=/#|\^|\/|>|\{|&|=|!/;function g(e){this.string=e,this.tail=e,this.pos=0}function y(e,t){this.view=e,this.cache={".":this.view},this.parent=t}function _(){this.templateCache={_cache:{},set:function(e,t){this._cache[e]=t},get:function(e){return this._cache[e]},clear:function(){this._cache={}}}}g.prototype.eos=function(){return""===this.tail},g.prototype.scan=function(e){var t=this.tail.match(e);if(!t||0!==t.index)return"";var a=t[0];return this.tail=this.tail.substring(a.length),this.pos+=a.length,a},g.prototype.scanUntil=function(e){var t,a=this.tail.search(e);switch(a){case -1:t=this.tail,this.tail="";break;case 0:t="";break;default:t=this.tail.substring(0,a),this.tail=this.tail.substring(a)}return this.pos+=t.length,t},y.prototype.push=function(e){return new y(e,this)},y.prototype.lookup=function(e){var t=this.cache;if(t.hasOwnProperty(e))i=t[e];else{for(var a,r,i,s,l,d,u=this,c=!1;u;){if(e.indexOf(".")>0)for(s=u.view,l=e.split("."),d=0;null!=s&&d<l.length;)d===l.length-1&&(c=o(s,l[d])||(a=s,r=l[d],null!=a&&"object"!=typeof a&&a.hasOwnProperty&&a.hasOwnProperty(r))),s=s[l[d++]];else s=u.view[e],c=o(u.view,e);if(c){i=s;break}u=u.parent}t[e]=i}return n(i)&&(i=i.call(this.view)),i},_.prototype.clearCache=function(){void 0!==this.templateCache&&this.templateCache.clear()},_.prototype.parse=function(e,t){var a=this.templateCache,r=e+":"+(t||b.tags).join(":"),n=void 0!==a,o=n?a.get(r):void 0;return void 0==o&&(o=function(e,t){if(!e)return[];var a,r,n,o,u,y,_,v,w,O=!1,k=[],E=[],S=[],x=!1,j=!1,T="",P=0;function A(){if(x&&!j)for(;S.length;)delete E[S.pop()];else S=[];x=!1,j=!1}function I(e){if("string"==typeof e&&(e=e.split(h,2)),!i(e)||2!==e.length)throw Error("Invalid tags: "+e);a=RegExp(s(e[0])+"\\s*"),r=RegExp("\\s*"+s(e[1])),n=RegExp("\\s*"+s("}"+e[1]))}I(t||b.tags);for(var C=new g(e);!C.eos();){if(o=C.pos,y=C.scanUntil(a))for(var $=0,N=y.length;$<N;++$)!function(e){return!l.call(d,e)}(_=y.charAt($))?(j=!0,O=!0,T+=" "):(S.push(E.length),T+=_),E.push(["text",_,o,o+1]),o+=1,"\n"===_&&(A(),T="",P=0,O=!1);if(!C.scan(a))break;if(x=!0,u=C.scan(f)||"name",C.scan(c),"="===u?(y=C.scanUntil(p),C.scan(p),C.scanUntil(r)):"{"===u?(y=C.scanUntil(n),C.scan(m),C.scanUntil(r),u="&"):y=C.scanUntil(r),!C.scan(r))throw Error("Unclosed tag at "+C.pos);if(v=">"==u?[u,y,o,C.pos,T,P,O]:[u,y,o,C.pos],P++,E.push(v),"#"===u||"^"===u)k.push(v);else if("/"===u){if(!(w=k.pop()))throw Error('Unopened section "'+y+'" at '+o);if(w[1]!==y)throw Error('Unclosed section "'+w[1]+'" at '+o)}else"name"===u||"{"===u||"&"===u?j=!0:"="===u&&I(y)}if(A(),w=k.pop())throw Error('Unclosed section "'+w[1]+'" at '+C.pos);return function(e){for(var t,a=[],r=a,i=[],n=0,s=e.length;n<s;++n)switch((t=e[n])[0]){case"#":case"^":r.push(t),i.push(t),r=t[4]=[];break;case"/":i.pop()[5]=t[2],r=i.length>0?i[i.length-1][4]:a;break;default:r.push(t)}return a}(function(e){for(var t,a,r=[],i=0,n=e.length;i<n;++i)(t=e[i])&&("text"===t[0]&&a&&"text"===a[0]?(a[1]+=t[1],a[3]=t[3]):(r.push(t),a=t));return r}(E))}(e,t),n&&a.set(r,o)),o},_.prototype.render=function(e,t,a,r){var i=this.getConfigTags(r),n=this.parse(e,i),s=t instanceof y?t:new y(t,void 0);return this.renderTokens(n,s,a,e,r)},_.prototype.renderTokens=function(e,t,a,r,i){for(var n,s,o,l="",d=0,u=e.length;d<u;++d)o=void 0,"#"===(s=(n=e[d])[0])?o=this.renderSection(n,t,a,r,i):"^"===s?o=this.renderInverted(n,t,a,r,i):">"===s?o=this.renderPartial(n,t,a,i):"&"===s?o=this.unescapedValue(n,t):"name"===s?o=this.escapedValue(n,t,i):"text"===s&&(o=this.rawValue(n)),void 0!==o&&(l+=o);return l},_.prototype.renderSection=function(e,t,a,r,s){var o=this,l="",d=t.lookup(e[1]);if(d){if(i(d))for(var u=0,c=d.length;u<c;++u)l+=this.renderTokens(e[4],t.push(d[u]),a,r,s);else if("object"==typeof d||"string"==typeof d||"number"==typeof d)l+=this.renderTokens(e[4],t.push(d),a,r,s);else if(n(d)){if("string"!=typeof r)throw Error("Cannot use higher-order sections without the original template");null!=(d=d.call(t.view,r.slice(e[3],e[5]),function(e){return o.render(e,t,a,s)}))&&(l+=d)}else l+=this.renderTokens(e[4],t,a,r,s);return l}},_.prototype.renderInverted=function(e,t,a,r,n){var s=t.lookup(e[1]);if(!s||i(s)&&0===s.length)return this.renderTokens(e[4],t,a,r,n)},_.prototype.indentPartial=function(e,t,a){for(var r=t.replace(/[^ \t]/g,""),i=e.split("\n"),n=0;n<i.length;n++)i[n].length&&(n>0||!a)&&(i[n]=r+i[n]);return i.join("\n")},_.prototype.renderPartial=function(e,t,a,r){if(a){var i=this.getConfigTags(r),s=n(a)?a(e[1]):a[e[1]];if(null!=s){var o=e[6],l=e[5],d=e[4],u=s;0==l&&d&&(u=this.indentPartial(s,d,o));var c=this.parse(u,i);return this.renderTokens(c,t,a,u,r)}}},_.prototype.unescapedValue=function(e,t){var a=t.lookup(e[1]);if(null!=a)return a},_.prototype.escapedValue=function(e,t,a){var r=this.getConfigEscape(a)||b.escape,i=t.lookup(e[1]);if(null!=i)return"number"==typeof i&&r===b.escape?String(i):r(i)},_.prototype.rawValue=function(e){return e[1]},_.prototype.getConfigTags=function(e){return i(e)?e:e&&"object"==typeof e?e.tags:void 0},_.prototype.getConfigEscape=function(e){return e&&"object"==typeof e&&!i(e)?e.escape:void 0};var b={name:"mustache.js",version:"4.2.0",tags:["{{","}}"],clearCache:void 0,escape:void 0,parse:void 0,render:void 0,Scanner:void 0,Context:void 0,Writer:void 0,set templateCache(cache){v.templateCache=cache},get templateCache(){return v.templateCache}},v=new _;b.clearCache=function(){return v.clearCache()},b.parse=function(e,t){return v.parse(e,t)},b.render=function(e,t,a,r){if("string"!=typeof e)throw TypeError('Invalid template! Template should be a "string" but "'+(i(e)?"array":typeof e)+'" was given as the first argument for mustache#render(template, view, partials)');return v.render(e,t,a,r)},b.escape=function(e){return String(e).replace(/[&<>"'`=\/]/g,function(e){return u[e]})},b.Scanner=g,b.Context=y,b.Writer=_;var w=a(18567);function O(){b.escape=e=>e}let k=e=>{let t=e.split(""),a=[],r=(e,a)=>{for(let r=a;r<t.length;r+=1)if(e.includes(t[r]))return r;return -1},i=0;for(;i<t.length;)if("{"===t[i]&&i+1<t.length&&"{"===t[i+1])a.push({type:"literal",text:"{"}),i+=2;else if("}"===t[i]&&i+1<t.length&&"}"===t[i+1])a.push({type:"literal",text:"}"}),i+=2;else if("{"===t[i]){let e=r("}",i);if(e<0)throw Error("Unclosed '{' in template.");a.push({type:"variable",name:t.slice(i+1,e).join("")}),i=e+1}else if("}"===t[i])throw Error("Single '}' in template.");else{let e=r("{}",i),n=(e<0?t.slice(i):t.slice(i,e)).join("");a.push({type:"literal",text:n}),i=e<0?t.length:e}return a},E=e=>e.map(e=>"name"===e[0]?{type:"variable",name:e[1].includes(".")?e[1].split(".")[0]:e[1]}:["#","&","^",">"].includes(e[0])?{type:"variable",name:e[1]}:{type:"literal",text:e[1]}),S=e=>(O(),E(b.parse(e))),x={"f-string":(e,t)=>k(e).reduce((e,a)=>{if("variable"===a.type){if(a.name in t)return e+("string"==typeof t[a.name]?t[a.name]:JSON.stringify(t[a.name]));throw Error(`(f-string) Missing value for input ${a.name}`)}return e+a.text},""),mustache:(e,t)=>(O(),b.render(e,t))},j={"f-string":k,mustache:S},T=(e,t,a)=>{try{return x[t](e,a)}catch(e){throw(0,w.r)(e,"INVALID_PROMPT_INPUT")}},P=(e,t)=>j[t](e),A=(e,t,a)=>{if(!(t in x)){let e=Object.keys(x);throw Error(`Invalid template format. Got \`${t}\`;
                         should be one of ${e}`)}try{let r=a.reduce((e,t)=>(e[t]="foo",e),{});Array.isArray(e)?e.forEach(e=>{if("text"===e.type)T(e.text,t,r);else if("image_url"===e.type)if("string"==typeof e.image_url)T(e.image_url,t,r);else{let a=e.image_url.url;T(a,t,r)}else throw Error(`Invalid message template received. ${JSON.stringify(e,null,2)}`)}):T(e,t,r)}catch(e){throw Error(`Invalid prompt schema: ${e.message}`)}}},58746:function(e,t,a){let r,i,n,s,o,l,d,u;a.d(t,{eq:()=>iS});var c,h,p,m,f,g,y,_,b,v,w={};a.r(w),a.d(w,{JsonPatchError:()=>ak,_areEquals:()=>a$,applyOperation:()=>aT,applyPatch:()=>aP,applyReducer:()=>aA,deepClone:()=>aE,getValueByPointer:()=>aj,validate:()=>aC,validator:()=>aI}),(c=m||(m={})).assertEqual=e=>e,c.assertIs=function(e){},c.assertNever=function(e){throw Error()},c.arrayToEnum=e=>{let t={};for(let a of e)t[a]=a;return t},c.getValidEnumValues=e=>{let t=c.objectKeys(e).filter(t=>"number"!=typeof e[e[t]]),a={};for(let r of t)a[r]=e[r];return c.objectValues(a)},c.objectValues=e=>c.objectKeys(e).map(function(t){return e[t]}),c.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.push(a);return t},c.find=(e,t)=>{for(let a of e)if(t(a))return a},c.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&isFinite(e)&&Math.floor(e)===e,c.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},c.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t,(f||(f={})).mergeShapes=(e,t)=>({...e,...t});let O=m.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),k=e=>{switch(typeof e){case"undefined":return O.undefined;case"string":return O.string;case"number":return isNaN(e)?O.nan:O.number;case"boolean":return O.boolean;case"function":return O.function;case"bigint":return O.bigint;case"symbol":return O.symbol;case"object":if(Array.isArray(e))return O.array;if(null===e)return O.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return O.promise;if("undefined"!=typeof Map&&e instanceof Map)return O.map;if("undefined"!=typeof Set&&e instanceof Set)return O.set;if("undefined"!=typeof Date&&e instanceof Date)return O.date;return O.object;default:return O.unknown}},E=m.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class S extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},a={_errors:[]},r=e=>{for(let i of e.issues)if("invalid_union"===i.code)i.unionErrors.map(r);else if("invalid_return_type"===i.code)r(i.returnTypeError);else if("invalid_arguments"===i.code)r(i.argumentsError);else if(0===i.path.length)a._errors.push(t(i));else{let e=a,r=0;for(;r<i.path.length;){let a=i.path[r];r===i.path.length-1?(e[a]=e[a]||{_errors:[]},e[a]._errors.push(t(i))):e[a]=e[a]||{_errors:[]},e=e[a],r++}}};return r(this),a}static assert(e){if(!(e instanceof S))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,m.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},a=[];for(let r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):a.push(e(r));return{formErrors:a,fieldErrors:t}}get formErrors(){return this.flatten()}}S.create=e=>new S(e);let x=(e,t)=>{let a;switch(e.code){case E.invalid_type:a=e.received===O.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case E.invalid_literal:a=`Invalid literal value, expected ${JSON.stringify(e.expected,m.jsonStringifyReplacer)}`;break;case E.unrecognized_keys:a=`Unrecognized key(s) in object: ${m.joinValues(e.keys,", ")}`;break;case E.invalid_union:a="Invalid input";break;case E.invalid_union_discriminator:a=`Invalid discriminator value. Expected ${m.joinValues(e.options)}`;break;case E.invalid_enum_value:a=`Invalid enum value. Expected ${m.joinValues(e.options)}, received '${e.received}'`;break;case E.invalid_arguments:a="Invalid function arguments";break;case E.invalid_return_type:a="Invalid function return type";break;case E.invalid_date:a="Invalid date";break;case E.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(a=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(a=`${a} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?a=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?a=`Invalid input: must end with "${e.validation.endsWith}"`:m.assertNever(e.validation):a="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case E.too_small:a="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case E.too_big:a="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case E.custom:a="Invalid input";break;case E.invalid_intersection_types:a="Intersection results could not be merged";break;case E.not_multiple_of:a=`Number must be a multiple of ${e.multipleOf}`;break;case E.not_finite:a="Number must be finite";break;default:a=t.defaultError,m.assertNever(e)}return{message:a}},j=x;function T(){return j}let P=e=>{let{data:t,path:a,errorMaps:r,issueData:i}=e,n=[...a,...i.path||[]],s={...i,path:n};if(void 0!==i.message)return{...i,path:n,message:i.message};let o="";for(let e of r.filter(e=>!!e).slice().reverse())o=e(s,{data:t,defaultError:o}).message;return{...i,path:n,message:o}};function A(e,t){let a=T(),r=P({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,a,a===x?void 0:x].filter(e=>!!e)});e.common.issues.push(r)}class I{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let a=[];for(let r of t){if("aborted"===r.status)return C;"dirty"===r.status&&e.dirty(),a.push(r.value)}return{status:e.value,value:a}}static async mergeObjectAsync(e,t){let a=[];for(let e of t){let t=await e.key,r=await e.value;a.push({key:t,value:r})}return I.mergeObjectSync(e,a)}static mergeObjectSync(e,t){let a={};for(let r of t){let{key:t,value:i}=r;if("aborted"===t.status||"aborted"===i.status)return C;"dirty"===t.status&&e.dirty(),"dirty"===i.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==i.value||r.alwaysSet)&&(a[t.value]=i.value)}return{status:e.value,value:a}}}let C=Object.freeze({status:"aborted"}),$=e=>({status:"dirty",value:e}),N=e=>({status:"valid",value:e}),R=e=>"aborted"===e.status,M=e=>"dirty"===e.status,L=e=>"valid"===e.status,U=e=>"undefined"!=typeof Promise&&e instanceof Promise;function Z(e,t,a,r){if("a"===a&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===a?r:"a"===a?r.call(e):r?r.value:t.get(e)}function D(e,t,a,r,i){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?i.call(e,a):i?i.value=a:t.set(e,a),a}"function"==typeof SuppressedError&&SuppressedError,(h=g||(g={})).errToObj=e=>"string"==typeof e?{message:e}:e||{},h.toString=e=>"string"==typeof e?e:null==e?void 0:e.message;class F{constructor(e,t,a,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=a,this._key=r}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let H=(e,t)=>{if(L(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new S(e.common.issues);return this._error=t,this._error}}};function z(e){if(!e)return{};let{errorMap:t,invalid_type_error:a,required_error:r,description:i}=e;if(t&&(a||r))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:i}:{errorMap:(t,i)=>{var n,s;let{message:o}=e;return"invalid_enum_value"===t.code?{message:null!=o?o:i.defaultError}:void 0===i.data?{message:null!==(n=null!=o?o:r)&&void 0!==n?n:i.defaultError}:"invalid_type"!==t.code?{message:i.defaultError}:{message:null!==(s=null!=o?o:a)&&void 0!==s?s:i.defaultError}},description:i}}class B{get description(){return this._def.description}_getType(e){return k(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:k(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new I,ctx:{common:e.parent.common,data:e.data,parsedType:k(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(U(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let a=this.safeParse(e,t);if(a.success)return a.data;throw a.error}safeParse(e,t){var a;let r={common:{issues:[],async:null!==(a=null==t?void 0:t.async)&&void 0!==a&&a,contextualErrorMap:null==t?void 0:t.errorMap},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:k(e)},i=this._parseSync({data:e,path:r.path,parent:r});return H(r,i)}"~validate"(e){var t,a;let r={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:k(e)};if(!this["~standard"].async)try{let t=this._parseSync({data:e,path:[],parent:r});return L(t)?{value:t.value}:{issues:r.common.issues}}catch(e){(null===(a=null===(t=null==e?void 0:e.message)||void 0===t?void 0:t.toLowerCase())||void 0===a?void 0:a.includes("encountered"))&&(this["~standard"].async=!0),r.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:r}).then(e=>L(e)?{value:e.value}:{issues:r.common.issues})}async parseAsync(e,t){let a=await this.safeParseAsync(e,t);if(a.success)return a.data;throw a.error}async safeParseAsync(e,t){let a={common:{issues:[],contextualErrorMap:null==t?void 0:t.errorMap,async:!0},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:k(e)},r=this._parse({data:e,path:a.path,parent:a});return H(a,await (U(r)?r:Promise.resolve(r)))}refine(e,t){let a=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,r)=>{let i=e(t),n=()=>r.addIssue({code:E.custom,...a(t)});return"undefined"!=typeof Promise&&i instanceof Promise?i.then(e=>!!e||(n(),!1)):!!i||(n(),!1)})}refinement(e,t){return this._refinement((a,r)=>!!e(a)||(r.addIssue("function"==typeof t?t(a,r):t),!1))}_refinement(e){return new eU({schema:this,typeName:b.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return eZ.create(this,this._def)}nullable(){return eD.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return ew.create(this)}promise(){return eL.create(this,this._def)}or(e){return ek.create([this,e],this._def)}and(e){return ex.create(this,e,this._def)}transform(e){return new eU({...z(this._def),schema:this,typeName:b.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new eF({...z(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:b.ZodDefault})}brand(){return new eV({typeName:b.ZodBranded,type:this,...z(this._def)})}catch(e){return new eH({...z(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:b.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return eq.create(this,e)}readonly(){return eG.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let V=/^c[^\s-]{8,}$/i,q=/^[0-9a-z]+$/,G=/^[0-9A-HJKMNP-TV-Z]{26}$/i,J=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,W=/^[a-z0-9_-]{21}$/i,K=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Q=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Y=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,X=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ee=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,et=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,ea=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,er=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,ei=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,en="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",es=RegExp(`^${en}$`);function eo(e){let t="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`),t}function el(e){let t=`${en}T${eo(e)}`,a=[];return a.push(e.local?"Z?":"Z"),e.offset&&a.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${a.join("|")})`,RegExp(`^${t}$`)}class ed extends B{_parse(e){var t,a,i,n;let s;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==O.string){let t=this._getOrReturnCtx(e);return A(t,{code:E.invalid_type,expected:O.string,received:t.parsedType}),C}let o=new I;for(let l of this._def.checks)if("min"===l.kind)e.data.length<l.value&&(A(s=this._getOrReturnCtx(e,s),{code:E.too_small,minimum:l.value,type:"string",inclusive:!0,exact:!1,message:l.message}),o.dirty());else if("max"===l.kind)e.data.length>l.value&&(A(s=this._getOrReturnCtx(e,s),{code:E.too_big,maximum:l.value,type:"string",inclusive:!0,exact:!1,message:l.message}),o.dirty());else if("length"===l.kind){let t=e.data.length>l.value,a=e.data.length<l.value;(t||a)&&(s=this._getOrReturnCtx(e,s),t?A(s,{code:E.too_big,maximum:l.value,type:"string",inclusive:!0,exact:!0,message:l.message}):a&&A(s,{code:E.too_small,minimum:l.value,type:"string",inclusive:!0,exact:!0,message:l.message}),o.dirty())}else if("email"===l.kind)Y.test(e.data)||(A(s=this._getOrReturnCtx(e,s),{validation:"email",code:E.invalid_string,message:l.message}),o.dirty());else if("emoji"===l.kind)r||(r=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),r.test(e.data)||(A(s=this._getOrReturnCtx(e,s),{validation:"emoji",code:E.invalid_string,message:l.message}),o.dirty());else if("uuid"===l.kind)J.test(e.data)||(A(s=this._getOrReturnCtx(e,s),{validation:"uuid",code:E.invalid_string,message:l.message}),o.dirty());else if("nanoid"===l.kind)W.test(e.data)||(A(s=this._getOrReturnCtx(e,s),{validation:"nanoid",code:E.invalid_string,message:l.message}),o.dirty());else if("cuid"===l.kind)V.test(e.data)||(A(s=this._getOrReturnCtx(e,s),{validation:"cuid",code:E.invalid_string,message:l.message}),o.dirty());else if("cuid2"===l.kind)q.test(e.data)||(A(s=this._getOrReturnCtx(e,s),{validation:"cuid2",code:E.invalid_string,message:l.message}),o.dirty());else if("ulid"===l.kind)G.test(e.data)||(A(s=this._getOrReturnCtx(e,s),{validation:"ulid",code:E.invalid_string,message:l.message}),o.dirty());else if("url"===l.kind)try{new URL(e.data)}catch(t){A(s=this._getOrReturnCtx(e,s),{validation:"url",code:E.invalid_string,message:l.message}),o.dirty()}else"regex"===l.kind?(l.regex.lastIndex=0,l.regex.test(e.data)||(A(s=this._getOrReturnCtx(e,s),{validation:"regex",code:E.invalid_string,message:l.message}),o.dirty())):"trim"===l.kind?e.data=e.data.trim():"includes"===l.kind?e.data.includes(l.value,l.position)||(A(s=this._getOrReturnCtx(e,s),{code:E.invalid_string,validation:{includes:l.value,position:l.position},message:l.message}),o.dirty()):"toLowerCase"===l.kind?e.data=e.data.toLowerCase():"toUpperCase"===l.kind?e.data=e.data.toUpperCase():"startsWith"===l.kind?e.data.startsWith(l.value)||(A(s=this._getOrReturnCtx(e,s),{code:E.invalid_string,validation:{startsWith:l.value},message:l.message}),o.dirty()):"endsWith"===l.kind?e.data.endsWith(l.value)||(A(s=this._getOrReturnCtx(e,s),{code:E.invalid_string,validation:{endsWith:l.value},message:l.message}),o.dirty()):"datetime"===l.kind?el(l).test(e.data)||(A(s=this._getOrReturnCtx(e,s),{code:E.invalid_string,validation:"datetime",message:l.message}),o.dirty()):"date"===l.kind?es.test(e.data)||(A(s=this._getOrReturnCtx(e,s),{code:E.invalid_string,validation:"date",message:l.message}),o.dirty()):"time"===l.kind?RegExp(`^${eo(l)}$`).test(e.data)||(A(s=this._getOrReturnCtx(e,s),{code:E.invalid_string,validation:"time",message:l.message}),o.dirty()):"duration"===l.kind?Q.test(e.data)||(A(s=this._getOrReturnCtx(e,s),{validation:"duration",code:E.invalid_string,message:l.message}),o.dirty()):"ip"===l.kind?(t=e.data,!(("v4"===(a=l.version)||!a)&&X.test(t)||("v6"===a||!a)&&et.test(t))&&1&&(A(s=this._getOrReturnCtx(e,s),{validation:"ip",code:E.invalid_string,message:l.message}),o.dirty())):"jwt"===l.kind?!function(e,t){if(!K.test(e))return!1;try{let[a]=e.split("."),r=a.replace(/-/g,"+").replace(/_/g,"/").padEnd(a.length+(4-a.length%4)%4,"="),i=JSON.parse(atob(r));if("object"!=typeof i||null===i||!i.typ||!i.alg||t&&i.alg!==t)return!1;return!0}catch(e){return!1}}(e.data,l.alg)&&(A(s=this._getOrReturnCtx(e,s),{validation:"jwt",code:E.invalid_string,message:l.message}),o.dirty()):"cidr"===l.kind?(i=e.data,!(("v4"===(n=l.version)||!n)&&ee.test(i)||("v6"===n||!n)&&ea.test(i))&&1&&(A(s=this._getOrReturnCtx(e,s),{validation:"cidr",code:E.invalid_string,message:l.message}),o.dirty())):"base64"===l.kind?er.test(e.data)||(A(s=this._getOrReturnCtx(e,s),{validation:"base64",code:E.invalid_string,message:l.message}),o.dirty()):"base64url"===l.kind?ei.test(e.data)||(A(s=this._getOrReturnCtx(e,s),{validation:"base64url",code:E.invalid_string,message:l.message}),o.dirty()):m.assertNever(l);return{status:o.value,value:e.data}}_regex(e,t,a){return this.refinement(t=>e.test(t),{validation:t,code:E.invalid_string,...g.errToObj(a)})}_addCheck(e){return new ed({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...g.errToObj(e)})}url(e){return this._addCheck({kind:"url",...g.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...g.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...g.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...g.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...g.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...g.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...g.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...g.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...g.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...g.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...g.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...g.errToObj(e)})}datetime(e){var t,a;return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===(null==e?void 0:e.precision)?null:null==e?void 0:e.precision,offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t&&t,local:null!==(a=null==e?void 0:e.local)&&void 0!==a&&a,...g.errToObj(null==e?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===(null==e?void 0:e.precision)?null:null==e?void 0:e.precision,...g.errToObj(null==e?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...g.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...g.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:null==t?void 0:t.position,...g.errToObj(null==t?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...g.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...g.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...g.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...g.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...g.errToObj(t)})}nonempty(e){return this.min(1,g.errToObj(e))}trim(){return new ed({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new ed({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new ed({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}ed.create=e=>{var t;return new ed({checks:[],typeName:b.ZodString,coerce:null!==(t=null==e?void 0:e.coerce)&&void 0!==t&&t,...z(e)})};class eu extends B{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==O.number){let t=this._getOrReturnCtx(e);return A(t,{code:E.invalid_type,expected:O.number,received:t.parsedType}),C}let a=new I;for(let r of this._def.checks)"int"===r.kind?m.isInteger(e.data)||(A(t=this._getOrReturnCtx(e,t),{code:E.invalid_type,expected:"integer",received:"float",message:r.message}),a.dirty()):"min"===r.kind?(r.inclusive?e.data<r.value:e.data<=r.value)&&(A(t=this._getOrReturnCtx(e,t),{code:E.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),a.dirty()):"max"===r.kind?(r.inclusive?e.data>r.value:e.data>=r.value)&&(A(t=this._getOrReturnCtx(e,t),{code:E.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),a.dirty()):"multipleOf"===r.kind?0!==function(e,t){let a=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,i=a>r?a:r;return parseInt(e.toFixed(i).replace(".",""))%parseInt(t.toFixed(i).replace(".",""))/Math.pow(10,i)}(e.data,r.value)&&(A(t=this._getOrReturnCtx(e,t),{code:E.not_multiple_of,multipleOf:r.value,message:r.message}),a.dirty()):"finite"===r.kind?Number.isFinite(e.data)||(A(t=this._getOrReturnCtx(e,t),{code:E.not_finite,message:r.message}),a.dirty()):m.assertNever(r);return{status:a.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,g.toString(t))}gt(e,t){return this.setLimit("min",e,!1,g.toString(t))}lte(e,t){return this.setLimit("max",e,!0,g.toString(t))}lt(e,t){return this.setLimit("max",e,!1,g.toString(t))}setLimit(e,t,a,r){return new eu({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:a,message:g.toString(r)}]})}_addCheck(e){return new eu({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:g.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:g.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:g.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:g.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:g.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:g.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:g.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:g.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:g.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&m.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let a of this._def.checks)if("finite"===a.kind||"int"===a.kind||"multipleOf"===a.kind)return!0;else"min"===a.kind?(null===t||a.value>t)&&(t=a.value):"max"===a.kind&&(null===e||a.value<e)&&(e=a.value);return Number.isFinite(t)&&Number.isFinite(e)}}eu.create=e=>new eu({checks:[],typeName:b.ZodNumber,coerce:(null==e?void 0:e.coerce)||!1,...z(e)});class ec extends B{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch(t){return this._getInvalidInput(e)}if(this._getType(e)!==O.bigint)return this._getInvalidInput(e);let a=new I;for(let r of this._def.checks)"min"===r.kind?(r.inclusive?e.data<r.value:e.data<=r.value)&&(A(t=this._getOrReturnCtx(e,t),{code:E.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),a.dirty()):"max"===r.kind?(r.inclusive?e.data>r.value:e.data>=r.value)&&(A(t=this._getOrReturnCtx(e,t),{code:E.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),a.dirty()):"multipleOf"===r.kind?e.data%r.value!==BigInt(0)&&(A(t=this._getOrReturnCtx(e,t),{code:E.not_multiple_of,multipleOf:r.value,message:r.message}),a.dirty()):m.assertNever(r);return{status:a.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return A(t,{code:E.invalid_type,expected:O.bigint,received:t.parsedType}),C}gte(e,t){return this.setLimit("min",e,!0,g.toString(t))}gt(e,t){return this.setLimit("min",e,!1,g.toString(t))}lte(e,t){return this.setLimit("max",e,!0,g.toString(t))}lt(e,t){return this.setLimit("max",e,!1,g.toString(t))}setLimit(e,t,a,r){return new ec({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:a,message:g.toString(r)}]})}_addCheck(e){return new ec({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:g.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:g.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:g.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:g.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:g.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}ec.create=e=>{var t;return new ec({checks:[],typeName:b.ZodBigInt,coerce:null!==(t=null==e?void 0:e.coerce)&&void 0!==t&&t,...z(e)})};class eh extends B{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==O.boolean){let t=this._getOrReturnCtx(e);return A(t,{code:E.invalid_type,expected:O.boolean,received:t.parsedType}),C}return N(e.data)}}eh.create=e=>new eh({typeName:b.ZodBoolean,coerce:(null==e?void 0:e.coerce)||!1,...z(e)});class ep extends B{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==O.date){let t=this._getOrReturnCtx(e);return A(t,{code:E.invalid_type,expected:O.date,received:t.parsedType}),C}if(isNaN(e.data.getTime()))return A(this._getOrReturnCtx(e),{code:E.invalid_date}),C;let a=new I;for(let r of this._def.checks)"min"===r.kind?e.data.getTime()<r.value&&(A(t=this._getOrReturnCtx(e,t),{code:E.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),a.dirty()):"max"===r.kind?e.data.getTime()>r.value&&(A(t=this._getOrReturnCtx(e,t),{code:E.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),a.dirty()):m.assertNever(r);return{status:a.value,value:new Date(e.data.getTime())}}_addCheck(e){return new ep({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:g.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:g.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}ep.create=e=>new ep({checks:[],coerce:(null==e?void 0:e.coerce)||!1,typeName:b.ZodDate,...z(e)});class em extends B{_parse(e){if(this._getType(e)!==O.symbol){let t=this._getOrReturnCtx(e);return A(t,{code:E.invalid_type,expected:O.symbol,received:t.parsedType}),C}return N(e.data)}}em.create=e=>new em({typeName:b.ZodSymbol,...z(e)});class ef extends B{_parse(e){if(this._getType(e)!==O.undefined){let t=this._getOrReturnCtx(e);return A(t,{code:E.invalid_type,expected:O.undefined,received:t.parsedType}),C}return N(e.data)}}ef.create=e=>new ef({typeName:b.ZodUndefined,...z(e)});class eg extends B{_parse(e){if(this._getType(e)!==O.null){let t=this._getOrReturnCtx(e);return A(t,{code:E.invalid_type,expected:O.null,received:t.parsedType}),C}return N(e.data)}}eg.create=e=>new eg({typeName:b.ZodNull,...z(e)});class ey extends B{constructor(){super(...arguments),this._any=!0}_parse(e){return N(e.data)}}ey.create=e=>new ey({typeName:b.ZodAny,...z(e)});class e_ extends B{constructor(){super(...arguments),this._unknown=!0}_parse(e){return N(e.data)}}e_.create=e=>new e_({typeName:b.ZodUnknown,...z(e)});class eb extends B{_parse(e){let t=this._getOrReturnCtx(e);return A(t,{code:E.invalid_type,expected:O.never,received:t.parsedType}),C}}eb.create=e=>new eb({typeName:b.ZodNever,...z(e)});class ev extends B{_parse(e){if(this._getType(e)!==O.undefined){let t=this._getOrReturnCtx(e);return A(t,{code:E.invalid_type,expected:O.void,received:t.parsedType}),C}return N(e.data)}}ev.create=e=>new ev({typeName:b.ZodVoid,...z(e)});class ew extends B{_parse(e){let{ctx:t,status:a}=this._processInputParams(e),r=this._def;if(t.parsedType!==O.array)return A(t,{code:E.invalid_type,expected:O.array,received:t.parsedType}),C;if(null!==r.exactLength){let e=t.data.length>r.exactLength.value,i=t.data.length<r.exactLength.value;(e||i)&&(A(t,{code:e?E.too_big:E.too_small,minimum:i?r.exactLength.value:void 0,maximum:e?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),a.dirty())}if(null!==r.minLength&&t.data.length<r.minLength.value&&(A(t,{code:E.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),a.dirty()),null!==r.maxLength&&t.data.length>r.maxLength.value&&(A(t,{code:E.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),a.dirty()),t.common.async)return Promise.all([...t.data].map((e,a)=>r.type._parseAsync(new F(t,e,t.path,a)))).then(e=>I.mergeArray(a,e));let i=[...t.data].map((e,a)=>r.type._parseSync(new F(t,e,t.path,a)));return I.mergeArray(a,i)}get element(){return this._def.type}min(e,t){return new ew({...this._def,minLength:{value:e,message:g.toString(t)}})}max(e,t){return new ew({...this._def,maxLength:{value:e,message:g.toString(t)}})}length(e,t){return new ew({...this._def,exactLength:{value:e,message:g.toString(t)}})}nonempty(e){return this.min(1,e)}}ew.create=(e,t)=>new ew({type:e,minLength:null,maxLength:null,exactLength:null,typeName:b.ZodArray,...z(t)});class eO extends B{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=m.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==O.object){let t=this._getOrReturnCtx(e);return A(t,{code:E.invalid_type,expected:O.object,received:t.parsedType}),C}let{status:t,ctx:a}=this._processInputParams(e),{shape:r,keys:i}=this._getCached(),n=[];if(!(this._def.catchall instanceof eb&&"strip"===this._def.unknownKeys))for(let e in a.data)i.includes(e)||n.push(e);let s=[];for(let e of i){let t=r[e],i=a.data[e];s.push({key:{status:"valid",value:e},value:t._parse(new F(a,i,a.path,e)),alwaysSet:e in a.data})}if(this._def.catchall instanceof eb){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of n)s.push({key:{status:"valid",value:e},value:{status:"valid",value:a.data[e]}});else if("strict"===e)n.length>0&&(A(a,{code:E.unrecognized_keys,keys:n}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of n){let r=a.data[t];s.push({key:{status:"valid",value:t},value:e._parse(new F(a,r,a.path,t)),alwaysSet:t in a.data})}}return a.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of s){let a=await t.key,r=await t.value;e.push({key:a,value:r,alwaysSet:t.alwaysSet})}return e}).then(e=>I.mergeObjectSync(t,e)):I.mergeObjectSync(t,s)}get shape(){return this._def.shape()}strict(e){return g.errToObj,new eO({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,a)=>{var r,i,n,s;let o=null!==(n=null===(i=(r=this._def).errorMap)||void 0===i?void 0:i.call(r,t,a).message)&&void 0!==n?n:a.defaultError;return"unrecognized_keys"===t.code?{message:null!==(s=g.errToObj(e).message)&&void 0!==s?s:o}:{message:o}}}:{}})}strip(){return new eO({...this._def,unknownKeys:"strip"})}passthrough(){return new eO({...this._def,unknownKeys:"passthrough"})}extend(e){return new eO({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new eO({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:b.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new eO({...this._def,catchall:e})}pick(e){let t={};return m.objectKeys(e).forEach(a=>{e[a]&&this.shape[a]&&(t[a]=this.shape[a])}),new eO({...this._def,shape:()=>t})}omit(e){let t={};return m.objectKeys(this.shape).forEach(a=>{e[a]||(t[a]=this.shape[a])}),new eO({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof eO){let a={};for(let r in t.shape){let i=t.shape[r];a[r]=eZ.create(e(i))}return new eO({...t._def,shape:()=>a})}if(t instanceof ew)return new ew({...t._def,type:e(t.element)});if(t instanceof eZ)return eZ.create(e(t.unwrap()));if(t instanceof eD)return eD.create(e(t.unwrap()));if(t instanceof ej)return ej.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};return m.objectKeys(this.shape).forEach(a=>{let r=this.shape[a];e&&!e[a]?t[a]=r:t[a]=r.optional()}),new eO({...this._def,shape:()=>t})}required(e){let t={};return m.objectKeys(this.shape).forEach(a=>{if(e&&!e[a])t[a]=this.shape[a];else{let e=this.shape[a];for(;e instanceof eZ;)e=e._def.innerType;t[a]=e}}),new eO({...this._def,shape:()=>t})}keyof(){return eN(m.objectKeys(this.shape))}}eO.create=(e,t)=>new eO({shape:()=>e,unknownKeys:"strip",catchall:eb.create(),typeName:b.ZodObject,...z(t)}),eO.strictCreate=(e,t)=>new eO({shape:()=>e,unknownKeys:"strict",catchall:eb.create(),typeName:b.ZodObject,...z(t)}),eO.lazycreate=(e,t)=>new eO({shape:e,unknownKeys:"strip",catchall:eb.create(),typeName:b.ZodObject,...z(t)});class ek extends B{_parse(e){let{ctx:t}=this._processInputParams(e),a=this._def.options;if(t.common.async)return Promise.all(a.map(async e=>{let a={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:a}),ctx:a}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let a of e)if("dirty"===a.result.status)return t.common.issues.push(...a.ctx.common.issues),a.result;let a=e.map(e=>new S(e.ctx.common.issues));return A(t,{code:E.invalid_union,unionErrors:a}),C});{let e,r=[];for(let i of a){let a={...t,common:{...t.common,issues:[]},parent:null},n=i._parseSync({data:t.data,path:t.path,parent:a});if("valid"===n.status)return n;"dirty"!==n.status||e||(e={result:n,ctx:a}),a.common.issues.length&&r.push(a.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let i=r.map(e=>new S(e));return A(t,{code:E.invalid_union,unionErrors:i}),C}}get options(){return this._def.options}}ek.create=(e,t)=>new ek({options:e,typeName:b.ZodUnion,...z(t)});let eE=e=>{if(e instanceof eC)return eE(e.schema);if(e instanceof eU)return eE(e.innerType());if(e instanceof e$)return[e.value];if(e instanceof eR)return e.options;if(e instanceof eM)return m.objectValues(e.enum);else if(e instanceof eF)return eE(e._def.innerType);else if(e instanceof ef)return[void 0];else if(e instanceof eg)return[null];else if(e instanceof eZ)return[void 0,...eE(e.unwrap())];else if(e instanceof eD)return[null,...eE(e.unwrap())];else if(e instanceof eV)return eE(e.unwrap());else if(e instanceof eG)return eE(e.unwrap());else if(e instanceof eH)return eE(e._def.innerType);else return[]};class eS extends B{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==O.object)return A(t,{code:E.invalid_type,expected:O.object,received:t.parsedType}),C;let a=this.discriminator,r=t.data[a],i=this.optionsMap.get(r);return i?t.common.async?i._parseAsync({data:t.data,path:t.path,parent:t}):i._parseSync({data:t.data,path:t.path,parent:t}):(A(t,{code:E.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[a]}),C)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,a){let r=new Map;for(let a of t){let t=eE(a.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let i of t){if(r.has(i))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(i)}`);r.set(i,a)}}return new eS({typeName:b.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...z(a)})}}class ex extends B{_parse(e){let{status:t,ctx:a}=this._processInputParams(e),r=(e,r)=>{if(R(e)||R(r))return C;let i=function e(t,a){let r=k(t),i=k(a);if(t===a)return{valid:!0,data:t};if(r===O.object&&i===O.object){let r=m.objectKeys(a),i=m.objectKeys(t).filter(e=>-1!==r.indexOf(e)),n={...t,...a};for(let r of i){let i=e(t[r],a[r]);if(!i.valid)return{valid:!1};n[r]=i.data}return{valid:!0,data:n}}if(r===O.array&&i===O.array){if(t.length!==a.length)return{valid:!1};let r=[];for(let i=0;i<t.length;i++){let n=e(t[i],a[i]);if(!n.valid)return{valid:!1};r.push(n.data)}return{valid:!0,data:r}}if(r===O.date&&i===O.date&&+t==+a)return{valid:!0,data:t};return{valid:!1}}(e.value,r.value);return i.valid?((M(e)||M(r))&&t.dirty(),{status:t.value,value:i.data}):(A(a,{code:E.invalid_intersection_types}),C)};return a.common.async?Promise.all([this._def.left._parseAsync({data:a.data,path:a.path,parent:a}),this._def.right._parseAsync({data:a.data,path:a.path,parent:a})]).then(([e,t])=>r(e,t)):r(this._def.left._parseSync({data:a.data,path:a.path,parent:a}),this._def.right._parseSync({data:a.data,path:a.path,parent:a}))}}ex.create=(e,t,a)=>new ex({left:e,right:t,typeName:b.ZodIntersection,...z(a)});class ej extends B{_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==O.array)return A(a,{code:E.invalid_type,expected:O.array,received:a.parsedType}),C;if(a.data.length<this._def.items.length)return A(a,{code:E.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),C;!this._def.rest&&a.data.length>this._def.items.length&&(A(a,{code:E.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let r=[...a.data].map((e,t)=>{let r=this._def.items[t]||this._def.rest;return r?r._parse(new F(a,e,a.path,t)):null}).filter(e=>!!e);return a.common.async?Promise.all(r).then(e=>I.mergeArray(t,e)):I.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new ej({...this._def,rest:e})}}ej.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new ej({items:e,typeName:b.ZodTuple,rest:null,...z(t)})};class eT extends B{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==O.object)return A(a,{code:E.invalid_type,expected:O.object,received:a.parsedType}),C;let r=[],i=this._def.keyType,n=this._def.valueType;for(let e in a.data)r.push({key:i._parse(new F(a,e,a.path,e)),value:n._parse(new F(a,a.data[e],a.path,e)),alwaysSet:e in a.data});return a.common.async?I.mergeObjectAsync(t,r):I.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,a){return new eT(t instanceof B?{keyType:e,valueType:t,typeName:b.ZodRecord,...z(a)}:{keyType:ed.create(),valueType:e,typeName:b.ZodRecord,...z(t)})}}class eP extends B{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==O.map)return A(a,{code:E.invalid_type,expected:O.map,received:a.parsedType}),C;let r=this._def.keyType,i=this._def.valueType,n=[...a.data.entries()].map(([e,t],n)=>({key:r._parse(new F(a,e,a.path,[n,"key"])),value:i._parse(new F(a,t,a.path,[n,"value"]))}));if(a.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let a of n){let r=await a.key,i=await a.value;if("aborted"===r.status||"aborted"===i.status)return C;("dirty"===r.status||"dirty"===i.status)&&t.dirty(),e.set(r.value,i.value)}return{status:t.value,value:e}})}{let e=new Map;for(let a of n){let r=a.key,i=a.value;if("aborted"===r.status||"aborted"===i.status)return C;("dirty"===r.status||"dirty"===i.status)&&t.dirty(),e.set(r.value,i.value)}return{status:t.value,value:e}}}}eP.create=(e,t,a)=>new eP({valueType:t,keyType:e,typeName:b.ZodMap,...z(a)});class eA extends B{_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==O.set)return A(a,{code:E.invalid_type,expected:O.set,received:a.parsedType}),C;let r=this._def;null!==r.minSize&&a.data.size<r.minSize.value&&(A(a,{code:E.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),null!==r.maxSize&&a.data.size>r.maxSize.value&&(A(a,{code:E.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());let i=this._def.valueType;function n(e){let a=new Set;for(let r of e){if("aborted"===r.status)return C;"dirty"===r.status&&t.dirty(),a.add(r.value)}return{status:t.value,value:a}}let s=[...a.data.values()].map((e,t)=>i._parse(new F(a,e,a.path,t)));return a.common.async?Promise.all(s).then(e=>n(e)):n(s)}min(e,t){return new eA({...this._def,minSize:{value:e,message:g.toString(t)}})}max(e,t){return new eA({...this._def,maxSize:{value:e,message:g.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}eA.create=(e,t)=>new eA({valueType:e,minSize:null,maxSize:null,typeName:b.ZodSet,...z(t)});class eI extends B{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==O.function)return A(t,{code:E.invalid_type,expected:O.function,received:t.parsedType}),C;function a(e,a){return P({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,T(),x].filter(e=>!!e),issueData:{code:E.invalid_arguments,argumentsError:a}})}function r(e,a){return P({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,T(),x].filter(e=>!!e),issueData:{code:E.invalid_return_type,returnTypeError:a}})}let i={errorMap:t.common.contextualErrorMap},n=t.data;if(this._def.returns instanceof eL){let e=this;return N(async function(...t){let s=new S([]),o=await e._def.args.parseAsync(t,i).catch(e=>{throw s.addIssue(a(t,e)),s}),l=await Reflect.apply(n,this,o);return await e._def.returns._def.type.parseAsync(l,i).catch(e=>{throw s.addIssue(r(l,e)),s})})}{let e=this;return N(function(...t){let s=e._def.args.safeParse(t,i);if(!s.success)throw new S([a(t,s.error)]);let o=Reflect.apply(n,this,s.data),l=e._def.returns.safeParse(o,i);if(!l.success)throw new S([r(o,l.error)]);return l.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new eI({...this._def,args:ej.create(e).rest(e_.create())})}returns(e){return new eI({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,a){return new eI({args:e||ej.create([]).rest(e_.create()),returns:t||e_.create(),typeName:b.ZodFunction,...z(a)})}}class eC extends B{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}eC.create=(e,t)=>new eC({getter:e,typeName:b.ZodLazy,...z(t)});class e$ extends B{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return A(t,{received:t.data,code:E.invalid_literal,expected:this._def.value}),C}return{status:"valid",value:e.data}}get value(){return this._def.value}}function eN(e,t){return new eR({values:e,typeName:b.ZodEnum,...z(t)})}e$.create=(e,t)=>new e$({value:e,typeName:b.ZodLiteral,...z(t)});class eR extends B{constructor(){super(...arguments),y.set(this,void 0)}_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),a=this._def.values;return A(t,{expected:m.joinValues(a),received:t.parsedType,code:E.invalid_type}),C}if(Z(this,y,"f")||D(this,y,new Set(this._def.values),"f"),!Z(this,y,"f").has(e.data)){let t=this._getOrReturnCtx(e),a=this._def.values;return A(t,{received:t.data,code:E.invalid_enum_value,options:a}),C}return N(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return eR.create(e,{...this._def,...t})}exclude(e,t=this._def){return eR.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}y=new WeakMap,eR.create=eN;class eM extends B{constructor(){super(...arguments),_.set(this,void 0)}_parse(e){let t=m.getValidEnumValues(this._def.values),a=this._getOrReturnCtx(e);if(a.parsedType!==O.string&&a.parsedType!==O.number){let e=m.objectValues(t);return A(a,{expected:m.joinValues(e),received:a.parsedType,code:E.invalid_type}),C}if(Z(this,_,"f")||D(this,_,new Set(m.getValidEnumValues(this._def.values)),"f"),!Z(this,_,"f").has(e.data)){let e=m.objectValues(t);return A(a,{received:a.data,code:E.invalid_enum_value,options:e}),C}return N(e.data)}get enum(){return this._def.values}}_=new WeakMap,eM.create=(e,t)=>new eM({values:e,typeName:b.ZodNativeEnum,...z(t)});class eL extends B{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==O.promise&&!1===t.common.async?(A(t,{code:E.invalid_type,expected:O.promise,received:t.parsedType}),C):N((t.parsedType===O.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}eL.create=(e,t)=>new eL({type:e,typeName:b.ZodPromise,...z(t)});class eU extends B{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===b.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:a}=this._processInputParams(e),r=this._def.effect||null,i={addIssue:e=>{A(a,e),e.fatal?t.abort():t.dirty()},get path(){return a.path}};if(i.addIssue=i.addIssue.bind(i),"preprocess"===r.type){let e=r.transform(a.data,i);if(a.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return C;let r=await this._def.schema._parseAsync({data:e,path:a.path,parent:a});return"aborted"===r.status?C:"dirty"===r.status||"dirty"===t.value?$(r.value):r});{if("aborted"===t.value)return C;let r=this._def.schema._parseSync({data:e,path:a.path,parent:a});return"aborted"===r.status?C:"dirty"===r.status||"dirty"===t.value?$(r.value):r}}if("refinement"===r.type){let e=e=>{let t=r.refinement(e,i);if(a.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==a.common.async)return this._def.schema._parseAsync({data:a.data,path:a.path,parent:a}).then(a=>"aborted"===a.status?C:("dirty"===a.status&&t.dirty(),e(a.value).then(()=>({status:t.value,value:a.value}))));{let r=this._def.schema._parseSync({data:a.data,path:a.path,parent:a});return"aborted"===r.status?C:("dirty"===r.status&&t.dirty(),e(r.value),{status:t.value,value:r.value})}}if("transform"===r.type)if(!1!==a.common.async)return this._def.schema._parseAsync({data:a.data,path:a.path,parent:a}).then(e=>L(e)?Promise.resolve(r.transform(e.value,i)).then(e=>({status:t.value,value:e})):e);else{let e=this._def.schema._parseSync({data:a.data,path:a.path,parent:a});if(!L(e))return e;let n=r.transform(e.value,i);if(n instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:n}}m.assertNever(r)}}eU.create=(e,t,a)=>new eU({schema:e,typeName:b.ZodEffects,effect:t,...z(a)}),eU.createWithPreprocess=(e,t,a)=>new eU({schema:t,effect:{type:"preprocess",transform:e},typeName:b.ZodEffects,...z(a)});class eZ extends B{_parse(e){return this._getType(e)===O.undefined?N(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eZ.create=(e,t)=>new eZ({innerType:e,typeName:b.ZodOptional,...z(t)});class eD extends B{_parse(e){return this._getType(e)===O.null?N(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eD.create=(e,t)=>new eD({innerType:e,typeName:b.ZodNullable,...z(t)});class eF extends B{_parse(e){let{ctx:t}=this._processInputParams(e),a=t.data;return t.parsedType===O.undefined&&(a=this._def.defaultValue()),this._def.innerType._parse({data:a,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}eF.create=(e,t)=>new eF({innerType:e,typeName:b.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...z(t)});class eH extends B{_parse(e){let{ctx:t}=this._processInputParams(e),a={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:a.data,path:a.path,parent:{...a}});return U(r)?r.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new S(a.common.issues)},input:a.data})})):{status:"valid",value:"valid"===r.status?r.value:this._def.catchValue({get error(){return new S(a.common.issues)},input:a.data})}}removeCatch(){return this._def.innerType}}eH.create=(e,t)=>new eH({innerType:e,typeName:b.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...z(t)});class ez extends B{_parse(e){if(this._getType(e)!==O.nan){let t=this._getOrReturnCtx(e);return A(t,{code:E.invalid_type,expected:O.nan,received:t.parsedType}),C}return{status:"valid",value:e.data}}}ez.create=e=>new ez({typeName:b.ZodNaN,...z(e)});let eB=Symbol("zod_brand");class eV extends B{_parse(e){let{ctx:t}=this._processInputParams(e),a=t.data;return this._def.type._parse({data:a,path:t.path,parent:t})}unwrap(){return this._def.type}}class eq extends B{_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?C:"dirty"===e.status?(t.dirty(),$(e.value)):this._def.out._parseAsync({data:e.value,path:a.path,parent:a})})();{let e=this._def.in._parseSync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?C:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:a.path,parent:a})}}static create(e,t){return new eq({in:e,out:t,typeName:b.ZodPipeline})}}class eG extends B{_parse(e){let t=this._def.innerType._parse(e),a=e=>(L(e)&&(e.value=Object.freeze(e.value)),e);return U(t)?t.then(e=>a(e)):a(t)}unwrap(){return this._def.innerType}}function eJ(e,t){let a="function"==typeof e?e(t):"string"==typeof e?{message:e}:e;return"string"==typeof a?{message:a}:a}function eW(e,t={},a){return e?ey.create().superRefine((r,i)=>{var n,s;let o=e(r);if(o instanceof Promise)return o.then(e=>{var n,s;if(!e){let e=eJ(t,r),o=null===(s=null!==(n=e.fatal)&&void 0!==n?n:a)||void 0===s||s;i.addIssue({code:"custom",...e,fatal:o})}});if(!o){let e=eJ(t,r),o=null===(s=null!==(n=e.fatal)&&void 0!==n?n:a)||void 0===s||s;i.addIssue({code:"custom",...e,fatal:o})}}):ey.create()}eG.create=(e,t)=>new eG({innerType:e,typeName:b.ZodReadonly,...z(t)});let eK={object:eO.lazycreate};(p=b||(b={})).ZodString="ZodString",p.ZodNumber="ZodNumber",p.ZodNaN="ZodNaN",p.ZodBigInt="ZodBigInt",p.ZodBoolean="ZodBoolean",p.ZodDate="ZodDate",p.ZodSymbol="ZodSymbol",p.ZodUndefined="ZodUndefined",p.ZodNull="ZodNull",p.ZodAny="ZodAny",p.ZodUnknown="ZodUnknown",p.ZodNever="ZodNever",p.ZodVoid="ZodVoid",p.ZodArray="ZodArray",p.ZodObject="ZodObject",p.ZodUnion="ZodUnion",p.ZodDiscriminatedUnion="ZodDiscriminatedUnion",p.ZodIntersection="ZodIntersection",p.ZodTuple="ZodTuple",p.ZodRecord="ZodRecord",p.ZodMap="ZodMap",p.ZodSet="ZodSet",p.ZodFunction="ZodFunction",p.ZodLazy="ZodLazy",p.ZodLiteral="ZodLiteral",p.ZodEnum="ZodEnum",p.ZodEffects="ZodEffects",p.ZodNativeEnum="ZodNativeEnum",p.ZodOptional="ZodOptional",p.ZodNullable="ZodNullable",p.ZodDefault="ZodDefault",p.ZodCatch="ZodCatch",p.ZodPromise="ZodPromise",p.ZodBranded="ZodBranded",p.ZodPipeline="ZodPipeline",p.ZodReadonly="ZodReadonly";let eQ=ed.create,eY=eu.create,eX=ez.create,e0=ec.create,e1=eh.create,e9=ep.create,e4=em.create,e2=ef.create,e5=eg.create,e6=ey.create,e3=e_.create,e7=eb.create,e8=ev.create,te=ew.create,tt=eO.create,ta=eO.strictCreate,tr=ek.create,ti=eS.create,tn=ex.create,ts=ej.create,to=eT.create,tl=eP.create,td=eA.create,tu=eI.create,tc=eC.create,th=e$.create,tp=eR.create,tm=eM.create,tf=eL.create,tg=eU.create,ty=eZ.create,t_=eD.create,tb=eU.createWithPreprocess,tv=eq.create;var tw=Object.freeze({__proto__:null,defaultErrorMap:x,setErrorMap:function(e){j=e},getErrorMap:T,makeIssue:P,EMPTY_PATH:[],addIssueToContext:A,ParseStatus:I,INVALID:C,DIRTY:$,OK:N,isAborted:R,isDirty:M,isValid:L,isAsync:U,get util(){return m},get objectUtil(){return f},ZodParsedType:O,getParsedType:k,ZodType:B,datetimeRegex:el,ZodString:ed,ZodNumber:eu,ZodBigInt:ec,ZodBoolean:eh,ZodDate:ep,ZodSymbol:em,ZodUndefined:ef,ZodNull:eg,ZodAny:ey,ZodUnknown:e_,ZodNever:eb,ZodVoid:ev,ZodArray:ew,ZodObject:eO,ZodUnion:ek,ZodDiscriminatedUnion:eS,ZodIntersection:ex,ZodTuple:ej,ZodRecord:eT,ZodMap:eP,ZodSet:eA,ZodFunction:eI,ZodLazy:eC,ZodLiteral:e$,ZodEnum:eR,ZodNativeEnum:eM,ZodPromise:eL,ZodEffects:eU,ZodTransformer:eU,ZodOptional:eZ,ZodNullable:eD,ZodDefault:eF,ZodCatch:eH,ZodNaN:ez,BRAND:eB,ZodBranded:eV,ZodPipeline:eq,ZodReadonly:eG,custom:eW,Schema:B,ZodSchema:B,late:eK,get ZodFirstPartyTypeKind(){return b},coerce:{string:e=>ed.create({...e,coerce:!0}),number:e=>eu.create({...e,coerce:!0}),boolean:e=>eh.create({...e,coerce:!0}),bigint:e=>ec.create({...e,coerce:!0}),date:e=>ep.create({...e,coerce:!0})},any:e6,array:te,bigint:e0,boolean:e1,date:e9,discriminatedUnion:ti,effect:tg,enum:tp,function:tu,instanceof:(e,t={message:`Input not instance of ${e.name}`})=>eW(t=>t instanceof e,t),intersection:tn,lazy:tc,literal:th,map:tl,nan:eX,nativeEnum:tm,never:e7,null:e5,nullable:t_,number:eY,object:tt,oboolean:()=>e1().optional(),onumber:()=>eY().optional(),optional:ty,ostring:()=>eQ().optional(),pipeline:tv,preprocess:tb,promise:tf,record:to,set:td,strictObject:ta,string:eQ,symbol:e4,transformer:tg,tuple:ts,undefined:e2,union:tr,unknown:e3,void:e8,NEVER:C,ZodIssueCode:E,quotelessJson:e=>JSON.stringify(e,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:S}),tO=a(32656);let tk={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};for(var tE=new Uint8Array(16),tS=[],tx=0;tx<256;++tx)tS.push((tx+256).toString(16).slice(1));let tj=function(e,t,a){if(tk.randomUUID&&!t&&!e)return tk.randomUUID();var r=(e=e||{}).random||(e.rng||function(){if(!v&&!(v="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return v(tE)})();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){a=a||0;for(var i=0;i<16;++i)t[a+i]=r[i];return t}return function(e,t=0){return(tS[e[t+0]]+tS[e[t+1]]+tS[e[t+2]]+tS[e[t+3]]+"-"+tS[e[t+4]]+tS[e[t+5]]+"-"+tS[e[t+6]]+tS[e[t+7]]+"-"+tS[e[t+8]]+tS[e[t+9]]+"-"+tS[e[t+10]]+tS[e[t+11]]+tS[e[t+12]]+tS[e[t+13]]+tS[e[t+14]]+tS[e[t+15]]).toLowerCase()}(r)};var tT=a(88464);let tP=(...e)=>fetch(...e),tA=Symbol.for("ls:fetch_implementation"),tI=()=>globalThis[tA]??tP,tC=[400,401,403,404,405,406,407,408],t$=[409];class tN{constructor(e){Object.defineProperty(this,"maxConcurrency",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"maxRetries",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"queue",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"onFailedResponseHook",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxConcurrency=e.maxConcurrency??1/0,this.maxRetries=e.maxRetries??6,"default"in tT?this.queue=new tT.default({concurrency:this.maxConcurrency}):this.queue=new tT({concurrency:this.maxConcurrency}),this.onFailedResponseHook=e?.onFailedResponseHook}call(e,...t){let a=this.onFailedResponseHook;return this.queue.add(()=>tO(()=>e(...t).catch(e=>{if(e instanceof Error)throw e;throw Error(e)}),{async onFailedAttempt(e){if(e.message.startsWith("Cancel")||e.message.startsWith("TimeoutError")||e.message.startsWith("AbortError")||e?.code==="ECONNABORTED")throw e;let t=e?.response,r=t?.status;if(r){if(tC.includes(+r))throw e;if(t$.includes(+r))return;a&&await a(t)}},retries:this.maxRetries,randomize:!0}),{throwOnTimeout:!0})}callWithOptions(e,t,...a){return e.signal?Promise.race([this.call(t,...a),new Promise((t,a)=>{e.signal?.addEventListener("abort",()=>{a(Error("AbortError"))})})]):this.call(t,...a)}fetch(...e){return this.call(()=>tI()(...e).then(e=>e.ok?e:Promise.reject(e)))}}function tR(e){return"function"==typeof e?._getType}function tM(e){let t={type:e._getType(),data:{content:e.content}};return e?.additional_kwargs&&Object.keys(e.additional_kwargs).length>0&&(t.data.additional_kwargs={...e.additional_kwargs}),t}let tL=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i,tU=function(e){return"string"==typeof e&&tL.test(e)};function tZ(e,t){if(!tU(e))throw Error(void 0!==t?`Invalid UUID for ${t}: ${e}`:`Invalid UUID: ${e}`);return e}let tD={};function tF(e){tD[e]||(console.warn(e),tD[e]=!0)}function tH(e){if(!e||e.split("/").length>2||e.startsWith("/")||e.endsWith("/")||e.split(":").length>2)throw Error(`Invalid identifier format: ${e}`);let[t,a]=e.split(":"),r=a||"latest";if(t.includes("/")){let[a,i]=t.split("/",2);if(!a||!i)throw Error(`Invalid identifier format: ${e}`);return[a,i,r]}if(!t)throw Error(`Invalid identifier format: ${e}`);return["-",t,r]}a(7968);class tz extends Error{constructor(e){super(e),this.name="LangSmithConflictError"}}async function tB(e,t,a){let r;if(e.ok){a&&(r=await e.text());return}r=await e.text();let i=`Failed to ${t}. Received status [${e.status}]: ${e.statusText}. Server response: ${r}`;if(409===e.status)throw new tz(i);throw Error(i)}var tV={result:"[Circular]"},tq=[],tG=[];function tJ(e,t,a,r){try{return JSON.stringify(e,t,a)}catch(o){if(!o.message?.includes("Converting circular structure to JSON"))return console.warn("[WARNING]: LangSmith received unserializable value."),"[Unserializable]";console.warn("[WARNING]: LangSmith received circular JSON. This will decrease tracer performance."),void 0===r&&(r={depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}),function e(t,a,r,i,n,s,o){if(s+=1,"object"==typeof t&&null!==t){for(l=0;l<i.length;l++)if(i[l]===t){tW(tV,t,a,n);return}if(void 0!==o.depthLimit&&s>o.depthLimit||void 0!==o.edgesLimit&&r+1>o.edgesLimit){tW("[...]",t,a,n);return}if(i.push(t),Array.isArray(t))for(l=0;l<t.length;l++)e(t[l],l,l,i,t,s,o);else{var l,d=Object.keys(t);for(l=0;l<d.length;l++){var u=d[l];e(t[u],u,l,i,t,s,o)}}i.pop()}}(e,"",0,[],void 0,0,r);try{var i;n=0===tG.length?JSON.stringify(e,t,a):JSON.stringify(e,(i=t,i=void 0!==i?i:function(e,t){return t},function(e,t){if(tG.length>0)for(var a=0;a<tG.length;a++){var r=tG[a];if(r[1]===e&&r[0]===t){t=r[2],tG.splice(a,1);break}}return i.call(this,e,t)}),a)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==tq.length;){var n,s=tq.pop();4===s.length?Object.defineProperty(s[0],s[1],s[3]):s[0][s[1]]=s[2]}}return n}}function tW(e,t,a,r){var i=Object.getOwnPropertyDescriptor(r,a);void 0!==i.get?i.configurable?(Object.defineProperty(r,a,{value:e}),tq.push([r,a,t,i])):tG.push([t,a,e]):(r[a]=e,tq.push([r,a,t]))}function tK(e){let t=aa(),a=function(){let e=function(){try{if(void 0!==t5&&t5.env)return Object.entries(t5.env).reduce((e,[t,a])=>(e[t]=String(a),e),{});return}catch(e){return}}()||{},t={},a=["LANGCHAIN_API_KEY","LANGCHAIN_ENDPOINT","LANGCHAIN_TRACING_V2","LANGCHAIN_PROJECT","LANGCHAIN_SESSION","LANGSMITH_API_KEY","LANGSMITH_ENDPOINT","LANGSMITH_TRACING_V2","LANGSMITH_PROJECT","LANGSMITH_SESSION"];for(let[r,i]of Object.entries(e))(r.startsWith("LANGCHAIN_")||r.startsWith("LANGSMITH_"))&&"string"==typeof i&&!a.includes(r)&&!r.toLowerCase().includes("key")&&!r.toLowerCase().includes("secret")&&!r.toLowerCase().includes("token")&&("LANGCHAIN_REVISION_ID"===r?t.revision_id=i:t[r]=i);return t}(),r=e.extra??{},i=r.metadata;return e.extra={...r,runtime:{...t,...r?.runtime},metadata:{...a,...a.revision_id||e.revision_id?{revision_id:e.revision_id??a.revision_id}:{},...i}},e}let tQ=()=>{let e=ai("TRACING_SAMPLING_RATE");if(void 0===e)return;let t=parseFloat(e);if(t<0||t>1)throw Error(`LANGSMITH_TRACING_SAMPLING_RATE must be between 0 and 1 if set. Got: ${t}`);return t},tY=e=>{let t=e.replace("http://","").replace("https://","").split("/")[0].split(":")[0];return"localhost"===t||"127.0.0.1"===t||"::1"===t};async function tX(e){let t=[];for await(let a of e)t.push(a);return t}function t0(e){if(void 0!==e)return e.trim().replace(/^"(.*)"$/,"$1").replace(/^'(.*)'$/,"$1")}let t1=async e=>{if(e?.status===429){let t=1e3*parseInt(e.headers.get("retry-after")??"30",10);if(t>0)return await new Promise(e=>setTimeout(e,t)),!0}return!1};class t9{constructor(){Object.defineProperty(this,"items",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"sizeBytes",{enumerable:!0,configurable:!0,writable:!0,value:0})}peek(){return this.items[0]}push(e){let t,a=new Promise(e=>{t=e}),r=tJ(e.item).length;return this.items.push({action:e.action,payload:e.item,itemPromiseResolve:t,itemPromise:a,size:r}),this.sizeBytes+=r,a}pop(e){if(e<1)throw Error("Number of bytes to pop off may not be less than 1.");let t=[],a=0;for(;a+(this.peek()?.size??0)<e&&this.items.length>0;){let e=this.items.shift();e&&(t.push(e),a+=e.size,this.sizeBytes-=e.size)}if(0===t.length&&this.items.length>0){let e=this.items.shift();t.push(e),a+=e.size,this.sizeBytes-=e.size}return[t.map(e=>({action:e.action,item:e.payload})),()=>t.forEach(e=>e.itemPromiseResolve())]}}class t4{constructor(e={}){Object.defineProperty(this,"apiKey",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"apiUrl",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"webUrl",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"caller",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"batchIngestCaller",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"timeout_ms",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_tenantId",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"hideInputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"hideOutputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tracingSampleRate",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"filteredPostUuids",{enumerable:!0,configurable:!0,writable:!0,value:new Set}),Object.defineProperty(this,"autoBatchTracing",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"autoBatchQueue",{enumerable:!0,configurable:!0,writable:!0,value:new t9}),Object.defineProperty(this,"autoBatchTimeout",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"autoBatchAggregationDelayMs",{enumerable:!0,configurable:!0,writable:!0,value:250}),Object.defineProperty(this,"batchSizeBytesLimit",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"fetchOptions",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"settings",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"blockOnRootRunFinalization",{enumerable:!0,configurable:!0,writable:!0,value:"false"===ar("LANGSMITH_TRACING_BACKGROUND")}),Object.defineProperty(this,"traceBatchConcurrency",{enumerable:!0,configurable:!0,writable:!0,value:5}),Object.defineProperty(this,"_serverInfo",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_getServerInfoPromise",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"manualFlushMode",{enumerable:!0,configurable:!0,writable:!0,value:!1});let t=t4.getDefaultClientConfig();if(this.tracingSampleRate=tQ(),this.apiUrl=t0(e.apiUrl??t.apiUrl)??"",this.apiUrl.endsWith("/")&&(this.apiUrl=this.apiUrl.slice(0,-1)),this.apiKey=t0(e.apiKey??t.apiKey),this.webUrl=t0(e.webUrl??t.webUrl),this.webUrl?.endsWith("/")&&(this.webUrl=this.webUrl.slice(0,-1)),this.timeout_ms=e.timeout_ms??9e4,this.caller=new tN(e.callerOptions??{}),this.traceBatchConcurrency=e.traceBatchConcurrency??this.traceBatchConcurrency,this.traceBatchConcurrency<1)throw Error("Trace batch concurrency must be positive.");this.batchIngestCaller=new tN({maxRetries:2,maxConcurrency:this.traceBatchConcurrency,...e.callerOptions??{},onFailedResponseHook:t1}),this.hideInputs=e.hideInputs??e.anonymizer??t.hideInputs,this.hideOutputs=e.hideOutputs??e.anonymizer??t.hideOutputs,this.autoBatchTracing=e.autoBatchTracing??this.autoBatchTracing,this.blockOnRootRunFinalization=e.blockOnRootRunFinalization??this.blockOnRootRunFinalization,this.batchSizeBytesLimit=e.batchSizeBytesLimit,this.fetchOptions=e.fetchOptions||{},this.manualFlushMode=e.manualFlushMode??this.manualFlushMode}static getDefaultClientConfig(){let e=ai("API_KEY"),t=ai("ENDPOINT")??"https://api.smith.langchain.com";return{apiUrl:t,apiKey:e,webUrl:void 0,hideInputs:"true"===ai("HIDE_INPUTS"),hideOutputs:"true"===ai("HIDE_OUTPUTS")}}getHostUrl(){if(this.webUrl)return this.webUrl;if(tY(this.apiUrl))return this.webUrl="http://localhost:3000",this.webUrl;if(this.apiUrl.includes("/api")&&!this.apiUrl.split(".",1)[0].endsWith("api"))return this.webUrl=this.apiUrl.replace("/api",""),this.webUrl;if(this.apiUrl.split(".",1)[0].includes("dev"))return this.webUrl="https://dev.smith.langchain.com",this.webUrl;if(this.apiUrl.split(".",1)[0].includes("eu"))return this.webUrl="https://eu.smith.langchain.com",this.webUrl;else return this.webUrl="https://smith.langchain.com",this.webUrl}get headers(){let e={"User-Agent":`langsmith-js/${t2}`};return this.apiKey&&(e["x-api-key"]=`${this.apiKey}`),e}processInputs(e){return!1===this.hideInputs?e:!0===this.hideInputs?{}:"function"==typeof this.hideInputs?this.hideInputs(e):e}processOutputs(e){return!1===this.hideOutputs?e:!0===this.hideOutputs?{}:"function"==typeof this.hideOutputs?this.hideOutputs(e):e}prepareRunCreateOrUpdateInputs(e){let t={...e};return void 0!==t.inputs&&(t.inputs=this.processInputs(t.inputs)),void 0!==t.outputs&&(t.outputs=this.processOutputs(t.outputs)),t}async _getResponse(e,t){let a=t?.toString()??"",r=`${this.apiUrl}${e}?${a}`,i=await this.caller.call(tI(),r,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await tB(i,`Failed to fetch ${e}`),i}async _get(e,t){return(await this._getResponse(e,t)).json()}async *_getPaginated(e,t=new URLSearchParams,a){let r=Number(t.get("offset"))||0,i=Number(t.get("limit"))||100;for(;;){t.set("offset",String(r)),t.set("limit",String(i));let n=`${this.apiUrl}${e}?${t}`,s=await this.caller.call(tI(),n,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(s,`Failed to fetch ${e}`);let o=a?a(await s.json()):await s.json();if(0===o.length||(yield o,o.length<i))break;r+=o.length}}async *_getCursorPaginatedList(e,t=null,a="POST",r="runs"){let i=t?{...t}:{};for(;;){let t=await this.caller.call(tI(),`${this.apiUrl}${e}`,{method:a,headers:{...this.headers,"Content-Type":"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions,body:JSON.stringify(i)}),n=await t.json();if(!n||!n[r])break;yield n[r];let s=n.cursors;if(!s||!s.next)break;i.cursor=s.next}}_filterForSampling(e,t=!1){if(void 0===this.tracingSampleRate)return e;if(t){let t=[];for(let a of e)this.filteredPostUuids.has(a.id)?this.filteredPostUuids.delete(a.id):t.push(a);return t}{let t=[];for(let a of e)a.id!==a.trace_id&&!this.filteredPostUuids.has(a.trace_id)||Math.random()<this.tracingSampleRate?t.push(a):this.filteredPostUuids.add(a.id);return t}}async _getBatchSizeLimitBytes(){let e=await this._ensureServerInfo();return this.batchSizeBytesLimit??e.batch_ingest_config?.size_limit_bytes??0x1400000}async _getMultiPartSupport(){let e=await this._ensureServerInfo();return e.instance_flags?.dataset_examples_multipart_enabled??!1}drainAutoBatchQueue(e){let t=[];for(;this.autoBatchQueue.items.length>0;){let[a,r]=this.autoBatchQueue.pop(e);if(!a.length){r();break}let i=this._processBatch(a,r).catch(console.error);t.push(i)}return Promise.all(t)}async _processBatch(e,t){if(!e.length){t();return}try{let t={runCreates:e.filter(e=>"create"===e.action).map(e=>e.item),runUpdates:e.filter(e=>"update"===e.action).map(e=>e.item)},a=await this._ensureServerInfo();a?.batch_ingest_config?.use_multipart_endpoint?await this.multipartIngestRuns(t):await this.batchIngestRuns(t)}finally{t()}}async processRunOperation(e){clearTimeout(this.autoBatchTimeout),this.autoBatchTimeout=void 0,"create"===e.action&&(e.item=tK(e.item));let t=this.autoBatchQueue.push(e);if(this.manualFlushMode)return t;let a=await this._getBatchSizeLimitBytes();return this.autoBatchQueue.sizeBytes>a&&this.drainAutoBatchQueue(a),this.autoBatchQueue.items.length>0&&(this.autoBatchTimeout=setTimeout(()=>{this.autoBatchTimeout=void 0,this.drainAutoBatchQueue(a)},this.autoBatchAggregationDelayMs)),t}async _getServerInfo(){let e=await tI()(`${this.apiUrl}/info`,{method:"GET",headers:{Accept:"application/json"},signal:AbortSignal.timeout(2500),...this.fetchOptions});return await tB(e,"get server info"),e.json()}async _ensureServerInfo(){return void 0===this._getServerInfoPromise&&(this._getServerInfoPromise=(async()=>{if(void 0===this._serverInfo)try{this._serverInfo=await this._getServerInfo()}catch(e){console.warn("[WARNING]: LangSmith failed to fetch info on supported operations. Falling back to batch operations and default limits.")}return this._serverInfo??{}})()),this._getServerInfoPromise.then(e=>(void 0===this._serverInfo&&(this._getServerInfoPromise=void 0),e))}async _getSettings(){return this.settings||(this.settings=this._get("/settings")),await this.settings}async flush(){let e=await this._getBatchSizeLimitBytes();await this.drainAutoBatchQueue(e)}async createRun(e){if(!this._filterForSampling([e]).length)return;let t={...this.headers,"Content-Type":"application/json"},a=e.project_name;delete e.project_name;let r=this.prepareRunCreateOrUpdateInputs({session_name:a,...e,start_time:e.start_time??Date.now()});if(this.autoBatchTracing&&void 0!==r.trace_id&&void 0!==r.dotted_order){this.processRunOperation({action:"create",item:r}).catch(console.error);return}let i=tK(r),n=await this.caller.call(tI(),`${this.apiUrl}/runs`,{method:"POST",headers:t,body:tJ(i),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(n,"create run",!0)}async batchIngestRuns({runCreates:e,runUpdates:t}){if(void 0===e&&void 0===t)return;let a=e?.map(e=>this.prepareRunCreateOrUpdateInputs(e))??[],r=t?.map(e=>this.prepareRunCreateOrUpdateInputs(e))??[];if(a.length>0&&r.length>0){let e=a.reduce((e,t)=>(t.id&&(e[t.id]=t),e),{}),t=[];for(let a of r)void 0!==a.id&&e[a.id]?e[a.id]={...e[a.id],...a}:t.push(a);a=Object.values(e),r=t}let i={post:this._filterForSampling(a),patch:this._filterForSampling(r,!0)};if(!i.post.length&&!i.patch.length)return;let n={post:[],patch:[]};for(let e of["post","patch"]){let t=i[e].reverse(),a=t.pop();for(;void 0!==a;)n[e].push(a),a=t.pop()}(n.post.length>0||n.patch.length>0)&&await this._postBatchIngestRuns(tJ(n))}async _postBatchIngestRuns(e){let t={...this.headers,"Content-Type":"application/json",Accept:"application/json"},a=await this.batchIngestCaller.call(tI(),`${this.apiUrl}/runs/batch`,{method:"POST",headers:t,body:e,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(a,"batch create run",!0)}async multipartIngestRuns({runCreates:e,runUpdates:t}){if(void 0===e&&void 0===t)return;let a={},r=[];for(let t of e??[]){let e=this.prepareRunCreateOrUpdateInputs(t);void 0!==e.id&&void 0!==e.attachments&&(a[e.id]=e.attachments),delete e.attachments,r.push(e)}let i=[];for(let e of t??[])i.push(this.prepareRunCreateOrUpdateInputs(e));if(void 0!==r.find(e=>void 0===e.trace_id||void 0===e.dotted_order))throw Error('Multipart ingest requires "trace_id" and "dotted_order" to be set when creating a run');if(void 0!==i.find(e=>void 0===e.trace_id||void 0===e.dotted_order))throw Error('Multipart ingest requires "trace_id" and "dotted_order" to be set when updating a run');if(r.length>0&&i.length>0){let e=r.reduce((e,t)=>(t.id&&(e[t.id]=t),e),{}),t=[];for(let a of i)void 0!==a.id&&e[a.id]?e[a.id]={...e[a.id],...a}:t.push(a);r=Object.values(e),i=t}if(0===r.length&&0===i.length)return;let n=[],s=[];for(let[e,t]of[["post",r],["patch",i]])for(let r of t){let{inputs:t,outputs:i,events:o,attachments:l,...d}=r,u={inputs:t,outputs:i,events:o},c=tJ(d);for(let[t,a]of(s.push({name:`${e}.${d.id}`,payload:new Blob([c],{type:`application/json; length=${c.length}`})}),Object.entries(u))){if(void 0===a)continue;let r=tJ(a);s.push({name:`${e}.${d.id}.${t}`,payload:new Blob([r],{type:`application/json; length=${r.length}`})})}if(void 0!==d.id){let e=a[d.id];if(e)for(let[t,r]of(delete a[d.id],Object.entries(e))){let e,a;if(Array.isArray(r)?[e,a]=r:(e=r.mimeType,a=r.data),t.includes(".")){console.warn(`Skipping attachment '${t}' for run ${d.id}: Invalid attachment name. Attachment names must not contain periods ('.'). Please rename the attachment and try again.`);continue}s.push({name:`attachment.${d.id}.${t}`,payload:new Blob([a],{type:`${e}; length=${a.byteLength}`})})}}n.push(`trace=${d.trace_id},id=${d.id}`)}await this._sendMultipartRequest(s,n.join("; "))}async _sendMultipartRequest(e,t){try{let t="----LangSmithFormBoundary"+Math.random().toString(36).slice(2),a=[];for(let r of e)a.push(new Blob([`--${t}\r
`])),a.push(new Blob([`Content-Disposition: form-data; name="${r.name}"\r
`,`Content-Type: ${r.payload.type}\r
\r
`])),a.push(r.payload),a.push(new Blob(["\r\n"]));a.push(new Blob([`--${t}--\r
`]));let r=new Blob(a),i=await r.arrayBuffer(),n=await this.batchIngestCaller.call(tI(),`${this.apiUrl}/runs/multipart`,{method:"POST",headers:{...this.headers,"Content-Type":`multipart/form-data; boundary=${t}`},body:i,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(n,"ingest multipart runs",!0)}catch(e){console.warn(`${e.message.trim()}

Context: ${t}`)}}async updateRun(e,t){tZ(e),t.inputs&&(t.inputs=this.processInputs(t.inputs)),t.outputs&&(t.outputs=this.processOutputs(t.outputs));let a={...t,id:e};if(!this._filterForSampling([a],!0).length)return;if(this.autoBatchTracing&&void 0!==a.trace_id&&void 0!==a.dotted_order){void 0!==t.end_time&&void 0===a.parent_run_id&&this.blockOnRootRunFinalization&&!this.manualFlushMode?await this.processRunOperation({action:"update",item:a}).catch(console.error):this.processRunOperation({action:"update",item:a}).catch(console.error);return}let r={...this.headers,"Content-Type":"application/json"},i=await this.caller.call(tI(),`${this.apiUrl}/runs/${e}`,{method:"PATCH",headers:r,body:tJ(t),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(i,"update run",!0)}async readRun(e,{loadChildRuns:t}={loadChildRuns:!1}){tZ(e);let a=await this._get(`/runs/${e}`);return t&&a.child_run_ids&&(a=await this._loadChildRuns(a)),a}async getRunUrl({runId:e,run:t,projectOpts:a}){if(void 0!==t){let e;e=t.session_id?t.session_id:a?.projectName?(await this.readProject({projectName:a?.projectName})).id:a?.projectId?a?.projectId:(await this.readProject({projectName:ai("PROJECT")||"default"})).id;let r=await this._getTenantId();return`${this.getHostUrl()}/o/${r}/projects/p/${e}/r/${t.id}?poll=true`}if(void 0!==e){let t=await this.readRun(e);if(!t.app_path)throw Error(`Run ${e} has no app_path`);let a=this.getHostUrl();return`${a}${t.app_path}`}throw Error("Must provide either runId or run")}async _loadChildRuns(e){let t=await tX(this.listRuns({id:e.child_run_ids})),a={},r={};for(let e of(t.sort((e,t)=>(e?.dotted_order??"").localeCompare(t?.dotted_order??"")),t)){if(null===e.parent_run_id||void 0===e.parent_run_id)throw Error(`Child run ${e.id} has no parent`);e.parent_run_id in a||(a[e.parent_run_id]=[]),a[e.parent_run_id].push(e),r[e.id]=e}for(let t in e.child_runs=a[e.id]||[],a)t!==e.id&&(r[t].child_runs=a[t]);return e}async *listRuns(e){let{projectId:t,projectName:a,parentRunId:r,traceId:i,referenceExampleId:n,startTime:s,executionOrder:o,isRoot:l,runType:d,error:u,id:c,query:h,filter:p,traceFilter:m,treeFilter:f,limit:g,select:y}=e,_=[];if(t&&(_=Array.isArray(t)?t:[t]),a){let e=Array.isArray(a)?a:[a],t=await Promise.all(e.map(e=>this.readProject({projectName:e}).then(e=>e.id)));_.push(...t)}let b={session:_.length?_:null,run_type:d,reference_example:n,query:h,filter:p,trace_filter:m,tree_filter:f,execution_order:o,parent_run:r,start_time:s?s.toISOString():null,error:u,id:c,limit:g,trace:i,select:y||["app_path","child_run_ids","completion_cost","completion_tokens","dotted_order","end_time","error","events","extra","feedback_stats","first_token_time","id","inputs","name","outputs","parent_run_id","parent_run_ids","prompt_cost","prompt_tokens","reference_example_id","run_type","session_id","start_time","status","tags","total_cost","total_tokens","trace_id"],is_root:l},v=0;for await(let e of this._getCursorPaginatedList("/runs/query",b))if(g){if(v>=g)break;if(e.length+v>g){let t=e.slice(0,g-v);yield*t;break}v+=e.length,yield*e}else yield*e}async getRunStats({id:e,trace:t,parentRun:a,runType:r,projectNames:i,projectIds:n,referenceExampleIds:s,startTime:o,endTime:l,error:d,query:u,filter:c,traceFilter:h,treeFilter:p,isRoot:m,dataSourceType:f}){let g=n||[];i&&(g=[...n||[],...await Promise.all(i.map(e=>this.readProject({projectName:e}).then(e=>e.id)))]);let y=Object.fromEntries(Object.entries({id:e,trace:t,parent_run:a,run_type:r,session:g,reference_example:s,start_time:o,end_time:l,error:d,query:u,filter:c,trace_filter:h,tree_filter:p,is_root:m,data_source_type:f}).filter(([e,t])=>void 0!==t)),_=await this.caller.call(tI(),`${this.apiUrl}/runs/stats`,{method:"POST",headers:this.headers,body:JSON.stringify(y),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _.json()}async shareRun(e,{shareId:t}={}){let a={run_id:e,share_token:t||tj()};tZ(e);let r=await this.caller.call(tI(),`${this.apiUrl}/runs/${e}/share`,{method:"PUT",headers:this.headers,body:JSON.stringify(a),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),i=await r.json();if(null===i||!("share_token"in i))throw Error("Invalid response from server");return`${this.getHostUrl()}/public/${i.share_token}/r`}async unshareRun(e){tZ(e);let t=await this.caller.call(tI(),`${this.apiUrl}/runs/${e}/share`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(t,"unshare run",!0)}async readRunSharedLink(e){tZ(e);let t=await this.caller.call(tI(),`${this.apiUrl}/runs/${e}/share`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),a=await t.json();if(null!==a&&"share_token"in a)return`${this.getHostUrl()}/public/${a.share_token}/r`}async listSharedRuns(e,{runIds:t}={}){let a=new URLSearchParams({share_token:e});if(void 0!==t)for(let e of t)a.append("id",e);tZ(e);let r=await this.caller.call(tI(),`${this.apiUrl}/public/${e}/runs${a}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await r.json()}async readDatasetSharedSchema(e,t){if(!e&&!t)throw Error("Either datasetId or datasetName must be given");e||(e=(await this.readDataset({datasetName:t})).id),tZ(e);let a=await this.caller.call(tI(),`${this.apiUrl}/datasets/${e}/share`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),r=await a.json();return r.url=`${this.getHostUrl()}/public/${r.share_token}/d`,r}async shareDataset(e,t){if(!e&&!t)throw Error("Either datasetId or datasetName must be given");e||(e=(await this.readDataset({datasetName:t})).id);let a={dataset_id:e};tZ(e);let r=await this.caller.call(tI(),`${this.apiUrl}/datasets/${e}/share`,{method:"PUT",headers:this.headers,body:JSON.stringify(a),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),i=await r.json();return i.url=`${this.getHostUrl()}/public/${i.share_token}/d`,i}async unshareDataset(e){tZ(e);let t=await this.caller.call(tI(),`${this.apiUrl}/datasets/${e}/share`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(t,"unshare dataset",!0)}async readSharedDataset(e){tZ(e);let t=await this.caller.call(tI(),`${this.apiUrl}/public/${e}/datasets`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await t.json()}async listSharedExamples(e,t){let a={};t?.exampleIds&&(a.id=t.exampleIds);let r=new URLSearchParams;Object.entries(a).forEach(([e,t])=>{Array.isArray(t)?t.forEach(t=>r.append(e,t)):r.append(e,t)});let i=await this.caller.call(tI(),`${this.apiUrl}/public/${e}/examples?${r.toString()}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),n=await i.json();if(!i.ok){if("detail"in n)throw Error(`Failed to list shared examples.
Status: ${i.status}
Message: ${n.detail.join("\n")}`);throw Error(`Failed to list shared examples: ${i.status} ${i.statusText}`)}return n.map(e=>({...e,_hostUrl:this.getHostUrl()}))}async createProject({projectName:e,description:t=null,metadata:a=null,upsert:r=!1,projectExtra:i=null,referenceDatasetId:n=null}){let s=`${this.apiUrl}/sessions${r?"?upsert=true":""}`,o=i||{};a&&(o.metadata=a);let l={name:e,extra:o,description:t};null!==n&&(l.reference_dataset_id=n);let d=await this.caller.call(tI(),s,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(l),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await tB(d,"create project"),await d.json()}async updateProject(e,{name:t=null,description:a=null,metadata:r=null,projectExtra:i=null,endTime:n=null}){let s=`${this.apiUrl}/sessions/${e}`,o=i;r&&(o={...o||{},metadata:r});let l={name:t,extra:o,description:a,end_time:n?new Date(n).toISOString():null},d=await this.caller.call(tI(),s,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(l),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await tB(d,"update project"),await d.json()}async hasProject({projectId:e,projectName:t}){let a="/sessions",r=new URLSearchParams;if(void 0!==e&&void 0!==t)throw Error("Must provide either projectName or projectId, not both");if(void 0!==e)tZ(e),a+=`/${e}`;else if(void 0!==t)r.append("name",t);else throw Error("Must provide projectName or projectId");let i=await this.caller.call(tI(),`${this.apiUrl}${a}?${r}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});try{let e=await i.json();if(!i.ok)return!1;if(Array.isArray(e))return e.length>0;return!0}catch(e){return!1}}async readProject({projectId:e,projectName:t,includeStats:a}){let r,i="/sessions",n=new URLSearchParams;if(void 0!==e&&void 0!==t)throw Error("Must provide either projectName or projectId, not both");if(void 0!==e)tZ(e),i+=`/${e}`;else if(void 0!==t)n.append("name",t);else throw Error("Must provide projectName or projectId");void 0!==a&&n.append("include_stats",a.toString());let s=await this._get(i,n);if(Array.isArray(s)){if(0===s.length)throw Error(`Project[id=${e}, name=${t}] not found`);r=s[0]}else r=s;return r}async getProjectUrl({projectId:e,projectName:t}){if(void 0===e&&void 0===t)throw Error("Must provide either projectName or projectId");let a=await this.readProject({projectId:e,projectName:t}),r=await this._getTenantId();return`${this.getHostUrl()}/o/${r}/projects/p/${a.id}`}async getDatasetUrl({datasetId:e,datasetName:t}){if(void 0===e&&void 0===t)throw Error("Must provide either datasetName or datasetId");let a=await this.readDataset({datasetId:e,datasetName:t}),r=await this._getTenantId();return`${this.getHostUrl()}/o/${r}/datasets/${a.id}`}async _getTenantId(){if(null!==this._tenantId)return this._tenantId;let e=new URLSearchParams({limit:"1"});for await(let t of this._getPaginated("/sessions",e))return this._tenantId=t[0].tenant_id,t[0].tenant_id;throw Error("No projects found to resolve tenant.")}async *listProjects({projectIds:e,name:t,nameContains:a,referenceDatasetId:r,referenceDatasetName:i,referenceFree:n,metadata:s}={}){let o=new URLSearchParams;if(void 0!==e)for(let t of e)o.append("id",t);if(void 0!==t&&o.append("name",t),void 0!==a&&o.append("name_contains",a),void 0!==r)o.append("reference_dataset",r);else if(void 0!==i){let e=await this.readDataset({datasetName:i});o.append("reference_dataset",e.id)}for await(let e of(void 0!==n&&o.append("reference_free",n.toString()),void 0!==s&&o.append("metadata",JSON.stringify(s)),this._getPaginated("/sessions",o)))yield*e}async deleteProject({projectId:e,projectName:t}){let a;if(void 0===e&&void 0===t)throw Error("Must provide projectName or projectId");if(void 0!==e&&void 0!==t)throw Error("Must provide either projectName or projectId, not both");tZ(a=void 0===e?(await this.readProject({projectName:t})).id:e);let r=await this.caller.call(tI(),`${this.apiUrl}/sessions/${a}`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(r,`delete session ${a} (${t})`,!0)}async uploadCsv({csvFile:e,fileName:t,inputKeys:a,outputKeys:r,description:i,dataType:n,name:s}){let o=`${this.apiUrl}/datasets/upload`,l=new FormData;l.append("file",e,t),a.forEach(e=>{l.append("input_keys",e)}),r.forEach(e=>{l.append("output_keys",e)}),i&&l.append("description",i),n&&l.append("data_type",n),s&&l.append("name",s);let d=await this.caller.call(tI(),o,{method:"POST",headers:this.headers,body:l,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await tB(d,"upload CSV"),await d.json()}async createDataset(e,{description:t,dataType:a,inputsSchema:r,outputsSchema:i,metadata:n}={}){let s={name:e,description:t,extra:n?{metadata:n}:void 0};a&&(s.data_type=a),r&&(s.inputs_schema_definition=r),i&&(s.outputs_schema_definition=i);let o=await this.caller.call(tI(),`${this.apiUrl}/datasets`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(s),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await tB(o,"create dataset"),await o.json()}async readDataset({datasetId:e,datasetName:t}){let a,r="/datasets",i=new URLSearchParams({limit:"1"});if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");if(void 0!==e)tZ(e),r+=`/${e}`;else if(void 0!==t)i.append("name",t);else throw Error("Must provide datasetName or datasetId");let n=await this._get(r,i);if(Array.isArray(n)){if(0===n.length)throw Error(`Dataset[id=${e}, name=${t}] not found`);a=n[0]}else a=n;return a}async hasDataset({datasetId:e,datasetName:t}){try{return await this.readDataset({datasetId:e,datasetName:t}),!0}catch(e){if(e instanceof Error&&e.message.toLocaleLowerCase().includes("not found"))return!1;throw e}}async diffDatasetVersions({datasetId:e,datasetName:t,fromVersion:a,toVersion:r}){let i=e;if(void 0===i&&void 0===t)throw Error("Must provide either datasetName or datasetId");if(void 0!==i&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");void 0===i&&(i=(await this.readDataset({datasetName:t})).id);let n=new URLSearchParams({from_version:"string"==typeof a?a:a.toISOString(),to_version:"string"==typeof r?r:r.toISOString()});return await this._get(`/datasets/${i}/versions/diff`,n)}async readDatasetOpenaiFinetuning({datasetId:e,datasetName:t}){if(void 0!==e);else if(void 0!==t)e=(await this.readDataset({datasetName:t})).id;else throw Error("Must provide datasetName or datasetId");let a=await this._getResponse(`/datasets/${e}/openai_ft`);return(await a.text()).trim().split("\n").map(e=>JSON.parse(e))}async *listDatasets({limit:e=100,offset:t=0,datasetIds:a,datasetName:r,datasetNameContains:i,metadata:n}={}){let s=new URLSearchParams({limit:e.toString(),offset:t.toString()});if(void 0!==a)for(let e of a)s.append("id",e);for await(let e of(void 0!==r&&s.append("name",r),void 0!==i&&s.append("name_contains",i),void 0!==n&&s.append("metadata",JSON.stringify(n)),this._getPaginated("/datasets",s)))yield*e}async updateDataset(e){let{datasetId:t,datasetName:a,...r}=e;if(!t&&!a)throw Error("Must provide either datasetName or datasetId");let i=t??(await this.readDataset({datasetName:a})).id;tZ(i);let n=await this.caller.call(tI(),`${this.apiUrl}/datasets/${i}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(r),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await tB(n,"update dataset"),await n.json()}async deleteDataset({datasetId:e,datasetName:t}){let a="/datasets",r=e;if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");if(void 0!==t&&(r=(await this.readDataset({datasetName:t})).id),void 0!==r)tZ(r),a+=`/${r}`;else throw Error("Must provide datasetName or datasetId");let i=await this.caller.call(tI(),this.apiUrl+a,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(i,`delete ${a}`),await i.json()}async indexDataset({datasetId:e,datasetName:t,tag:a}){let r=e;if(r||t)if(r&&t)throw Error("Must provide either datasetName or datasetId, not both");else r||(r=(await this.readDataset({datasetName:t})).id);else throw Error("Must provide either datasetName or datasetId");tZ(r);let i=await this.caller.call(tI(),`${this.apiUrl}/datasets/${r}/index`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify({tag:a}),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(i,"index dataset"),await i.json()}async similarExamples(e,t,a,{filter:r}={}){let i={limit:a,inputs:e};void 0!==r&&(i.filter=r),tZ(t);let n=await this.caller.call(tI(),`${this.apiUrl}/datasets/${t}/search`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(i),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await tB(n,"fetch similar examples"),(await n.json()).examples}async createExample(e,t,{datasetId:a,datasetName:r,createdAt:i,exampleId:n,metadata:s,split:o,sourceRunId:l}){let d=a;if(void 0===d&&void 0===r)throw Error("Must provide either datasetName or datasetId");if(void 0!==d&&void 0!==r)throw Error("Must provide either datasetName or datasetId, not both");void 0===d&&(d=(await this.readDataset({datasetName:r})).id);let u=i||new Date,c={dataset_id:d,inputs:e,outputs:t,created_at:u?.toISOString(),id:n,metadata:s,split:o,source_run_id:l},h=await this.caller.call(tI(),`${this.apiUrl}/examples`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(c),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await tB(h,"create example"),await h.json()}async createExamples(e){let{inputs:t,outputs:a,metadata:r,sourceRunIds:i,exampleIds:n,datasetId:s,datasetName:o}=e,l=s;if(void 0===l&&void 0===o)throw Error("Must provide either datasetName or datasetId");if(void 0!==l&&void 0!==o)throw Error("Must provide either datasetName or datasetId, not both");void 0===l&&(l=(await this.readDataset({datasetName:o})).id);let d=t.map((t,s)=>({dataset_id:l,inputs:t,outputs:a?a[s]:void 0,metadata:r?r[s]:void 0,split:e.splits?e.splits[s]:void 0,id:n?n[s]:void 0,source_run_id:i?i[s]:void 0})),u=await this.caller.call(tI(),`${this.apiUrl}/examples/bulk`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(d),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await tB(u,"create examples"),await u.json()}async createLLMExample(e,t,a){return this.createExample({input:e},{output:t},a)}async createChatExample(e,t,a){let r=e.map(e=>tR(e)?tM(e):e),i=tR(t)?tM(t):t;return this.createExample({input:r},{output:i},a)}async readExample(e){tZ(e);let t=`/examples/${e}`,{attachment_urls:a,...r}=await this._get(t);return a&&(r.attachments=Object.entries(a).reduce((e,[t,a])=>(e[t.slice(11)]={presigned_url:a.presigned_url},e),{})),r}async *listExamples({datasetId:e,datasetName:t,exampleIds:a,asOf:r,splits:i,inlineS3Urls:n,metadata:s,limit:o,offset:l,filter:d,includeAttachments:u}={}){let c;if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");if(void 0!==e)c=e;else if(void 0!==t)c=(await this.readDataset({datasetName:t})).id;else throw Error("Must provide a datasetName or datasetId");let h=new URLSearchParams({dataset:c}),p=r?"string"==typeof r?r:r?.toISOString():void 0;if(p&&h.append("as_of",p),h.append("inline_s3_urls",(n??!0).toString()),void 0!==a)for(let e of a)h.append("id",e);if(void 0!==i)for(let e of i)h.append("splits",e);if(void 0!==s){let e=JSON.stringify(s);h.append("metadata",e)}void 0!==o&&h.append("limit",o.toString()),void 0!==l&&h.append("offset",l.toString()),void 0!==d&&h.append("filter",d),!0===u&&["attachment_urls","outputs","metadata"].forEach(e=>h.append("select",e));let m=0;for await(let e of this._getPaginated("/examples",h)){for(let t of e){let{attachment_urls:e,...a}=t;e&&(a.attachments=Object.entries(e).reduce((e,[t,a])=>(e[t.slice(11)]={presigned_url:a.presigned_url},e),{})),yield a,m++}if(void 0!==o&&m>=o)break}}async deleteExample(e){tZ(e);let t=`/examples/${e}`,a=await this.caller.call(tI(),this.apiUrl+t,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(a,`delete ${t}`),await a.json()}async updateExample(e,t){tZ(e);let a=await this.caller.call(tI(),`${this.apiUrl}/examples/${e}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(t),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await tB(a,"update example"),await a.json()}async updateExamples(e){let t=await this.caller.call(tI(),`${this.apiUrl}/examples/bulk`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(e),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await tB(t,"update examples"),await t.json()}async listDatasetSplits({datasetId:e,datasetName:t,asOf:a}){let r;if(void 0===e&&void 0===t)throw Error("Must provide dataset name or ID");if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");tZ(r=void 0===e?(await this.readDataset({datasetName:t})).id:e);let i=new URLSearchParams,n=a?"string"==typeof a?a:a?.toISOString():void 0;return n&&i.append("as_of",n),await this._get(`/datasets/${r}/splits`,i)}async updateDatasetSplits({datasetId:e,datasetName:t,splitName:a,exampleIds:r,remove:i=!1}){let n;if(void 0===e&&void 0===t)throw Error("Must provide dataset name or ID");if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");tZ(n=void 0===e?(await this.readDataset({datasetName:t})).id:e);let s={split_name:a,examples:r.map(e=>(tZ(e),e)),remove:i},o=await this.caller.call(tI(),`${this.apiUrl}/datasets/${n}/splits`,{method:"PUT",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(s),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(o,"update dataset splits",!0)}async evaluateRun(e,t,{sourceInfo:a,loadChildRuns:r,referenceExample:i}={loadChildRuns:!1}){let n;if(tF("This method is deprecated and will be removed in future LangSmith versions, use `evaluate` from `langsmith/evaluation` instead."),"string"==typeof e)n=await this.readRun(e,{loadChildRuns:r});else if("object"==typeof e&&"id"in e)n=e;else throw Error(`Invalid run type: ${typeof e}`);null!==n.reference_example_id&&void 0!==n.reference_example_id&&(i=await this.readExample(n.reference_example_id));let s=await t.evaluateRun(n,i),[o,l]=await this._logEvaluationFeedback(s,n,a);return l[0]}async createFeedback(e,t,{score:a,value:r,correction:i,comment:n,sourceInfo:s,feedbackSourceType:o="api",sourceRunId:l,feedbackId:d,feedbackConfig:u,projectId:c,comparativeExperimentId:h}){if(!e&&!c)throw Error("One of runId or projectId must be provided");if(e&&c)throw Error("Only one of runId or projectId can be provided");let p={type:o??"api",metadata:s??{}};void 0===l||p?.metadata===void 0||p.metadata.__run||(p.metadata.__run={run_id:l}),p?.metadata!==void 0&&p.metadata.__run?.run_id!==void 0&&tZ(p.metadata.__run.run_id);let m={id:d??tj(),run_id:e,key:t,score:a,value:r,correction:i,comment:n,feedback_source:p,comparative_experiment_id:h,feedbackConfig:u,session_id:c},f=`${this.apiUrl}/feedback`,g=await this.caller.call(tI(),f,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(m),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await tB(g,"create feedback",!0),m}async updateFeedback(e,{score:t,value:a,correction:r,comment:i}){let n={};null!=t&&(n.score=t),null!=a&&(n.value=a),null!=r&&(n.correction=r),null!=i&&(n.comment=i),tZ(e);let s=await this.caller.call(tI(),`${this.apiUrl}/feedback/${e}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(n),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(s,"update feedback",!0)}async readFeedback(e){tZ(e);let t=`/feedback/${e}`;return await this._get(t)}async deleteFeedback(e){tZ(e);let t=`/feedback/${e}`,a=await this.caller.call(tI(),this.apiUrl+t,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(a,`delete ${t}`),await a.json()}async *listFeedback({runIds:e,feedbackKeys:t,feedbackSourceTypes:a}={}){let r=new URLSearchParams;if(e&&r.append("run",e.join(",")),t)for(let e of t)r.append("key",e);if(a)for(let e of a)r.append("source",e);for await(let e of this._getPaginated("/feedback",r))yield*e}async createPresignedFeedbackToken(e,t,{expiration:a,feedbackConfig:r}={}){let i={run_id:e,feedback_key:t,feedback_config:r};a?"string"==typeof a?i.expires_at=a:(a?.hours||a?.minutes||a?.days)&&(i.expires_in=a):i.expires_in={hours:3};let n=await this.caller.call(tI(),`${this.apiUrl}/feedback/tokens`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(i),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await n.json()}async createComparativeExperiment({name:e,experimentIds:t,referenceDatasetId:a,createdAt:r,description:i,metadata:n,id:s}){if(0===t.length)throw Error("At least one experiment is required");if(a||(a=(await this.readProject({projectId:t[0]})).reference_dataset_id),null==!a)throw Error("A reference dataset is required");let o={id:s,name:e,experiment_ids:t,reference_dataset_id:a,description:i,created_at:(r??new Date)?.toISOString(),extra:{}};n&&(o.extra.metadata=n);let l=await this.caller.call(tI(),`${this.apiUrl}/datasets/comparative`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(o),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await l.json()}async *listPresignedFeedbackTokens(e){tZ(e);let t=new URLSearchParams({run_id:e});for await(let e of this._getPaginated("/feedback/tokens",t))yield*e}_selectEvalResults(e){let t;return"results"in e?e.results:[e]}async _logEvaluationFeedback(e,t,a){let r=this._selectEvalResults(e),i=[];for(let e of r){let r=a||{};e.evaluatorInfo&&(r={...e.evaluatorInfo,...r});let n=null;e.targetRunId?n=e.targetRunId:t&&(n=t.id),i.push(await this.createFeedback(n,e.key,{score:e.score,value:e.value,comment:e.comment,correction:e.correction,sourceInfo:r,sourceRunId:e.sourceRunId,feedbackConfig:e.feedbackConfig,feedbackSourceType:"model"}))}return[r,i]}async logEvaluationFeedback(e,t,a){let[r]=await this._logEvaluationFeedback(e,t,a);return r}async *listAnnotationQueues(e={}){let{queueIds:t,name:a,nameContains:r,limit:i}=e,n=new URLSearchParams;t&&t.forEach((e,t)=>{tZ(e,`queueIds[${t}]`),n.append("ids",e)}),a&&n.append("name",a),r&&n.append("name_contains",r),n.append("limit",(void 0!==i?Math.min(i,100):100).toString());let s=0;for await(let e of this._getPaginated("/annotation-queues",n))if(yield*e,s++,void 0!==i&&s>=i)break}async createAnnotationQueue(e){let{name:t,description:a,queueId:r}=e,i={name:t,description:a,id:r||tj()},n=await this.caller.call(tI(),`${this.apiUrl}/annotation-queues`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(Object.fromEntries(Object.entries(i).filter(([e,t])=>void 0!==t))),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await tB(n,"create annotation queue"),await n.json()}async readAnnotationQueue(e){let t=await this.listAnnotationQueues({queueIds:[e]}).next();if(t.done)throw Error(`Annotation queue with ID ${e} not found`);return t.value}async updateAnnotationQueue(e,t){let{name:a,description:r}=t,i=await this.caller.call(tI(),`${this.apiUrl}/annotation-queues/${tZ(e,"queueId")}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify({name:a,description:r}),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(i,"update annotation queue")}async deleteAnnotationQueue(e){let t=await this.caller.call(tI(),`${this.apiUrl}/annotation-queues/${tZ(e,"queueId")}`,{method:"DELETE",headers:{...this.headers,Accept:"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(t,"delete annotation queue")}async addRunsToAnnotationQueue(e,t){let a=await this.caller.call(tI(),`${this.apiUrl}/annotation-queues/${tZ(e,"queueId")}/runs`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(t.map((e,t)=>tZ(e,`runIds[${t}]`).toString())),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(a,"add runs to annotation queue")}async getRunFromAnnotationQueue(e,t){let a=`/annotation-queues/${tZ(e,"queueId")}/run`,r=await this.caller.call(tI(),`${this.apiUrl}${a}/${t}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await tB(r,"get run from annotation queue"),await r.json()}async deleteRunFromAnnotationQueue(e,t){let a=await this.caller.call(tI(),`${this.apiUrl}/annotation-queues/${tZ(e,"queueId")}/runs/${tZ(t,"queueRunId")}`,{method:"DELETE",headers:{...this.headers,Accept:"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(a,"delete run from annotation queue")}async getSizeFromAnnotationQueue(e){let t=await this.caller.call(tI(),`${this.apiUrl}/annotation-queues/${tZ(e,"queueId")}/size`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await tB(t,"get size from annotation queue"),await t.json()}async _currentTenantIsOwner(e){let t=await this._getSettings();return"-"==e||t.tenant_handle===e}async _ownerConflictError(e,t){let a=await this._getSettings();return Error(`Cannot ${e} for another tenant.

      Current tenant: ${a.tenant_handle}

      Requested tenant: ${t}`)}async _getLatestCommitHash(e){let t=await this.caller.call(tI(),`${this.apiUrl}/commits/${e}/?limit=1&offset=0`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),a=await t.json();if(!t.ok){let e="string"==typeof a.detail?a.detail:JSON.stringify(a.detail),r=Error(`Error ${t.status}: ${t.statusText}
${e}`);throw r.statusCode=t.status,r}if(0!==a.commits.length)return a.commits[0].commit_hash}async _likeOrUnlikePrompt(e,t){let[a,r,i]=tH(e),n=await this.caller.call(tI(),`${this.apiUrl}/likes/${a}/${r}`,{method:"POST",body:JSON.stringify({like:t}),headers:{...this.headers,"Content-Type":"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await tB(n,`${t?"like":"unlike"} prompt`),await n.json()}async _getPromptUrl(e){let[t,a,r]=tH(e);if(await this._currentTenantIsOwner(t)){let e=await this._getSettings();return"latest"!==r?`${this.getHostUrl()}/prompts/${a}/${r.substring(0,8)}?organizationId=${e.id}`:`${this.getHostUrl()}/prompts/${a}?organizationId=${e.id}`}return"latest"!==r?`${this.getHostUrl()}/hub/${t}/${a}/${r.substring(0,8)}`:`${this.getHostUrl()}/hub/${t}/${a}`}async promptExists(e){return!!await this.getPrompt(e)}async likePrompt(e){return this._likeOrUnlikePrompt(e,!0)}async unlikePrompt(e){return this._likeOrUnlikePrompt(e,!1)}async *listCommits(e){for await(let t of this._getPaginated(`/commits/${e}/`,new URLSearchParams,e=>e.commits))yield*t}async *listPrompts(e){let t=new URLSearchParams;for await(let a of(t.append("sort_field",e?.sortField??"updated_at"),t.append("sort_direction","desc"),t.append("is_archived",(!!e?.isArchived).toString()),e?.isPublic!==void 0&&t.append("is_public",e.isPublic.toString()),e?.query&&t.append("query",e.query),this._getPaginated("/repos",t,e=>e.repos)))yield*a}async getPrompt(e){let[t,a,r]=tH(e),i=await this.caller.call(tI(),`${this.apiUrl}/repos/${t}/${a}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});if(404===i.status)return null;await tB(i,"get prompt");let n=await i.json();return n.repo?n.repo:null}async createPrompt(e,t){let a=await this._getSettings();if(t?.isPublic&&!a.tenant_handle)throw Error(`Cannot create a public prompt without first

        creating a LangChain Hub handle. 
        You can add a handle by creating a public prompt at:

        https://smith.langchain.com/prompts`);let[r,i,n]=tH(e);if(!await this._currentTenantIsOwner(r))throw await this._ownerConflictError("create a prompt",r);let s={repo_handle:i,...t?.description&&{description:t.description},...t?.readme&&{readme:t.readme},...t?.tags&&{tags:t.tags},is_public:!!t?.isPublic},o=await this.caller.call(tI(),`${this.apiUrl}/repos/`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(s),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(o,"create prompt");let{repo:l}=await o.json();return l}async createCommit(e,t,a){if(!await this.promptExists(e))throw Error("Prompt does not exist, you must create it first.");let[r,i,n]=tH(e),s=a?.parentCommitHash!=="latest"&&a?.parentCommitHash?a?.parentCommitHash:await this._getLatestCommitHash(`${r}/${i}`),o={manifest:JSON.parse(JSON.stringify(t)),parent_commit:s},l=await this.caller.call(tI(),`${this.apiUrl}/commits/${r}/${i}`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(o),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(l,"create commit");let d=await l.json();return this._getPromptUrl(`${r}/${i}${d.commit_hash?`:${d.commit_hash}`:""}`)}async updateExamplesMultipart(e,t=[]){if(!await this._getMultiPartSupport())throw Error("Your LangSmith version does not allow using the multipart examples endpoint, please update to the latest version.");let a=new FormData;for(let e of t){let t=e.id,r=new Blob([tJ({...e.metadata&&{metadata:e.metadata},...e.split&&{split:e.split}})],{type:"application/json"});if(a.append(t,r),e.inputs){let r=new Blob([tJ(e.inputs)],{type:"application/json"});a.append(`${t}.inputs`,r)}if(e.outputs){let r=new Blob([tJ(e.outputs)],{type:"application/json"});a.append(`${t}.outputs`,r)}if(e.attachments)for(let[r,i]of Object.entries(e.attachments)){let e,n;Array.isArray(i)?[e,n]=i:(e=i.mimeType,n=i.data);let s=new Blob([n],{type:`${e}; length=${n.byteLength}`});a.append(`${t}.attachment.${r}`,s)}if(e.attachments_operations){let r=new Blob([tJ(e.attachments_operations)],{type:"application/json"});a.append(`${t}.attachments_operations`,r)}}let r=await this.caller.call(tI(),`${this.apiUrl}/v1/platform/datasets/${e}/examples`,{method:"PATCH",headers:this.headers,body:a});return await r.json()}async uploadExamplesMultipart(e,t=[]){if(!await this._getMultiPartSupport())throw Error("Your LangSmith version does not allow using the multipart examples endpoint, please update to the latest version.");let a=new FormData;for(let e of t){let t=(e.id??tj()).toString(),r=new Blob([tJ({created_at:e.created_at,...e.metadata&&{metadata:e.metadata},...e.split&&{split:e.split}})],{type:"application/json"});a.append(t,r);let i=new Blob([tJ(e.inputs)],{type:"application/json"});if(a.append(`${t}.inputs`,i),e.outputs){let r=new Blob([tJ(e.outputs)],{type:"application/json"});a.append(`${t}.outputs`,r)}if(e.attachments)for(let[r,i]of Object.entries(e.attachments)){let e,n;Array.isArray(i)?[e,n]=i:(e=i.mimeType,n=i.data);let s=new Blob([n],{type:`${e}; length=${n.byteLength}`});a.append(`${t}.attachment.${r}`,s)}}let r=await this.caller.call(tI(),`${this.apiUrl}/v1/platform/datasets/${e}/examples`,{method:"POST",headers:this.headers,body:a});return await r.json()}async updatePrompt(e,t){if(!await this.promptExists(e))throw Error("Prompt does not exist, you must create it first.");let[a,r]=tH(e);if(!await this._currentTenantIsOwner(a))throw await this._ownerConflictError("update a prompt",a);let i={};if(t?.description!==void 0&&(i.description=t.description),t?.readme!==void 0&&(i.readme=t.readme),t?.tags!==void 0&&(i.tags=t.tags),t?.isPublic!==void 0&&(i.is_public=t.isPublic),t?.isArchived!==void 0&&(i.is_archived=t.isArchived),0===Object.keys(i).length)throw Error("No valid update options provided");let n=await this.caller.call(tI(),`${this.apiUrl}/repos/${a}/${r}`,{method:"PATCH",body:JSON.stringify(i),headers:{...this.headers,"Content-Type":"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await tB(n,"update prompt"),n.json()}async deletePrompt(e){if(!await this.promptExists(e))throw Error("Prompt does not exist, you must create it first.");let[t,a,r]=tH(e);if(!await this._currentTenantIsOwner(t))throw await this._ownerConflictError("delete a prompt",t);let i=await this.caller.call(tI(),`${this.apiUrl}/repos/${t}/${a}`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await i.json()}async pullPromptCommit(e,t){let[a,r,i]=tH(e),n=await this.caller.call(tI(),`${this.apiUrl}/commits/${a}/${r}/${i}${t?.includeModel?"?include_model=true":""}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await tB(n,"pull prompt commit");let s=await n.json();return{owner:a,repo:r,commit_hash:s.commit_hash,manifest:s.manifest,examples:s.examples}}async _pullPrompt(e,t){return JSON.stringify((await this.pullPromptCommit(e,{includeModel:t?.includeModel})).manifest)}async pushPrompt(e,t){return(await this.promptExists(e)?t&&Object.keys(t).some(e=>"object"!==e)&&await this.updatePrompt(e,{description:t?.description,readme:t?.readme,tags:t?.tags,isPublic:t?.isPublic}):await this.createPrompt(e,{description:t?.description,readme:t?.readme,tags:t?.tags,isPublic:t?.isPublic}),t?.object)?await this.createCommit(e,t?.object,{parentCommitHash:t?.parentCommitHash}):await this._getPromptUrl(e)}async clonePublicDataset(e,t={}){let{sourceApiUrl:a=this.apiUrl,datasetName:r}=t,[i,n]=this.parseTokenOrUrl(e,a),s=new t4({apiUrl:i,apiKey:"placeholder"}),o=await s.readSharedDataset(n),l=r||o.name;try{if(await this.hasDataset({datasetId:l})){console.log(`Dataset ${l} already exists in your tenant. Skipping.`);return}}catch(e){}let d=await s.listSharedExamples(n),u=await this.createDataset(l,{description:o.description,dataType:o.data_type||"kv",inputsSchema:o.inputs_schema_definition??void 0,outputsSchema:o.outputs_schema_definition??void 0});try{await this.createExamples({inputs:d.map(e=>e.inputs),outputs:d.flatMap(e=>e.outputs?[e.outputs]:[]),datasetId:u.id})}catch(e){throw console.error(`An error occurred while creating dataset ${l}. You should delete it manually.`),e}}parseTokenOrUrl(e,t,a=2,r="dataset"){try{return tZ(e),[t,e]}catch(e){}try{let i=new URL(e).pathname.split("/").filter(e=>""!==e);if(i.length>=a){let e=i[i.length-a];return[t,e]}throw Error(`Invalid public ${r} URL: ${e}`)}catch(t){throw Error(`Invalid public ${r} URL or token: ${e}`)}}awaitPendingTraceBatches(){return this.manualFlushMode?(console.warn("[WARNING]: When tracing in manual flush mode, you must call `await client.flush()` manually to submit trace batches."),Promise.resolve()):Promise.all([...this.autoBatchQueue.items.map(({itemPromise:e})=>e),this.batchIngestCaller.queue.onIdle()])}}let t2="0.2.15";var t5=a(73656);let t6=()=>"undefined"!=typeof window&&void 0!==window.document,t3=()=>"object"==typeof globalThis&&globalThis.constructor&&"DedicatedWorkerGlobalScope"===globalThis.constructor.name,t7=()=>"undefined"!=typeof window&&"nodejs"===window.name||"undefined"!=typeof navigator&&(navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom")),t8=()=>"undefined"!=typeof Deno,ae=()=>void 0!==t5&&void 0!==t5.versions&&void 0!==t5.versions.node&&!t8(),at=()=>i||(i=t6()?"browser":ae()?"node":t3()?"webworker":t7()?"jsdom":t8()?"deno":"other");function aa(){return void 0===n&&(n={library:"langsmith",runtime:at(),sdk:"langsmith-js",sdk_version:t2,...function(){if(void 0!==s)return s;let e={};for(let t of["VERCEL_GIT_COMMIT_SHA","NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA","COMMIT_REF","RENDER_GIT_COMMIT","CI_COMMIT_SHA","CIRCLE_SHA1","CF_PAGES_COMMIT_SHA","REACT_APP_GIT_SHA","SOURCE_VERSION","GITHUB_SHA","TRAVIS_COMMIT","GIT_COMMIT","BUILD_VCS_NUMBER","bamboo_planRepository_revision","Build.SourceVersion","BITBUCKET_COMMIT","DRONE_COMMIT_SHA","SEMAPHORE_GIT_SHA","BUILDKITE_COMMIT"]){let a=ar(t);void 0!==a&&(e[t]=a)}return s=e,e}()}),n}function ar(e){try{return void 0!==t5?t5.env?.[e]:void 0}catch(e){return}}function ai(e){return ar(`LANGSMITH_${e}`)||ar(`LANGCHAIN_${e}`)}let an=e=>void 0!==e?e:!!["TRACING_V2","TRACING"].find(e=>"true"===ai(e)),as=Symbol.for("lc:context_variables");class ao{constructor(e,t){Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.metadata=e,this.tags=t}static fromHeader(e){let t=e.split(","),a={},r=[];for(let e of t){let[t,i]=e.split("="),n=decodeURIComponent(i);"langsmith-metadata"===t?a=JSON.parse(n):"langsmith-tags"===t&&(r=n.split(","))}return new ao(a,r)}toHeader(){let e=[];return this.metadata&&Object.keys(this.metadata).length>0&&e.push(`langsmith-metadata=${encodeURIComponent(JSON.stringify(this.metadata))}`),this.tags&&this.tags.length>0&&e.push(`langsmith-tags=${encodeURIComponent(this.tags.join(","))}`),e.join(",")}}class al{constructor(e){if(Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"run_type",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"project_name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"parent_run",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"child_runs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"start_time",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"end_time",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"extra",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"error",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"serialized",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"inputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"outputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"reference_example_id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"client",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"events",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"trace_id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"dotted_order",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tracingEnabled",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"execution_order",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"child_execution_order",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"attachments",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),ad(e)){Object.assign(this,{...e});return}let t=al.getDefaultConfig(),{metadata:a,...r}=e,i=r.client??al.getSharedClient(),n={...a,...r?.extra?.metadata};if(r.extra={...r.extra,metadata:n},Object.assign(this,{...t,...r,client:i}),this.trace_id||(this.parent_run?this.trace_id=this.parent_run.trace_id??this.id:this.trace_id=this.id),this.execution_order??=1,this.child_execution_order??=1,!this.dotted_order){let e=function(e,t,a=1){let r=a.toFixed(0).slice(0,3).padStart(3,"0");return`${new Date(e).toISOString().slice(0,-1)}${r}Z`.replace(/[-:.]/g,"")+t}(this.start_time,this.id,this.execution_order);this.parent_run?this.dotted_order=this.parent_run.dotted_order+"."+e:this.dotted_order=e}}static getDefaultConfig(){return{id:tj(),run_type:"chain",project_name:ar("LANGCHAIN_PROJECT")??ar("LANGCHAIN_SESSION")??"default",child_runs:[],api_url:ar("LANGCHAIN_ENDPOINT")??"http://localhost:1984",api_key:ar("LANGCHAIN_API_KEY"),caller_options:{},start_time:Date.now(),serialized:{},inputs:{},extra:{}}}static getSharedClient(){return al.sharedClient||(al.sharedClient=new t4),al.sharedClient}createChild(e){var t,a;let r=this.child_execution_order+1,i=new al({...e,parent_run:this,project_name:this.project_name,client:this.client,tracingEnabled:this.tracingEnabled,execution_order:r,child_execution_order:r});as in this&&(i[as]=this[as]);let n=Symbol.for("lc:child_config"),s=e.extra?.[n]??this.extra[n];if(void 0!==(t=s)&&"object"==typeof t.callbacks&&(ac(t.callbacks?.handlers)||ac(t.callbacks))){let e={...s},t="object"==typeof(a=e.callbacks)&&null!=a&&Array.isArray(a.handlers)?e.callbacks.copy?.():void 0;t&&(Object.assign(t,{_parentRunId:i.id}),t.handlers?.find(au)?.updateFromRunTree?.(i),e.callbacks=t),i.extra[n]=e}let o=new Set,l=this;for(;null!=l&&!o.has(l.id);)o.add(l.id),l.child_execution_order=Math.max(l.child_execution_order,r),l=l.parent_run;return this.child_runs.push(i),i}async end(e,t,a=Date.now(),r){this.outputs=this.outputs??e,this.error=this.error??t,this.end_time=this.end_time??a,r&&Object.keys(r).length>0&&(this.extra=this.extra?{...this.extra,metadata:{...this.extra.metadata,...r}}:{metadata:r})}_convertToCreate(e,t,a=!0){let r,i,n=e.extra??{};if(n.runtime||(n.runtime={}),t)for(let[e,a]of Object.entries(t))n.runtime[e]||(n.runtime[e]=a);return a?(i=e.parent_run?.id,r=[]):(r=e.child_runs.map(e=>this._convertToCreate(e,t,a)),i=void 0),{id:e.id,name:e.name,start_time:e.start_time,end_time:e.end_time,run_type:e.run_type,reference_example_id:e.reference_example_id,extra:n,serialized:e.serialized,error:e.error,inputs:e.inputs,outputs:e.outputs,session_name:e.project_name,child_runs:r,parent_run_id:i,trace_id:e.trace_id,dotted_order:e.dotted_order,tags:e.tags,attachments:e.attachments}}async postRun(e=!0){try{let t=aa(),a=await this._convertToCreate(this,t,!0);if(await this.client.createRun(a),!e)for(let e of(tF("Posting with excludeChildRuns=false is deprecated and will be removed in a future version."),this.child_runs))await e.postRun(!1)}catch(e){console.error(`Error in postRun for run ${this.id}:`,e)}}async patchRun(){try{let e={end_time:this.end_time,error:this.error,inputs:this.inputs,outputs:this.outputs,parent_run_id:this.parent_run?.id,reference_example_id:this.reference_example_id,extra:this.extra,events:this.events,dotted_order:this.dotted_order,trace_id:this.trace_id,tags:this.tags,attachments:this.attachments};await this.client.updateRun(this.id,e)}catch(e){console.error(`Error in patchRun for run ${this.id}`,e)}}toJSON(){return this._convertToCreate(this,void 0,!1)}static fromRunnableConfig(e,t){let a,r,i,n=e?.callbacks,s=an();if(n){let e=n?.getParentRunId?.()??"",t=n?.handlers?.find(e=>e?.name=="langchain_tracer");a=t?.getRun?.(e),r=t?.projectName,i=t?.client,s=s||!!t}return a?new al({name:a.name,id:a.id,trace_id:a.trace_id,dotted_order:a.dotted_order,client:i,tracingEnabled:s,project_name:r,tags:[...new Set((a?.tags??[]).concat(e?.tags??[]))],extra:{metadata:{...a?.extra?.metadata,...e?.metadata}}}).createChild(t):new al({...t,client:i,tracingEnabled:s,project_name:r})}static fromDottedOrder(e){return this.fromHeaders({"langsmith-trace":e})}static fromHeaders(e,t){let a="get"in e&&"function"==typeof e.get?{"langsmith-trace":e.get("langsmith-trace"),baggage:e.get("baggage")}:e,r=a["langsmith-trace"];if(!r||"string"!=typeof r)return;let i=r.trim(),n=i.split(".").map(e=>{let[t,a]=e.split("Z");return{strTime:t,time:Date.parse(t+"Z"),uuid:a}}),s=n[0].uuid,o={...t,name:t?.name??"parent",run_type:t?.run_type??"chain",start_time:t?.start_time??Date.now(),id:n.at(-1)?.uuid,trace_id:s,dotted_order:i};if(a.baggage&&"string"==typeof a.baggage){let e=ao.fromHeader(a.baggage);o.metadata=e.metadata,o.tags=e.tags}return new al(o)}toHeaders(e){let t={"langsmith-trace":this.dotted_order,baggage:new ao(this.extra?.metadata,this.tags).toHeader()};if(e)for(let[a,r]of Object.entries(t))e.set(a,r);return t}}function ad(e){return void 0!==e&&"function"==typeof e.createChild&&"function"==typeof e.postRun}function au(e){return"object"==typeof e&&null!=e&&"string"==typeof e.name&&"langchain_tracer"===e.name}function ac(e){return Array.isArray(e)&&e.some(e=>au(e))}Object.defineProperty(al,"sharedClient",{enumerable:!0,configurable:!0,writable:!0,value:null});let ah=Symbol.for("ls:tracing_async_local_storage"),ap=new class{getStore(){}run(e,t){return t()}},am=new class{getInstance(){return globalThis[ah]??ap}initializeGlobalInstance(e){void 0===globalThis[ah]&&(globalThis[ah]=e)}},af=()=>{let e=am.getInstance().getStore();if(!ad(e))throw Error("Could not get the current run tree.\n\nPlease make sure you are calling this method within a traceable function or the tracing is enabled.");return e};function ag(e){return"function"==typeof e&&"langsmith:traceable"in e}Symbol.for("langsmith:traceable:root");let ay=Object.prototype.hasOwnProperty;function a_(e){switch(typeof e){case"object":return JSON.parse(JSON.stringify(e));case"undefined":return null;default:return e}}function ab(e){let t,a=0,r=e.length;for(;a<r;){if((t=e.charCodeAt(a))>=48&&t<=57){a++;continue}return!1}return!0}function av(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function aw(e,t){let a=[e];for(let e in t){let r="object"==typeof t[e]?JSON.stringify(t[e],null,2):t[e];void 0!==r&&a.push(`${e}: ${r}`)}return a.join("\n")}class aO extends Error{constructor(e,t,a,r,i){super(aw(e,{name:t,index:a,operation:r,tree:i})),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:t}),Object.defineProperty(this,"index",{enumerable:!0,configurable:!0,writable:!0,value:a}),Object.defineProperty(this,"operation",{enumerable:!0,configurable:!0,writable:!0,value:r}),Object.defineProperty(this,"tree",{enumerable:!0,configurable:!0,writable:!0,value:i}),Object.setPrototypeOf(this,new.target.prototype),this.message=aw(e,{name:t,index:a,operation:r,tree:i})}}let ak=aO,aE=a_,aS={add:function(e,t,a){return e[t]=this.value,{newDocument:a}},remove:function(e,t,a){var r=e[t];return delete e[t],{newDocument:a,removed:r}},replace:function(e,t,a){var r=e[t];return e[t]=this.value,{newDocument:a,removed:r}},move:function(e,t,a){let r=aj(a,this.path);r&&(r=a_(r));let i=aT(a,{op:"remove",path:this.from}).removed;return aT(a,{op:"add",path:this.path,value:i}),{newDocument:a,removed:r}},copy:function(e,t,a){let r=aj(a,this.from);return aT(a,{op:"add",path:this.path,value:a_(r)}),{newDocument:a}},test:function(e,t,a){return{newDocument:a,test:a$(e[t],this.value)}},_get:function(e,t,a){return this.value=e[t],{newDocument:a}}};var ax={add:function(e,t,a){return ab(t)?e.splice(t,0,this.value):e[t]=this.value,{newDocument:a,index:t}},remove:function(e,t,a){return{newDocument:a,removed:e.splice(t,1)[0]}},replace:function(e,t,a){var r=e[t];return e[t]=this.value,{newDocument:a,removed:r}},move:aS.move,copy:aS.copy,test:aS.test,_get:aS._get};function aj(e,t){if(""==t)return e;var a={op:"_get",path:t};return aT(e,a),a.value}function aT(e,t,a=!1,r=!0,i=!0,n=0){if(a&&("function"==typeof a?a(t,0,e,t.path):aI(t,0)),""===t.path){let r={newDocument:e};if("add"===t.op)return r.newDocument=t.value,r;if("replace"===t.op)return r.newDocument=t.value,r.removed=e,r;if("move"===t.op||"copy"===t.op)return r.newDocument=aj(e,t.from),"move"===t.op&&(r.removed=e),r;else if("test"===t.op){if(r.test=a$(e,t.value),!1===r.test)throw new ak("Test operation failed","TEST_OPERATION_FAILED",n,t,e);return r.newDocument=e,r}else if("remove"===t.op)return r.removed=e,r.newDocument=null,r;else if("_get"===t.op)return t.value=e,r;else if(!a)return r;else throw new ak("Operation `op` property is not one of operations defined in RFC-6902","OPERATION_OP_INVALID",n,t,e)}{let s,o,l;r||(e=a_(e));let d=(t.path||"").split("/"),u=e,c=1,h=d.length;for(o="function"==typeof a?a:aI;;){if((s=d[c])&&-1!=s.indexOf("~")&&(s=av(s)),i&&("__proto__"==s||"prototype"==s&&c>0&&"constructor"==d[c-1]))throw TypeError("JSON-Patch: modifying `__proto__` or `constructor/prototype` prop is banned for security reasons, if this was on purpose, please set `banPrototypeModifications` flag false and pass it to this function. More info in fast-json-patch README");if(a&&void 0===l&&(void 0===u[s]?l=d.slice(0,c).join("/"):c==h-1&&(l=t.path),void 0!==l&&o(t,0,e,l)),c++,Array.isArray(u)){if("-"===s)s=u.length;else if(a&&!ab(s))throw new ak("Expected an unsigned base-10 integer value, making the new referenced value the array element with the zero-based index","OPERATION_PATH_ILLEGAL_ARRAY_INDEX",n,t,e);else ab(s)&&(s=~~s);if(c>=h){if(a&&"add"===t.op&&s>u.length)throw new ak("The specified index MUST NOT be greater than the number of elements in the array","OPERATION_VALUE_OUT_OF_BOUNDS",n,t,e);let r=ax[t.op].call(t,u,s,e);if(!1===r.test)throw new ak("Test operation failed","TEST_OPERATION_FAILED",n,t,e);return r}}else if(c>=h){let a=aS[t.op].call(t,u,s,e);if(!1===a.test)throw new ak("Test operation failed","TEST_OPERATION_FAILED",n,t,e);return a}if(u=u[s],a&&c<h&&(!u||"object"!=typeof u))throw new ak("Cannot perform operation at the desired path","OPERATION_PATH_UNRESOLVABLE",n,t,e)}}}function aP(e,t,a,r=!0,i=!0){if(a&&!Array.isArray(t))throw new ak("Patch sequence must be an array","SEQUENCE_NOT_AN_ARRAY");r||(e=a_(e));let n=Array(t.length);for(let r=0,s=t.length;r<s;r++)n[r]=aT(e,t[r],a,!0,i,r),e=n[r].newDocument;return n.newDocument=e,n}function aA(e,t,a){let r=aT(e,t);if(!1===r.test)throw new ak("Test operation failed","TEST_OPERATION_FAILED",a,t,e);return r.newDocument}function aI(e,t,a,r){if("object"!=typeof e||null===e||Array.isArray(e))throw new ak("Operation is not an object","OPERATION_NOT_AN_OBJECT",t,e,a);if(aS[e.op]){if("string"!=typeof e.path)throw new ak("Operation `path` property is not a string","OPERATION_PATH_INVALID",t,e,a);else if(0!==e.path.indexOf("/")&&e.path.length>0)throw new ak('Operation `path` property must start with "/"',"OPERATION_PATH_INVALID",t,e,a);else if(("move"===e.op||"copy"===e.op)&&"string"!=typeof e.from)throw new ak("Operation `from` property is not present (applicable in `move` and `copy` operations)","OPERATION_FROM_REQUIRED",t,e,a);else if(("add"===e.op||"replace"===e.op||"test"===e.op)&&void 0===e.value)throw new ak("Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)","OPERATION_VALUE_REQUIRED",t,e,a);else if(("add"===e.op||"replace"===e.op||"test"===e.op)&&function e(t){if(void 0===t)return!0;if(t){if(Array.isArray(t)){for(let a=0,r=t.length;a<r;a++)if(e(t[a]))return!0}else if("object"==typeof t){let r=function(e){if(Array.isArray(e)){let t=Array(e.length);for(let e=0;e<t.length;e++)t[e]=""+e;return t}if(Object.keys)return Object.keys(e);let t=[];for(let a in e)ay.call(e,a)&&t.push(a);return t}(t),i=r.length;for(var a=0;a<i;a++)if(e(t[r[a]]))return!0}}return!1}(e.value))throw new ak("Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)","OPERATION_VALUE_CANNOT_CONTAIN_UNDEFINED",t,e,a);else if(a){if("add"==e.op){var i=e.path.split("/").length,n=r.split("/").length;if(i!==n+1&&i!==n)throw new ak("Cannot perform an `add` operation at the desired path","OPERATION_PATH_CANNOT_ADD",t,e,a)}else if("replace"===e.op||"remove"===e.op||"_get"===e.op){if(e.path!==r)throw new ak("Cannot perform the operation at a path that does not exist","OPERATION_PATH_UNRESOLVABLE",t,e,a)}else if("move"===e.op||"copy"===e.op){var s=aC([{op:"_get",path:e.from,value:void 0}],a);if(s&&"OPERATION_PATH_UNRESOLVABLE"===s.name)throw new ak("Cannot perform the operation from a path that does not exist","OPERATION_FROM_UNRESOLVABLE",t,e,a)}}}else throw new ak("Operation `op` property is not one of operations defined in RFC-6902","OPERATION_OP_INVALID",t,e,a)}function aC(e,t,a){try{if(!Array.isArray(e))throw new ak("Patch sequence must be an array","SEQUENCE_NOT_AN_ARRAY");if(t)aP(a_(t),a_(e),a||!0);else{a=a||aI;for(var r=0;r<e.length;r++)a(e[r],r,t,void 0)}}catch(e){if(e instanceof ak)return e;throw e}}function a$(e,t){if(e===t)return!0;if(e&&t&&"object"==typeof e&&"object"==typeof t){var a,r,i,n=Array.isArray(e),s=Array.isArray(t);if(n&&s){if((r=e.length)!=t.length)return!1;for(a=r;0!=a--;)if(!a$(e[a],t[a]))return!1;return!0}if(n!=s)return!1;var o=Object.keys(e);if((r=o.length)!==Object.keys(t).length)return!1;for(a=r;0!=a--;)if(!t.hasOwnProperty(o[a]))return!1;for(a=r;0!=a--;)if(!a$(e[i=o[a]],t[i]))return!1;return!0}return e!=e&&t!=t}new WeakMap;var aN=a(56393),aR=a(73656);let aM=()=>"undefined"!=typeof window&&void 0!==window.document,aL=()=>"object"==typeof globalThis&&globalThis.constructor&&"DedicatedWorkerGlobalScope"===globalThis.constructor.name,aU=()=>"undefined"!=typeof window&&"nodejs"===window.name||"undefined"!=typeof navigator&&(navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom")),aZ=()=>"undefined"!=typeof Deno,aD=()=>void 0!==aR&&void 0!==aR.versions&&void 0!==aR.versions.node&&!aZ(),aF=()=>{let e;return aM()?"browser":aD()?"node":aL()?"webworker":aU()?"jsdom":aZ()?"deno":"other"};async function aH(){return void 0===o&&(o={library:"langchain-js",runtime:aF()}),o}function az(e){try{if(void 0!==aR)return aR.env?.[e];if(aZ())return Deno?.env.get(e);return}catch(e){return}}class aB{}class aV extends aB{get lc_namespace(){return["langchain_core","callbacks",this.name]}get lc_secrets(){}get lc_attributes(){}get lc_aliases(){}static lc_name(){return this.name}get lc_id(){return[...this.lc_namespace,(0,aN.j)(this.constructor)]}constructor(e){super(),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"ignoreLLM",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreChain",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreAgent",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreRetriever",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreCustomEvent",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"raiseError",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"awaitHandlers",{enumerable:!0,configurable:!0,writable:!0,value:"false"===az("LANGCHAIN_CALLBACKS_BACKGROUND")}),this.lc_kwargs=e||{},e&&(this.ignoreLLM=e.ignoreLLM??this.ignoreLLM,this.ignoreChain=e.ignoreChain??this.ignoreChain,this.ignoreAgent=e.ignoreAgent??this.ignoreAgent,this.ignoreRetriever=e.ignoreRetriever??this.ignoreRetriever,this.ignoreCustomEvent=e.ignoreCustomEvent??this.ignoreCustomEvent,this.raiseError=e.raiseError??this.raiseError,this.awaitHandlers=this.raiseError||(e._awaitHandler??this.awaitHandlers))}copy(){return new this.constructor(this)}toJSON(){return aN.i.prototype.toJSON.call(this)}toJSONNotImplemented(){return aN.i.prototype.toJSONNotImplemented.call(this)}static fromMethods(e){return new class extends aV{constructor(){super(),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:tj()}),Object.assign(this,e)}}}}let aq=e=>void 0!==e&&"function"==typeof e.copy&&"string"==typeof e.name&&"boolean"==typeof e.awaitHandlers;function aG(e,t){return e&&!Array.isArray(e)&&"object"==typeof e?e:{[t]:e}}function aJ(e){return"function"==typeof e._addRunToRunMap}class aW extends aV{constructor(e){super(...arguments),Object.defineProperty(this,"runMap",{enumerable:!0,configurable:!0,writable:!0,value:new Map})}copy(){return this}stringifyError(e){return e instanceof Error?e.message+(e?.stack?`

${e.stack}`:""):"string"==typeof e?e:`${e}`}_addChildRun(e,t){e.child_runs.push(t)}_addRunToRunMap(e){let t=function(e,t,a){let r=a.toFixed(0).slice(0,3).padStart(3,"0");return`${new Date(e).toISOString().slice(0,-1)}${r}Z`.replace(/[-:.]/g,"")+t}(e.start_time,e.id,e.execution_order),a={...e};if(void 0!==a.parent_run_id){let e=this.runMap.get(a.parent_run_id);e&&(this._addChildRun(e,a),e.child_execution_order=Math.max(e.child_execution_order,a.child_execution_order),a.trace_id=e.trace_id,void 0!==e.dotted_order&&(a.dotted_order=[e.dotted_order,t].join(".")))}else a.trace_id=a.id,a.dotted_order=t;return this.runMap.set(a.id,a),a}async _endTrace(e){let t=void 0!==e.parent_run_id&&this.runMap.get(e.parent_run_id);t?t.child_execution_order=Math.max(t.child_execution_order,e.child_execution_order):await this.persistRun(e),this.runMap.delete(e.id),await this.onRunUpdate?.(e)}_getExecutionOrder(e){let t=void 0!==e&&this.runMap.get(e);return t?t.child_execution_order+1:1}_createRunForLLMStart(e,t,a,r,i,n,s,o){let l=this._getExecutionOrder(r),d=Date.now(),u=s?{...i,metadata:s}:i,c={id:a,name:o??e.id[e.id.length-1],parent_run_id:r,start_time:d,serialized:e,events:[{name:"start",time:new Date(d).toISOString()}],inputs:{prompts:t},execution_order:l,child_runs:[],child_execution_order:l,run_type:"llm",extra:u??{},tags:n||[]};return this._addRunToRunMap(c)}async handleLLMStart(e,t,a,r,i,n,s,o){let l=this.runMap.get(a)??this._createRunForLLMStart(e,t,a,r,i,n,s,o);return await this.onRunCreate?.(l),await this.onLLMStart?.(l),l}_createRunForChatModelStart(e,t,a,r,i,n,s,o){let l=this._getExecutionOrder(r),d=Date.now(),u=s?{...i,metadata:s}:i,c={id:a,name:o??e.id[e.id.length-1],parent_run_id:r,start_time:d,serialized:e,events:[{name:"start",time:new Date(d).toISOString()}],inputs:{messages:t},execution_order:l,child_runs:[],child_execution_order:l,run_type:"llm",extra:u??{},tags:n||[]};return this._addRunToRunMap(c)}async handleChatModelStart(e,t,a,r,i,n,s,o){let l=this.runMap.get(a)??this._createRunForChatModelStart(e,t,a,r,i,n,s,o);return await this.onRunCreate?.(l),await this.onLLMStart?.(l),l}async handleLLMEnd(e,t){let a=this.runMap.get(t);if(!a||a?.run_type!=="llm")throw Error("No LLM run to end.");return a.end_time=Date.now(),a.outputs=e,a.events.push({name:"end",time:new Date(a.end_time).toISOString()}),await this.onLLMEnd?.(a),await this._endTrace(a),a}async handleLLMError(e,t){let a=this.runMap.get(t);if(!a||a?.run_type!=="llm")throw Error("No LLM run to end.");return a.end_time=Date.now(),a.error=this.stringifyError(e),a.events.push({name:"error",time:new Date(a.end_time).toISOString()}),await this.onLLMError?.(a),await this._endTrace(a),a}_createRunForChainStart(e,t,a,r,i,n,s,o){let l=this._getExecutionOrder(r),d=Date.now(),u={id:a,name:o??e.id[e.id.length-1],parent_run_id:r,start_time:d,serialized:e,events:[{name:"start",time:new Date(d).toISOString()}],inputs:t,execution_order:l,child_execution_order:l,run_type:s??"chain",child_runs:[],extra:n?{metadata:n}:{},tags:i||[]};return this._addRunToRunMap(u)}async handleChainStart(e,t,a,r,i,n,s,o){let l=this.runMap.get(a)??this._createRunForChainStart(e,t,a,r,i,n,s,o);return await this.onRunCreate?.(l),await this.onChainStart?.(l),l}async handleChainEnd(e,t,a,r,i){let n=this.runMap.get(t);if(!n)throw Error("No chain run to end.");return n.end_time=Date.now(),n.outputs=aG(e,"output"),n.events.push({name:"end",time:new Date(n.end_time).toISOString()}),i?.inputs!==void 0&&(n.inputs=aG(i.inputs,"input")),await this.onChainEnd?.(n),await this._endTrace(n),n}async handleChainError(e,t,a,r,i){let n=this.runMap.get(t);if(!n)throw Error("No chain run to end.");return n.end_time=Date.now(),n.error=this.stringifyError(e),n.events.push({name:"error",time:new Date(n.end_time).toISOString()}),i?.inputs!==void 0&&(n.inputs=aG(i.inputs,"input")),await this.onChainError?.(n),await this._endTrace(n),n}_createRunForToolStart(e,t,a,r,i,n,s){let o=this._getExecutionOrder(r),l=Date.now(),d={id:a,name:s??e.id[e.id.length-1],parent_run_id:r,start_time:l,serialized:e,events:[{name:"start",time:new Date(l).toISOString()}],inputs:{input:t},execution_order:o,child_execution_order:o,run_type:"tool",child_runs:[],extra:n?{metadata:n}:{},tags:i||[]};return this._addRunToRunMap(d)}async handleToolStart(e,t,a,r,i,n,s){let o=this.runMap.get(a)??this._createRunForToolStart(e,t,a,r,i,n,s);return await this.onRunCreate?.(o),await this.onToolStart?.(o),o}async handleToolEnd(e,t){let a=this.runMap.get(t);if(!a||a?.run_type!=="tool")throw Error("No tool run to end");return a.end_time=Date.now(),a.outputs={output:e},a.events.push({name:"end",time:new Date(a.end_time).toISOString()}),await this.onToolEnd?.(a),await this._endTrace(a),a}async handleToolError(e,t){let a=this.runMap.get(t);if(!a||a?.run_type!=="tool")throw Error("No tool run to end");return a.end_time=Date.now(),a.error=this.stringifyError(e),a.events.push({name:"error",time:new Date(a.end_time).toISOString()}),await this.onToolError?.(a),await this._endTrace(a),a}async handleAgentAction(e,t){let a=this.runMap.get(t);a&&a?.run_type==="chain"&&(a.actions=a.actions||[],a.actions.push(e),a.events.push({name:"agent_action",time:new Date().toISOString(),kwargs:{action:e}}),await this.onAgentAction?.(a))}async handleAgentEnd(e,t){let a=this.runMap.get(t);a&&a?.run_type==="chain"&&(a.events.push({name:"agent_end",time:new Date().toISOString(),kwargs:{action:e}}),await this.onAgentEnd?.(a))}_createRunForRetrieverStart(e,t,a,r,i,n,s){let o=this._getExecutionOrder(r),l=Date.now(),d={id:a,name:s??e.id[e.id.length-1],parent_run_id:r,start_time:l,serialized:e,events:[{name:"start",time:new Date(l).toISOString()}],inputs:{query:t},execution_order:o,child_execution_order:o,run_type:"retriever",child_runs:[],extra:n?{metadata:n}:{},tags:i||[]};return this._addRunToRunMap(d)}async handleRetrieverStart(e,t,a,r,i,n,s){let o=this.runMap.get(a)??this._createRunForRetrieverStart(e,t,a,r,i,n,s);return await this.onRunCreate?.(o),await this.onRetrieverStart?.(o),o}async handleRetrieverEnd(e,t){let a=this.runMap.get(t);if(!a||a?.run_type!=="retriever")throw Error("No retriever run to end");return a.end_time=Date.now(),a.outputs={documents:e},a.events.push({name:"end",time:new Date(a.end_time).toISOString()}),await this.onRetrieverEnd?.(a),await this._endTrace(a),a}async handleRetrieverError(e,t){let a=this.runMap.get(t);if(!a||a?.run_type!=="retriever")throw Error("No retriever run to end");return a.end_time=Date.now(),a.error=this.stringifyError(e),a.events.push({name:"error",time:new Date(a.end_time).toISOString()}),await this.onRetrieverError?.(a),await this._endTrace(a),a}async handleText(e,t){let a=this.runMap.get(t);a&&a?.run_type==="chain"&&(a.events.push({name:"text",time:new Date().toISOString(),kwargs:{text:e}}),await this.onText?.(a))}async handleLLMNewToken(e,t,a,r,i,n){let s=this.runMap.get(a);if(!s||s?.run_type!=="llm")throw Error('Invalid "runId" provided to "handleLLMNewToken" callback.');return s.events.push({name:"new_token",time:new Date().toISOString(),kwargs:{token:e,idx:t,chunk:n?.chunk}}),await this.onLLMNewToken?.(s,e,{chunk:n?.chunk}),s}}var aK=a(8300);function aQ(e,t){return`${e.open}${t}${e.close}`}function aY(e,t){try{return JSON.stringify(e,null,2)}catch(e){return t}}function aX(e){return"string"==typeof e?e.trim():null==e?e:aY(e,e.toString())}function a0(e){if(!e.end_time)return"";let t=e.end_time-e.start_time;return t<1e3?`${t}ms`:`${(t/1e3).toFixed(2)}s`}let{color:a1}=aK;class a9 extends aW{constructor(){super(...arguments),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"console_callback_handler"})}persistRun(e){return Promise.resolve()}getParents(e){let t=[],a=e;for(;a.parent_run_id;){let e=this.runMap.get(a.parent_run_id);if(e)t.push(e),a=e;else break}return t}getBreadcrumbs(e){let t=[...this.getParents(e).reverse(),e].map((e,t,a)=>{let r=`${e.execution_order}:${e.run_type}:${e.name}`;return t===a.length-1?aQ(aK.bold,r):r}).join(" > ");return aQ(a1.grey,t)}onChainStart(e){let t=this.getBreadcrumbs(e);console.log(`${aQ(a1.green,"[chain/start]")} [${t}] Entering Chain run with input: ${aY(e.inputs,"[inputs]")}`)}onChainEnd(e){let t=this.getBreadcrumbs(e);console.log(`${aQ(a1.cyan,"[chain/end]")} [${t}] [${a0(e)}] Exiting Chain run with output: ${aY(e.outputs,"[outputs]")}`)}onChainError(e){let t=this.getBreadcrumbs(e);console.log(`${aQ(a1.red,"[chain/error]")} [${t}] [${a0(e)}] Chain run errored with error: ${aY(e.error,"[error]")}`)}onLLMStart(e){let t=this.getBreadcrumbs(e),a="prompts"in e.inputs?{prompts:e.inputs.prompts.map(e=>e.trim())}:e.inputs;console.log(`${aQ(a1.green,"[llm/start]")} [${t}] Entering LLM run with input: ${aY(a,"[inputs]")}`)}onLLMEnd(e){let t=this.getBreadcrumbs(e);console.log(`${aQ(a1.cyan,"[llm/end]")} [${t}] [${a0(e)}] Exiting LLM run with output: ${aY(e.outputs,"[response]")}`)}onLLMError(e){let t=this.getBreadcrumbs(e);console.log(`${aQ(a1.red,"[llm/error]")} [${t}] [${a0(e)}] LLM run errored with error: ${aY(e.error,"[error]")}`)}onToolStart(e){let t=this.getBreadcrumbs(e);console.log(`${aQ(a1.green,"[tool/start]")} [${t}] Entering Tool run with input: "${aX(e.inputs.input)}"`)}onToolEnd(e){let t=this.getBreadcrumbs(e);console.log(`${aQ(a1.cyan,"[tool/end]")} [${t}] [${a0(e)}] Exiting Tool run with output: "${aX(e.outputs?.output)}"`)}onToolError(e){let t=this.getBreadcrumbs(e);console.log(`${aQ(a1.red,"[tool/error]")} [${t}] [${a0(e)}] Tool run errored with error: ${aY(e.error,"[error]")}`)}onRetrieverStart(e){let t=this.getBreadcrumbs(e);console.log(`${aQ(a1.green,"[retriever/start]")} [${t}] Entering Retriever run with input: ${aY(e.inputs,"[inputs]")}`)}onRetrieverEnd(e){let t=this.getBreadcrumbs(e);console.log(`${aQ(a1.cyan,"[retriever/end]")} [${t}] [${a0(e)}] Exiting Retriever run with output: ${aY(e.outputs,"[outputs]")}`)}onRetrieverError(e){let t=this.getBreadcrumbs(e);console.log(`${aQ(a1.red,"[retriever/error]")} [${t}] [${a0(e)}] Retriever run errored with error: ${aY(e.error,"[error]")}`)}onAgentAction(e){let t=this.getBreadcrumbs(e);console.log(`${aQ(a1.blue,"[agent/action]")} [${t}] Agent selected action: ${aY(e.actions[e.actions.length-1],"[action]")}`)}}var a4=a(94509);let a2=()=>(void 0===l&&(l=new t4("false"===az("LANGCHAIN_CALLBACKS_BACKGROUND")?{blockOnRootRunFinalization:!0}:{})),l);class a5 extends aW{constructor(e={}){super(e),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"langchain_tracer"}),Object.defineProperty(this,"projectName",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"exampleId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"client",{enumerable:!0,configurable:!0,writable:!0,value:void 0});let{exampleId:t,projectName:a,client:r}=e;this.projectName=a??az("LANGCHAIN_PROJECT")??az("LANGCHAIN_SESSION"),this.exampleId=t,this.client=r??a2();let i=a5.getTraceableRunTree();i&&this.updateFromRunTree(i)}async _convertToCreate(e,t){return{...e,extra:{...e.extra,runtime:await aH()},child_runs:void 0,session_name:this.projectName,reference_example_id:e.parent_run_id?void 0:t}}async persistRun(e){}async onRunCreate(e){let t=await this._convertToCreate(e,this.exampleId);await this.client.createRun(t)}async onRunUpdate(e){let t={end_time:e.end_time,error:e.error,outputs:e.outputs,events:e.events,inputs:e.inputs,trace_id:e.trace_id,dotted_order:e.dotted_order,parent_run_id:e.parent_run_id};await this.client.updateRun(e.id,t)}getRun(e){return this.runMap.get(e)}updateFromRunTree(e){let t=e,a=new Set;for(;t.parent_run&&!a.has(t.id)&&(a.add(t.id),t.parent_run);){;t=t.parent_run}a.clear();let r=[t];for(;r.length>0;){let e=r.shift();!(!e||a.has(e.id))&&(a.add(e.id),this.runMap.set(e.id,e),e.child_runs&&r.push(...e.child_runs))}this.client=e.client??this.client,this.projectName=e.project_name??this.projectName,this.exampleId=e.reference_example_id??this.exampleId}convertToRunTree(e){let t={},a=[];for(let[e,r]of this.runMap){let i=new al({...r,child_runs:[],parent_run:void 0,client:this.client,project_name:this.projectName,reference_example_id:this.exampleId,tracingEnabled:!0});t[e]=i,a.push([e,r.dotted_order])}for(let[e]of(a.sort((e,t)=>e[1]&&t[1]?e[1].localeCompare(t[1]):0),a)){let a=this.runMap.get(e),r=t[e];if(a&&r&&a.parent_run_id){let e=t[a.parent_run_id];e&&(e.child_runs.push(r),r.parent_run=e)}}return t[e]}static getTraceableRunTree(){try{return af()}catch{return}}}let a6=Symbol.for("ls:tracing_async_local_storage"),a3=Symbol.for("lc:context_variables"),a7=e=>{globalThis[a6]=e},a8=()=>globalThis[a6];async function re(e,t){if(!0===t){let t=a8();void 0!==t?await t.run(void 0,async()=>e()):await e()}else void 0===d&&(d=new("default"in tT?tT.default:tT)({autoStart:!0,concurrency:1})),d.add(async()=>{let t=a8();void 0!==t?await t.run(void 0,async()=>e()):await e()})}let rt=e=>void 0!==e?e:!!["LANGSMITH_TRACING_V2","LANGCHAIN_TRACING_V2","LANGSMITH_TRACING","LANGCHAIN_TRACING"].find(e=>"true"===az(e));function ra(e){let t=a8();if(void 0===t)return;let a=t.getStore();return a?.[a3]?.[e]}let rr=Symbol("lc:configure_hooks"),ri=()=>ra(rr)||[];class rn{setHandler(e){return this.setHandlers([e])}}class rs{constructor(e,t,a,r,i,n,s,o){Object.defineProperty(this,"runId",{enumerable:!0,configurable:!0,writable:!0,value:e}),Object.defineProperty(this,"handlers",{enumerable:!0,configurable:!0,writable:!0,value:t}),Object.defineProperty(this,"inheritableHandlers",{enumerable:!0,configurable:!0,writable:!0,value:a}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:r}),Object.defineProperty(this,"inheritableTags",{enumerable:!0,configurable:!0,writable:!0,value:i}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:n}),Object.defineProperty(this,"inheritableMetadata",{enumerable:!0,configurable:!0,writable:!0,value:s}),Object.defineProperty(this,"_parentRunId",{enumerable:!0,configurable:!0,writable:!0,value:o})}get parentRunId(){return this._parentRunId}async handleText(e){await Promise.all(this.handlers.map(t=>re(async()=>{try{await t.handleText?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleText: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleCustomEvent(e,t,a,r,i){await Promise.all(this.handlers.map(a=>re(async()=>{try{await a.handleCustomEvent?.(e,t,this.runId,this.tags,this.metadata)}catch(e){if((a.raiseError?console.error:console.warn)(`Error in handler ${a.constructor.name}, handleCustomEvent: ${e}`),a.raiseError)throw e}},a.awaitHandlers)))}}class ro extends rs{getChild(e){let t=new rc(this.runId);return t.setHandlers(this.inheritableHandlers),t.addTags(this.inheritableTags),t.addMetadata(this.inheritableMetadata),e&&t.addTags([e],!1),t}async handleRetrieverEnd(e){await Promise.all(this.handlers.map(t=>re(async()=>{if(!t.ignoreRetriever)try{await t.handleRetrieverEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleRetriever`),t.raiseError)throw e}},t.awaitHandlers)))}async handleRetrieverError(e){await Promise.all(this.handlers.map(t=>re(async()=>{if(!t.ignoreRetriever)try{await t.handleRetrieverError?.(e,this.runId,this._parentRunId,this.tags)}catch(a){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleRetrieverError: ${a}`),t.raiseError)throw e}},t.awaitHandlers)))}}class rl extends rs{async handleLLMNewToken(e,t,a,r,i,n){await Promise.all(this.handlers.map(a=>re(async()=>{if(!a.ignoreLLM)try{await a.handleLLMNewToken?.(e,t??{prompt:0,completion:0},this.runId,this._parentRunId,this.tags,n)}catch(e){if((a.raiseError?console.error:console.warn)(`Error in handler ${a.constructor.name}, handleLLMNewToken: ${e}`),a.raiseError)throw e}},a.awaitHandlers)))}async handleLLMError(e){await Promise.all(this.handlers.map(t=>re(async()=>{if(!t.ignoreLLM)try{await t.handleLLMError?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleLLMError: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleLLMEnd(e){await Promise.all(this.handlers.map(t=>re(async()=>{if(!t.ignoreLLM)try{await t.handleLLMEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleLLMEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}}class rd extends rs{getChild(e){let t=new rc(this.runId);return t.setHandlers(this.inheritableHandlers),t.addTags(this.inheritableTags),t.addMetadata(this.inheritableMetadata),e&&t.addTags([e],!1),t}async handleChainError(e,t,a,r,i){await Promise.all(this.handlers.map(t=>re(async()=>{if(!t.ignoreChain)try{await t.handleChainError?.(e,this.runId,this._parentRunId,this.tags,i)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleChainError: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleChainEnd(e,t,a,r,i){await Promise.all(this.handlers.map(t=>re(async()=>{if(!t.ignoreChain)try{await t.handleChainEnd?.(e,this.runId,this._parentRunId,this.tags,i)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleChainEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleAgentAction(e){await Promise.all(this.handlers.map(t=>re(async()=>{if(!t.ignoreAgent)try{await t.handleAgentAction?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleAgentAction: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleAgentEnd(e){await Promise.all(this.handlers.map(t=>re(async()=>{if(!t.ignoreAgent)try{await t.handleAgentEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleAgentEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}}class ru extends rs{getChild(e){let t=new rc(this.runId);return t.setHandlers(this.inheritableHandlers),t.addTags(this.inheritableTags),t.addMetadata(this.inheritableMetadata),e&&t.addTags([e],!1),t}async handleToolError(e){await Promise.all(this.handlers.map(t=>re(async()=>{if(!t.ignoreAgent)try{await t.handleToolError?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleToolError: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleToolEnd(e){await Promise.all(this.handlers.map(t=>re(async()=>{if(!t.ignoreAgent)try{await t.handleToolEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleToolEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}}class rc extends rn{constructor(e,t){super(),Object.defineProperty(this,"handlers",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"inheritableHandlers",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"inheritableTags",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"inheritableMetadata",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"callback_manager"}),Object.defineProperty(this,"_parentRunId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.handlers=t?.handlers??this.handlers,this.inheritableHandlers=t?.inheritableHandlers??this.inheritableHandlers,this.tags=t?.tags??this.tags,this.inheritableTags=t?.inheritableTags??this.inheritableTags,this.metadata=t?.metadata??this.metadata,this.inheritableMetadata=t?.inheritableMetadata??this.inheritableMetadata,this._parentRunId=e}getParentRunId(){return this._parentRunId}async handleLLMStart(e,t,a,r,i,n,s,o){return Promise.all(t.map(async(t,r)=>{let n=0===r&&a?a:tj();return await Promise.all(this.handlers.map(a=>{if(!a.ignoreLLM)return aJ(a)&&a._createRunForLLMStart(e,[t],n,this._parentRunId,i,this.tags,this.metadata,o),re(async()=>{try{await a.handleLLMStart?.(e,[t],n,this._parentRunId,i,this.tags,this.metadata,o)}catch(e){if((a.raiseError?console.error:console.warn)(`Error in handler ${a.constructor.name}, handleLLMStart: ${e}`),a.raiseError)throw e}},a.awaitHandlers)})),new rl(n,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}))}async handleChatModelStart(e,t,a,r,i,n,s,o){return Promise.all(t.map(async(t,r)=>{let n=0===r&&a?a:tj();return await Promise.all(this.handlers.map(a=>{if(!a.ignoreLLM)return aJ(a)&&a._createRunForChatModelStart(e,[t],n,this._parentRunId,i,this.tags,this.metadata,o),re(async()=>{try{if(a.handleChatModelStart)await a.handleChatModelStart?.(e,[t],n,this._parentRunId,i,this.tags,this.metadata,o);else if(a.handleLLMStart){let r=(0,a4.zs)(t);await a.handleLLMStart?.(e,[r],n,this._parentRunId,i,this.tags,this.metadata,o)}}catch(e){if((a.raiseError?console.error:console.warn)(`Error in handler ${a.constructor.name}, handleLLMStart: ${e}`),a.raiseError)throw e}},a.awaitHandlers)})),new rl(n,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}))}async handleChainStart(e,t,a=tj(),r,i,n,s){return await Promise.all(this.handlers.map(i=>{if(!i.ignoreChain)return aJ(i)&&i._createRunForChainStart(e,t,a,this._parentRunId,this.tags,this.metadata,r,s),re(async()=>{try{await i.handleChainStart?.(e,t,a,this._parentRunId,this.tags,this.metadata,r,s)}catch(e){if((i.raiseError?console.error:console.warn)(`Error in handler ${i.constructor.name}, handleChainStart: ${e}`),i.raiseError)throw e}},i.awaitHandlers)})),new rd(a,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}async handleToolStart(e,t,a=tj(),r,i,n,s){return await Promise.all(this.handlers.map(r=>{if(!r.ignoreAgent)return aJ(r)&&r._createRunForToolStart(e,t,a,this._parentRunId,this.tags,this.metadata,s),re(async()=>{try{await r.handleToolStart?.(e,t,a,this._parentRunId,this.tags,this.metadata,s)}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleToolStart: ${e}`),r.raiseError)throw e}},r.awaitHandlers)})),new ru(a,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}async handleRetrieverStart(e,t,a=tj(),r,i,n,s){return await Promise.all(this.handlers.map(r=>{if(!r.ignoreRetriever)return aJ(r)&&r._createRunForRetrieverStart(e,t,a,this._parentRunId,this.tags,this.metadata,s),re(async()=>{try{await r.handleRetrieverStart?.(e,t,a,this._parentRunId,this.tags,this.metadata,s)}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleRetrieverStart: ${e}`),r.raiseError)throw e}},r.awaitHandlers)})),new ro(a,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}async handleCustomEvent(e,t,a,r,i){await Promise.all(this.handlers.map(r=>re(async()=>{if(!r.ignoreCustomEvent)try{await r.handleCustomEvent?.(e,t,a,this.tags,this.metadata)}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleCustomEvent: ${e}`),r.raiseError)throw e}},r.awaitHandlers)))}addHandler(e,t=!0){this.handlers.push(e),t&&this.inheritableHandlers.push(e)}removeHandler(e){this.handlers=this.handlers.filter(t=>t!==e),this.inheritableHandlers=this.inheritableHandlers.filter(t=>t!==e)}setHandlers(e,t=!0){for(let a of(this.handlers=[],this.inheritableHandlers=[],e))this.addHandler(a,t)}addTags(e,t=!0){this.removeTags(e),this.tags.push(...e),t&&this.inheritableTags.push(...e)}removeTags(e){this.tags=this.tags.filter(t=>!e.includes(t)),this.inheritableTags=this.inheritableTags.filter(t=>!e.includes(t))}addMetadata(e,t=!0){this.metadata={...this.metadata,...e},t&&(this.inheritableMetadata={...this.inheritableMetadata,...e})}removeMetadata(e){for(let t of Object.keys(e))delete this.metadata[t],delete this.inheritableMetadata[t]}copy(e=[],t=!0){let a=new rc(this._parentRunId);for(let e of this.handlers){let t=this.inheritableHandlers.includes(e);a.addHandler(e,t)}for(let e of this.tags){let t=this.inheritableTags.includes(e);a.addTags([e],t)}for(let e of Object.keys(this.metadata)){let t=Object.keys(this.inheritableMetadata).includes(e);a.addMetadata({[e]:this.metadata[e]},t)}for(let r of e)a.handlers.filter(e=>"console_callback_handler"===e.name).some(e=>e.name===r.name)||a.addHandler(r,t);return a}static fromHandlers(e){let t=new this;return t.addHandler(new class extends aV{constructor(){super(),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:tj()}),Object.assign(this,e)}}),t}static configure(e,t,a,r,i,n,s){return this._configureSync(e,t,a,r,i,n,s)}static _configureSync(e,t,a,r,i,n,s){let o;(e||t)&&(Array.isArray(e)||!e?(o=new rc).setHandlers(e?.map(rh)??[],!0):o=e,o=o.copy(Array.isArray(t)?t.map(rh):t?.handlers,!1));let l="true"===az("LANGCHAIN_VERBOSE")||s?.verbose,d=a5.getTraceableRunTree()?.tracingEnabled||rt(),u=d||(az("LANGCHAIN_TRACING")??!1);if(l||u){if(o||(o=new rc),l&&!o.handlers.some(e=>e.name===a9.prototype.name)){let e=new a9;o.addHandler(e,!0)}if(u&&!o.handlers.some(e=>"langchain_tracer"===e.name)&&d){let e=new a5;o.addHandler(e,!0),o._parentRunId=a5.getTraceableRunTree()?.id??o._parentRunId}}for(let{contextVar:e,inheritable:t=!0,handlerClass:a,envVar:r}of ri()){let i,n=r&&"true"===az(r)&&a,s=void 0!==e?ra(e):void 0;s&&aq(s)?i=s:n&&(i=new a({})),void 0!==i&&(o||(o=new rc),o.handlers.some(e=>e.name===i.name)||o.addHandler(i,t))}return(a||r)&&o&&(o.addTags(a??[]),o.addTags(r??[],!1)),(i||n)&&o&&(o.addMetadata(i??{}),o.addMetadata(n??{},!1)),o}}function rh(e){return"name"in e?e:aV.fromMethods(e)}let rp=new class{getStore(){}run(e,t){return t()}enterWith(e){}},rm=Symbol.for("lc:child_config"),rf=new class{getInstance(){return a8()??rp}getRunnableConfig(){let e=this.getInstance();return e.getStore()?.extra?.[rm]}runWithConfig(e,t,a){let r,i=rc._configureSync(e?.callbacks,void 0,e?.tags,void 0,e?.metadata),n=this.getInstance(),s=n.getStore(),o=i?.getParentRunId(),l=i?.handlers?.find(e=>e?.name==="langchain_tracer");return l&&o?r=l.convertToRunTree(o):a||(r=new al({name:"<runnable_lambda>",tracingEnabled:!1})),r&&(r.extra={...r.extra,[rm]:e}),void 0!==s&&void 0!==s[a3]&&(r[a3]=s[a3]),n.run(r,t)}initializeGlobalInstance(e){void 0===a8()&&a7(e)}};async function rg(e){return rc._configureSync(e?.callbacks,void 0,e?.tags,void 0,e?.metadata)}function ry(...e){let t={};for(let a of e.filter(e=>!!e))for(let e of Object.keys(a))if("metadata"===e)t[e]={...t[e],...a[e]};else if("tags"===e){let r=t[e]??[];t[e]=[...new Set(r.concat(a[e]??[]))]}else if("configurable"===e)t[e]={...t[e],...a[e]};else if("timeout"===e)void 0===t.timeout?t.timeout=a.timeout:void 0!==a.timeout&&(t.timeout=Math.min(t.timeout,a.timeout));else if("signal"===e)void 0===t.signal?t.signal=a.signal:void 0!==a.signal&&("any"in AbortSignal?t.signal=AbortSignal.any([t.signal,a.signal]):t.signal=a.signal);else if("callbacks"===e){let e=t.callbacks,r=a.callbacks;if(Array.isArray(r))if(e)if(Array.isArray(e))t.callbacks=e.concat(r);else{let a=e.copy();for(let e of r)a.addHandler(rh(e),!0);t.callbacks=a}else t.callbacks=r;else if(r)if(e)if(Array.isArray(e)){let a=r.copy();for(let t of e)a.addHandler(rh(t),!0);t.callbacks=a}else t.callbacks=new rc(r._parentRunId,{handlers:e.handlers.concat(r.handlers),inheritableHandlers:e.inheritableHandlers.concat(r.inheritableHandlers),tags:Array.from(new Set(e.tags.concat(r.tags))),inheritableTags:Array.from(new Set(e.inheritableTags.concat(r.inheritableTags))),metadata:{...e.metadata,...r.metadata}});else t.callbacks=r}else t[e]=a[e]??t[e];return t}let r_=new Set(["string","number","boolean"]);function rb(e){let t=rf.getRunnableConfig(),a={tags:[],metadata:{},recursionLimit:25,runId:void 0};if(t){let{runId:e,runName:r,...i}=t;a=Object.entries(i).reduce((e,[t,a])=>(void 0!==a&&(e[t]=a),e),a)}if(e&&(a=Object.entries(e).reduce((e,[t,a])=>(void 0!==a&&(e[t]=a),e),a)),a?.configurable)for(let e of Object.keys(a.configurable))r_.has(typeof a.configurable[e])&&!a.metadata?.[e]&&(a.metadata||(a.metadata={}),a.metadata[e]=a.configurable[e]);if(void 0!==a.timeout){if(a.timeout<=0)throw Error("Timeout must be a positive number");let e=AbortSignal.timeout(a.timeout);void 0!==a.signal?"any"in AbortSignal&&(a.signal=AbortSignal.any([a.signal,e])):a.signal=e,delete a.timeout}return a}function rv(e={},{callbacks:t,maxConcurrency:a,recursionLimit:r,runName:i,configurable:n,runId:s}={}){let o=rb(e);return void 0!==t&&(delete o.runName,o.callbacks=t),void 0!==r&&(o.recursionLimit=r),void 0!==a&&(o.maxConcurrency=a),void 0!==i&&(o.runName=i),void 0!==n&&(o.configurable={...o.configurable,...n}),void 0!==s&&delete o.runId,o}function rw(e){return e?{configurable:e.configurable,recursionLimit:e.recursionLimit,callbacks:e.callbacks,tags:e.tags,metadata:e.metadata,maxConcurrency:e.maxConcurrency,timeout:e.timeout,signal:e.signal}:void 0}async function rO(e,t){let a;return void 0===t?e:Promise.race([e.catch(e=>{if(!t?.aborted)throw e}),new Promise((e,r)=>{a=()=>{r(Error("Aborted"))},t.addEventListener("abort",a),t.aborted&&r(Error("Aborted"))})]).finally(()=>t.removeEventListener("abort",a))}class rk extends ReadableStream{constructor(){super(...arguments),Object.defineProperty(this,"reader",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}ensureReader(){this.reader||(this.reader=this.getReader())}async next(){this.ensureReader();try{let e=await this.reader.read();if(e.done)return this.reader.releaseLock(),{done:!0,value:void 0};return{done:!1,value:e.value}}catch(e){throw this.reader.releaseLock(),e}}async return(){if(this.ensureReader(),this.locked){let e=this.reader.cancel();this.reader.releaseLock(),await e}return{done:!0,value:void 0}}async throw(e){if(this.ensureReader(),this.locked){let e=this.reader.cancel();this.reader.releaseLock(),await e}throw e}[Symbol.asyncIterator](){return this}async [Symbol.asyncDispose](){await this.return()}static fromReadableStream(e){let t=e.getReader();return new rk({start:e=>(function a(){return t.read().then(({done:t,value:r})=>{if(t){e.close();return}return e.enqueue(r),a()})})(),cancel(){t.releaseLock()}})}static fromAsyncGenerator(e){return new rk({async pull(t){let{value:a,done:r}=await e.next();r&&t.close(),t.enqueue(a)},async cancel(t){await e.return(t)}})}}function rE(e,t=2){let a=Array.from({length:t},()=>[]);return a.map(async function*(t){for(;;)if(0===t.length){let t=await e.next();for(let e of a)e.push(t)}else{if(t[0].done)return;yield t.shift().value}})}function rS(e,t){if(Array.isArray(e)&&Array.isArray(t))return e.concat(t);if("string"==typeof e&&"string"==typeof t)return e+t;if("number"==typeof e&&"number"==typeof t)return e+t;if("concat"in e&&"function"==typeof e.concat)return e.concat(t);if("object"==typeof e&&"object"==typeof t){let a={...e};for(let[e,r]of Object.entries(t))e in a&&!Array.isArray(a[e])?a[e]=rS(a[e],r):a[e]=r;return a}else throw Error(`Cannot concat ${typeof e} and ${typeof t}`)}class rx{constructor(e){Object.defineProperty(this,"generator",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"setup",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"signal",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"firstResult",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"firstResultUsed",{enumerable:!0,configurable:!0,writable:!0,value:!1}),this.generator=e.generator,this.config=e.config,this.signal=e.signal??this.config?.signal,this.setup=new Promise((t,a)=>{rf.runWithConfig(rw(e.config),async()=>{this.firstResult=e.generator.next(),e.startSetup?this.firstResult.then(e.startSetup).then(t,a):this.firstResult.then(e=>t(void 0),a)},!0)})}async next(...e){return(this.signal?.throwIfAborted(),this.firstResultUsed)?rf.runWithConfig(rw(this.config),this.signal?async()=>rO(this.generator.next(...e),this.signal):async()=>this.generator.next(...e),!0):(this.firstResultUsed=!0,this.firstResult)}async return(e){return this.generator.return(e)}async throw(e){return this.generator.throw(e)}[Symbol.asyncIterator](){return this}async [Symbol.asyncDispose](){await this.return()}}async function rj(e,t,a,r,...i){let n=new rx({generator:t,startSetup:a,signal:r}),s=await n.setup;return{output:e(n,s,...i),setup:s}}var rT=a(89590);class rP{constructor(e){Object.defineProperty(this,"ops",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.ops=e.ops??[]}concat(e){let t=this.ops.concat(e.ops),a=aP({},t);return new rA({ops:t,state:a[a.length-1].newDocument})}}class rA extends rP{constructor(e){super(e),Object.defineProperty(this,"state",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.state=e.state}concat(e){let t=this.ops.concat(e.ops),a=aP(this.state,e.ops);return new rA({ops:t,state:a[a.length-1].newDocument})}static fromRunLogPatch(e){let t=aP({},e.ops);return new rA({ops:e.ops,state:t[t.length-1].newDocument})}}let rI=e=>"log_stream_tracer"===e.name;async function rC(e,t){if("original"===t)throw Error("Do not assign inputs with original schema drop the key for now. When inputs are added to streamLog they should be added with standardized schema for streaming events.");let{inputs:a}=e;return["retriever","llm","prompt"].includes(e.run_type)?a:1!==Object.keys(a).length||a?.input!==""?a.input:void 0}async function r$(e,t){let{outputs:a}=e;return"original"===t||["retriever","llm","prompt"].includes(e.run_type)?a:void 0!==a&&1===Object.keys(a).length&&a?.output!==void 0?a.output:a}class rN extends aW{constructor(e){super({_awaitHandler:!0,...e}),Object.defineProperty(this,"autoClose",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"includeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_schemaFormat",{enumerable:!0,configurable:!0,writable:!0,value:"original"}),Object.defineProperty(this,"rootId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"keyMapByRunId",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"counterMapByRunName",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"transformStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"writer",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"receiveStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"log_stream_tracer"}),Object.defineProperty(this,"lc_prefer_streaming",{enumerable:!0,configurable:!0,writable:!0,value:!0}),this.autoClose=e?.autoClose??!0,this.includeNames=e?.includeNames,this.includeTypes=e?.includeTypes,this.includeTags=e?.includeTags,this.excludeNames=e?.excludeNames,this.excludeTypes=e?.excludeTypes,this.excludeTags=e?.excludeTags,this._schemaFormat=e?._schemaFormat??this._schemaFormat,this.transformStream=new TransformStream,this.writer=this.transformStream.writable.getWriter(),this.receiveStream=rk.fromReadableStream(this.transformStream.readable)}[Symbol.asyncIterator](){return this.receiveStream}async persistRun(e){}_includeRun(e){if(e.id===this.rootId)return!1;let t=e.tags??[],a=void 0===this.includeNames&&void 0===this.includeTags&&void 0===this.includeTypes;return void 0!==this.includeNames&&(a=a||this.includeNames.includes(e.name)),void 0!==this.includeTypes&&(a=a||this.includeTypes.includes(e.run_type)),void 0!==this.includeTags&&(a=a||void 0!==t.find(e=>this.includeTags?.includes(e))),void 0!==this.excludeNames&&(a=a&&!this.excludeNames.includes(e.name)),void 0!==this.excludeTypes&&(a=a&&!this.excludeTypes.includes(e.run_type)),void 0!==this.excludeTags&&(a=a&&t.every(e=>!this.excludeTags?.includes(e))),a}async *tapOutputIterable(e,t){for await(let a of t){if(e!==this.rootId){let t=this.keyMapByRunId[e];t&&await this.writer.write(new rP({ops:[{op:"add",path:`/logs/${t}/streamed_output/-`,value:a}]}))}yield a}}async onRunCreate(e){if(void 0===this.rootId&&(this.rootId=e.id,await this.writer.write(new rP({ops:[{op:"replace",path:"",value:{id:e.id,name:e.name,type:e.run_type,streamed_output:[],final_output:void 0,logs:{}}}]}))),!this._includeRun(e))return;void 0===this.counterMapByRunName[e.name]&&(this.counterMapByRunName[e.name]=0),this.counterMapByRunName[e.name]+=1;let t=this.counterMapByRunName[e.name];this.keyMapByRunId[e.id]=1===t?e.name:`${e.name}:${t}`;let a={id:e.id,name:e.name,type:e.run_type,tags:e.tags??[],metadata:e.extra?.metadata??{},start_time:new Date(e.start_time).toISOString(),streamed_output:[],streamed_output_str:[],final_output:void 0,end_time:void 0};"streaming_events"===this._schemaFormat&&(a.inputs=await rC(e,this._schemaFormat)),await this.writer.write(new rP({ops:[{op:"add",path:`/logs/${this.keyMapByRunId[e.id]}`,value:a}]}))}async onRunUpdate(e){try{let t=this.keyMapByRunId[e.id];if(void 0===t)return;let a=[];"streaming_events"===this._schemaFormat&&a.push({op:"replace",path:`/logs/${t}/inputs`,value:await rC(e,this._schemaFormat)}),a.push({op:"add",path:`/logs/${t}/final_output`,value:await r$(e,this._schemaFormat)}),void 0!==e.end_time&&a.push({op:"add",path:`/logs/${t}/end_time`,value:new Date(e.end_time).toISOString()});let r=new rP({ops:a});await this.writer.write(r)}finally{if(e.id===this.rootId){let t=new rP({ops:[{op:"replace",path:"/final_output",value:await r$(e,this._schemaFormat)}]});await this.writer.write(t),this.autoClose&&await this.writer.close()}}}async onLLMNewToken(e,t,a){let r,i=this.keyMapByRunId[e.id];if(void 0===i)return;if(void 0!==e.inputs.messages){var n;r=void 0!==(n=a?.chunk)&&void 0!==n.message?a?.chunk:new rT.GC({id:`run-${e.id}`,content:t})}else r=t;let s=new rP({ops:[{op:"add",path:`/logs/${i}/streamed_output_str/-`,value:t},{op:"add",path:`/logs/${i}/streamed_output/-`,value:r}]});await this.writer.write(s)}}class rR{constructor(e){Object.defineProperty(this,"text",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"generationInfo",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.text=e.text,this.generationInfo=e.generationInfo}concat(e){return new rR({text:this.text+e.text,generationInfo:{...this.generationInfo,...e.generationInfo}})}}function rM({name:e,serialized:t}){return void 0!==e?e:t?.name!==void 0?t.name:t?.id!==void 0&&Array.isArray(t?.id)?t.id[t.id.length-1]:"Unnamed"}let rL=e=>"event_stream_tracer"===e.name;class rU extends aW{constructor(e){super({_awaitHandler:!0,...e}),Object.defineProperty(this,"autoClose",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"includeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"runInfoMap",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"tappedPromises",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"transformStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"writer",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"receiveStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"event_stream_tracer"}),Object.defineProperty(this,"lc_prefer_streaming",{enumerable:!0,configurable:!0,writable:!0,value:!0}),this.autoClose=e?.autoClose??!0,this.includeNames=e?.includeNames,this.includeTypes=e?.includeTypes,this.includeTags=e?.includeTags,this.excludeNames=e?.excludeNames,this.excludeTypes=e?.excludeTypes,this.excludeTags=e?.excludeTags,this.transformStream=new TransformStream,this.writer=this.transformStream.writable.getWriter(),this.receiveStream=rk.fromReadableStream(this.transformStream.readable)}[Symbol.asyncIterator](){return this.receiveStream}async persistRun(e){}_includeRun(e){let t=e.tags??[],a=void 0===this.includeNames&&void 0===this.includeTags&&void 0===this.includeTypes;return void 0!==this.includeNames&&(a=a||this.includeNames.includes(e.name)),void 0!==this.includeTypes&&(a=a||this.includeTypes.includes(e.runType)),void 0!==this.includeTags&&(a=a||void 0!==t.find(e=>this.includeTags?.includes(e))),void 0!==this.excludeNames&&(a=a&&!this.excludeNames.includes(e.name)),void 0!==this.excludeTypes&&(a=a&&!this.excludeTypes.includes(e.runType)),void 0!==this.excludeTags&&(a=a&&t.every(e=>!this.excludeTags?.includes(e))),a}async *tapOutputIterable(e,t){let a=await t.next();if(a.done)return;let r=this.runInfoMap.get(e);if(void 0===r){yield a.value;return}function i(e,t){return"llm"===e&&"string"==typeof t?new rR({text:t}):t}let n=this.tappedPromises.get(e);if(void 0===n){let s;n=new Promise(e=>{s=e}),this.tappedPromises.set(e,n);try{let n={event:`on_${r.runType}_stream`,run_id:e,name:r.name,tags:r.tags,metadata:r.metadata,data:{}};for await(let e of(await this.send({...n,data:{chunk:i(r.runType,a.value)}},r),yield a.value,t))"tool"!==r.runType&&"retriever"!==r.runType&&await this.send({...n,data:{chunk:i(r.runType,e)}},r),yield e}finally{s()}}else for await(let e of(yield a.value,t))yield e}async send(e,t){this._includeRun(t)&&await this.writer.write(e)}async sendEndEvent(e,t){let a=this.tappedPromises.get(e.run_id);void 0!==a?a.then(()=>{this.send(e,t)}):await this.send(e,t)}async onLLMStart(e){let t=rM(e),a=void 0!==e.inputs.messages?"chat_model":"llm",r={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:a,inputs:e.inputs};this.runInfoMap.set(e.id,r);let i=`on_${a}_start`;await this.send({event:i,data:{input:e.inputs},name:t,tags:e.tags??[],run_id:e.id,metadata:e.extra?.metadata??{}},r)}async onLLMNewToken(e,t,a){let r,i,n=this.runInfoMap.get(e.id);if(void 0===n)throw Error(`onLLMNewToken: Run ID ${e.id} not found in run map.`);if(1!==this.runInfoMap.size){if("chat_model"===n.runType)i="on_chat_model_stream",r=a?.chunk===void 0?new rT.GC({content:t,id:`run-${e.id}`}):a.chunk.message;else if("llm"===n.runType)i="on_llm_stream",r=a?.chunk===void 0?new rR({text:t}):a.chunk;else throw Error(`Unexpected run type ${n.runType}`);await this.send({event:i,data:{chunk:r},run_id:e.id,name:n.name,tags:n.tags,metadata:n.metadata},n)}}async onLLMEnd(e){let t,a,r=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===r)throw Error(`onLLMEnd: Run ID ${e.id} not found in run map.`);let i=e.outputs?.generations;if("chat_model"===r.runType){for(let e of i??[]){if(void 0!==a)break;a=e[0]?.message}t="on_chat_model_end"}else if("llm"===r.runType)a={generations:i?.map(e=>e.map(e=>({text:e.text,generationInfo:e.generationInfo}))),llmOutput:e.outputs?.llmOutput??{}},t="on_llm_end";else throw Error(`onLLMEnd: Unexpected run type: ${r.runType}`);await this.sendEndEvent({event:t,data:{output:a,input:r.inputs},run_id:e.id,name:r.name,tags:r.tags,metadata:r.metadata},r)}async onChainStart(e){let t=rM(e),a=e.run_type??"chain",r={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:e.run_type},i={};""===e.inputs.input&&1===Object.keys(e.inputs).length?(i={},r.inputs={}):void 0!==e.inputs.input?(i.input=e.inputs.input,r.inputs=e.inputs.input):(i.input=e.inputs,r.inputs=e.inputs),this.runInfoMap.set(e.id,r),await this.send({event:`on_${a}_start`,data:i,name:t,tags:e.tags??[],run_id:e.id,metadata:e.extra?.metadata??{}},r)}async onChainEnd(e){let t=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===t)throw Error(`onChainEnd: Run ID ${e.id} not found in run map.`);let a=`on_${e.run_type}_end`,r=e.inputs??t.inputs??{},i={output:e.outputs?.output??e.outputs,input:r};r.input&&1===Object.keys(r).length&&(i.input=r.input,t.inputs=r.input),await this.sendEndEvent({event:a,data:i,run_id:e.id,name:t.name,tags:t.tags,metadata:t.metadata??{}},t)}async onToolStart(e){let t=rM(e),a={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:"tool",inputs:e.inputs??{}};this.runInfoMap.set(e.id,a),await this.send({event:"on_tool_start",data:{input:e.inputs??{}},name:t,run_id:e.id,tags:e.tags??[],metadata:e.extra?.metadata??{}},a)}async onToolEnd(e){let t=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===t)throw Error(`onToolEnd: Run ID ${e.id} not found in run map.`);if(void 0===t.inputs)throw Error(`onToolEnd: Run ID ${e.id} is a tool call, and is expected to have traced inputs.`);let a=e.outputs?.output===void 0?e.outputs:e.outputs.output;await this.sendEndEvent({event:"on_tool_end",data:{output:a,input:t.inputs},run_id:e.id,name:t.name,tags:t.tags,metadata:t.metadata},t)}async onRetrieverStart(e){let t=rM(e),a={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:"retriever",inputs:{query:e.inputs.query}};this.runInfoMap.set(e.id,a),await this.send({event:"on_retriever_start",data:{input:{query:e.inputs.query}},name:t,tags:e.tags??[],run_id:e.id,metadata:e.extra?.metadata??{}},a)}async onRetrieverEnd(e){let t=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===t)throw Error(`onRetrieverEnd: Run ID ${e.id} not found in run map.`);await this.sendEndEvent({event:"on_retriever_end",data:{output:e.outputs?.documents??e.outputs,input:t.inputs},run_id:e.id,name:t.name,tags:t.tags,metadata:t.metadata},t)}async handleCustomEvent(e,t,a){let r=this.runInfoMap.get(a);if(void 0===r)throw Error(`handleCustomEvent: Run ID ${a} not found in run map.`);await this.send({event:"on_custom_event",run_id:a,name:e,tags:r.tags,metadata:r.metadata,data:t},r)}async finish(){Promise.all([...this.tappedPromises.values()]).finally(()=>{this.writer.close()})}}let rZ=[400,401,402,403,404,405,406,407,409],rD=e=>{if(e.message.startsWith("Cancel")||e.message.startsWith("AbortError")||"AbortError"===e.name||e?.code==="ECONNABORTED")throw e;let t=e?.response?.status??e?.status;if(t&&rZ.includes(+t))throw e;if(e?.error?.code==="insufficient_quota"){let t=Error(e?.message);throw t.name="InsufficientQuotaError",t}};class rF{constructor(e){Object.defineProperty(this,"maxConcurrency",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"maxRetries",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"onFailedAttempt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"queue",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxConcurrency=e.maxConcurrency??1/0,this.maxRetries=e.maxRetries??6,this.onFailedAttempt=e.onFailedAttempt??rD;let t="default"in tT?tT.default:tT;this.queue=new t({concurrency:this.maxConcurrency})}call(e,...t){return this.queue.add(()=>tO(()=>e(...t).catch(e=>{if(e instanceof Error)throw e;throw Error(e)}),{onFailedAttempt:this.onFailedAttempt,retries:this.maxRetries,randomize:!0}),{throwOnTimeout:!0})}callWithOptions(e,t,...a){return e.signal?Promise.race([this.call(t,...a),new Promise((t,a)=>{e.signal?.addEventListener("abort",()=>{a(Error("AbortError"))})})]):this.call(t,...a)}fetch(...e){return this.call(()=>fetch(...e).then(e=>e.ok?e:Promise.reject(e)))}}class rH extends aW{constructor({config:e,onStart:t,onEnd:a,onError:r}){super({_awaitHandler:!0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"RootListenersTracer"}),Object.defineProperty(this,"rootId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"argOnStart",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"argOnEnd",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"argOnError",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.config=e,this.argOnStart=t,this.argOnEnd=a,this.argOnError=r}persistRun(e){return Promise.resolve()}async onRunCreate(e){!this.rootId&&(this.rootId=e.id,this.argOnStart&&await this.argOnStart(e,this.config))}async onRunUpdate(e){e.id===this.rootId&&(e.error?this.argOnError&&await this.argOnError(e,this.config):this.argOnEnd&&await this.argOnEnd(e,this.config))}}function rz(e){return!!e&&e.lc_runnable}class rB{constructor(e){Object.defineProperty(this,"includeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.includeNames=e.includeNames,this.includeTypes=e.includeTypes,this.includeTags=e.includeTags,this.excludeNames=e.excludeNames,this.excludeTypes=e.excludeTypes,this.excludeTags=e.excludeTags}includeEvent(e,t){let a=void 0===this.includeNames&&void 0===this.includeTypes&&void 0===this.includeTags,r=e.tags??[];return void 0!==this.includeNames&&(a=a||this.includeNames.includes(e.name)),void 0!==this.includeTypes&&(a=a||this.includeTypes.includes(t)),void 0!==this.includeTags&&(a=a||r.some(e=>this.includeTags?.includes(e))),void 0!==this.excludeNames&&(a=a&&!this.excludeNames.includes(e.name)),void 0!==this.excludeTypes&&(a=a&&!this.excludeTypes.includes(t)),void 0!==this.excludeTags&&(a=a&&r.every(e=>!this.excludeTags?.includes(e))),a}}let rV=Symbol("Let zodToJsonSchema decide on which parser to use"),rq={name:void 0,$refStrategy:"root",basePath:["#"],effectStrategy:"input",pipeStrategy:"all",dateStrategy:"format:date-time",mapStrategy:"entries",removeAdditionalStrategy:"passthrough",definitionPath:"definitions",target:"jsonSchema7",strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:"escape",applyRegexFlags:!1,emailStrategy:"format:email",base64Strategy:"contentEncoding:base64",nameStrategy:"ref"},rG=e=>"string"==typeof e?{...rq,name:e}:{...rq,...e},rJ=e=>{let t=rG(e),a=void 0!==t.name?[...t.basePath,t.definitionPath,t.name]:t.basePath;return{...t,currentPath:a,propertyPath:void 0,seen:new Map(Object.entries(t.definitions).map(([e,a])=>[a._def,{def:a._def,path:[...t.basePath,t.definitionPath,e],jsonSchema:void 0}]))}};function rW(e,t,a,r){r?.errorMessages&&a&&(e.errorMessage={...e.errorMessage,[t]:a})}function rK(e,t,a,r,i){e[t]=a,rW(e,t,r,i)}function rQ(e,t){return ii(e.type._def,t)}let rY=(e,t)=>ii(e.innerType._def,t),rX=(e,t)=>{let a={type:"integer",format:"unix-time"};if("openApi3"===t.target)return a;for(let r of e.checks)switch(r.kind){case"min":rK(a,"minimum",r.value,r.message,t);break;case"max":rK(a,"maximum",r.value,r.message,t)}return a},r0=e=>(!("type"in e)||"string"!==e.type)&&"allOf"in e,r1={cuid:/^[cC][^\s-]{8,}$/,cuid2:/^[0-9a-z]+$/,ulid:/^[0-9A-HJKMNP-TV-Z]{26}$/,email:/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,emoji:()=>(void 0===u&&(u=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),u),ipv4Cidr:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6Cidr:/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64:/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64url:/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,nanoid:/^[a-zA-Z0-9_-]{21}$/,jwt:/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/};function r9(e,t){let a={type:"string"};if(e.checks)for(let r of e.checks)switch(r.kind){case"min":rK(a,"minLength","number"==typeof a.minLength?Math.max(a.minLength,r.value):r.value,r.message,t);break;case"max":rK(a,"maxLength","number"==typeof a.maxLength?Math.min(a.maxLength,r.value):r.value,r.message,t);break;case"email":switch(t.emailStrategy){case"format:email":r5(a,"email",r.message,t);break;case"format:idn-email":r5(a,"idn-email",r.message,t);break;case"pattern:zod":r6(a,r1.email,r.message,t)}break;case"url":r5(a,"uri",r.message,t);break;case"uuid":r5(a,"uuid",r.message,t);break;case"regex":r6(a,r.regex,r.message,t);break;case"cuid":r6(a,r1.cuid,r.message,t);break;case"cuid2":r6(a,r1.cuid2,r.message,t);break;case"startsWith":r6(a,RegExp(`^${r4(r.value,t)}`),r.message,t);break;case"endsWith":r6(a,RegExp(`${r4(r.value,t)}$`),r.message,t);break;case"datetime":r5(a,"date-time",r.message,t);break;case"date":r5(a,"date",r.message,t);break;case"time":r5(a,"time",r.message,t);break;case"duration":r5(a,"duration",r.message,t);break;case"length":rK(a,"minLength","number"==typeof a.minLength?Math.max(a.minLength,r.value):r.value,r.message,t),rK(a,"maxLength","number"==typeof a.maxLength?Math.min(a.maxLength,r.value):r.value,r.message,t);break;case"includes":r6(a,RegExp(r4(r.value,t)),r.message,t);break;case"ip":"v6"!==r.version&&r5(a,"ipv4",r.message,t),"v4"!==r.version&&r5(a,"ipv6",r.message,t);break;case"base64url":r6(a,r1.base64url,r.message,t);break;case"jwt":r6(a,r1.jwt,r.message,t);break;case"cidr":"v6"!==r.version&&r6(a,r1.ipv4Cidr,r.message,t),"v4"!==r.version&&r6(a,r1.ipv6Cidr,r.message,t);break;case"emoji":r6(a,r1.emoji(),r.message,t);break;case"ulid":r6(a,r1.ulid,r.message,t);break;case"base64":switch(t.base64Strategy){case"format:binary":r5(a,"binary",r.message,t);break;case"contentEncoding:base64":rK(a,"contentEncoding","base64",r.message,t);break;case"pattern:zod":r6(a,r1.base64,r.message,t)}break;case"nanoid":r6(a,r1.nanoid,r.message,t)}return a}function r4(e,t){return"escape"===t.patternStrategy?function(e){let t="";for(let a=0;a<e.length;a++)r2.has(e[a])||(t+="\\"),t+=e[a];return t}(e):e}let r2=new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");function r5(e,t,a,r){e.format||e.anyOf?.some(e=>e.format)?(e.anyOf||(e.anyOf=[]),e.format&&(e.anyOf.push({format:e.format,...e.errorMessage&&r.errorMessages&&{errorMessage:{format:e.errorMessage.format}}}),delete e.format,e.errorMessage&&(delete e.errorMessage.format,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.anyOf.push({format:t,...a&&r.errorMessages&&{errorMessage:{format:a}}})):rK(e,"format",t,a,r)}function r6(e,t,a,r){e.pattern||e.allOf?.some(e=>e.pattern)?(e.allOf||(e.allOf=[]),e.pattern&&(e.allOf.push({pattern:e.pattern,...e.errorMessage&&r.errorMessages&&{errorMessage:{pattern:e.errorMessage.pattern}}}),delete e.pattern,e.errorMessage&&(delete e.errorMessage.pattern,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.allOf.push({pattern:r3(t,r),...a&&r.errorMessages&&{errorMessage:{pattern:a}}})):rK(e,"pattern",r3(t,r),a,r)}function r3(e,t){if(!t.applyRegexFlags||!e.flags)return e.source;let a={i:e.flags.includes("i"),m:e.flags.includes("m"),s:e.flags.includes("s")},r=a.i?e.source.toLowerCase():e.source,i="",n=!1,s=!1,o=!1;for(let e=0;e<r.length;e++){if(n){i+=r[e],n=!1;continue}if(a.i){if(s){if(r[e].match(/[a-z]/)){o?(i+=r[e],i+=`${r[e-2]}-${r[e]}`.toUpperCase(),o=!1):"-"===r[e+1]&&r[e+2]?.match(/[a-z]/)?(i+=r[e],o=!0):i+=`${r[e]}${r[e].toUpperCase()}`;continue}}else if(r[e].match(/[a-z]/)){i+=`[${r[e]}${r[e].toUpperCase()}]`;continue}}if(a.m){if("^"===r[e]){i+=`(^|(?<=[\r
]))`;continue}else if("$"===r[e]){i+=`($|(?=[\r
]))`;continue}}if(a.s&&"."===r[e]){i+=s?`${r[e]}\r
`:`[${r[e]}\r
]`;continue}i+=r[e],"\\"===r[e]?n=!0:s&&"]"===r[e]?s=!1:s||"["!==r[e]||(s=!0)}try{new RegExp(i)}catch{return console.warn(`Could not convert regex pattern at ${t.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`),e.source}return i}function r7(e,t){if("openAi"===t.target&&console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead."),"openApi3"===t.target&&e.keyType?._def.typeName===b.ZodEnum)return{type:"object",required:e.keyType._def.values,properties:e.keyType._def.values.reduce((a,r)=>({...a,[r]:ii(e.valueType._def,{...t,currentPath:[...t.currentPath,"properties",r]})??{}}),{}),additionalProperties:!1};let a={type:"object",additionalProperties:ii(e.valueType._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]})??{}};if("openApi3"===t.target)return a;if(e.keyType?._def.typeName===b.ZodString&&e.keyType._def.checks?.length){let{type:r,...i}=r9(e.keyType._def,t);return{...a,propertyNames:i}}if(e.keyType?._def.typeName===b.ZodEnum)return{...a,propertyNames:{enum:e.keyType._def.values}};if(e.keyType?._def.typeName===b.ZodBranded&&e.keyType._def.type._def.typeName===b.ZodString&&e.keyType._def.type._def.checks?.length){let{type:r,...i}=rQ(e.keyType._def,t);return{...a,propertyNames:i}}return a}let r8={ZodString:"string",ZodNumber:"number",ZodBigInt:"integer",ZodBoolean:"boolean",ZodNull:"null"},ie=(e,t)=>{let a=(e.options instanceof Map?Array.from(e.options.values()):e.options).map((e,a)=>ii(e._def,{...t,currentPath:[...t.currentPath,"anyOf",`${a}`]})).filter(e=>!!e&&(!t.strictUnions||"object"==typeof e&&Object.keys(e).length>0));return a.length?{anyOf:a}:void 0},it=(e,t)=>{if(t.currentPath.toString()===t.propertyPath?.toString())return ii(e.innerType._def,t);let a=ii(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","1"]});return a?{anyOf:[{not:{}},a]}:{}},ia=(e,t)=>{if("input"===t.pipeStrategy)return ii(e.in._def,t);if("output"===t.pipeStrategy)return ii(e.out._def,t);let a=ii(e.in._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),r=ii(e.out._def,{...t,currentPath:[...t.currentPath,"allOf",a?"1":"0"]});return{allOf:[a,r].filter(e=>void 0!==e)}},ir=(e,t)=>ii(e.innerType._def,t);function ii(e,t,a=!1){let r=t.seen.get(e);if(t.override){let i=t.override?.(e,t,r,a);if(i!==rV)return i}if(r&&!a){let e=is(r,t);if(void 0!==e)return e}let i={def:e,path:t.currentPath,jsonSchema:void 0};t.seen.set(e,i);let n=il(e,e.typeName,t);return n&&id(e,t,n),i.jsonSchema=n,n}let is=(e,t)=>{switch(t.$refStrategy){case"root":return{$ref:e.path.join("/")};case"relative":return{$ref:io(t.currentPath,e.path)};case"none":case"seen":if(e.path.length<t.currentPath.length&&e.path.every((e,a)=>t.currentPath[a]===e))return console.warn(`Recursive reference detected at ${t.currentPath.join("/")}! Defaulting to any`),{};return"seen"===t.$refStrategy?{}:void 0}},io=(e,t)=>{let a=0;for(;a<e.length&&a<t.length&&e[a]===t[a];a++);return[(e.length-a).toString(),...t.slice(a)].join("/")},il=(e,t,a)=>{switch(t){case b.ZodString:return r9(e,a);case b.ZodNumber:let r={type:"number"};if(!e.checks)return r;for(let t of e.checks)switch(t.kind){case"int":r.type="integer",rW(r,"type",t.message,a);break;case"min":"jsonSchema7"===a.target?t.inclusive?rK(r,"minimum",t.value,t.message,a):rK(r,"exclusiveMinimum",t.value,t.message,a):(t.inclusive||(r.exclusiveMinimum=!0),rK(r,"minimum",t.value,t.message,a));break;case"max":"jsonSchema7"===a.target?t.inclusive?rK(r,"maximum",t.value,t.message,a):rK(r,"exclusiveMaximum",t.value,t.message,a):(t.inclusive||(r.exclusiveMaximum=!0),rK(r,"maximum",t.value,t.message,a));break;case"multipleOf":rK(r,"multipleOf",t.value,t.message,a)}return r;case b.ZodObject:let i="openAi"===a.target,n={type:"object",...Object.entries(e.shape()).reduce((e,[t,r])=>{if(void 0===r||void 0===r._def)return e;let n=r.isOptional();n&&i&&(r instanceof eZ&&(r=r._def.innerType),r.isNullable()||(r=r.nullable()),n=!1);let s=ii(r._def,{...a,currentPath:[...a.currentPath,"properties",t],propertyPath:[...a.currentPath,"properties",t]});return void 0===s?e:{properties:{...e.properties,[t]:s},required:n?e.required:[...e.required,t]}},{properties:{},required:[]}),additionalProperties:"strict"===a.removeAdditionalStrategy?"ZodNever"===e.catchall._def.typeName?"strict"!==e.unknownKeys:ii(e.catchall._def,{...a,currentPath:[...a.currentPath,"additionalProperties"]})??!0:"ZodNever"===e.catchall._def.typeName?"passthrough"===e.unknownKeys:ii(e.catchall._def,{...a,currentPath:[...a.currentPath,"additionalProperties"]})??!0};return n.required.length||delete n.required,n;case b.ZodBigInt:let s={type:"integer",format:"int64"};if(!e.checks)return s;for(let t of e.checks)switch(t.kind){case"min":"jsonSchema7"===a.target?t.inclusive?rK(s,"minimum",t.value,t.message,a):rK(s,"exclusiveMinimum",t.value,t.message,a):(t.inclusive||(s.exclusiveMinimum=!0),rK(s,"minimum",t.value,t.message,a));break;case"max":"jsonSchema7"===a.target?t.inclusive?rK(s,"maximum",t.value,t.message,a):rK(s,"exclusiveMaximum",t.value,t.message,a):(t.inclusive||(s.exclusiveMaximum=!0),rK(s,"maximum",t.value,t.message,a));break;case"multipleOf":rK(s,"multipleOf",t.value,t.message,a)}return s;case b.ZodBoolean:return{type:"boolean"};case b.ZodDate:return function e(t,a,r){let i=r??a.dateStrategy;if(Array.isArray(i))return{anyOf:i.map((r,i)=>e(t,a,r))};switch(i){case"string":case"format:date-time":return{type:"string",format:"date-time"};case"format:date":return{type:"string",format:"date"};case"integer":return rX(t,a)}}(e,a);case b.ZodUndefined:return{not:{}};case b.ZodNull:return"openApi3"===a.target?{enum:["null"],nullable:!0}:{type:"null"};case b.ZodArray:let o={type:"array"};return e.type?._def&&e.type?._def?.typeName!==b.ZodAny&&(o.items=ii(e.type._def,{...a,currentPath:[...a.currentPath,"items"]})),e.minLength&&rK(o,"minItems",e.minLength.value,e.minLength.message,a),e.maxLength&&rK(o,"maxItems",e.maxLength.value,e.maxLength.message,a),e.exactLength&&(rK(o,"minItems",e.exactLength.value,e.exactLength.message,a),rK(o,"maxItems",e.exactLength.value,e.exactLength.message,a)),o;case b.ZodUnion:case b.ZodDiscriminatedUnion:if("openApi3"===a.target)return ie(e,a);let l=e.options instanceof Map?Array.from(e.options.values()):e.options;if(l.every(e=>e._def.typeName in r8&&(!e._def.checks||!e._def.checks.length))){let e=l.reduce((e,t)=>{let a=r8[t._def.typeName];return a&&!e.includes(a)?[...e,a]:e},[]);return{type:e.length>1?e:e[0]}}if(l.every(e=>"ZodLiteral"===e._def.typeName&&!e.description)){let e=l.reduce((e,t)=>{let a=typeof t._def.value;switch(a){case"string":case"number":case"boolean":return[...e,a];case"bigint":return[...e,"integer"];case"object":if(null===t._def.value)return[...e,"null"];default:return e}},[]);if(e.length===l.length){let t=e.filter((e,t,a)=>a.indexOf(e)===t);return{type:t.length>1?t:t[0],enum:l.reduce((e,t)=>e.includes(t._def.value)?e:[...e,t._def.value],[])}}}else if(l.every(e=>"ZodEnum"===e._def.typeName))return{type:"string",enum:l.reduce((e,t)=>[...e,...t._def.values.filter(t=>!e.includes(t))],[])};return ie(e,a);case b.ZodIntersection:let d=[ii(e.left._def,{...a,currentPath:[...a.currentPath,"allOf","0"]}),ii(e.right._def,{...a,currentPath:[...a.currentPath,"allOf","1"]})].filter(e=>!!e),u="jsonSchema2019-09"===a.target?{unevaluatedProperties:!1}:void 0,c=[];return d.forEach(e=>{if(r0(e))c.push(...e.allOf),void 0===e.unevaluatedProperties&&(u=void 0);else{let t=e;if("additionalProperties"in e&&!1===e.additionalProperties){let{additionalProperties:a,...r}=e;t=r}else u=void 0;c.push(t)}}),c.length?{allOf:c,...u}:void 0;case b.ZodTuple:return e.rest?{type:"array",minItems:e.items.length,items:e.items.map((e,t)=>ii(e._def,{...a,currentPath:[...a.currentPath,"items",`${t}`]})).reduce((e,t)=>void 0===t?e:[...e,t],[]),additionalItems:ii(e.rest._def,{...a,currentPath:[...a.currentPath,"additionalItems"]})}:{type:"array",minItems:e.items.length,maxItems:e.items.length,items:e.items.map((e,t)=>ii(e._def,{...a,currentPath:[...a.currentPath,"items",`${t}`]})).reduce((e,t)=>void 0===t?e:[...e,t],[])};case b.ZodRecord:return r7(e,a);case b.ZodLiteral:let h=typeof e.value;return"bigint"!==h&&"number"!==h&&"boolean"!==h&&"string"!==h?{type:Array.isArray(e.value)?"array":"object"}:"openApi3"===a.target?{type:"bigint"===h?"integer":h,enum:[e.value]}:{type:"bigint"===h?"integer":h,const:e.value};case b.ZodEnum:return{type:"string",enum:Array.from(e.values)};case b.ZodNativeEnum:let p=e.values,m=Object.keys(e.values).filter(e=>"number"!=typeof p[p[e]]).map(e=>p[e]),f=Array.from(new Set(m.map(e=>typeof e)));return{type:1===f.length?"string"===f[0]?"string":"number":["string","number"],enum:m};case b.ZodNullable:if(["ZodString","ZodNumber","ZodBigInt","ZodBoolean","ZodNull"].includes(e.innerType._def.typeName)&&(!e.innerType._def.checks||!e.innerType._def.checks.length))return"openApi3"===a.target?{type:r8[e.innerType._def.typeName],nullable:!0}:{type:[r8[e.innerType._def.typeName],"null"]};if("openApi3"===a.target){let t=ii(e.innerType._def,{...a,currentPath:[...a.currentPath]});return t&&"$ref"in t?{allOf:[t],nullable:!0}:t&&{...t,nullable:!0}}let g=ii(e.innerType._def,{...a,currentPath:[...a.currentPath,"anyOf","0"]});return g&&{anyOf:[g,{type:"null"}]};case b.ZodOptional:return it(e,a);case b.ZodMap:if("record"===a.mapStrategy)return r7(e,a);return{type:"array",maxItems:125,items:{type:"array",items:[ii(e.keyType._def,{...a,currentPath:[...a.currentPath,"items","items","0"]})||{},ii(e.valueType._def,{...a,currentPath:[...a.currentPath,"items","items","1"]})||{}],minItems:2,maxItems:2}};case b.ZodSet:let y={type:"array",uniqueItems:!0,items:ii(e.valueType._def,{...a,currentPath:[...a.currentPath,"items"]})};return e.minSize&&rK(y,"minItems",e.minSize.value,e.minSize.message,a),e.maxSize&&rK(y,"maxItems",e.maxSize.value,e.maxSize.message,a),y;case b.ZodLazy:return ii(e.getter()._def,a);case b.ZodPromise:return ii(e.type._def,a);case b.ZodNaN:case b.ZodNever:return{not:{}};case b.ZodEffects:return"input"===a.effectStrategy?ii(e.schema._def,a):{};case b.ZodAny:case b.ZodUnknown:return{};case b.ZodDefault:return{...ii(e.innerType._def,a),default:e.defaultValue()};case b.ZodBranded:return rQ(e,a);case b.ZodReadonly:return ir(e,a);case b.ZodCatch:return rY(e,a);case b.ZodPipeline:return ia(e,a);case b.ZodFunction:case b.ZodVoid:case b.ZodSymbol:default:return}},id=(e,t,a)=>(e.description&&(a.description=e.description,t.markdownDescription&&(a.markdownDescription=e.description)),a),iu=(e,t)=>{let a=rJ(t),r="object"==typeof t&&t.definitions?Object.entries(t.definitions).reduce((e,[t,r])=>({...e,[t]:ii(r._def,{...a,currentPath:[...a.basePath,a.definitionPath,t]},!0)??{}}),{}):void 0,i="string"==typeof t?t:t?.nameStrategy==="title"?void 0:t?.name,n=ii(e._def,void 0===i?a:{...a,currentPath:[...a.basePath,a.definitionPath,i]},!1)??{},s="object"==typeof t&&void 0!==t.name&&"title"===t.nameStrategy?t.name:void 0;void 0!==s&&(n.title=s);let o=void 0===i?r?{...n,[a.definitionPath]:r}:n:{$ref:[..."relative"===a.$refStrategy?[]:a.basePath,a.definitionPath,i].join("/"),[a.definitionPath]:{...r,[i]:n}};return"jsonSchema7"===a.target?o.$schema="http://json-schema.org/draft-07/schema#":("jsonSchema2019-09"===a.target||"openAi"===a.target)&&(o.$schema="https://json-schema.org/draft/2019-09/schema#"),"openAi"===a.target&&("anyOf"in o||"oneOf"in o||"allOf"in o||"type"in o&&Array.isArray(o.type))&&console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property."),o};function ic(e){return e.replace(/[^a-zA-Z-_0-9]/g,"_")}let ih=["*","_","`"];async function ip(e,t){let{backgroundColor:a="white"}=t??{},r=btoa(e);void 0!==a&&(/^#(?:[0-9a-fA-F]{3}){1,2}$/.test(a)||(a=`!${a}`));let i=`https://mermaid.ink/img/${r}?bgColor=${a}`,n=await fetch(i);if(!n.ok)throw Error(`Failed to render the graph using the Mermaid.INK API.
Status code: ${n.status}
Status text: ${n.statusText}`);return await n.blob()}class im{constructor(e){Object.defineProperty(this,"nodes",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"edges",{enumerable:!0,configurable:!0,writable:!0,value:[]}),this.nodes=e?.nodes??this.nodes,this.edges=e?.edges??this.edges}toJSON(){let e={};return Object.values(this.nodes).forEach((t,a)=>{e[t.id]=tU(t.id)?a:t.id}),{nodes:Object.values(this.nodes).map(t=>({id:e[t.id],...rz(t.data)?{type:"runnable",data:{id:t.data.lc_id,name:t.data.getName()}}:{type:"schema",data:{...iu(t.data.schema),title:t.data.name}}})),edges:this.edges.map(t=>{let a={source:e[t.source],target:e[t.target]};return void 0!==t.data&&(a.data=t.data),void 0!==t.conditional&&(a.conditional=t.conditional),a})}}addNode(e,t,a){if(void 0!==t&&void 0!==this.nodes[t])throw Error(`Node with id ${t} already exists`);let r=t??tj(),i={id:r,data:e,name:function(e,t){if(void 0!==e&&!tU(e))return e;if(!rz(t))return t.name??"UnknownSchema";try{let e=t.getName();return e=e.startsWith("Runnable")?e.slice(8):e}catch(e){return t.getName()}}(t,e),metadata:a};return this.nodes[r]=i,i}removeNode(e){delete this.nodes[e.id],this.edges=this.edges.filter(t=>t.source!==e.id&&t.target!==e.id)}addEdge(e,t,a,r){if(void 0===this.nodes[e.id])throw Error(`Source node ${e.id} not in graph`);if(void 0===this.nodes[t.id])throw Error(`Target node ${t.id} not in graph`);let i={source:e.id,target:t.id,data:a,conditional:r};return this.edges.push(i),i}firstNode(){return ig(this)}lastNode(){return iy(this)}extend(e,t=""){let a=t;Object.values(e.nodes).map(e=>e.id).every(tU)&&(a="");let r=e=>a?`${a}:${e}`:e;Object.entries(e.nodes).forEach(([e,t])=>{this.nodes[r(e)]={...t,id:r(e)}});let i=e.edges.map(e=>({...e,source:r(e.source),target:r(e.target)}));this.edges=[...this.edges,...i];let n=e.firstNode(),s=e.lastNode();return[n?{id:r(n.id),data:n.data}:void 0,s?{id:r(s.id),data:s.data}:void 0]}trimFirstNode(){let e=this.firstNode();e&&ig(this,[e.id])&&this.removeNode(e)}trimLastNode(){let e=this.lastNode();e&&iy(this,[e.id])&&this.removeNode(e)}reid(){let e=Object.fromEntries(Object.values(this.nodes).map(e=>[e.id,e.name])),t=new Map;Object.values(e).forEach(e=>{t.set(e,(t.get(e)||0)+1)});let a=a=>{let r=e[a];return tU(a)&&1===t.get(r)?r:a};return new im({nodes:Object.fromEntries(Object.entries(this.nodes).map(([e,t])=>[a(e),{...t,id:a(e)}])),edges:this.edges.map(e=>({...e,source:a(e.source),target:a(e.target)}))})}drawMermaid(e){let{withStyles:t,curveStyle:a,nodeColors:r={default:"fill:#f2f0ff,line-height:1.2",first:"fill-opacity:0",last:"fill:#bfb6fc"},wrapLabelNWords:i}=e??{},n=this.reid(),s=n.firstNode(),o=n.lastNode();return function(e,t,a){let{firstNode:r,lastNode:i,nodeColors:n,withStyles:s=!0,curveStyle:o="linear",wrapLabelNWords:l=9}=a??{},d=s?`%%{init: {'flowchart': {'curve': '${o}'}}}%%
graph TD;
`:"graph TD;\n";if(s){let t="default",a={[t]:"{0}({1})"};for(let[n,s]of(void 0!==r&&(a[r]="{0}([{1}]):::first"),void 0!==i&&(a[i]="{0}([{1}]):::last"),Object.entries(e))){let e=s.name.split(":").pop()??"",r=ih.some(t=>e.startsWith(t)&&e.endsWith(t))?`<p>${e}</p>`:e;Object.keys(s.metadata??{}).length&&(r+=`<hr/><small><em>${Object.entries(s.metadata??{}).map(([e,t])=>`${e} = ${t}`).join("\n")}</em></small>`);let i=(a[n]??a[t]).replace("{0}",ic(n)).replace("{1}",r);d+=`	${i}
`}}let u={};for(let e of t){let t=e.source.split(":"),a=e.target.split(":"),r=t.filter((e,t)=>e===a[t]).join(":");u[r]||(u[r]=[]),u[r].push(e)}let c=new Set;function h(e,t){let a=1===e.length&&e[0].source===e[0].target;if(t&&!a){let e=t.split(":").pop();if(c.has(e))throw Error(`Found duplicate subgraph '${e}' -- this likely means that you're reusing a subgraph node with the same name. Please adjust your graph to have subgraph nodes with unique names.`);c.add(e),d+=`	subgraph ${e}
`}for(let t of e){let{source:e,target:a,data:r,conditional:i}=t,n="";if(void 0!==r){let e=r,t=e.split(" ");t.length>l&&(e=Array.from({length:Math.ceil(t.length/l)},(e,a)=>t.slice(a*l,(a+1)*l).join(" ")).join("&nbsp;<br>&nbsp;")),n=i?` -. &nbsp;${e}&nbsp; .-> `:` -- &nbsp;${e}&nbsp; --> `}else n=i?" -.-> ":" --\x3e ";d+=`	${ic(e)}${n}${ic(a)};
`}for(let e in u)e.startsWith(`${t}:`)&&e!==t&&h(u[e],e);t&&!a&&(d+="	end\n")}for(let e in h(u[""]??[],""),u)e.includes(":")||""===e||h(u[e],e);return s&&(d+=function(e){let t="";for(let[a,r]of Object.entries(e))t+=`	classDef ${a} ${r};
`;return t}(n??{})),d}(n.nodes,n.edges,{firstNode:s?.id,lastNode:o?.id,withStyles:t,curveStyle:a,nodeColors:r,wrapLabelNWords:i})}async drawMermaidPng(e){return ip(this.drawMermaid(e),{backgroundColor:e?.backgroundColor})}}function ig(e,t=[]){let a=new Set(e.edges.filter(e=>!t.includes(e.source)).map(e=>e.target)),r=[];for(let i of Object.values(e.nodes))t.includes(i.id)||a.has(i.id)||r.push(i);return 1===r.length?r[0]:void 0}function iy(e,t=[]){let a=new Set(e.edges.filter(e=>!t.includes(e.target)).map(e=>e.source)),r=[];for(let i of Object.values(e.nodes))t.includes(i.id)||a.has(i.id)||r.push(i);return 1===r.length?r[0]:void 0}function i_(e){return"object"==typeof e&&null!==e&&"function"==typeof e[Symbol.iterator]&&"function"==typeof e.next}let ib=e=>null!=e&&"object"==typeof e&&"next"in e&&"function"==typeof e.next;function iv(e){return"object"==typeof e&&null!==e&&"function"==typeof e[Symbol.asyncIterator]}function*iw(e,t){for(;;){let{value:a,done:r}=rf.runWithConfig(rw(e),t.next.bind(t),!0);if(r)break;yield a}}async function*iO(e,t){let a=t[Symbol.asyncIterator]();for(;;){let{value:r,done:i}=await rf.runWithConfig(rw(e),a.next.bind(t),!0);if(i)break;yield r}}var ik=a(33702);function iE(e,t){return!e||Array.isArray(e)||e instanceof Date||"object"!=typeof e?{[t]:e}:e}class iS extends aN.i{constructor(){super(...arguments),Object.defineProperty(this,"lc_runnable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}getName(e){let t=this.name??this.constructor.lc_name()??this.constructor.name;return e?`${t}${e}`:t}bind(e){return new ix({bound:this,kwargs:e,config:{}})}map(){return new ij({bound:this})}withRetry(e){return new iT({bound:this,kwargs:{},config:{},maxAttemptNumber:e?.stopAfterAttempt,...e})}withConfig(e){return new ix({bound:this,config:e,kwargs:{}})}withFallbacks(e){return new i$({runnable:this,fallbacks:Array.isArray(e)?e:e.fallbacks})}_getOptionsList(e,t=0){if(Array.isArray(e)&&e.length!==t)throw Error(`Passed "options" must be an array with the same length as the inputs, but got ${e.length} options for ${t} inputs`);if(Array.isArray(e))return e.map(rb);if(t>1&&!Array.isArray(e)&&e.runId){console.warn("Provided runId will be used only for the first element of the batch.");let a=Object.fromEntries(Object.entries(e).filter(([e])=>"runId"!==e));return Array.from({length:t},(t,r)=>rb(0===r?e:a))}return Array.from({length:t},()=>rb(e))}async batch(e,t,a){let r=this._getOptionsList(t??{},e.length),i=new rF({maxConcurrency:r[0]?.maxConcurrency??a?.maxConcurrency,onFailedAttempt:e=>{throw e}});return Promise.all(e.map((e,t)=>i.call(async()=>{try{return await this.invoke(e,r[t])}catch(e){if(a?.returnExceptions)return e;throw e}})))}async *_streamIterator(e,t){yield this.invoke(e,t)}async stream(e,t){let a=rb(t),r=new rx({generator:this._streamIterator(e,a),config:a});return await r.setup,rk.fromAsyncGenerator(r)}_separateRunnableConfigFromCallOptions(e){let t;t=void 0===e?rb(e):rb({callbacks:e.callbacks,tags:e.tags,metadata:e.metadata,runName:e.runName,configurable:e.configurable,recursionLimit:e.recursionLimit,maxConcurrency:e.maxConcurrency,runId:e.runId,timeout:e.timeout,signal:e.signal});let a={...e};return delete a.callbacks,delete a.tags,delete a.metadata,delete a.runName,delete a.configurable,delete a.recursionLimit,delete a.maxConcurrency,delete a.runId,delete a.timeout,delete a.signal,[t,a]}async _callWithConfig(e,t,a){let r,i=rb(a),n=await rg(i),s=await n?.handleChainStart(this.toJSON(),iE(t,"input"),i.runId,i?.runType,void 0,void 0,i?.runName??this.getName());delete i.runId;try{let n=e.call(this,t,i,s);r=await rO(n,a?.signal)}catch(e){throw await s?.handleChainError(e),e}return await s?.handleChainEnd(iE(r,"output")),r}async _batchWithConfig(e,t,a,r){let i,n=this._getOptionsList(a??{},t.length),s=await Promise.all(n.map(rg)),o=await Promise.all(s.map(async(e,a)=>{let r=await e?.handleChainStart(this.toJSON(),iE(t[a],"input"),n[a].runId,n[a].runType,void 0,void 0,n[a].runName??this.getName());return delete n[a].runId,r}));try{let a=e.call(this,t,n,o,r);i=await rO(a,n?.[0]?.signal)}catch(e){throw await Promise.all(o.map(t=>t?.handleChainError(e))),e}return await Promise.all(o.map(e=>e?.handleChainEnd(iE(i,"output")))),i}async *_transformStreamWithConfig(e,t,a){let r,i,n,s=!0,o=!0,l=rb(a),d=await rg(l);async function*u(){for await(let t of e){if(s)if(void 0===r)r=t;else try{r=rS(r,t)}catch{r=void 0,s=!1}yield t}}try{let e=await rj(t.bind(this),u(),async()=>d?.handleChainStart(this.toJSON(),{input:""},l.runId,l.runType,void 0,void 0,l.runName??this.getName()),a?.signal,l);delete l.runId,n=e.setup;let r=n?.handlers.find(rL),s=e.output;void 0!==r&&void 0!==n&&(s=r.tapOutputIterable(n.runId,s));let c=n?.handlers.find(rI);for await(let e of(void 0!==c&&void 0!==n&&(s=c.tapOutputIterable(n.runId,s)),s))if(yield e,o)if(void 0===i)i=e;else try{i=rS(i,e)}catch{i=void 0,o=!1}}catch(e){throw await n?.handleChainError(e,void 0,void 0,void 0,{inputs:iE(r,"input")}),e}await n?.handleChainEnd(i??{},void 0,void 0,void 0,{inputs:iE(r,"input")})}getGraph(e){let t=new im,a=t.addNode({name:`${this.getName()}Input`,schema:tw.any()}),r=t.addNode(this),i=t.addNode({name:`${this.getName()}Output`,schema:tw.any()});return t.addEdge(a,r),t.addEdge(r,i),t}pipe(e){return new iP({first:this,last:iN(e)})}pick(e){return this.pipe(new iM(e))}assign(e){return this.pipe(new iR(new iA({steps:e})))}async *transform(e,t){let a;for await(let t of e)a=void 0===a?t:rS(a,t);yield*this._streamIterator(a,rb(t))}async *streamLog(e,t,a){let r=new rN({...a,autoClose:!1,_schemaFormat:"original"}),i=rb(t);yield*this._streamLog(e,r,i)}async *_streamLog(e,t,a){let{callbacks:r}=a;if(void 0===r)a.callbacks=[t];else if(Array.isArray(r))a.callbacks=r.concat([t]);else{let e=r.copy();e.addHandler(t,!0),a.callbacks=e}let i=this.stream(e,a),n=async function(){try{for await(let e of(await i)){let a=new rP({ops:[{op:"add",path:"/streamed_output/-",value:e}]});await t.writer.write(a)}}finally{await t.writer.close()}}();try{for await(let e of t)yield e}finally{await n}}streamEvents(e,t,a){let r;if("v1"===t.version)r=this._streamEventsV1(e,t,a);else if("v2"===t.version)r=this._streamEventsV2(e,t,a);else throw Error('Only versions "v1" and "v2" of the schema are currently supported.');if("text/event-stream"!==t.encoding)return rk.fromAsyncGenerator(r);var i=r;let n=new TextEncoder,s=new ReadableStream({async start(e){for await(let t of i)e.enqueue(n.encode(`event: data
data: ${JSON.stringify(t)}

`));e.enqueue(n.encode("event: end\n\n")),e.close()}});return rk.fromReadableStream(s)}async *_streamEventsV2(e,t,a){let r,i=new rU({...a,autoClose:!1}),n=rb(t),s=n.runId??tj();n.runId=s;let o=n.callbacks;if(void 0===o)n.callbacks=[i];else if(Array.isArray(o))n.callbacks=o.concat(i);else{let e=o.copy();e.addHandler(i,!0),n.callbacks=e}let l=this,d=async function(){try{let t=await l.stream(e,n);for await(let e of i.tapOutputIterable(s,t));}finally{await i.finish()}}(),u=!1;try{for await(let t of i){if(!u){t.data.input=e,u=!0,r=t.run_id,yield t;continue}t.run_id===r&&t.event.endsWith("_end")&&t.data?.input&&delete t.data.input,yield t}}finally{await d}}async *_streamEventsV1(e,t,a){let r,i=!1,n=rb(t),s=n.tags??[],o=n.metadata??{},l=n.runName??this.getName(),d=new rN({...a,autoClose:!1,_schemaFormat:"streaming_events"}),u=new rB({...a});for await(let t of this._streamLog(e,d,n)){if(void 0===(r=r?r.concat(t):rA.fromRunLogPatch(t)).state)throw Error('Internal error: "streamEvents" state is missing. Please open a bug report.');if(!i){i=!0;let t={...r.state},a={run_id:t.id,event:`on_${t.type}_start`,name:l,tags:s,metadata:o,data:{input:e}};u.includeEvent(a,t.type)&&(yield a)}for(let e of[...new Set(t.ops.filter(e=>e.path.startsWith("/logs/")).map(e=>e.path.split("/")[2]))]){let t,a={},i=r.state.logs[e];if("start"==(t=void 0===i.end_time?i.streamed_output.length>0?"stream":"start":"end"))void 0!==i.inputs&&(a.input=i.inputs);else if("end"===t)void 0!==i.inputs&&(a.input=i.inputs),a.output=i.final_output;else if("stream"===t){let e=i.streamed_output.length;if(1!==e)throw Error(`Expected exactly one chunk of streamed output, got ${e} instead. Encountered in: "${i.name}"`);a={chunk:i.streamed_output[0]},i.streamed_output=[]}yield{event:`on_${i.type}_${t}`,name:i.name,run_id:i.id,tags:i.tags,metadata:i.metadata,data:a}}let{state:a}=r;if(a.streamed_output.length>0){let e=a.streamed_output.length;if(1!==e)throw Error(`Expected exactly one chunk of streamed output, got ${e} instead. Encountered in: "${a.name}"`);let t={chunk:a.streamed_output[0]};a.streamed_output=[];let r={event:`on_${a.type}_stream`,run_id:a.id,tags:s,metadata:o,name:l,data:t};u.includeEvent(r,a.type)&&(yield r)}}let c=r?.state;if(void 0!==c){let e={event:`on_${c.type}_end`,name:l,run_id:c.id,tags:s,metadata:o,data:{output:c.final_output}};u.includeEvent(e,c.type)&&(yield e)}}static isRunnable(e){return rz(e)}withListeners({onStart:e,onEnd:t,onError:a}){return new ix({bound:this,config:{},configFactories:[r=>({callbacks:[new rH({config:r,onStart:e,onEnd:t,onError:a})]})]})}asTool(e){var t=this,a=e;let r=a.name??t.getName(),i=a.description??a.schema?.description;return new iL(a.schema.constructor===tw.ZodString?{name:r,description:i,schema:tw.object({input:tw.string()}).transform(e=>e.input),bound:t}:{name:r,description:i,schema:a.schema,bound:t})}}class ix extends iS{static lc_name(){return"RunnableBinding"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"bound",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"configFactories",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.bound=e.bound,this.kwargs=e.kwargs,this.config=e.config,this.configFactories=e.configFactories}getName(e){return this.bound.getName(e)}async _mergeConfig(...e){let t=ry(this.config,...e);return ry(t,...this.configFactories?await Promise.all(this.configFactories.map(async e=>await e(t))):[])}bind(e){return new this.constructor({bound:this.bound,kwargs:{...this.kwargs,...e},config:this.config})}withConfig(e){return new this.constructor({bound:this.bound,kwargs:this.kwargs,config:{...this.config,...e}})}withRetry(e){return new this.constructor({bound:this.bound.withRetry(e),kwargs:this.kwargs,config:this.config})}async invoke(e,t){return this.bound.invoke(e,await this._mergeConfig(rb(t),this.kwargs))}async batch(e,t,a){let r=Array.isArray(t)?await Promise.all(t.map(async e=>this._mergeConfig(rb(e),this.kwargs))):await this._mergeConfig(rb(t),this.kwargs);return this.bound.batch(e,r,a)}async *_streamIterator(e,t){yield*this.bound._streamIterator(e,await this._mergeConfig(rb(t),this.kwargs))}async stream(e,t){return this.bound.stream(e,await this._mergeConfig(rb(t),this.kwargs))}async *transform(e,t){yield*this.bound.transform(e,await this._mergeConfig(rb(t),this.kwargs))}streamEvents(e,t,a){let r=this,i=async function*(){yield*r.bound.streamEvents(e,{...await r._mergeConfig(rb(t),r.kwargs),version:t.version},a)};return rk.fromAsyncGenerator(i())}static isRunnableBinding(e){return e.bound&&iS.isRunnable(e.bound)}withListeners({onStart:e,onEnd:t,onError:a}){return new ix({bound:this.bound,kwargs:this.kwargs,config:this.config,configFactories:[r=>({callbacks:[new rH({config:r,onStart:e,onEnd:t,onError:a})]})]})}}class ij extends iS{static lc_name(){return"RunnableEach"}constructor(e){super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"bound",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.bound=e.bound}bind(e){return new ij({bound:this.bound.bind(e)})}async invoke(e,t){return this._callWithConfig(this._invoke.bind(this),e,t)}async _invoke(e,t,a){return this.bound.batch(e,rv(t,{callbacks:a?.getChild()}))}withListeners({onStart:e,onEnd:t,onError:a}){return new ij({bound:this.bound.withListeners({onStart:e,onEnd:t,onError:a})})}}class iT extends ix{static lc_name(){return"RunnableRetry"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"maxAttemptNumber",{enumerable:!0,configurable:!0,writable:!0,value:3}),Object.defineProperty(this,"onFailedAttempt",{enumerable:!0,configurable:!0,writable:!0,value:()=>{}}),this.maxAttemptNumber=e.maxAttemptNumber??this.maxAttemptNumber,this.onFailedAttempt=e.onFailedAttempt??this.onFailedAttempt}_patchConfigForRetry(e,t,a){let r=e>1?`retry:attempt:${e}`:void 0;return rv(t,{callbacks:a?.getChild(r)})}async _invoke(e,t,a){return tO(r=>super.invoke(e,this._patchConfigForRetry(r,t,a)),{onFailedAttempt:t=>this.onFailedAttempt(t,e),retries:Math.max(this.maxAttemptNumber-1,0),randomize:!0})}async invoke(e,t){return this._callWithConfig(this._invoke.bind(this),e,t)}async _batch(e,t,a,r){let i={};try{await tO(async n=>{let s,o=e.map((e,t)=>t).filter(e=>void 0===i[e.toString()]||i[e.toString()]instanceof Error),l=o.map(t=>e[t]),d=o.map(e=>this._patchConfigForRetry(n,t?.[e],a?.[e])),u=await super.batch(l,d,{...r,returnExceptions:!0});for(let e=0;e<u.length;e+=1){let t=u[e],a=o[e];t instanceof Error&&void 0===s&&((s=t).input=l[e]),i[a.toString()]=t}if(s)throw s;return u},{onFailedAttempt:e=>this.onFailedAttempt(e,e.input),retries:Math.max(this.maxAttemptNumber-1,0),randomize:!0})}catch(e){if(r?.returnExceptions!==!0)throw e}return Object.keys(i).sort((e,t)=>parseInt(e,10)-parseInt(t,10)).map(e=>i[parseInt(e,10)])}async batch(e,t,a){return this._batchWithConfig(this._batch.bind(this),e,t,a)}}class iP extends iS{static lc_name(){return"RunnableSequence"}constructor(e){super(e),Object.defineProperty(this,"first",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"middle",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"last",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"omitSequenceTags",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),this.first=e.first,this.middle=e.middle??this.middle,this.last=e.last,this.name=e.name,this.omitSequenceTags=e.omitSequenceTags??this.omitSequenceTags}get steps(){return[this.first,...this.middle,this.last]}async invoke(e,t){let a,r=rb(t),i=await rg(r),n=await i?.handleChainStart(this.toJSON(),iE(e,"input"),r.runId,void 0,void 0,void 0,r?.runName);delete r.runId;let s=e;try{let e=[this.first,...this.middle];for(let a=0;a<e.length;a+=1){let i=e[a].invoke(s,rv(r,{callbacks:n?.getChild(this.omitSequenceTags?void 0:`seq:step:${a+1}`)}));s=await rO(i,t?.signal)}if(t?.signal?.aborted)throw Error("Aborted");a=await this.last.invoke(s,rv(r,{callbacks:n?.getChild(this.omitSequenceTags?void 0:`seq:step:${this.steps.length}`)}))}catch(e){throw await n?.handleChainError(e),e}return await n?.handleChainEnd(iE(a,"output")),a}async batch(e,t,a){let r=this._getOptionsList(t??{},e.length),i=await Promise.all(r.map(rg)),n=await Promise.all(i.map(async(t,a)=>{let i=await t?.handleChainStart(this.toJSON(),iE(e[a],"input"),r[a].runId,void 0,void 0,void 0,r[a].runName);return delete r[a].runId,i})),s=e;try{for(let e=0;e<this.steps.length;e+=1){let t=this.steps[e].batch(s,n.map((t,a)=>{let i=t?.getChild(this.omitSequenceTags?void 0:`seq:step:${e+1}`);return rv(r[a],{callbacks:i})}),a);s=await rO(t,r[0]?.signal)}}catch(e){throw await Promise.all(n.map(t=>t?.handleChainError(e))),e}return await Promise.all(n.map(e=>e?.handleChainEnd(iE(s,"output")))),s}async *_streamIterator(e,t){let a,r=await rg(t),{runId:i,...n}=t??{},s=await r?.handleChainStart(this.toJSON(),iE(e,"input"),i,void 0,void 0,void 0,n?.runName),o=[this.first,...this.middle,this.last],l=!0;async function*d(){yield e}try{let e=o[0].transform(d(),rv(n,{callbacks:s?.getChild(this.omitSequenceTags?void 0:"seq:step:1")}));for(let t=1;t<o.length;t+=1){let a=o[t];e=await a.transform(e,rv(n,{callbacks:s?.getChild(this.omitSequenceTags?void 0:`seq:step:${t+1}`)}))}for await(let r of e)if(t?.signal?.throwIfAborted(),yield r,l)if(void 0===a)a=r;else try{a=rS(a,r)}catch(e){a=void 0,l=!1}}catch(e){throw await s?.handleChainError(e),e}await s?.handleChainEnd(iE(a,"output"))}getGraph(e){let t=new im,a=null;return this.steps.forEach((r,i)=>{let n=r.getGraph(e);0!==i&&n.trimFirstNode(),i!==this.steps.length-1&&n.trimLastNode(),t.extend(n);let s=n.firstNode();if(!s)throw Error(`Runnable ${r} has no first node`);a&&t.addEdge(a,s),a=n.lastNode()}),t}pipe(e){return new iP(iP.isRunnableSequence(e)?{first:this.first,middle:this.middle.concat([this.last,e.first,...e.middle]),last:e.last,name:this.name??e.name}:{first:this.first,middle:[...this.middle,this.last],last:iN(e),name:this.name})}static isRunnableSequence(e){return Array.isArray(e.middle)&&iS.isRunnable(e)}static from([e,...t],a){let r={};return"string"==typeof a?r.name=a:void 0!==a&&(r=a),new iP({...r,first:iN(e),middle:t.slice(0,-1).map(iN),last:iN(t[t.length-1])})}}class iA extends iS{static lc_name(){return"RunnableMap"}getStepsKeys(){return Object.keys(this.steps)}constructor(e){for(let[t,a]of(super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"steps",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.steps={},Object.entries(e.steps)))this.steps[t]=iN(a)}static from(e){return new iA({steps:e})}async invoke(e,t){let a=rb(t),r=await rg(a),i=await r?.handleChainStart(this.toJSON(),{input:e},a.runId,void 0,void 0,void 0,a?.runName);delete a.runId;let n={};try{let r=Object.entries(this.steps).map(async([t,r])=>{n[t]=await r.invoke(e,rv(a,{callbacks:i?.getChild(`map:key:${t}`)}))});await rO(Promise.all(r),t?.signal)}catch(e){throw await i?.handleChainError(e),e}return await i?.handleChainEnd(n),n}async *_transform(e,t,a){let r={...this.steps},i=rE(e,Object.keys(r).length),n=new Map(Object.entries(r).map(([e,r],n)=>{let s=r.transform(i[n],rv(a,{callbacks:t?.getChild(`map:key:${e}`)}));return[e,s.next().then(t=>({key:e,gen:s,result:t}))]}));for(;n.size;){let e=Promise.race(n.values()),{key:t,result:r,gen:i}=await rO(e,a?.signal);n.delete(t),r.done||(yield{[t]:r.value},n.set(t,i.next().then(e=>({key:t,gen:i,result:e}))))}}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*a(){yield e}let r=rb(t),i=new rx({generator:this.transform(a(),r),config:r});return await i.setup,rk.fromAsyncGenerator(i)}}class iI extends iS{constructor(e){if(super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),!ag(e.func))throw Error("RunnableTraceable requires a function that is wrapped in traceable higher-order function");this.func=e.func}async invoke(e,t){let[a]=this._getOptionsList(t??{},1),r=await rg(a);return rO(this.func(rv(a,{callbacks:r}),e),a?.signal)}async *_streamIterator(e,t){let[a]=this._getOptionsList(t??{},1),r=await this.invoke(e,t);if(iv(r)){for await(let e of r)a?.signal?.throwIfAborted(),yield e;return}if(ib(r)){for(;;){a?.signal?.throwIfAborted();let e=r.next();if(e.done)break;yield e.value}return}yield r}static from(e){return new iI({func:e})}}class iC extends iS{static lc_name(){return"RunnableLambda"}constructor(e){if(ag(e.func))return iI.from(e.func);if(super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),ag(e.func))throw Error("RunnableLambda requires a function that is not wrapped in traceable higher-order function. This shouldn't happen.");this.func=e.func}static from(e){return new iC({func:e})}async _invoke(e,t,a){return new Promise((r,i)=>{let n=rv(t,{callbacks:a?.getChild(),recursionLimit:(t?.recursionLimit??25)-1});rf.runWithConfig(rw(n),async()=>{try{let a=await this.func(e,{...n});if(a&&iS.isRunnable(a)){if(t?.recursionLimit===0)throw Error("Recursion limit reached.");a=await a.invoke(e,{...n,recursionLimit:(n.recursionLimit??25)-1})}else if(iv(a)){let e;for await(let r of iO(n,a))if(t?.signal?.throwIfAborted(),void 0===e)e=r;else try{e=rS(e,r)}catch(t){e=r}a=e}else if(i_(a)){let e;for(let r of iw(n,a))if(t?.signal?.throwIfAborted(),void 0===e)e=r;else try{e=rS(e,r)}catch(t){e=r}a=e}r(a)}catch(e){i(e)}})})}async invoke(e,t){return this._callWithConfig(this._invoke.bind(this),e,t)}async *_transform(e,t,a){let r;for await(let t of e)if(void 0===r)r=t;else try{r=rS(r,t)}catch(e){r=t}let i=rv(a,{callbacks:t?.getChild(),recursionLimit:(a?.recursionLimit??25)-1}),n=await new Promise((e,t)=>{rf.runWithConfig(rw(i),async()=>{try{let t=await this.func(r,{...i,config:i});e(t)}catch(e){t(e)}})});if(n&&iS.isRunnable(n)){if(a?.recursionLimit===0)throw Error("Recursion limit reached.");for await(let e of(await n.stream(r,i)))yield e}else if(iv(n))for await(let e of iO(i,n))a?.signal?.throwIfAborted(),yield e;else if(i_(n))for(let e of iw(i,n))a?.signal?.throwIfAborted(),yield e;else yield n}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*a(){yield e}let r=rb(t),i=new rx({generator:this.transform(a(),r),config:r});return await i.setup,rk.fromAsyncGenerator(i)}}class i$ extends iS{static lc_name(){return"RunnableWithFallbacks"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"runnable",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"fallbacks",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.runnable=e.runnable,this.fallbacks=e.fallbacks}*runnables(){for(let e of(yield this.runnable,this.fallbacks))yield e}async invoke(e,t){let a=rb(t),r=await rg(a),{runId:i,...n}=a,s=await r?.handleChainStart(this.toJSON(),iE(e,"input"),i,void 0,void 0,void 0,n?.runName),o=rv(n,{callbacks:s?.getChild()});return await rf.runWithConfig(o,async()=>{let t;for(let r of this.runnables()){a?.signal?.throwIfAborted();try{let t=await r.invoke(e,o);return await s?.handleChainEnd(iE(t,"output")),t}catch(e){void 0===t&&(t=e)}}if(void 0===t)throw Error("No error stored at end of fallback.");throw await s?.handleChainError(t),t})}async *_streamIterator(e,t){let a,r,i,n=rb(t),s=await rg(n),{runId:o,...l}=n,d=await s?.handleChainStart(this.toJSON(),iE(e,"input"),o,void 0,void 0,void 0,l?.runName);for(let t of this.runnables()){n?.signal?.throwIfAborted();let i=rv(l,{callbacks:d?.getChild()});try{let a=await t.stream(e,i);r=iO(i,a);break}catch(e){void 0===a&&(a=e)}}if(void 0===r){let e=a??Error("No error stored at end of fallback.");throw await d?.handleChainError(e),e}try{for await(let e of r){yield e;try{i=void 0===i?i:rS(i,e)}catch(e){i=void 0}}}catch(e){throw await d?.handleChainError(e),e}await d?.handleChainEnd(iE(i,"output"))}async batch(e,t,a){let r;if(a?.returnExceptions)throw Error("Not implemented.");let i=this._getOptionsList(t??{},e.length),n=await Promise.all(i.map(e=>rg(e))),s=await Promise.all(n.map(async(t,a)=>{let r=await t?.handleChainStart(this.toJSON(),iE(e[a],"input"),i[a].runId,void 0,void 0,void 0,i[a].runName);return delete i[a].runId,r}));for(let t of this.runnables()){i[0].signal?.throwIfAborted();try{let r=await t.batch(e,s.map((e,t)=>rv(i[t],{callbacks:e?.getChild()})),a);return await Promise.all(s.map((e,t)=>e?.handleChainEnd(iE(r[t],"output")))),r}catch(e){void 0===r&&(r=e)}}if(!r)throw Error("No error stored at end of fallbacks.");throw await Promise.all(s.map(e=>e?.handleChainError(r))),r}}function iN(e){if("function"==typeof e)return new iC({func:e});if(iS.isRunnable(e))return e;if(Array.isArray(e)||"object"!=typeof e)throw Error(`Expected a Runnable, function or object.
Instead got an unsupported type.`);{let t={};for(let[a,r]of Object.entries(e))t[a]=iN(r);return new iA({steps:t})}}class iR extends iS{static lc_name(){return"RunnableAssign"}constructor(e){e instanceof iA&&(e={mapper:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"mapper",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.mapper=e.mapper}async invoke(e,t){let a=await this.mapper.invoke(e,t);return{...e,...a}}async *_transform(e,t,a){let r=this.mapper.getStepsKeys(),[i,n]=rE(e),s=this.mapper.transform(n,rv(a,{callbacks:t?.getChild()})),o=s.next();for await(let e of i){if("object"!=typeof e||Array.isArray(e))throw Error(`RunnableAssign can only be used with objects as input, got ${typeof e}`);let t=Object.fromEntries(Object.entries(e).filter(([e])=>!r.includes(e)));Object.keys(t).length>0&&(yield t)}for await(let e of(yield(await o).value,s))yield e}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*a(){yield e}let r=rb(t),i=new rx({generator:this.transform(a(),r),config:r});return await i.setup,rk.fromAsyncGenerator(i)}}class iM extends iS{static lc_name(){return"RunnablePick"}constructor(e){("string"==typeof e||Array.isArray(e))&&(e={keys:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"keys",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.keys=e.keys}async _pick(e){if("string"==typeof this.keys)return e[this.keys];{let t=this.keys.map(t=>[t,e[t]]).filter(e=>void 0!==e[1]);return 0===t.length?void 0:Object.fromEntries(t)}}async invoke(e,t){return this._callWithConfig(this._pick.bind(this),e,t)}async *_transform(e){for await(let t of e){let e=await this._pick(t);void 0!==e&&(yield e)}}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*a(){yield e}let r=rb(t),i=new rx({generator:this.transform(a(),r),config:r});return await i.setup,rk.fromAsyncGenerator(i)}}class iL extends ix{constructor(e){super({bound:iP.from([iC.from(async e=>{let t;if((0,ik.u)(e))try{t=await this.schema.parseAsync(e.args)}catch(t){throw new ik.Y("Received tool input did not match expected schema",JSON.stringify(e.args))}else t=e;return t}).withConfig({runName:`${e.name}:parse_input`}),e.bound]).withConfig({runName:e.name}),config:e.config??{}}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"description",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"schema",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=e.name,this.description=e.description,this.schema=e.schema}static lc_name(){return"RunnableToolLike"}}},33702:function(e,t,a){function r(e){return!!(e&&"object"==typeof e&&"type"in e&&"tool_call"===e.type)}a.d(t,{Y:()=>i,u:()=>r});class i extends Error{constructor(e,t){super(e),Object.defineProperty(this,"output",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.output=t}}}}]);
//# sourceMappingURL=18.f4dc3b35.js.map