import { <PERSON><PERSON><PERSON> } from 'node:buffer';
import <PERSON><PERSON> from 'jimp';
import { R as Rect, B as BaseElement } from './index-a171e36c.js';
import './constants.js';

interface Size {
    width: number;
    height: number;
    dpr?: number;
}
interface ImageInfo extends Size {
    jimpImage: Jimp;
}
/**
 * Retrieves the dimensions of an image asynchronously
 *
 * @param image - The image data, which can be a string path or a buffer
 * @returns A Promise that resolves to an object containing the width and height of the image
 * @throws Error if the image data is invalid
 */
declare function imageInfo(image: string | Buffer | Jimp): Promise<ImageInfo>;
/**
 * Retrieves the dimensions of an image from a base64-encoded string
 *
 * @param imageBase64 - The base64-encoded image data
 * @returns A Promise that resolves to an object containing the width and height of the image
 * @throws Error if the image data is invalid
 */
declare function imageInfoOfBase64(imageBase64: string): Promise<ImageInfo>;
declare function bufferFromBase64(imageBase64: string): Promise<Buffer>;
/**
 * Encodes an image file to a base64 encoded string
 *
 * @param image The path of the image file
 * @param withHeader Determine whether to return data including the file header information, the default is true
 *
 * @returns The base64 encoded string of the image file, which may or may not include header information depending on the withHeader parameter
 *
 * @throws When the image type is not supported, an error will be thrown
 */
declare function base64Encoded(image: string, withHeader?: boolean): string;
/**
 * Check if the Buffer is a valid PNG image
 * @param buffer The Buffer to check
 * @returns true if the Buffer is a valid PNG image, otherwise false
 */
declare function isValidPNGImageBuffer(buffer: Buffer): boolean;

/**
/**
 * Saves a Base64-encoded image to a file
 *
 * @param options - An object containing the Base64-encoded image data and the output file path
 * @param options.base64Data - The Base64-encoded image data
 * @param options.outputPath - The path where the image will be saved
 * @throws Error if there is an error during the saving process
 */
declare function saveBase64Image(options: {
    base64Data: string;
    outputPath: string;
}): Promise<void>;
/**
 * Transforms an image path into a base64-encoded string
 * @param inputPath - The path of the image file to be encoded
 * @returns A Promise that resolves to a base64-encoded string representing the image file
 */
declare function transformImgPathToBase64(inputPath: string): Promise<string>;
/**
 * Resizes an image from a base64-encoded string
 *
 * @param base64Data - A base64-encoded string representing the image
 * @returns A Promise that resolves to a base64-encoded string representing the resized image
 * @throws An error if the width or height cannot be determined from the metadata
 */
declare function resizeImg(inputData: Buffer, newSize: {
    width: number;
    height: number;
}): Promise<Buffer>;
declare function resizeImgBase64(inputBase64: string, newSize: {
    width: number;
    height: number;
}): Promise<string>;
/**
 * Calculates new dimensions for an image while maintaining its aspect ratio.
 *
 * This function is designed to resize an image to fit within a specified maximum width and height
 * while maintaining the original aspect ratio. If the original width or height exceeds the maximum
 * dimensions, the image will be scaled down to fit.
 *
 * @param {number} originalWidth - The original width of the image.
 * @param {number} originalHeight - The original height of the image.
 * @returns {Object} An object containing the new width and height.
 * @throws {Error} Throws an error if the width or height is not a positive number.
 */
declare function zoomForGPT4o(originalWidth: number, originalHeight: number): {
    width: number;
    height: number;
};
/**
 * Trims an image and returns the trimming information, including the offset from the left and top edges, and the trimmed width and height
 *
 * @param image - The image to be trimmed. This can be a file path or a Buffer object containing the image data
 * @returns A Promise that resolves to an object containing the trimming information. If the image does not need to be trimmed, this object will be null
 */
declare function trimImage(image: string | Buffer): Promise<{
    trimOffsetLeft: number;
    trimOffsetTop: number;
    width: number;
    height: number;
} | null>;
declare function jimpFromBase64(base64: string): Promise<Jimp>;
declare function paddingToMatchBlock(image: Jimp, blockSize?: number): Promise<Jimp>;
declare function paddingToMatchBlockByBase64(imageBase64: string, blockSize?: number): Promise<string>;
declare function cropByRect(imageBase64: string, rect: Rect, paddingImage: boolean): Promise<string>;
declare function jimpToBase64(image: Jimp): Promise<string>;

interface ElementForOverlay {
    rect: Rect;
    indexId?: number;
}
declare const compositeElementInfoImg: (options: {
    inputImgBase64: string;
    elementsPositionInfo: Array<ElementForOverlay>;
    size?: {
        width: number;
        height: number;
    };
    annotationPadding?: number;
    borderThickness?: number;
    prompt?: string;
}) => Promise<string>;
declare const processImageElementInfo: (options: {
    inputImgBase64: string;
    elementsPositionInfo: Array<BaseElement>;
    elementsPositionInfoWithoutText: Array<BaseElement>;
}) => Promise<{
    compositeElementInfoImgBase64: string;
    compositeElementInfoImgWithoutTextBase64: string;
}>;

declare function drawBoxOnImage(options: {
    inputImgBase64: string;
    rect: {
        x: number;
        y: number;
    };
}): Promise<string>;
declare function savePositionImg(options: {
    inputImgBase64: string;
    rect: {
        x: number;
        y: number;
    };
    outputPath: string;
}): Promise<void>;

export { base64Encoded, bufferFromBase64, compositeElementInfoImg, cropByRect, drawBoxOnImage, imageInfo, imageInfoOfBase64, isValidPNGImageBuffer, jimpFromBase64, jimpToBase64, paddingToMatchBlock, paddingToMatchBlockByBase64, processImageElementInfo, resizeImg, resizeImgBase64, saveBase64Image, savePositionImg, transformImgPathToBase64, trimImage, zoomForGPT4o };
