declare const MIDSCENE_OPENAI_INIT_CONFIG_JSON = "MIDSCENE_OPENAI_INIT_CONFIG_JSON";
declare const MIDSCENE_MODEL_NAME = "MIDSCENE_MODEL_NAME";
declare const MIDSCENE_LANGSMITH_DEBUG = "MIDSCENE_LANGSMITH_DEBUG";
declare const MIDSCENE_DEBUG_AI_PROFILE = "MIDSCENE_DEBUG_AI_PROFILE";
declare const MIDSCENE_DEBUG_AI_RESPONSE = "MIDSCENE_DEBUG_AI_RESPONSE";
declare const MIDSCENE_DANGEROUSLY_PRINT_ALL_CONFIG = "MIDSCENE_DANGEROUSLY_PRINT_ALL_CONFIG";
declare const MIDSCENE_DEBUG_MODE = "MIDSCENE_DEBUG_MODE";
declare const MIDSCENE_MCP_USE_PUPPETEER_MODE = "MIDSCENE_MCP_USE_PUPPETEER_MODE";
declare const MIDSCENE_FORCE_DEEP_THINK = "MIDSCENE_FORCE_DEEP_THINK";
declare const MIDSCENE_OPENAI_SOCKS_PROXY = "MIDSCENE_OPENAI_SOCKS_PROXY";
declare const MIDSCENE_OPENAI_HTTP_PROXY = "MIDSCENE_OPENAI_HTTP_PROXY";
declare const OPENAI_API_KEY = "OPENAI_API_KEY";
declare const OPENAI_BASE_URL = "OPENAI_BASE_URL";
declare const OPENAI_MAX_TOKENS = "OPENAI_MAX_TOKENS";
declare const MIDSCENE_ADB_PATH = "MIDSCENE_ADB_PATH";
declare const MIDSCENE_ADB_REMOTE_HOST = "MIDSCENE_ADB_REMOTE_HOST";
declare const MIDSCENE_ADB_REMOTE_PORT = "MIDSCENE_ADB_REMOTE_PORT";
declare const MIDSCENE_CACHE = "MIDSCENE_CACHE";
declare const MIDSCENE_USE_VLM_UI_TARS = "MIDSCENE_USE_VLM_UI_TARS";
declare const MIDSCENE_USE_QWEN_VL = "MIDSCENE_USE_QWEN_VL";
declare const MIDSCENE_USE_DOUBAO_VISION = "MIDSCENE_USE_DOUBAO_VISION";
declare const MIDSCENE_USE_GEMINI = "MIDSCENE_USE_GEMINI";
declare const MIDSCENE_USE_VL_MODEL = "MIDSCENE_USE_VL_MODEL";
declare const MATCH_BY_POSITION = "MATCH_BY_POSITION";
declare const MIDSCENE_API_TYPE = "MIDSCENE-API-TYPE";
declare const MIDSCENE_REPORT_TAG_NAME = "MIDSCENE_REPORT_TAG_NAME";
declare const MIDSCENE_PREFERRED_LANGUAGE = "MIDSCENE_PREFERRED_LANGUAGE";
declare const MIDSCENE_USE_AZURE_OPENAI = "MIDSCENE_USE_AZURE_OPENAI";
declare const MIDSCENE_AZURE_OPENAI_SCOPE = "MIDSCENE_AZURE_OPENAI_SCOPE";
declare const MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON = "MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON";
declare const AZURE_OPENAI_ENDPOINT = "AZURE_OPENAI_ENDPOINT";
declare const AZURE_OPENAI_KEY = "AZURE_OPENAI_KEY";
declare const AZURE_OPENAI_API_VERSION = "AZURE_OPENAI_API_VERSION";
declare const AZURE_OPENAI_DEPLOYMENT = "AZURE_OPENAI_DEPLOYMENT";
declare const MIDSCENE_USE_ANTHROPIC_SDK = "MIDSCENE_USE_ANTHROPIC_SDK";
declare const ANTHROPIC_API_KEY = "ANTHROPIC_API_KEY";
declare const MIDSCENE_RUN_DIR = "MIDSCENE_RUN_DIR";
declare const OPENAI_USE_AZURE = "OPENAI_USE_AZURE";
declare const allConfigFromEnv: () => {
    MIDSCENE_OPENAI_INIT_CONFIG_JSON: string | undefined;
    MIDSCENE_MODEL_NAME: string | undefined;
    MIDSCENE_DEBUG_MODE: string | undefined;
    MIDSCENE_FORCE_DEEP_THINK: string | undefined;
    MIDSCENE_LANGSMITH_DEBUG: string | undefined;
    MIDSCENE_DEBUG_AI_PROFILE: string | undefined;
    MIDSCENE_DEBUG_AI_RESPONSE: string | undefined;
    MIDSCENE_DANGEROUSLY_PRINT_ALL_CONFIG: string | undefined;
    OPENAI_API_KEY: string | undefined;
    OPENAI_BASE_URL: string | undefined;
    OPENAI_MAX_TOKENS: string | undefined;
    OPENAI_USE_AZURE: string | undefined;
    MIDSCENE_ADB_PATH: string | undefined;
    MIDSCENE_ADB_REMOTE_HOST: string | undefined;
    MIDSCENE_ADB_REMOTE_PORT: string | undefined;
    MIDSCENE_CACHE: string | undefined;
    MATCH_BY_POSITION: string | undefined;
    MIDSCENE_REPORT_TAG_NAME: string | undefined;
    MIDSCENE_OPENAI_SOCKS_PROXY: string | undefined;
    MIDSCENE_OPENAI_HTTP_PROXY: string | undefined;
    MIDSCENE_USE_AZURE_OPENAI: string | undefined;
    MIDSCENE_AZURE_OPENAI_SCOPE: string | undefined;
    MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON: string | undefined;
    MIDSCENE_USE_ANTHROPIC_SDK: string | undefined;
    MIDSCENE_USE_VLM_UI_TARS: string | undefined;
    MIDSCENE_USE_QWEN_VL: string | undefined;
    MIDSCENE_USE_DOUBAO_VISION: string | undefined;
    MIDSCENE_USE_GEMINI: string | undefined;
    MIDSCENE_USE_VL_MODEL: string | undefined;
    ANTHROPIC_API_KEY: string | undefined;
    AZURE_OPENAI_ENDPOINT: string | undefined;
    AZURE_OPENAI_KEY: string | undefined;
    AZURE_OPENAI_API_VERSION: string | undefined;
    AZURE_OPENAI_DEPLOYMENT: string | undefined;
    MIDSCENE_MCP_USE_PUPPETEER_MODE: string | undefined;
    MIDSCENE_RUN_DIR: string | undefined;
    MIDSCENE_PREFERRED_LANGUAGE: string | undefined;
};
declare enum UITarsModelVersion {
    V1_0 = "1.0",
    V1_5 = "1.5",
    DOUBAO_1_5_15B = "doubao-1.5-15B",
    DOUBAO_1_5_20B = "doubao-1.5-20B"
}
declare const uiTarsModelVersion: () => UITarsModelVersion | false;
declare const vlLocateMode: () => "qwen-vl" | "doubao-vision" | "gemini" | "vl-model" | "vlm-ui-tars" | false;
declare const getAIConfig: (configKey: keyof ReturnType<typeof allConfigFromEnv>) => string | undefined;
declare const getAIConfigInBoolean: (configKey: keyof ReturnType<typeof allConfigFromEnv>) => boolean;
declare const getAIConfigInJson: (configKey: keyof ReturnType<typeof allConfigFromEnv>) => any;
declare const overrideAIConfig: (newConfig: Partial<ReturnType<typeof allConfigFromEnv>>, extendMode?: boolean) => void;
declare const getPreferredLanguage: () => string | undefined;

export { ANTHROPIC_API_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_DEPLOYMENT, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_KEY, MATCH_BY_POSITION, MIDSCENE_ADB_PATH, MIDSCENE_ADB_REMOTE_HOST, MIDSCENE_ADB_REMOTE_PORT, MIDSCENE_API_TYPE, MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON, MIDSCENE_AZURE_OPENAI_SCOPE, MIDSCENE_CACHE, MIDSCENE_DANGEROUSLY_PRINT_ALL_CONFIG, MIDSCENE_DEBUG_AI_PROFILE, MIDSCENE_DEBUG_AI_RESPONSE, MIDSCENE_DEBUG_MODE, MIDSCENE_FORCE_DEEP_THINK, MIDSCENE_LANGSMITH_DEBUG, MIDSCENE_MCP_USE_PUPPETEER_MODE, MIDSCENE_MODEL_NAME, MIDSCENE_OPENAI_HTTP_PROXY, MIDSCENE_OPENAI_INIT_CONFIG_JSON, MIDSCENE_OPENAI_SOCKS_PROXY, MIDSCENE_PREFERRED_LANGUAGE, MIDSCENE_REPORT_TAG_NAME, MIDSCENE_RUN_DIR, MIDSCENE_USE_ANTHROPIC_SDK, MIDSCENE_USE_AZURE_OPENAI, MIDSCENE_USE_DOUBAO_VISION, MIDSCENE_USE_GEMINI, MIDSCENE_USE_QWEN_VL, MIDSCENE_USE_VLM_UI_TARS, MIDSCENE_USE_VL_MODEL, OPENAI_API_KEY, OPENAI_BASE_URL, OPENAI_MAX_TOKENS, OPENAI_USE_AZURE, UITarsModelVersion, allConfigFromEnv, getAIConfig, getAIConfigInBoolean, getAIConfigInJson, getPreferredLanguage, overrideAIConfig, uiTarsModelVersion, vlLocateMode };
