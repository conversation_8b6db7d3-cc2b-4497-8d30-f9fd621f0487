"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/env.ts
var env_exports = {};
__export(env_exports, {
  ANTHROPIC_API_KEY: () => ANTHROPIC_API_KEY,
  AZURE_OPENAI_API_VERSION: () => AZURE_OPENAI_API_VERSION,
  AZURE_OPENAI_DEPLOYMENT: () => AZURE_OPENAI_DEPLOYMENT,
  AZURE_OPENAI_ENDPOINT: () => AZURE_OPENAI_ENDPOINT,
  AZURE_OPENAI_KEY: () => AZURE_OPENAI_KEY,
  MATCH_BY_POSITION: () => MATCH_BY_POSITION,
  MIDSCENE_ADB_PATH: () => MIDSCENE_ADB_PATH,
  MIDSCENE_ADB_REMOTE_HOST: () => MIDSCENE_ADB_REMOTE_HOST,
  MIDSCENE_ADB_REMOTE_PORT: () => MIDSCENE_ADB_REMOTE_PORT,
  MIDSCENE_API_TYPE: () => MIDSCENE_API_TYPE,
  MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON: () => MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON,
  MIDSCENE_AZURE_OPENAI_SCOPE: () => MIDSCENE_AZURE_OPENAI_SCOPE,
  MIDSCENE_CACHE: () => MIDSCENE_CACHE,
  MIDSCENE_DANGEROUSLY_PRINT_ALL_CONFIG: () => MIDSCENE_DANGEROUSLY_PRINT_ALL_CONFIG,
  MIDSCENE_DEBUG_AI_PROFILE: () => MIDSCENE_DEBUG_AI_PROFILE,
  MIDSCENE_DEBUG_AI_RESPONSE: () => MIDSCENE_DEBUG_AI_RESPONSE,
  MIDSCENE_DEBUG_MODE: () => MIDSCENE_DEBUG_MODE,
  MIDSCENE_FORCE_DEEP_THINK: () => MIDSCENE_FORCE_DEEP_THINK,
  MIDSCENE_LANGSMITH_DEBUG: () => MIDSCENE_LANGSMITH_DEBUG,
  MIDSCENE_MCP_USE_PUPPETEER_MODE: () => MIDSCENE_MCP_USE_PUPPETEER_MODE,
  MIDSCENE_MODEL_NAME: () => MIDSCENE_MODEL_NAME,
  MIDSCENE_OPENAI_HTTP_PROXY: () => MIDSCENE_OPENAI_HTTP_PROXY,
  MIDSCENE_OPENAI_INIT_CONFIG_JSON: () => MIDSCENE_OPENAI_INIT_CONFIG_JSON,
  MIDSCENE_OPENAI_SOCKS_PROXY: () => MIDSCENE_OPENAI_SOCKS_PROXY,
  MIDSCENE_PREFERRED_LANGUAGE: () => MIDSCENE_PREFERRED_LANGUAGE,
  MIDSCENE_REPORT_TAG_NAME: () => MIDSCENE_REPORT_TAG_NAME,
  MIDSCENE_RUN_DIR: () => MIDSCENE_RUN_DIR,
  MIDSCENE_USE_ANTHROPIC_SDK: () => MIDSCENE_USE_ANTHROPIC_SDK,
  MIDSCENE_USE_AZURE_OPENAI: () => MIDSCENE_USE_AZURE_OPENAI,
  MIDSCENE_USE_DOUBAO_VISION: () => MIDSCENE_USE_DOUBAO_VISION,
  MIDSCENE_USE_GEMINI: () => MIDSCENE_USE_GEMINI,
  MIDSCENE_USE_QWEN_VL: () => MIDSCENE_USE_QWEN_VL,
  MIDSCENE_USE_VLM_UI_TARS: () => MIDSCENE_USE_VLM_UI_TARS,
  MIDSCENE_USE_VL_MODEL: () => MIDSCENE_USE_VL_MODEL,
  OPENAI_API_KEY: () => OPENAI_API_KEY,
  OPENAI_BASE_URL: () => OPENAI_BASE_URL,
  OPENAI_MAX_TOKENS: () => OPENAI_MAX_TOKENS,
  OPENAI_USE_AZURE: () => OPENAI_USE_AZURE,
  UITarsModelVersion: () => UITarsModelVersion,
  allConfigFromEnv: () => allConfigFromEnv,
  getAIConfig: () => getAIConfig,
  getAIConfigInBoolean: () => getAIConfigInBoolean,
  getAIConfigInJson: () => getAIConfigInJson,
  getPreferredLanguage: () => getPreferredLanguage,
  overrideAIConfig: () => overrideAIConfig,
  uiTarsModelVersion: () => uiTarsModelVersion,
  vlLocateMode: () => vlLocateMode
});
module.exports = __toCommonJS(env_exports);
var MIDSCENE_OPENAI_INIT_CONFIG_JSON = "MIDSCENE_OPENAI_INIT_CONFIG_JSON";
var MIDSCENE_MODEL_NAME = "MIDSCENE_MODEL_NAME";
var MIDSCENE_LANGSMITH_DEBUG = "MIDSCENE_LANGSMITH_DEBUG";
var MIDSCENE_DEBUG_AI_PROFILE = "MIDSCENE_DEBUG_AI_PROFILE";
var MIDSCENE_DEBUG_AI_RESPONSE = "MIDSCENE_DEBUG_AI_RESPONSE";
var MIDSCENE_DANGEROUSLY_PRINT_ALL_CONFIG = "MIDSCENE_DANGEROUSLY_PRINT_ALL_CONFIG";
var MIDSCENE_DEBUG_MODE = "MIDSCENE_DEBUG_MODE";
var MIDSCENE_MCP_USE_PUPPETEER_MODE = "MIDSCENE_MCP_USE_PUPPETEER_MODE";
var MIDSCENE_FORCE_DEEP_THINK = "MIDSCENE_FORCE_DEEP_THINK";
var MIDSCENE_OPENAI_SOCKS_PROXY = "MIDSCENE_OPENAI_SOCKS_PROXY";
var MIDSCENE_OPENAI_HTTP_PROXY = "MIDSCENE_OPENAI_HTTP_PROXY";
var OPENAI_API_KEY = "OPENAI_API_KEY";
var OPENAI_BASE_URL = "OPENAI_BASE_URL";
var OPENAI_MAX_TOKENS = "OPENAI_MAX_TOKENS";
var MIDSCENE_ADB_PATH = "MIDSCENE_ADB_PATH";
var MIDSCENE_ADB_REMOTE_HOST = "MIDSCENE_ADB_REMOTE_HOST";
var MIDSCENE_ADB_REMOTE_PORT = "MIDSCENE_ADB_REMOTE_PORT";
var MIDSCENE_CACHE = "MIDSCENE_CACHE";
var MIDSCENE_USE_VLM_UI_TARS = "MIDSCENE_USE_VLM_UI_TARS";
var MIDSCENE_USE_QWEN_VL = "MIDSCENE_USE_QWEN_VL";
var MIDSCENE_USE_DOUBAO_VISION = "MIDSCENE_USE_DOUBAO_VISION";
var MIDSCENE_USE_GEMINI = "MIDSCENE_USE_GEMINI";
var MIDSCENE_USE_VL_MODEL = "MIDSCENE_USE_VL_MODEL";
var MATCH_BY_POSITION = "MATCH_BY_POSITION";
var MIDSCENE_API_TYPE = "MIDSCENE-API-TYPE";
var MIDSCENE_REPORT_TAG_NAME = "MIDSCENE_REPORT_TAG_NAME";
var MIDSCENE_PREFERRED_LANGUAGE = "MIDSCENE_PREFERRED_LANGUAGE";
var MIDSCENE_USE_AZURE_OPENAI = "MIDSCENE_USE_AZURE_OPENAI";
var MIDSCENE_AZURE_OPENAI_SCOPE = "MIDSCENE_AZURE_OPENAI_SCOPE";
var MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON = "MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON";
var AZURE_OPENAI_ENDPOINT = "AZURE_OPENAI_ENDPOINT";
var AZURE_OPENAI_KEY = "AZURE_OPENAI_KEY";
var AZURE_OPENAI_API_VERSION = "AZURE_OPENAI_API_VERSION";
var AZURE_OPENAI_DEPLOYMENT = "AZURE_OPENAI_DEPLOYMENT";
var MIDSCENE_USE_ANTHROPIC_SDK = "MIDSCENE_USE_ANTHROPIC_SDK";
var ANTHROPIC_API_KEY = "ANTHROPIC_API_KEY";
var MIDSCENE_RUN_DIR = "MIDSCENE_RUN_DIR";
var OPENAI_USE_AZURE = "OPENAI_USE_AZURE";
var allConfigFromEnv = () => {
  return {
    [MIDSCENE_OPENAI_INIT_CONFIG_JSON]: process.env[MIDSCENE_OPENAI_INIT_CONFIG_JSON] || void 0,
    [MIDSCENE_MODEL_NAME]: process.env[MIDSCENE_MODEL_NAME] || void 0,
    [MIDSCENE_DEBUG_MODE]: process.env[MIDSCENE_DEBUG_MODE] || void 0,
    [MIDSCENE_FORCE_DEEP_THINK]: process.env[MIDSCENE_FORCE_DEEP_THINK] || void 0,
    [MIDSCENE_LANGSMITH_DEBUG]: process.env[MIDSCENE_LANGSMITH_DEBUG] || void 0,
    [MIDSCENE_DEBUG_AI_PROFILE]: process.env[MIDSCENE_DEBUG_AI_PROFILE] || void 0,
    [MIDSCENE_DEBUG_AI_RESPONSE]: process.env[MIDSCENE_DEBUG_AI_RESPONSE] || void 0,
    [MIDSCENE_DANGEROUSLY_PRINT_ALL_CONFIG]: process.env[MIDSCENE_DANGEROUSLY_PRINT_ALL_CONFIG] || void 0,
    [OPENAI_API_KEY]: process.env[OPENAI_API_KEY] || void 0,
    [OPENAI_BASE_URL]: process.env[OPENAI_BASE_URL] || void 0,
    [OPENAI_MAX_TOKENS]: process.env[OPENAI_MAX_TOKENS] || void 0,
    [OPENAI_USE_AZURE]: process.env[OPENAI_USE_AZURE] || void 0,
    [MIDSCENE_ADB_PATH]: process.env[MIDSCENE_ADB_PATH] || void 0,
    [MIDSCENE_ADB_REMOTE_HOST]: process.env[MIDSCENE_ADB_REMOTE_HOST] || void 0,
    [MIDSCENE_ADB_REMOTE_PORT]: process.env[MIDSCENE_ADB_REMOTE_PORT] || void 0,
    [MIDSCENE_CACHE]: process.env[MIDSCENE_CACHE] || void 0,
    [MATCH_BY_POSITION]: process.env[MATCH_BY_POSITION] || void 0,
    [MIDSCENE_REPORT_TAG_NAME]: process.env[MIDSCENE_REPORT_TAG_NAME] || void 0,
    [MIDSCENE_OPENAI_SOCKS_PROXY]: process.env[MIDSCENE_OPENAI_SOCKS_PROXY] || void 0,
    [MIDSCENE_OPENAI_HTTP_PROXY]: process.env[MIDSCENE_OPENAI_HTTP_PROXY] || void 0,
    [MIDSCENE_USE_AZURE_OPENAI]: process.env[MIDSCENE_USE_AZURE_OPENAI] || void 0,
    [MIDSCENE_AZURE_OPENAI_SCOPE]: process.env[MIDSCENE_AZURE_OPENAI_SCOPE] || void 0,
    [MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON]: process.env[MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON] || void 0,
    [MIDSCENE_USE_ANTHROPIC_SDK]: process.env[MIDSCENE_USE_ANTHROPIC_SDK] || void 0,
    [MIDSCENE_USE_VLM_UI_TARS]: process.env[MIDSCENE_USE_VLM_UI_TARS] || void 0,
    [MIDSCENE_USE_QWEN_VL]: process.env[MIDSCENE_USE_QWEN_VL] || void 0,
    [MIDSCENE_USE_DOUBAO_VISION]: process.env[MIDSCENE_USE_DOUBAO_VISION] || void 0,
    [MIDSCENE_USE_GEMINI]: process.env[MIDSCENE_USE_GEMINI] || void 0,
    [MIDSCENE_USE_VL_MODEL]: process.env[MIDSCENE_USE_VL_MODEL] || void 0,
    [ANTHROPIC_API_KEY]: process.env[ANTHROPIC_API_KEY] || void 0,
    [AZURE_OPENAI_ENDPOINT]: process.env[AZURE_OPENAI_ENDPOINT] || void 0,
    [AZURE_OPENAI_KEY]: process.env[AZURE_OPENAI_KEY] || void 0,
    [AZURE_OPENAI_API_VERSION]: process.env[AZURE_OPENAI_API_VERSION] || void 0,
    [AZURE_OPENAI_DEPLOYMENT]: process.env[AZURE_OPENAI_DEPLOYMENT] || void 0,
    [MIDSCENE_MCP_USE_PUPPETEER_MODE]: process.env[MIDSCENE_MCP_USE_PUPPETEER_MODE] || void 0,
    [MIDSCENE_RUN_DIR]: process.env[MIDSCENE_RUN_DIR] || void 0,
    [MIDSCENE_PREFERRED_LANGUAGE]: process.env[MIDSCENE_PREFERRED_LANGUAGE] || void 0
  };
};
var globalConfig = null;
var getGlobalConfig = () => {
  if (globalConfig === null) {
    globalConfig = allConfigFromEnv();
  }
  return globalConfig;
};
var UITarsModelVersion = /* @__PURE__ */ ((UITarsModelVersion2) => {
  UITarsModelVersion2["V1_0"] = "1.0";
  UITarsModelVersion2["V1_5"] = "1.5";
  UITarsModelVersion2["DOUBAO_1_5_15B"] = "doubao-1.5-15B";
  UITarsModelVersion2["DOUBAO_1_5_20B"] = "doubao-1.5-20B";
  return UITarsModelVersion2;
})(UITarsModelVersion || {});
var uiTarsModelVersion = () => {
  if (vlLocateMode() !== "vlm-ui-tars") {
    return false;
  }
  const versionConfig = getAIConfig(MIDSCENE_USE_VLM_UI_TARS);
  if (versionConfig === "1" || versionConfig === 1) {
    return "1.0" /* V1_0 */;
  }
  if (versionConfig === "DOUBAO" || versionConfig === "DOUBAO-1.5") {
    return "doubao-1.5-20B" /* DOUBAO_1_5_20B */;
  }
  return `${versionConfig}`;
};
var vlLocateMode = () => {
  const enabledModes = [
    getAIConfigInBoolean(MIDSCENE_USE_DOUBAO_VISION) && "MIDSCENE_USE_DOUBAO_VISION",
    getAIConfigInBoolean(MIDSCENE_USE_QWEN_VL) && "MIDSCENE_USE_QWEN_VL",
    getAIConfigInBoolean(MIDSCENE_USE_VLM_UI_TARS) && "MIDSCENE_USE_VLM_UI_TARS",
    getAIConfigInBoolean(MIDSCENE_USE_GEMINI) && "MIDSCENE_USE_GEMINI"
  ].filter(Boolean);
  if (enabledModes.length > 1) {
    throw new Error(
      `Only one vision mode can be enabled at a time. Currently enabled modes: ${enabledModes.join(", ")}. Please disable all but one mode.`
    );
  }
  if (getAIConfigInBoolean(MIDSCENE_USE_QWEN_VL)) {
    return "qwen-vl";
  }
  if (getAIConfigInBoolean(MIDSCENE_USE_DOUBAO_VISION)) {
    return "doubao-vision";
  }
  if (getAIConfigInBoolean(MIDSCENE_USE_GEMINI)) {
    return "gemini";
  }
  if (getAIConfigInBoolean(MIDSCENE_USE_VL_MODEL)) {
    return "vl-model";
  }
  if (getAIConfigInBoolean(MIDSCENE_USE_VLM_UI_TARS)) {
    return "vlm-ui-tars";
  }
  return false;
};
var getAIConfig = (configKey) => {
  if (configKey === MATCH_BY_POSITION) {
    throw new Error(
      "MATCH_BY_POSITION is deprecated, use MIDSCENE_USE_VL_MODEL instead"
    );
  }
  return getGlobalConfig()[configKey]?.trim?.();
};
var getAIConfigInBoolean = (configKey) => {
  const config = getAIConfig(configKey) || "";
  if (/^(true|1)$/i.test(config)) {
    return true;
  }
  if (/^(false|0)$/i.test(config)) {
    return false;
  }
  return !!config.trim();
};
var getAIConfigInJson = (configKey) => {
  const config = getAIConfig(configKey);
  try {
    return config ? JSON.parse(config) : void 0;
  } catch (error) {
    throw new Error(
      `Failed to parse json config: ${configKey}. ${error.message}`,
      {
        cause: error
      }
    );
  }
};
var overrideAIConfig = (newConfig, extendMode = false) => {
  for (const key in newConfig) {
    if (typeof key !== "string") {
      throw new Error(`Failed to override AI config, invalid key: ${key}`);
    }
    if (typeof newConfig[key] === "object") {
      throw new Error(
        `Failed to override AI config, invalid value for key: ${key}, value: ${newConfig[key]}`
      );
    }
  }
  const currentConfig = getGlobalConfig();
  globalConfig = extendMode ? { ...currentConfig, ...newConfig } : { ...newConfig };
};
var getPreferredLanguage = () => {
  if (getAIConfig(MIDSCENE_PREFERRED_LANGUAGE)) {
    return getAIConfig(MIDSCENE_PREFERRED_LANGUAGE);
  }
  const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const isChina = timeZone === "Asia/Shanghai";
  return isChina ? "Chinese" : "English";
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  ANTHROPIC_API_KEY,
  AZURE_OPENAI_API_VERSION,
  AZURE_OPENAI_DEPLOYMENT,
  AZURE_OPENAI_ENDPOINT,
  AZURE_OPENAI_KEY,
  MATCH_BY_POSITION,
  MIDSCENE_ADB_PATH,
  MIDSCENE_ADB_REMOTE_HOST,
  MIDSCENE_ADB_REMOTE_PORT,
  MIDSCENE_API_TYPE,
  MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON,
  MIDSCENE_AZURE_OPENAI_SCOPE,
  MIDSCENE_CACHE,
  MIDSCENE_DANGEROUSLY_PRINT_ALL_CONFIG,
  MIDSCENE_DEBUG_AI_PROFILE,
  MIDSCENE_DEBUG_AI_RESPONSE,
  MIDSCENE_DEBUG_MODE,
  MIDSCENE_FORCE_DEEP_THINK,
  MIDSCENE_LANGSMITH_DEBUG,
  MIDSCENE_MCP_USE_PUPPETEER_MODE,
  MIDSCENE_MODEL_NAME,
  MIDSCENE_OPENAI_HTTP_PROXY,
  MIDSCENE_OPENAI_INIT_CONFIG_JSON,
  MIDSCENE_OPENAI_SOCKS_PROXY,
  MIDSCENE_PREFERRED_LANGUAGE,
  MIDSCENE_REPORT_TAG_NAME,
  MIDSCENE_RUN_DIR,
  MIDSCENE_USE_ANTHROPIC_SDK,
  MIDSCENE_USE_AZURE_OPENAI,
  MIDSCENE_USE_DOUBAO_VISION,
  MIDSCENE_USE_GEMINI,
  MIDSCENE_USE_QWEN_VL,
  MIDSCENE_USE_VLM_UI_TARS,
  MIDSCENE_USE_VL_MODEL,
  OPENAI_API_KEY,
  OPENAI_BASE_URL,
  OPENAI_MAX_TOKENS,
  OPENAI_USE_AZURE,
  UITarsModelVersion,
  allConfigFromEnv,
  getAIConfig,
  getAIConfigInBoolean,
  getAIConfigInJson,
  getPreferredLanguage,
  overrideAIConfig,
  uiTarsModelVersion,
  vlLocateMode
});
