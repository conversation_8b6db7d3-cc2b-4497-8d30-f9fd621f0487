/*! For license information please see popup.6311459a.js.LICENSE.txt */
(()=>{var e={6020:function(e){e.exports=function(){"use strict";var e="millisecond",t="second",a="minute",i="hour",r="week",s="month",n="quarter",o="year",c="date",l="Invalid Date",u=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,h=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,d=function(e,t,a){var i=String(e);return!i||i.length>=t?e:""+Array(t+1-i.length).join(a)+e},p="en",g={};g[p]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],a=e%100;return"["+e+(t[(a-20)%10]||t[a]||t[0])+"]"}};var m="$isDayjsObject",f=function(e){return e instanceof v||!(!e||!e[m])},y=function e(t,a,i){var r;if(!t)return p;if("string"==typeof t){var s=t.toLowerCase();g[s]&&(r=s),a&&(g[s]=a,r=s);var n=t.split("-");if(!r&&n.length>1)return e(n[0])}else{var o=t.name;g[o]=t,r=o}return!i&&r&&(p=r),r||!i&&p},w=function(e,t){if(f(e))return e.clone();var a="object"==typeof t?t:{};return a.date=e,a.args=arguments,new v(a)},b={s:d,z:function(e){var t=-e.utcOffset(),a=Math.abs(t);return(t<=0?"+":"-")+d(Math.floor(a/60),2,"0")+":"+d(a%60,2,"0")},m:function e(t,a){if(t.date()<a.date())return-e(a,t);var i=12*(a.year()-t.year())+(a.month()-t.month()),r=t.clone().add(i,s),n=a-r<0,o=t.clone().add(i+(n?-1:1),s);return+(-(i+(a-r)/(n?r-o:o-r))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(l){return({M:s,y:o,w:r,d:"day",D:c,h:i,m:a,s:t,ms:e,Q:n})[l]||String(l||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};b.l=y,b.i=f,b.w=function(e,t){return w(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var v=function(){function d(e){this.$L=y(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[m]=!0}var p=d.prototype;return p.parse=function(e){this.$d=function(e){var t=e.date,a=e.utc;if(null===t)return new Date(NaN);if(b.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var i=t.match(u);if(i){var r=i[2]-1||0,s=(i[7]||"0").substring(0,3);return a?new Date(Date.UTC(i[1],r,i[3]||1,i[4]||0,i[5]||0,i[6]||0,s)):new Date(i[1],r,i[3]||1,i[4]||0,i[5]||0,i[6]||0,s)}}return new Date(t)}(e),this.init()},p.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},p.$utils=function(){return b},p.isValid=function(){return this.$d.toString()!==l},p.isSame=function(e,t){var a=w(e);return this.startOf(t)<=a&&a<=this.endOf(t)},p.isAfter=function(e,t){return w(e)<this.startOf(t)},p.isBefore=function(e,t){return this.endOf(t)<w(e)},p.$g=function(e,t,a){return b.u(e)?this[t]:this.set(a,e)},p.unix=function(){return Math.floor(this.valueOf()/1e3)},p.valueOf=function(){return this.$d.getTime()},p.startOf=function(e,n){var l=this,u=!!b.u(n)||n,h=b.p(e),d=function(e,t){var a=b.w(l.$u?Date.UTC(l.$y,t,e):new Date(l.$y,t,e),l);return u?a:a.endOf("day")},p=function(e,t){return b.w(l.toDate()[e].apply(l.toDate("s"),(u?[0,0,0,0]:[23,59,59,999]).slice(t)),l)},g=this.$W,m=this.$M,f=this.$D,y="set"+(this.$u?"UTC":"");switch(h){case o:return u?d(1,0):d(31,11);case s:return u?d(1,m):d(0,m+1);case r:var w=this.$locale().weekStart||0,v=(g<w?g+7:g)-w;return d(u?f-v:f+(6-v),m);case"day":case c:return p(y+"Hours",0);case i:return p(y+"Minutes",1);case a:return p(y+"Seconds",2);case t:return p(y+"Milliseconds",3);default:return this.clone()}},p.endOf=function(e){return this.startOf(e,!1)},p.$set=function(r,n){var l,u=b.p(r),h="set"+(this.$u?"UTC":""),d=((l={}).day=h+"Date",l[c]=h+"Date",l[s]=h+"Month",l[o]=h+"FullYear",l[i]=h+"Hours",l[a]=h+"Minutes",l[t]=h+"Seconds",l[e]=h+"Milliseconds",l)[u],p="day"===u?this.$D+(n-this.$W):n;if(u===s||u===o){var g=this.clone().set(c,1);g.$d[d](p),g.init(),this.$d=g.set(c,Math.min(this.$D,g.daysInMonth())).$d}else d&&this.$d[d](p);return this.init(),this},p.set=function(e,t){return this.clone().$set(e,t)},p.get=function(e){return this[b.p(e)]()},p.add=function(e,n){var c,l=this;e=Number(e);var u=b.p(n),h=function(t){var a=w(l);return b.w(a.date(a.date()+Math.round(t*e)),l)};if(u===s)return this.set(s,this.$M+e);if(u===o)return this.set(o,this.$y+e);if("day"===u)return h(1);if(u===r)return h(7);var d=((c={})[a]=6e4,c[i]=36e5,c[t]=1e3,c)[u]||1,p=this.$d.getTime()+e*d;return b.w(p,this)},p.subtract=function(e,t){return this.add(-1*e,t)},p.format=function(e){var t=this,a=this.$locale();if(!this.isValid())return a.invalidDate||l;var i=e||"YYYY-MM-DDTHH:mm:ssZ",r=b.z(this),s=this.$H,n=this.$m,o=this.$M,c=a.weekdays,u=a.months,d=a.meridiem,p=function(e,a,r,s){return e&&(e[a]||e(t,i))||r[a].slice(0,s)},g=function(e){return b.s(s%12||12,e,"0")},m=d||function(e,t,a){var i=e<12?"AM":"PM";return a?i.toLowerCase():i};return i.replace(h,function(e,i){return i||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return b.s(t.$y,4,"0");case"M":return o+1;case"MM":return b.s(o+1,2,"0");case"MMM":return p(a.monthsShort,o,u,3);case"MMMM":return p(u,o);case"D":return t.$D;case"DD":return b.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return p(a.weekdaysMin,t.$W,c,2);case"ddd":return p(a.weekdaysShort,t.$W,c,3);case"dddd":return c[t.$W];case"H":return String(s);case"HH":return b.s(s,2,"0");case"h":return g(1);case"hh":return g(2);case"a":return m(s,n,!0);case"A":return m(s,n,!1);case"m":return String(n);case"mm":return b.s(n,2,"0");case"s":return String(t.$s);case"ss":return b.s(t.$s,2,"0");case"SSS":return b.s(t.$ms,3,"0");case"Z":return r}return null}(e)||r.replace(":","")})},p.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},p.diff=function(e,c,l){var u,h=this,d=b.p(c),p=w(e),g=(p.utcOffset()-this.utcOffset())*6e4,m=this-p,f=function(){return b.m(h,p)};switch(d){case o:u=f()/12;break;case s:u=f();break;case n:u=f()/3;break;case r:u=(m-g)/6048e5;break;case"day":u=(m-g)/864e5;break;case i:u=m/36e5;break;case a:u=m/6e4;break;case t:u=m/1e3;break;default:u=m}return l?u:b.a(u)},p.daysInMonth=function(){return this.endOf(s).$D},p.$locale=function(){return g[this.$L]},p.locale=function(e,t){if(!e)return this.$L;var a=this.clone(),i=y(e,t,!0);return i&&(a.$L=i),a},p.clone=function(){return b.w(this.$d,this)},p.toDate=function(){return new Date(this.valueOf())},p.toJSON=function(){return this.isValid()?this.toISOString():null},p.toISOString=function(){return this.$d.toISOString()},p.toString=function(){return this.$d.toUTCString()},d}(),T=v.prototype;return w.prototype=T,[["$ms",e],["$s",t],["$m",a],["$H",i],["$W","day"],["$M",s],["$y",o],["$D",c]].forEach(function(e){T[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),w.extend=function(e,t){return e.$i||(e(t,v,w),e.$i=!0),w},w.locale=y,w.isDayjs=f,w.unix=function(e){return w(1e3*e)},w.en=g[p],w.Ls=g,w.p={},w}()},95297:function(e,t,a){"use strict";let i,r,s;var n,o,c,l,u,h,d=Object.create,p=Object.defineProperty,g=Object.getOwnPropertyDescriptor,m=Object.getOwnPropertyNames,f=Object.getPrototypeOf,y=Object.prototype.hasOwnProperty,w=(e,t,a,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let r of m(t))y.call(e,r)||r===a||p(e,r,{get:()=>t[r],enumerable:!(i=g(t,r))||i.enumerable});return e},b=(e,t,a)=>{if(!t.has(e))throw TypeError("Cannot "+a)},v=(e,t,a)=>(b(e,t,"read from private field"),a?a.call(e):t.get(e)),T=(e,t,a)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,a)},k=(e,t,a,i)=>(b(e,t,"write to private field"),i?i.call(e,a):t.set(e,a),a),x=(e,t,a)=>(b(e,t,"access private method"),a),C={},I={ExtensionBridgePageBrowserSide:()=>Y};for(var D in I)p(C,D,{get:I[D],enumerable:!0});e.exports=w(p({},"__esModule",{value:!0}),C);var E=a(8434),S=`
if (!window.__MIDSCENE_NEW_TAB_INTERCEPTOR_INITIALIZED__) {
  window.__MIDSCENE_NEW_TAB_INTERCEPTOR_INITIALIZED__ = true;

  // Intercept the window.open method (only once)
  window.open = function(url) {
    console.log('Blocked window.open:', url);
    window.location.href = url;
    return null;
  };

  // Block all a tag clicks with target="_blank" (only once)
  document.addEventListener('click', function(e) {
    const target = e.target.closest('a');
    if (target && target.target === '_blank') {
      e.preventDefault();
      console.log('Blocked new tab:', target.href);
      window.location.href = target.href;
      target.removeAttribute('target');
    }
  }, true);
}
`,A=a(97188),$=a(8434),M=a(21415),P=a(8434),_=class{constructor(e){T(this,c),T(this,u),T(this,n,new Set),T(this,o,void 0),this._modifiers=0,k(this,o,e)}updateClient(e){k(this,o,e)}async down(e,t={text:void 0,commands:[]}){let a=x(this,u,h).call(this,e),i=v(this,n).has(a.code);v(this,n).add(a.code),this._modifiers|=x(this,c,l).call(this,a.key);let r=void 0===t.text?a.text:t.text;await v(this,o).send("Input.dispatchKeyEvent",{type:r?"keyDown":"rawKeyDown",modifiers:this._modifiers,windowsVirtualKeyCode:a.keyCode,code:a.code,key:a.key,text:r,unmodifiedText:r,autoRepeat:i,location:a.location,isKeypad:3===a.location,commands:t.commands})}async up(e){let t=x(this,u,h).call(this,e);this._modifiers&=~x(this,c,l).call(this,t.key),v(this,n).delete(t.code),await v(this,o).send("Input.dispatchKeyEvent",{type:"keyUp",modifiers:this._modifiers,key:t.key,windowsVirtualKeyCode:t.keyCode,code:t.code,location:t.location})}async sendCharacter(e){await v(this,o).send("Input.insertText",{text:e})}charIsKey(e){return!!M._keyDefinitions[e]}async type(e,t={}){let a=t.delay||void 0;for(let t of e)this.charIsKey(t)?await this.press(t,{delay:a}):(a&&await new Promise(e=>setTimeout(e,a)),await this.sendCharacter(t))}async press(e,t={}){let{delay:a=null}=t,i=Array.isArray(e)?e:[e];for(let e of i)await this.down(e,t);for(let e of(a&&await new Promise(e=>setTimeout(e,t.delay)),[...i].reverse()))await this.up(e)}};n=new WeakMap,o=new WeakMap,c=new WeakSet,l=function(e){return"Alt"===e?1:"Control"===e?2:"Meta"===e?4:8*("Shift"===e)},u=new WeakSet,h=function(e){let t=8&this._modifiers,a={key:"",keyCode:0,code:"",text:"",location:0},i=M._keyDefinitions[e];return(0,P.assert)(i,`Unknown key: "${e}"`),i.key&&(a.key=i.key),t&&i.shiftKey&&(a.key=i.shiftKey),i.keyCode&&(a.keyCode=i.keyCode),t&&i.shiftKeyCode&&(a.keyCode=i.shiftKeyCode),i.code&&(a.code=i.code),i.location&&(a.location=i.location),1===a.key.length&&(a.text=a.key),i.text&&(a.text=i.text),t&&i.shiftText&&(a.text=i.shiftText),-9&this._modifiers&&(a.text=""),a};var O=(s=null!=(i=a(15117))?d(f(i)):{},w(!r&&i&&i.__esModule?s:p(s,"default",{value:i,enumerable:!0}),i)),F=a(8434),R=null,N=async()=>{let e=chrome.runtime.getURL("scripts/htmlElement.js");if(R)return R;if(F.ifInBrowser){let t=await fetch(e);return R=await t.text()}return O.default.readFileSync(e,"utf8")},L=null,W=async()=>{let e=chrome.runtime.getURL("scripts/water-flow.js");if(L)return L;if(F.ifInBrowser){let t=await fetch(e);return L=await t.text()}return O.default.readFileSync(e,"utf8")},B=null,j=async()=>{let e=chrome.runtime.getURL("scripts/stop-water-flow.js");if(B)return B;if(F.ifInBrowser){let t=await fetch(e);return B=await t.text()}return O.default.readFileSync(e,"utf8")};function U(e){return new Promise(t=>setTimeout(t,e))}var H=class{constructor(e){this.pageType="chrome-extension-proxy",this.version="0.17.5",this.activeTabId=null,this.tabIdOfDebuggerAttached=null,this.attachingDebugger=null,this.destroyed=!1,this.isMobileEmulation=null,this.latestMouseX=100,this.latestMouseY=100,this.mouse={click:async(e,t,a)=>{let{button:i="left",count:r=1}=a||{};if(await this.mouse.move(e,t),null===this.isMobileEmulation){let e=await this.sendCommandToDebugger("Runtime.evaluate",{expression:`(() => {
            return /Android|iPhone|iPad|iPod|Mobile/i.test(navigator.userAgent);
          })()`,returnByValue:!0});this.isMobileEmulation=e?.result?.value}if(this.isMobileEmulation&&"left"===i){let a=[{x:Math.round(e),y:Math.round(t)}];await this.sendCommandToDebugger("Input.dispatchTouchEvent",{type:"touchStart",touchPoints:a,modifiers:0}),await this.sendCommandToDebugger("Input.dispatchTouchEvent",{type:"touchEnd",touchPoints:[],modifiers:0})}else await this.sendCommandToDebugger("Input.dispatchMouseEvent",{type:"mousePressed",x:e,y:t,button:i,clickCount:r}),await this.sendCommandToDebugger("Input.dispatchMouseEvent",{type:"mouseReleased",x:e,y:t,button:i,clickCount:r})},wheel:async(e,t,a,i)=>{let r=a||this.latestMouseX,s=i||this.latestMouseY;await this.showMousePointer(r,s),await this.sendCommandToDebugger("Input.dispatchMouseEvent",{type:"mouseWheel",x:r,y:s,deltaX:e,deltaY:t}),this.latestMouseX=r,this.latestMouseY=s},move:async(e,t)=>{await this.showMousePointer(e,t),await this.sendCommandToDebugger("Input.dispatchMouseEvent",{type:"mouseMoved",x:e,y:t}),this.latestMouseX=e,this.latestMouseY=t},drag:async(e,t)=>{await this.mouse.move(e.x,e.y),await this.sendCommandToDebugger("Input.dispatchMouseEvent",{type:"mousePressed",x:e.x,y:e.y,button:"left",clickCount:1}),await this.mouse.move(t.x,t.y),await this.sendCommandToDebugger("Input.dispatchMouseEvent",{type:"mouseReleased",x:t.x,y:t.y,button:"left",clickCount:1})}},this.keyboard={type:async e=>{let t=new _({send:this.sendCommandToDebugger.bind(this)});await t.type(e,{delay:0})},press:async e=>{let t=new _({send:this.sendCommandToDebugger.bind(this)}),a=Array.isArray(e)?e:[e];for(let e of a){let a=e.command?[e.command]:[];await t.down(e.key,{commands:a})}for(let e of[...a].reverse())await t.up(e.key)}},this.forceSameTabNavigation=e}async setActiveTabId(e){if(this.activeTabId)throw Error(`Active tab id is already set, which is ${this.activeTabId}, cannot set it to ${e}`);await chrome.tabs.update(e,{active:!0}),this.activeTabId=e}async getActiveTabId(){return this.activeTabId}async getBrowserTabList(){return(await chrome.tabs.query({currentWindow:!0})).map(e=>({id:`${e.id}`,title:e.title,url:e.url,currentActiveTab:e.active})).filter(e=>e.id&&e.title&&e.url)}async getTabIdOrConnectToCurrentTab(){if(this.activeTabId)return this.activeTabId;let e=await chrome.tabs.query({active:!0,currentWindow:!0}).then(e=>e[0]?.id);return this.activeTabId=e||0,this.activeTabId}async attachDebugger(){if((0,$.assert)(!this.destroyed,"Page is destroyed"),this.attachingDebugger){await this.attachingDebugger;return}this.attachingDebugger=(async()=>{let e=await this.url(),t=null;if(e.startsWith("chrome://"))throw Error("Cannot attach debugger to chrome:// pages, please use Midscene in a normal page with http://, https:// or file://");try{let e=await this.getTabIdOrConnectToCurrentTab();if(this.tabIdOfDebuggerAttached===e)return;if(this.tabIdOfDebuggerAttached&&this.tabIdOfDebuggerAttached!==e){console.log("detach the previous tab",this.tabIdOfDebuggerAttached,"->",e);try{await this.detachDebugger(this.tabIdOfDebuggerAttached)}catch(e){console.error("Failed to detach debugger",e)}}console.log("attaching debugger",e),await chrome.debugger.attach({tabId:e},"1.3"),await U(500),this.tabIdOfDebuggerAttached=e,await this.enableWaterFlowAnimation()}catch(e){console.error("Failed to attach debugger",e),t=e}finally{this.attachingDebugger=null}if(t)throw t})(),await this.attachingDebugger}async showMousePointer(e,t){let a=`(() => {
      if(typeof window.midsceneWaterFlowAnimation !== 'undefined') {
        window.midsceneWaterFlowAnimation.enable();
        window.midsceneWaterFlowAnimation.showMousePointer(${e}, ${t});
      } else {
        console.log('midsceneWaterFlowAnimation is not defined');
      }
    })()`;await this.sendCommandToDebugger("Runtime.evaluate",{expression:`${a}`})}async hideMousePointer(){await this.sendCommandToDebugger("Runtime.evaluate",{expression:`(() => {
        if(typeof window.midsceneWaterFlowAnimation !== 'undefined') {
          window.midsceneWaterFlowAnimation.hideMousePointer();
        }
      })()`})}async detachDebugger(e){let t=e||this.tabIdOfDebuggerAttached;if(console.log("detaching debugger",t),!t){console.warn("No tab id to detach");return}try{await this.disableWaterFlowAnimation(t),await U(200)}catch(e){console.warn("Failed to disable water flow animation",e)}try{await chrome.debugger.detach({tabId:t})}catch(e){console.warn("Failed to detach debugger",e)}this.tabIdOfDebuggerAttached=null}async enableWaterFlowAnimation(){this.forceSameTabNavigation&&await chrome.debugger.sendCommand({tabId:this.tabIdOfDebuggerAttached},"Runtime.evaluate",{expression:S});let e=await W();await chrome.debugger.sendCommand({tabId:this.tabIdOfDebuggerAttached},"Runtime.evaluate",{expression:e})}async disableWaterFlowAnimation(e){let t=await j();await chrome.debugger.sendCommand({tabId:e},"Runtime.evaluate",{expression:t})}async sendCommandToDebugger(e,t){return await this.attachDebugger(),(0,$.assert)(this.tabIdOfDebuggerAttached,"Debugger is not attached"),this.enableWaterFlowAnimation(),await chrome.debugger.sendCommand({tabId:this.tabIdOfDebuggerAttached},e,t)}async getPageContentByCDP(){let e=await N();await this.sendCommandToDebugger("Runtime.evaluate",{expression:e});let t=await this.sendCommandToDebugger("Runtime.evaluate",{expression:`(${(()=>(window.midscene_element_inspector.setNodeHashCacheListOnWindow(),{tree:window.midscene_element_inspector.webExtractNodeTree(),size:{width:document.documentElement.clientWidth,height:document.documentElement.clientHeight,dpr:window.devicePixelRatio}})).toString()})()`,returnByValue:!0});if(!t.result.value){let e=t.exceptionDetails?.exception?.description||"";throw e||console.error("returnValue from cdp",t),Error(`Failed to get page content from page, error: ${e}`)}return t.result.value}async evaluateJavaScript(e){return this.sendCommandToDebugger("Runtime.evaluate",{expression:e})}async waitUntilNetworkIdle(){let e=Date.now(),t="";for(;Date.now()-e<1e4;){if("complete"===(t=(await this.sendCommandToDebugger("Runtime.evaluate",{expression:"document.readyState"})).result.value)){await new Promise(e=>setTimeout(e,300));return}await new Promise(e=>setTimeout(e,300))}throw Error(`Failed to wait until network idle, last readyState: ${t}`)}async getElementsInfo(){let e=await this.getElementsNodeTree();return(0,A.treeToList)(e)}async getXpathsById(e){let t=await N();return await this.sendCommandToDebugger("Runtime.evaluate",{expression:t}),(await this.sendCommandToDebugger("Runtime.evaluate",{expression:`window.midscene_element_inspector.getXpathsById('${e}')`,returnByValue:!0})).result.value}async getElementInfoByXpath(e){let t=await N();return await this.sendCommandToDebugger("Runtime.evaluate",{expression:t}),(await this.sendCommandToDebugger("Runtime.evaluate",{expression:`window.midscene_element_inspector.getElementInfoByXpath('${e}')`,returnByValue:!0})).result.value}async getElementsNodeTree(){await this.hideMousePointer();let e=await this.getPageContentByCDP();return e?.size&&(this.viewportSize=e.size),e?.tree||{node:null,children:[]}}async size(){return this.viewportSize?this.viewportSize:(await this.getPageContentByCDP()).size}async screenshotBase64(){await this.hideMousePointer();let e=await this.sendCommandToDebugger("Page.captureScreenshot",{format:"jpeg",quality:90});return`data:image/jpeg;base64,${e.data}`}async url(){let e=await this.getTabIdOrConnectToCurrentTab();return await chrome.tabs.get(e).then(e=>e.url)||""}async scrollUntilTop(e){return e&&await this.mouse.move(e.left,e.top),this.mouse.wheel(0,-9999999)}async scrollUntilBottom(e){return e&&await this.mouse.move(e.left,e.top),this.mouse.wheel(0,9999999)}async scrollUntilLeft(e){return e&&await this.mouse.move(e.left,e.top),this.mouse.wheel(-9999999,0)}async scrollUntilRight(e){return e&&await this.mouse.move(e.left,e.top),this.mouse.wheel(9999999,0)}async scrollUp(e,t){let{height:a}=await this.size();return this.mouse.wheel(0,-(e||.7*a),t?.left,t?.top)}async scrollDown(e,t){let{height:a}=await this.size();return this.mouse.wheel(0,e||.7*a,t?.left,t?.top)}async scrollLeft(e,t){let{width:a}=await this.size();return this.mouse.wheel(-(e||.7*a),0,t?.left,t?.top)}async scrollRight(e,t){let{width:a}=await this.size();return this.mouse.wheel(e||.7*a,0,t?.left,t?.top)}async clearInput(e){if(!e){console.warn("No element to clear input");return}await this.mouse.click(e.center[0],e.center[1]),await this.sendCommandToDebugger("Input.dispatchKeyEvent",{type:"keyDown",commands:["selectAll"]}),await this.sendCommandToDebugger("Input.dispatchKeyEvent",{type:"keyUp",commands:["selectAll"]}),await U(100),await this.keyboard.press({key:"Backspace"})}async destroy(){this.activeTabId=null,await this.detachDebugger(),this.destroyed=!0}},K=a(8434),V=a(73511),z=class{constructor(e,t,a){this.endpoint=e,this.onBridgeCall=t,this.onDisconnect=a,this.socket=null,this.serverVersion=null}async connect(){return new Promise((e,t)=>{this.socket=(0,V.io)(this.endpoint,{reconnection:!1,query:{version:"0.17.5"}});let a=setTimeout(()=>{try{this.socket?.offAny(),this.socket?.close()}catch(e){console.warn("got error when offing socket",e)}this.socket=null,t(Error("failed to connect to bridge server after timeout"))},1e3);this.socket.on("disconnect",e=>{this.socket=null,this.onDisconnect?.()}),this.socket.on("connect_error",e=>{console.error("bridge-connect-error",e),t(Error(e||"bridge connect error"))}),this.socket.on("bridge-connected",t=>{clearTimeout(a),this.serverVersion=t?.version||"unknown",e(this.socket)}),this.socket.on("bridge-refused",e=>{console.error("bridge-refused",e);try{this.socket?.disconnect()}catch(e){}t(Error(e||"bridge refused"))}),this.socket.on("bridge-call",e=>{let t=e.id;(0,K.assert)(void 0!==t,"call id is required"),(async()=>{let a;try{a=await this.onBridgeCall(e.method,e.args)}catch(i){let a=`Error from bridge client when calling, method: ${e.method}, args: ${e.args}, error: ${i?.message||i}
${i?.stack||""}`;return console.error(a),this.socket?.emit("bridge-call-response",{id:t,error:a})}this.socket?.emit("bridge-call-response",{id:t,response:a})})()})})}disconnect(){this.socket?.disconnect(),this.socket=null}},Y=class extends H{constructor(e=()=>{},t=()=>{},a=!0){super(a),this.onDisconnect=e,this.onLogMessage=t,this.bridgeClient=null,this.newlyCreatedTabIds=[]}async setupBridgeClient(){this.bridgeClient=new z("ws://localhost:3766",async(e,t)=>{if(console.log("bridge call from cli side",e,t),"connectNewTabWithUrl"===e)return this.connectNewTabWithUrl.apply(this,t);if("getBrowserTabList"===e)return this.getBrowserTabList.apply(this,t);if("setActiveTabId"===e)return this.setActiveTabId.apply(this,t);if("connectCurrentTab"===e)return this.connectCurrentTab.apply(this,t);if("bridge-update-agent-status"===e)return this.onLogMessage(t[0],"status");let a=await this.getActiveTabId();if(!a||0===a)throw Error("no tab is connected");if(e.startsWith("mouse.")){let a=e.split(".")[1];return this.mouse[a].apply(this.mouse,t)}if(e.startsWith("keyboard.")){let a=e.split(".")[1];return this.keyboard[a].apply(this.keyboard,t)}try{return await this[e](...t)}catch(i){let a=i instanceof Error?i.message:"Unknown error";throw console.error("error calling method",e,t,i),this.onLogMessage(`Error calling method: ${e}, ${a}`,"log"),Error(a,{cause:i})}},()=>this.destroy()),await this.bridgeClient.connect(),this.onLogMessage(`Bridge connected, cli-side version v${this.bridgeClient.serverVersion}, browser-side version v0.17.5`,"log")}async connect(){return await this.setupBridgeClient()}async connectNewTabWithUrl(e,t={forceSameTabNavigation:!0}){let a=(await chrome.tabs.create({url:e})).id;(0,E.assert)(a,"failed to get tabId after creating a new tab"),this.onLogMessage(`Creating new tab: ${e}`,"log"),this.newlyCreatedTabIds.push(a),t?.forceSameTabNavigation&&(this.forceSameTabNavigation=!0),await this.setActiveTabId(a)}async connectCurrentTab(e={forceSameTabNavigation:!0}){let t=await chrome.tabs.query({active:!0,currentWindow:!0}),a=t[0]?.id;(0,E.assert)(a,"failed to get tabId"),this.onLogMessage(`Connected to current tab: ${t[0]?.url}`,"log"),e?.forceSameTabNavigation&&(this.forceSameTabNavigation=!0),await this.setActiveTabId(a)}async setDestroyOptions(e){this.destroyOptions=e}async destroy(){if(this.destroyOptions?.closeTab&&this.newlyCreatedTabIds.length>0){for(let e of(this.onLogMessage("Closing all newly created tabs by bridge...","log"),this.newlyCreatedTabIds))await chrome.tabs.remove(e);this.newlyCreatedTabIds=[]}await super.destroy(),this.bridgeClient&&(this.bridgeClient.disconnect(),this.bridgeClient=null,this.onDisconnect())}}},43436:function(e,t,a){"use strict";var i,r,s,n,o,c,l=a(73656),u=Object.create,h=Object.defineProperty,d=Object.getOwnPropertyDescriptor,p=Object.getOwnPropertyNames,g=Object.getPrototypeOf,m=Object.prototype.hasOwnProperty,f=(e,t,a,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let r of p(t))m.call(e,r)||r===a||h(e,r,{get:()=>t[r],enumerable:!(i=d(t,r))||i.enumerable});return e},y=(e,t,a)=>(a=null!=e?u(g(e)):{},f(!t&&e&&e.__esModule?a:h(a,"default",{value:e,enumerable:!0}),e)),w=(e,t,a)=>{if(!t.has(e))throw TypeError("Cannot "+a)},b=(e,t,a)=>(w(e,t,"read from private field"),a?a.call(e):t.get(e)),v=(e,t,a)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,a)},T=(e,t,a,i)=>(w(e,t,"write to private field"),i?i.call(e,a):t.set(e,a),a),k=(e,t,a)=>(w(e,t,"access private method"),a),x={},C={ChromeExtensionProxyPage:()=>ez,ChromeExtensionProxyPageAgent:()=>eM,ERROR_CODE_NOT_IMPLEMENTED_AS_DESIGNED:()=>F,overrideAIConfig:()=>eY.overrideAIConfig};for(var I in C)h(x,I,{get:C[I],enumerable:!0});e.exports=f(h({},"__esModule",{value:!0}),x);var D=a(28689),E=a(83535),S=a(27966),A=a(97188),$=a(58453),M=a(8434),P=y(a(6020)),_=class{constructor({content:e,rect:t,locator:a,id:i,attributes:r,indexId:s,xpaths:n,isVisible:o}){this.content=e,this.rect=t,this.center=[Math.floor(t.left+t.width/2),Math.floor(t.top+t.height/2)],this.locator=a,this.id=i,this.attributes=r,this.indexId=s,this.xpaths=n,this.isVisible=o}};async function O(e,t){let a,i;if((0,M.assert)(e,"page is required"),e._forceUsePageContext)return await e._forceUsePageContext();let r=await e.url();(0,E.uploadTestInfoToServer)({testUrl:r}),await Promise.all([e.screenshotBase64().then(e=>{a=e}),e.getElementsNodeTree().then(async e=>{i=e})]);let s=(0,A.traverseTree)(i,e=>{let{rect:t,id:a,content:i,attributes:r,locator:s,indexId:n,isVisible:o}=e;return new _({rect:t,locator:s,id:a,content:i,attributes:r,indexId:n,isVisible:o})});(0,M.assert)(a,"screenshotBase64 is required");let n=(0,A.treeToList)(s),o=await e.size();return o.dpr&&o.dpr>1&&(a=await (0,$.resizeImgBase64)(a,{width:o.width,height:o.height})),{content:n,tree:s,size:o,screenshotBase64:a,url:r}}var F="NOT_IMPLEMENTED_AS_DESIGNED",R=a(72496),N=y(a(20097)),L=a(15117),W=a(77410),B=a(8434),j=a(19237),U=class{constructor(e,t,a){this.script=e,this.setupAgent=t,this.onTaskStatusChange=a,this.taskStatusList=[],this.status="init",this.unnamedResultIndex=0,this.pageAgent=null,this.result={};let i=e.target||e.web||e.android;B.ifInBrowser?this.output=void 0:i?.output?this.output=(0,W.resolve)(l.cwd(),i.output):this.output=(0,W.join)((0,j.getMidsceneRunSubDir)("output"),`${l.pid}.json`),this.taskStatusList=(e.tasks||[]).map((e,t)=>({...e,index:t,status:"init",totalSteps:e.flow?.length||0}))}setResult(e,t){let a=e||this.unnamedResultIndex++;this.result[a]&&console.warn(`result key ${a} already exists, will overwrite`),this.result[a]=t,this.flushResult()}setPlayerStatus(e,t){this.status=e,this.errorInSetup=t}notifyCurrentTaskStatusChange(e){let t="number"==typeof e?e:this.currentTaskIndex;if("number"!=typeof t)return;let a=this.taskStatusList[t];this.onTaskStatusChange&&this.onTaskStatusChange(a)}async setTaskStatus(e,t,a){this.taskStatusList[e].status=t,a&&(this.taskStatusList[e].error=a),this.notifyCurrentTaskStatusChange(e)}setTaskIndex(e){this.currentTaskIndex=e}flushResult(){if(Object.keys(this.result).length&&this.output){let e=(0,W.resolve)(l.cwd(),this.output),t=(0,W.dirname)(e);(0,L.existsSync)(t)||(0,L.mkdirSync)(t,{recursive:!0}),(0,L.writeFileSync)(e,JSON.stringify(this.result,void 0,2))}}async playTask(e,t){let{flow:a}=e;for(let i in(0,B.assert)(a,"missing flow in task"),a){e.currentStep=Number.parseInt(i,10);let r=a[i];if("aiAction"in r||"ai"in r){let e=r.aiAction||r.ai;(0,B.assert)(e,"missing prompt for ai (aiAction)"),(0,B.assert)("string"==typeof e,"prompt for aiAction must be a string"),await t.aiAction(e,{cacheable:r.cacheable})}else if("aiAssert"in r){let e=r.aiAssert,a=r.errorMessage;(0,B.assert)(e,"missing prompt for aiAssert"),(0,B.assert)("string"==typeof e,"prompt for aiAssert must be a string"),await t.aiAssert(e,a)}else if("aiQuery"in r){let e=r.aiQuery,a={domIncluded:r.domIncluded,screenshotIncluded:r.screenshotIncluded};(0,B.assert)(e,"missing prompt for aiQuery"),(0,B.assert)("string"==typeof e,"prompt for aiQuery must be a string");let i=await t.aiQuery(e,a);this.setResult(r.name,i)}else if("aiNumber"in r){let e=r.aiNumber,a={domIncluded:r.domIncluded,screenshotIncluded:r.screenshotIncluded};(0,B.assert)(e,"missing prompt for number"),(0,B.assert)("string"==typeof e,"prompt for number must be a string");let i=await t.aiNumber(e,a);this.setResult(r.name,i)}else if("aiString"in r){let e=r.aiString,a={domIncluded:r.domIncluded,screenshotIncluded:r.screenshotIncluded};(0,B.assert)(e,"missing prompt for string"),(0,B.assert)("string"==typeof e,"prompt for string must be a string");let i=await t.aiString(e,a);this.setResult(r.name,i)}else if("aiBoolean"in r){let e=r.aiBoolean,a={domIncluded:r.domIncluded,screenshotIncluded:r.screenshotIncluded};(0,B.assert)(e,"missing prompt for boolean"),(0,B.assert)("string"==typeof e,"prompt for boolean must be a string");let i=await t.aiBoolean(e,a);this.setResult(r.name,i)}else if("aiLocate"in r){let e=r.aiLocate;(0,B.assert)(e,"missing prompt for aiLocate"),(0,B.assert)("string"==typeof e,"prompt for aiLocate must be a string");let a=await t.aiLocate(e);this.setResult(r.name,a)}else if("aiWaitFor"in r){let e=r.aiWaitFor;(0,B.assert)(e,"missing prompt for aiWaitFor"),(0,B.assert)("string"==typeof e,"prompt for aiWaitFor must be a string");let a=r.timeout;await t.aiWaitFor(e,{timeoutMs:a})}else if("sleep"in r){let e=r.sleep,t=e;"string"==typeof e&&(t=Number.parseInt(e,10)),(0,B.assert)(t&&t>0,`ms for sleep must be greater than 0, but got ${e}`),await new Promise(e=>setTimeout(e,t))}else if("aiTap"in r)await t.aiTap(r.aiTap,r);else if("aiRightClick"in r)await t.aiRightClick(r.aiRightClick,r);else if("aiHover"in r)await t.aiHover(r.aiHover,r);else if("aiInput"in r)await t.aiInput(r.aiInput,r.locate,r);else if("aiKeyboardPress"in r)await t.aiKeyboardPress(r.aiKeyboardPress,r.locate,r);else if("aiScroll"in r)await t.aiScroll(r,r.locate,r);else if("javascript"in r){let e=await t.evaluateJavaScript(r.javascript);this.setResult(r.name,e)}else if("logScreenshot"in r)await t.logScreenshot(r.logScreenshot,{content:r.content||""});else throw Error(`unknown flowItem: ${JSON.stringify(r)}`)}this.reportFile=t.reportFile}async run(){let{target:e,web:t,android:a,tasks:i}=this.script;this.setPlayerStatus("running");let r=null,s=[];try{let{agent:i,freeFn:n}=await this.setupAgent(t||e||a),o=(r=i).onTaskStartTip;r.onTaskStartTip=e=>{"running"===this.status&&(this.agentStatusTip=e),o?.(e)},s=[...n||[],{name:"restore-agent-onTaskStartTip",fn:()=>{r&&(r.onTaskStartTip=o)}}]}catch(e){this.setPlayerStatus("error",e);return}this.pageAgent=r;let n=0;this.setPlayerStatus("running");let o=!1;for(;n<i.length;){let e=this.taskStatusList[n];this.setTaskStatus(n,"running"),this.setTaskIndex(n);try{await this.playTask(e,this.pageAgent),this.setTaskStatus(n,"done")}catch(t){if(this.setTaskStatus(n,"error",t),e.continueOnError);else{this.reportFile=r.reportFile,o=!0;break}}this.reportFile=r.reportFile,n++}for(let e of(o?this.setPlayerStatus("error"):this.setPlayerStatus("done"),this.agentStatusTip="",s))try{await e.fn()}catch(e){}}};y(a(20097));var H=a(8434),K=y(a(20097)),V=a(83535),z=a(18409),Y=a(27966),q=a(81026),X=a(8434),J=a(72496),Q=a(28689),G=a(83535),Z=a(18409),ee=a(81026),et=a(8434);function ea(e){return e.subType&&"Plan"!==e.subType?`${e.type} / ${e.subType||""}`:e.type}function ei(e){return e?"string"==typeof e?e:e.prompt:""}function er(e){return e?`${e.direction||"down"}, ${e.scrollType||"once"}, ${e.distance||"distance-not-set"}`:""}function es(e,t){return t?`${e} - ${t}`:e}var en=`
if (!window.__MIDSCENE_NEW_TAB_INTERCEPTOR_INITIALIZED__) {
  window.__MIDSCENE_NEW_TAB_INTERCEPTOR_INITIALIZED__ = true;

  // Intercept the window.open method (only once)
  window.open = function(url) {
    console.log('Blocked window.open:', url);
    window.location.href = url;
    return null;
  };

  // Block all a tag clicks with target="_blank" (only once)
  document.addEventListener('click', function(e) {
    const target = e.target.closest('a');
    if (target && target.target === '_blank') {
      e.preventDefault();
      console.log('Blocked new tab:', target.href);
      window.location.href = target.href;
      target.removeAttribute('target');
    }
  }, true);
}
`,eo=(0,ee.getDebug)("page-task-executor"),ec=e=>"android"===e.pageType,el=class{constructor(e,t,a){this.conversationHistory=[],this.page=e,this.insight=t,this.taskCache=a.taskCache,this.onTaskStartCallback=a?.onTaskStart}async recordScreenshot(e){let t=await this.page.screenshotBase64();return{type:"screenshot",ts:Date.now(),screenshot:t,timing:e}}async getElementXpath(e,t){let a=t?.id;if(t?.attributes?.nodeType===Z.NodeType.POSITION){await this.insight.contextRetrieverFn("locate");let i=(0,Q.elementByPositionWithElementInfo)(e.tree,{x:t.center[0],y:t.center[1]},{requireStrictDistance:!1,filterPositionElements:!0});i?.id?a=i.id:eo("no element id found for position node, will not update cache",t)}if(a)try{return await this.page.getXpathsById(a)}catch(e){eo("getXpathsById error: ",e)}}prependExecutorWithScreenshot(e,t=!1){return{...e,executor:async(a,i,...r)=>{let s=[],{task:n}=i;n.recorder=s;let o=await this.recordScreenshot(`before ${n.type}`);s.push(o);let c=await e.executor(a,i,...r);if("Action"===e.type&&await Promise.all([(async()=>{if(await (0,G.sleep)(100),this.page.waitUntilNetworkIdle)try{await this.page.waitUntilNetworkIdle()}catch(e){}})(),(0,G.sleep)(200)]),t){let e=await this.recordScreenshot("after Action");s.push(e)}return c}}}async convertPlanToExecutable(e,t){let a=[];return e.forEach(e=>{if("Locate"===e.type){if(null===e.locate||e.locate?.id===null||e.locate?.id==="null")return;let i={type:"Insight",subType:"Locate",param:e.locate?{...e.locate,cacheable:t?.cacheable}:void 0,thought:e.thought,locate:e.locate,executor:async(e,t)=>{let a,i,r,{task:s}=t;(0,et.assert)(e?.prompt||e?.id||e?.bbox,"No prompt or id or position or bbox to locate"),this.insight.onceDumpUpdatedFn=e=>{a=e,i=e?.taskInfo?.usage,s.log={dump:a},s.usage=i};let n=Date.now(),o=await this.insight.contextRetrieverFn("locate");s.pageContext=o,s.recorder=[{type:"screenshot",ts:n,screenshot:o.screenshotBase64,timing:"before locate"}];let c=!1,l=e.prompt,u=this.taskCache?.matchLocateCache(l),h=u?.cacheContent?.xpaths,d=null;try{if(h?.length&&this.taskCache?.isCacheResultUsed&&e?.cacheable!==!1)for(let e=0;e<h.length;e++){let t=await this.page.getElementInfoByXpath(h[e]);if(t?.id){d=t,eo("cache hit, prompt: %s",l),c=!0,eo("found a new new element with same xpath, xpath: %s, id: %s",h[e],t?.id);break}}}catch(e){eo("get element info by xpath error: ",e)}let p=Date.now(),g=d||function(e,t){if(e){if(e.id)return(0,A.getNodeFromCacheList)(e.id);if(e.bbox){let a={x:Math.floor((e.bbox[0]+e.bbox[2])/2),y:Math.floor((e.bbox[1]+e.bbox[3])/2)},i=(0,D.elementByPositionWithElementInfo)(t,a);return i||(i=(0,A.generateElementByPosition)(a)),i}}}(e,o.tree)||(await this.insight.locate(e,{context:o})).element,m=Date.now()-p;if(g&&this.taskCache&&!c&&e?.cacheable!==!1){let e=await this.getElementXpath(o,g);e?.length?(r=e,this.taskCache.updateOrAppendCacheRecord({type:"locate",prompt:l,xpaths:e},u)):eo("no xpaths found, will not update cache",l,e)}if(!g)throw Error(`Element not found: ${e.prompt}`);return{output:{element:g},pageContext:o,cache:{hit:c,originalXpaths:h,currentXpaths:r},aiCost:m}}};a.push(i)}else if("Assert"===e.type||"AssertWithoutThrow"===e.type){let t={type:"Insight",subType:"Assert",param:e.param,thought:e.thought,locate:e.locate,executor:async(t,a)=>{let i,{task:r}=a;this.insight.onceDumpUpdatedFn=e=>{i=e};let s=await this.insight.assert(e.param.assertion);if(!s.pass){if("Assert"===e.type)throw r.output=s,r.log={dump:i},Error(s.thought||"Assertion failed without reason");r.error=s.thought}return{output:s,log:{dump:i},usage:s.usage}}};a.push(t)}else if("Input"===e.type){let t={type:"Action",subType:"Input",param:e.param,thought:e.thought,locate:e.locate,executor:async(e,{element:t})=>{(!t||(await this.page.clearInput(t),e&&e.value))&&await this.page.keyboard.type(e.value,{autoDismissKeyboard:e.autoDismissKeyboard})}};a.push(t)}else if("KeyboardPress"===e.type){let t={type:"Action",subType:"KeyboardPress",param:e.param,thought:e.thought,locate:e.locate,executor:async e=>{let t=function(e){let t=Array.isArray(e)?e:[e];return t.reduce((e,a)=>{let i=t.includes("Meta")||t.includes("Control");return i&&("a"===a||"A"===a)?e.concat([{key:a,command:"SelectAll"}]):i&&("c"===a||"C"===a)?e.concat([{key:a,command:"Copy"}]):i&&("v"===a||"V"===a)?e.concat([{key:a,command:"Paste"}]):e.concat([{key:a}])},[])}(e.value);await this.page.keyboard.press(t)}};a.push(t)}else if("Tap"===e.type){let t={type:"Action",subType:"Tap",thought:e.thought,locate:e.locate,executor:async(e,{element:t})=>{(0,et.assert)(t,"Element not found, cannot tap"),await this.page.mouse.click(t.center[0],t.center[1])}};a.push(t)}else if("RightClick"===e.type){let t={type:"Action",subType:"RightClick",thought:e.thought,locate:e.locate,executor:async(e,{element:t})=>{(0,et.assert)(t,"Element not found, cannot right click"),await this.page.mouse.click(t.center[0],t.center[1],{button:"right"})}};a.push(t)}else if("Drag"===e.type){let t={type:"Action",subType:"Drag",param:e.param,thought:e.thought,locate:e.locate,executor:async e=>{(0,et.assert)(e?.start_box&&e?.end_box,"No start_box or end_box to drag"),await this.page.mouse.drag(e.start_box,e.end_box)}};a.push(t)}else if("Hover"===e.type){let t={type:"Action",subType:"Hover",thought:e.thought,locate:e.locate,executor:async(e,{element:t})=>{(0,et.assert)(t,"Element not found, cannot hover"),await this.page.mouse.move(t.center[0],t.center[1])}};a.push(t)}else if("Scroll"===e.type){let t={type:"Action",subType:"Scroll",param:e.param,thought:e.thought,locate:e.locate,executor:async(e,{element:t})=>{let a=t?{left:t.center[0],top:t.center[1]}:void 0,i=e?.scrollType;if("untilTop"===i)await this.page.scrollUntilTop(a);else if("untilBottom"===i)await this.page.scrollUntilBottom(a);else if("untilRight"===i)await this.page.scrollUntilRight(a);else if("untilLeft"===i)await this.page.scrollUntilLeft(a);else if("once"!==i&&i)throw Error(`Unknown scroll event type: ${i}, taskParam: ${JSON.stringify(e)}`);else{if(e?.direction!=="down"&&e&&e.direction)if("up"===e.direction)await this.page.scrollUp(e.distance||void 0,a);else if("left"===e.direction)await this.page.scrollLeft(e.distance||void 0,a);else if("right"===e.direction)await this.page.scrollRight(e.distance||void 0,a);else throw Error(`Unknown scroll direction: ${e.direction}`);else await this.page.scrollDown(e?.distance||void 0,a);await (0,G.sleep)(500)}}};a.push(t)}else if("Sleep"===e.type){let t={type:"Action",subType:"Sleep",param:e.param,thought:e.thought,locate:e.locate,executor:async e=>{await (0,G.sleep)(e?.timeMs||3e3)}};a.push(t)}else if("Error"===e.type){let t={type:"Action",subType:"Error",param:e.param,thought:e.thought||e.param?.thought,locate:e.locate,executor:async()=>{throw Error(e?.thought||e.param?.thought||"error without thought")}};a.push(t)}else if("ExpectedFalsyCondition"===e.type){let t={type:"Action",subType:"ExpectedFalsyCondition",param:null,thought:e.param?.reason,locate:e.locate,executor:async()=>{}};a.push(t)}else if("Finished"===e.type){let t={type:"Action",subType:"Finished",param:null,thought:e.thought,locate:e.locate,executor:async e=>{}};a.push(t)}else if("AndroidHomeButton"===e.type){let t={type:"Action",subType:"AndroidHomeButton",param:null,thought:e.thought,locate:e.locate,executor:async e=>{(0,et.assert)(ec(this.page),"Cannot use home button on non-Android devices"),await this.page.home()}};a.push(t)}else if("AndroidBackButton"===e.type){let t={type:"Action",subType:"AndroidBackButton",param:null,thought:e.thought,locate:e.locate,executor:async e=>{(0,et.assert)(ec(this.page),"Cannot use back button on non-Android devices"),await this.page.back()}};a.push(t)}else if("AndroidRecentAppsButton"===e.type){let t={type:"Action",subType:"AndroidRecentAppsButton",param:null,thought:e.thought,locate:e.locate,executor:async e=>{(0,et.assert)(ec(this.page),"Cannot use recent apps button on non-Android devices"),await this.page.recentApps()}};a.push(t)}else throw Error(`Unknown or unsupported task type: ${e.type}`)}),{tasks:a.map((e,t)=>"Action"===e.type?this.prependExecutorWithScreenshot(e,t===a.length-1):e)}}async setupPlanningContext(e){let t=Date.now(),a=await this.insight.contextRetrieverFn("locate"),i={type:"screenshot",ts:t,screenshot:a.screenshotBase64,timing:"before planning"};return e.task.recorder=[i],e.task.pageContext=a,{pageContext:a}}async loadYamlFlowAsPlanning(e,t){let a=new J.Executor(es("Action",e),{onTaskStart:this.onTaskStartCallback});return await a.append({type:"Planning",subType:"LoadYaml",locate:null,param:{userInstruction:e},executor:async(e,a)=>(await this.setupPlanningContext(a),{output:{actions:[],more_actions_needed_by_instruction:!1,log:"",yamlString:t},cache:{hit:!0}})}),await a.flush(),{executor:a}}planningTaskFromPrompt(e,t,a){return{type:"Planning",subType:"Plan",locate:null,param:{userInstruction:e,log:t},executor:async(e,t)=>{let i=Date.now(),{pageContext:r}=await this.setupPlanningContext(t),s=await (0,J.plan)(e.userInstruction,{context:r,log:e.log,actionContext:a,pageType:this.page.pageType}),{actions:n,log:o,more_actions_needed_by_instruction:c,error:l,usage:u,rawResponse:h,sleep:d}=s;t.task.log={...t.task.log||{},rawResponse:h},t.task.usage=u;let p=!1,g=!1,m="",f=(n||[]).reduce((e,t)=>{if(p)return e;if(t.locate)g&&t.locate.bbox&&delete t.locate.bbox,t.locate.bbox&&(g=!0),e.push({type:"Locate",locate:t.locate,param:null,thought:t.locate.prompt});else if(["Tap","Hover","Input"].includes(t.type))return m=`invalid planning response: ${JSON.stringify(t)}`,p=!0,e;return e.push(t),e},[]);if(d){let e=d-(Date.now()-i);e>0&&f.push({type:"Sleep",param:{timeMs:e},locate:null})}return 0===f.length&&(0,et.assert)(!c||d,l?`Failed to plan: ${l}`:m||"No plan found"),{output:{actions:f,more_actions_needed_by_instruction:c,log:o,yamlFlow:s.yamlFlow},cache:{hit:!1},pageContext:r}}}}planningTaskToGoal(e){return{type:"Planning",subType:"Plan",locate:null,param:{userInstruction:e},executor:async(e,t)=>{let{pageContext:a}=await this.setupPlanningContext(t),i=await (0,Q.resizeImageForUiTars)(a.screenshotBase64,a.size);this.appendConversationHistory({role:"user",content:[{type:"image_url",image_url:{url:i}}]});let r=Date.now(),s=await (0,Q.vlmPlanning)({userInstruction:e.userInstruction,conversationHistory:this.conversationHistory,size:a.size}),n=Date.now()-r,{actions:o,action_summary:c}=s;return this.appendConversationHistory({role:"assistant",content:c}),{output:{actions:o,thought:o[0]?.thought,actionType:o[0].type,more_actions_needed_by_instruction:!0,log:"",yamlFlow:s.yamlFlow},cache:{hit:!1},aiCost:n}}}}async runPlans(e,t,a){let i=new J.Executor(e,{onTaskStart:this.onTaskStartCallback}),{tasks:r}=await this.convertPlanToExecutable(t,a);return await i.append(r),{output:await i.flush(),executor:i}}async action(e,t,a){let i=new J.Executor(es("Action",e),{onTaskStart:this.onTaskStartCallback}),r=this.planningTaskFromPrompt(e,void 0,t),s=0,n=[],o=[];for(;r;){let c;if(s>10)return this.appendErrorPlan(i,"Replanning too many times, please split the task into multiple steps");await i.append(r);let l=await i.flush();if(i.isInErrorState())return{output:l,executor:i};let u=l.actions||[];o.push(...l.yamlFlow||[]);try{c=await this.convertPlanToExecutable(u,a),i.append(c.tasks)}catch(e){return this.appendErrorPlan(i,`Error converting plans to executable tasks: ${e}, plans: ${JSON.stringify(u)}`)}if(await i.flush(),i.isInErrorState())return{output:void 0,executor:i};if(l?.log&&n.push(l.log),!l.more_actions_needed_by_instruction){r=null;break}r=this.planningTaskFromPrompt(e,n.length>0?`- ${n.join("\n- ")}`:void 0,t),s++}return{output:{yamlFlow:o},executor:i}}async actionToGoal(e,t){let a=new J.Executor(es("Action",e),{onTaskStart:this.onTaskStartCallback});this.conversationHistory=[];let i=0,r=[];for(;i<40;){let s;i++;let n=this.planningTaskToGoal(e);await a.append(n);let o=await a.flush();if(a.isInErrorState())return{output:void 0,executor:a};let c=o.actions;r.push(...o.yamlFlow||[]);try{s=await this.convertPlanToExecutable(c,t),a.append(s.tasks)}catch(e){return this.appendErrorPlan(a,`Error converting plans to executable tasks: ${e}, plans: ${JSON.stringify(c)}`)}if(await a.flush(),a.isInErrorState())return{output:void 0,executor:a};if("Finished"===c[0].type)break}return{output:{yamlFlow:r},executor:a}}async createTypeQueryTask(e,t,a){let i=new J.Executor(es(e,"string"==typeof t?t:JSON.stringify(t)),{onTaskStart:this.onTaskStartCallback});return await i.append(this.prependExecutorWithScreenshot({type:"Insight",subType:e,locate:null,param:{dataDemand:t},executor:async i=>{let r;this.insight.onceDumpUpdatedFn=e=>{r=e};let s="Query"!==e,n=t;s&&(n={result:`${e}, ${t}`});let{data:o,usage:c}=await this.insight.extract(n,a),l=o;return s&&((0,et.assert)(o?.result!==void 0,"No result in query data"),l=o.result),{output:l,log:{dump:r},usage:c}}})),{output:await i.flush(),executor:i}}async query(e,t){return this.createTypeQueryTask("Query",e,t)}async boolean(e,t){return this.createTypeQueryTask("Boolean",e,t)}async number(e,t){return this.createTypeQueryTask("Number",e,t)}async string(e,t){return this.createTypeQueryTask("String",e,t)}async assert(e){let t=`assert: ${e}`,a=new J.Executor(es("Assert",t),{onTaskStart:this.onTaskStartCallback}),{tasks:i}=await this.convertPlanToExecutable([{type:"Assert",param:{assertion:e},locate:null}]);return await a.append(this.prependExecutorWithScreenshot(i[0])),{output:await a.flush(),executor:a}}appendConversationHistory(e){if("user"===e.role&&this.conversationHistory.filter(e=>"user"===e.role).length>=4&&"user"===e.role){let e=this.conversationHistory.findIndex(e=>"user"===e.role);e>=0&&this.conversationHistory.splice(e,1)}this.conversationHistory.push(e)}async appendErrorPlan(e,t){let{tasks:a}=await this.convertPlanToExecutable([{type:"Error",param:{thought:t},locate:null}]);return await e.append(this.prependExecutorWithScreenshot(a[0])),await e.flush(),{output:void 0,executor:e}}async waitFor(e,t){let a=`waitFor: ${e}`,i=new J.Executor(es("WaitFor",a),{onTaskStart:this.onTaskStartCallback}),{timeoutMs:r,checkIntervalMs:s}=t;(0,et.assert)(e,"No assertion for waitFor"),(0,et.assert)(r,"No timeoutMs for waitFor"),(0,et.assert)(s,"No checkIntervalMs for waitFor");let n=Date.now(),o=Date.now(),c="";for(;Date.now()-n<r;){o=Date.now();let t={type:"AssertWithoutThrow",param:{assertion:e},locate:null},{tasks:a}=await this.convertPlanToExecutable([t]);await i.append(this.prependExecutorWithScreenshot(a[0]));let r=await i.flush();if(r?.pass)return{output:void 0,executor:i};c=r?.thought||`unknown error when waiting for assertion: ${e}`;let n=Date.now();if(n-o<s){let e={type:"Sleep",param:{timeMs:s-(n-o)},locate:null},{tasks:t}=await this.convertPlanToExecutable([e]);await i.append(this.prependExecutorWithScreenshot(t[0])),await i.flush()}}return this.appendErrorPlan(i,`waitFor timeout: ${c}`)}},eu=a(81026),eh=a(8434),ed=(0,eu.getDebug)("plan-builder");function ep(e,t,a){let i=[],r=t?{type:"Locate",locate:t,param:t,thought:""}:null;if(("Tap"===e||"Hover"===e||"RightClick"===e)&&((0,eh.assert)(t,`missing locate info for action "${e}"`),(0,eh.assert)(r,`missing locate info for action "${e}"`),i=[r,{type:e,param:null,thought:"",locate:t}]),"Input"===e||"KeyboardPress"===e){"Input"===e&&(0,eh.assert)(t,`missing locate info for action "${e}"`),(0,eh.assert)(a,`missing param for action "${e}"`);let s={type:e,param:a,thought:"",locate:t};i=r?[r,s]:[s]}if("Scroll"===e){(0,eh.assert)(a,`missing param for action "${e}"`);let s={type:e,param:a,thought:"",locate:t};i=r?[r,s]:[s]}if("Sleep"===e&&((0,eh.assert)(a,`missing param for action "${e}"`),i=[{type:e,param:a,thought:"",locate:null}]),"Locate"===e&&((0,eh.assert)(t,`missing locate info for action "${e}"`),i=[{type:e,param:t,locate:t,thought:""}]),i)return ed("buildPlans",i),i;throw Error(`Not supported type: ${e}`)}var eg=y(a(75044)),em=a(15117),ef=a(77410),ey=a(19237),ew=a(81026),eb=a(8434),ev=y(a(20097)),eT=y(a(81507)),ek="0.17.5",ex=(0,ew.getDebug)("cache"),eC=".cache.yaml",eI=class{constructor(e,t,a){let i;this.matchedCacheIndices=new Set,(0,eg.default)(e,"cacheId is required"),this.cacheId=e.replace(/[:*?"<>| ]/g,"-"),this.cacheFilePath=eb.ifInBrowser?void 0:a||(0,ef.join)((0,ey.getMidsceneRunSubDir)("cache"),`${this.cacheId}${eC}`),this.isCacheResultUsed=t,this.cacheFilePath&&(i=this.loadCacheFromFile()),i||(i={midsceneVersion:ek,cacheId:this.cacheId,caches:[]}),this.cache=i,this.cacheOriginalLength=this.cache.caches.length}matchCache(e,t){for(let a=0;a<this.cacheOriginalLength;a++){let i=this.cache.caches[a],r=`${t}:${e}:${a}`;if(i.type===t&&i.prompt===e&&!this.matchedCacheIndices.has(r))return this.matchedCacheIndices.add(r),ex("cache found and marked as used, type: %s, prompt: %s, index: %d",t,e,a),{cacheContent:i,updateFn:r=>{ex("will call updateFn to update cache, type: %s, prompt: %s, index: %d",t,e,a),r(i),ex("cache updated, will flush to file, type: %s, prompt: %s, index: %d",t,e,a),this.flushCacheToFile()}}}ex("no unused cache found, type: %s, prompt: %s",t,e)}matchPlanCache(e){return this.matchCache(e,"plan")}matchLocateCache(e){return this.matchCache(e,"locate")}appendCache(e){ex("will append cache",e),this.cache.caches.push(e),this.flushCacheToFile()}loadCacheFromFile(){let e=this.cacheFilePath;if((0,eg.default)(e,"cache file path is required"),!(0,em.existsSync)(e)){ex("no cache file found, path: %s",e);return}let t=e.replace(eC,".json");if((0,em.existsSync)(t)&&this.isCacheResultUsed){console.warn(`An outdated cache file from an earlier version of Midscene has been detected. Since version 0.17, we have implemented an improved caching strategy. Please delete the old file located at: ${t}.`);return}try{let t=(0,em.readFileSync)(e,"utf8"),a=ev.default.load(t);if(!ek){ex("no midscene version info, will not read cache from file");return}if(eT.default.lt(a.midsceneVersion,"0.16.10")&&!a.midsceneVersion.includes("beta")){console.warn(`You are using an old version of Midscene cache file, and we cannot match any info from it. Starting from Midscene v0.17, we changed our strategy to use xpath for cache info, providing better performance.
Please delete the existing cache and rebuild it. Sorry for the inconvenience.
cache file: ${e}`);return}return ex("cache loaded from file, path: %s, cache version: %s, record length: %s",e,a.midsceneVersion,a.caches.length),a.midsceneVersion=ek,a}catch(t){ex("cache file exists but load failed, path: %s, error: %s",e,t);return}}flushCacheToFile(){if(!ek){ex("no midscene version info, will not write cache to file");return}if(!this.cacheFilePath){ex("no cache file path, will not write cache to file");return}try{let e=(0,ef.dirname)(this.cacheFilePath);(0,em.existsSync)(e)||((0,em.mkdirSync)(e,{recursive:!0}),ex("created cache directory: %s",e));let t=ev.default.dump(this.cache);(0,em.writeFileSync)(this.cacheFilePath,t),ex("cache flushed to file: %s",this.cacheFilePath)}catch(e){ex("write cache to file failed, path: %s, error: %s",this.cacheFilePath,e)}}updateOrAppendCacheRecord(e,t){t?"plan"===e.type?t.updateFn(t=>{t.yamlWorkflow=e.yamlWorkflow}):t.updateFn(t=>{t.xpaths=e.xpaths}):this.appendCache(e)}},eD=(0,q.getDebug)("web-integration"),eE=(e,t)=>{let[a,i]=e,[r,s]=t;return Math.round(Math.sqrt((a-r)**2+(i-s)**2))},eS=(e,t)=>{let[a,i]=e,{left:r,top:s,width:n,height:o}=t;return a>=r&&a<=r+n&&i>=s&&i<=s+o},eA={domIncluded:!1,screenshotIncluded:!0},e$=class{constructor(e,t){this.dryMode=!1,this.page=e,this.opts=Object.assign({generateReport:!0,autoPrintReportMsg:!0,groupName:"Midscene Report",groupDescription:""},t||{}),("puppeteer"===this.page.pageType||"playwright"===this.page.pageType)&&(this.page.waitForNavigationTimeout=this.opts.waitForNavigationTimeout||z.DEFAULT_WAIT_FOR_NAVIGATION_TIMEOUT,this.page.waitForNetworkIdleTimeout=this.opts.waitForNetworkIdleTimeout||z.DEFAULT_WAIT_FOR_NETWORK_IDLE_TIMEOUT),this.onTaskStartTip=this.opts.onTaskStartTip,this.insight=new R.Insight(async e=>this.getUIContext(e)),t?.cacheId&&"android"!==this.page.pageType&&(this.taskCache=new eI(t.cacheId,(0,Y.getAIConfigInBoolean)("MIDSCENE_CACHE"))),this.taskExecutor=new el(this.page,this.insight,{taskCache:this.taskCache,onTaskStart:this.callbackOnTaskStartTip.bind(this)}),this.dump=this.resetDump(),this.reportFileName=function(e="web"){let t=(0,S.getAIConfig)(S.MIDSCENE_REPORT_TAG_NAME),a=(0,P.default)().format("YYYY-MM-DD_HH-mm-ss"),i=(0,M.uuid)().substring(0,8);return`${t||e}-${a}-${i}`}(t?.testId||this.page.pageType||"web")}async getUIContext(e){return e&&("extract"===e||"assert"===e)?await O(this.page,{ignoreMarker:!0}):await O(this.page,{ignoreMarker:!!(0,Y.vlLocateMode)()})}async setAIActionContext(e){this.opts.aiActionContext=e}resetDump(){return this.dump={groupName:this.opts.groupName,groupDescription:this.opts.groupDescription,executions:[]},this.dump}appendExecutionDump(e){this.dump.executions.push(e)}dumpDataString(){return this.dump.groupName=this.opts.groupName,this.dump.groupDescription=this.opts.groupDescription,(0,V.stringifyDumpData)(this.dump)}reportHTMLString(){return(0,V.reportHTMLContent)(this.dumpDataString())}writeOutActionDumps(){let{generateReport:e,autoPrintReportMsg:t}=this.opts;if(this.reportFile=(0,V.writeLogFile)({fileName:this.reportFileName,fileExt:V.groupedActionDumpFileExt,fileContent:this.dumpDataString(),type:"dump",generateReport:e}),eD("writeOutActionDumps",this.reportFile),e&&t&&this.reportFile){var a;a=this.reportFile,(0,M.logMsg)(`Midscene - report file updated: ${a}`)}}async callbackOnTaskStartTip(e){let t=function(e){let t;if("Planning"===e.type&&(t=e?.param?.userInstruction),"Insight"===e.type&&(t=e?.param?.prompt||e?.param?.id||e?.param?.dataDemand||e?.param?.assertion),"Action"===e.type){let a=e?.locate,i=a?ei(a):"";t=e.thought||"","number"==typeof e?.param?.timeMs?t=`${e?.param?.timeMs}ms`:"string"==typeof e?.param?.scrollType?t=er(e?.param):void 0!==e?.param?.value&&(t=e?.param?.value),i&&(t=t?`${i} - ${t}`:i)}return void 0===t?"":"string"==typeof t?t:JSON.stringify(t,void 0,2)}(e),a=t?`${ea(e)} - ${t}`:ea(e);this.onTaskStartTip&&await this.onTaskStartTip(a)}afterTaskRunning(e,t=!1){if(this.appendExecutionDump(e.dump()),this.writeOutActionDumps(),e.isInErrorState()&&!t){let t=e.latestErrorTask();throw Error(`${t?.error}
${t?.errorStack}`)}}buildDetailedLocateParam(e,t){if((0,X.assert)(e,"missing locate prompt"),"object"==typeof t){let a=t.prompt??e;return{prompt:a,deepThink:t.deepThink??!1,cacheable:t.cacheable??!0}}return{prompt:e}}async aiTap(e,t){let a=this.buildDetailedLocateParam(e,t),i=ep("Tap",a),{executor:r,output:s}=await this.taskExecutor.runPlans(es("Tap",ei(a)),i,{cacheable:t?.cacheable});return this.afterTaskRunning(r),s}async aiRightClick(e,t){let a=this.buildDetailedLocateParam(e,t),i=ep("RightClick",a),{executor:r,output:s}=await this.taskExecutor.runPlans(es("RightClick",ei(a)),i,{cacheable:t?.cacheable});return this.afterTaskRunning(r),s}async aiHover(e,t){let a=this.buildDetailedLocateParam(e,t),i=ep("Hover",a),{executor:r,output:s}=await this.taskExecutor.runPlans(es("Hover",ei(a)),i,{cacheable:t?.cacheable});return this.afterTaskRunning(r),s}async aiInput(e,t,a){(0,X.assert)("string"==typeof e,"input value must be a string, use empty string if you want to clear the input"),(0,X.assert)(t,"missing locate prompt for input");let i=this.buildDetailedLocateParam(t,a),r=ep("Input",i,{value:e,autoDismissKeyboard:a?.autoDismissKeyboard}),{executor:s,output:n}=await this.taskExecutor.runPlans(es("Input",ei(i)),r,{cacheable:a?.cacheable});return this.afterTaskRunning(s),n}async aiKeyboardPress(e,t,a){(0,X.assert)(e,"missing keyName for keyboard press");let i=t?this.buildDetailedLocateParam(t,a):void 0,r=ep("KeyboardPress",i,{value:e}),{executor:s,output:n}=await this.taskExecutor.runPlans(es("KeyboardPress",ei(i)),r,{cacheable:a?.cacheable});return this.afterTaskRunning(s),n}async aiScroll(e,t,a){let i=t?this.buildDetailedLocateParam(t,a):void 0,r=ep("Scroll",i,e),s=t?`${ei(i)} - ${er(e)}`:er(e),{executor:n,output:o}=await this.taskExecutor.runPlans(es("Scroll",s),r,{cacheable:a?.cacheable});return this.afterTaskRunning(n),o}async aiAction(e,t){let a=t?.cacheable,i="vlm-ui-tars"===(0,Y.vlLocateMode)(),r=i||!1===a?void 0:this.taskCache?.matchPlanCache(e);if(r&&this.taskCache?.isCacheResultUsed){let{executor:t}=await this.taskExecutor.loadYamlFlowAsPlanning(e,r.cacheContent?.yamlWorkflow);await this.afterTaskRunning(t),eD("matched cache, will call .runYaml to run the action");let a=r.cacheContent?.yamlWorkflow;return this.runYaml(a)}let{output:s,executor:n}=await (i?this.taskExecutor.actionToGoal(e,{cacheable:a}):this.taskExecutor.action(e,this.opts.aiActionContext,{cacheable:a}));if(this.taskCache&&s?.yamlFlow&&!1!==a){let t={tasks:[{name:e,flow:s.yamlFlow}]},a=N.default.dump(t);this.taskCache.updateOrAppendCacheRecord({type:"plan",prompt:e,yamlWorkflow:a},r)}return this.afterTaskRunning(n),s}async aiQuery(e,t=eA){let{output:a,executor:i}=await this.taskExecutor.query(e,t);return this.afterTaskRunning(i),a}async aiBoolean(e,t=eA){let{output:a,executor:i}=await this.taskExecutor.boolean(e,t);return this.afterTaskRunning(i),a}async aiNumber(e,t=eA){let{output:a,executor:i}=await this.taskExecutor.number(e,t);return this.afterTaskRunning(i),a}async aiString(e,t=eA){let{output:a,executor:i}=await this.taskExecutor.string(e,t);return this.afterTaskRunning(i),a}async describeElementAtPoint(e,t){let a,{verifyPrompt:i=!0,retryLimit:r=3}=t||{},s=!1,n=0,o="",c=t?.deepThink||!1;for(;!s&&n<r;){n>=2&&(c=!0),eD("aiDescribe",e,"verifyPrompt",i,"retryCount",n,"deepThink",c);let r=await this.insight.describe(e,{deepThink:c});eD("aiDescribe text",r),(0,X.assert)(r.description,`failed to describe element at [${e}]`),o=r.description,(a=await this.verifyLocator(o,c?{deepThink:!0}:void 0,e,t)).pass?s=!0:n++}return{prompt:o,deepThink:c,verifyResult:a}}async verifyLocator(e,t,a,i){eD("verifyLocator",e,t,a,i);let{center:r,rect:s}=await this.aiLocate(e,t),n=eE(a,r),o=eS(a,s),c={pass:n<=(i?.centerDistanceThreshold||20)||o,rect:s,center:r,centerDistance:n};return eD("aiDescribe verifyResult",c),c}async aiLocate(e,t){let a=this.buildDetailedLocateParam(e,t),i=ep("Locate",a),{executor:r,output:s}=await this.taskExecutor.runPlans(es("Locate",ei(a)),i,{cacheable:t?.cacheable});this.afterTaskRunning(r);let{element:n}=s;return{rect:n?.rect,center:n?.center}}async aiAssert(e,t,a){let{output:i,executor:r}=await this.taskExecutor.assert(e);if(this.afterTaskRunning(r,!0),i&&a?.keepRawResponse)return i;if(!i?.pass){let a=t||`Assertion failed: ${e}`,s=`Reason: ${i?.thought||r.latestErrorTask()?.error||"(no_reason)"}`;throw Error(`${a}
${s}`)}}async aiWaitFor(e,t){let{executor:a}=await this.taskExecutor.waitFor(e,{timeoutMs:t?.timeoutMs||15e3,checkIntervalMs:t?.checkIntervalMs||3e3,assertion:e});if(this.appendExecutionDump(a.dump()),this.writeOutActionDumps(),a.isInErrorState()){let e=a.latestErrorTask();throw Error(`${e?.error}
${e?.errorStack}`)}}async ai(e,t="action"){if("action"===t)return this.aiAction(e);if("query"===t)return this.aiQuery(e);if("assert"===t)return this.aiAssert(e);if("tap"===t)return this.aiTap(e);if("rightClick"===t)return this.aiRightClick(e);throw Error(`Unknown type: ${t}, only support 'action', 'query', 'assert', 'tap', 'rightClick'`)}async runYaml(e){let t=new U(function(e,t,a){let i=e;if(-1!==e.indexOf("android")&&e.match(/deviceId:\s*(\d+)/)){let t;i=e.replace(/deviceId:\s*(\d+)/g,(e,a)=>(t=a,`deviceId: '${a}'`)),console.warn(`please use string-style deviceId in yaml script, for example: deviceId: "${t}"`)}let r=i.replace(/\$\{([^}]+)\}/g,(e,t)=>{let a=l.env[t.trim()];if(void 0===a)throw Error(`Environment variable "${t.trim()}" is not defined`);return a}),s=K.default.load(r,{schema:K.default.JSON_SCHEMA}),n=t?`, failed to load ${t}`:"",o=void 0!==s.android?Object.assign({},s.android||{}):void 0,c=s.web||s.target,u=void 0!==c?Object.assign({},c||{}):void 0;return!a&&((0,H.assert)(u||o,`at least one of "target", "web", or "android" properties is required in yaml script${n}`),(0,H.assert)(u&&!o||!u&&o,`only one of "target", "web", or "android" properties is allowed in yaml script${n}`),(u||o)&&(0,H.assert)("object"==typeof u||"object"==typeof o,`property "target/web/android" must be an object${n}`)),(0,H.assert)(s.tasks,`property "tasks" is required in yaml script ${n}`),(0,H.assert)(Array.isArray(s.tasks),`property "tasks" must be an array in yaml script, but got ${s.tasks}`),s}(e,"yaml",!0),async e=>({agent:this,freeFn:[]}));if(await t.run(),"error"===t.status){let e=t.taskStatusList.filter(e=>"error"===e.status).map(e=>`task - ${e.name}: ${e.error?.message}`).join("\n");throw Error(`Error(s) occurred in running yaml script:
${e}`)}return{result:t.result}}async evaluateJavaScript(e){return(0,X.assert)(this.page.evaluateJavaScript,"evaluateJavaScript is not supported in current agent"),this.page.evaluateJavaScript(e)}async destroy(){await this.page.destroy()}async logScreenshot(e,t){let a=await this.page.screenshotBase64(),i=Date.now(),r={type:"Log",subType:"Screenshot",status:"finished",recorder:[{type:"screenshot",ts:i,screenshot:a}],timing:{start:i,end:i,cost:0},param:{content:t?.content||""},executor:async()=>{}},s={sdkVersion:"",logTime:i,model_name:"",model_description:"",name:`Log - ${e||"untitled"}`,description:t?.content||"",tasks:[r]};this.appendExecutionDump(s),this.writeOutActionDumps()}},eM=class extends e${constructor(e,t){super(e,t)}},eP=a(97188),e_=a(8434),eO=a(21415),eF=a(8434),eR=class{constructor(e){v(this,s),v(this,o),v(this,i,new Set),v(this,r,void 0),this._modifiers=0,T(this,r,e)}updateClient(e){T(this,r,e)}async down(e,t={text:void 0,commands:[]}){let a=k(this,o,c).call(this,e),l=b(this,i).has(a.code);b(this,i).add(a.code),this._modifiers|=k(this,s,n).call(this,a.key);let u=void 0===t.text?a.text:t.text;await b(this,r).send("Input.dispatchKeyEvent",{type:u?"keyDown":"rawKeyDown",modifiers:this._modifiers,windowsVirtualKeyCode:a.keyCode,code:a.code,key:a.key,text:u,unmodifiedText:u,autoRepeat:l,location:a.location,isKeypad:3===a.location,commands:t.commands})}async up(e){let t=k(this,o,c).call(this,e);this._modifiers&=~k(this,s,n).call(this,t.key),b(this,i).delete(t.code),await b(this,r).send("Input.dispatchKeyEvent",{type:"keyUp",modifiers:this._modifiers,key:t.key,windowsVirtualKeyCode:t.keyCode,code:t.code,location:t.location})}async sendCharacter(e){await b(this,r).send("Input.insertText",{text:e})}charIsKey(e){return!!eO._keyDefinitions[e]}async type(e,t={}){let a=t.delay||void 0;for(let t of e)this.charIsKey(t)?await this.press(t,{delay:a}):(a&&await new Promise(e=>setTimeout(e,a)),await this.sendCharacter(t))}async press(e,t={}){let{delay:a=null}=t,i=Array.isArray(e)?e:[e];for(let e of i)await this.down(e,t);for(let e of(a&&await new Promise(e=>setTimeout(e,t.delay)),[...i].reverse()))await this.up(e)}};i=new WeakMap,r=new WeakMap,s=new WeakSet,n=function(e){return"Alt"===e?1:"Control"===e?2:"Meta"===e?4:8*("Shift"===e)},o=new WeakSet,c=function(e){let t=8&this._modifiers,a={key:"",keyCode:0,code:"",text:"",location:0},i=eO._keyDefinitions[e];return(0,eF.assert)(i,`Unknown key: "${e}"`),i.key&&(a.key=i.key),t&&i.shiftKey&&(a.key=i.shiftKey),i.keyCode&&(a.keyCode=i.keyCode),t&&i.shiftKeyCode&&(a.keyCode=i.shiftKeyCode),i.code&&(a.code=i.code),i.location&&(a.location=i.location),1===a.key.length&&(a.text=a.key),i.text&&(a.text=i.text),t&&i.shiftText&&(a.text=i.shiftText),-9&this._modifiers&&(a.text=""),a};var eN=y(a(15117)),eL=a(8434),eW=null,eB=async()=>{let e=chrome.runtime.getURL("scripts/htmlElement.js");if(eW)return eW;if(eL.ifInBrowser){let t=await fetch(e);return eW=await t.text()}return eN.default.readFileSync(e,"utf8")},ej=null,eU=async()=>{let e=chrome.runtime.getURL("scripts/water-flow.js");if(ej)return ej;if(eL.ifInBrowser){let t=await fetch(e);return ej=await t.text()}return eN.default.readFileSync(e,"utf8")},eH=null,eK=async()=>{let e=chrome.runtime.getURL("scripts/stop-water-flow.js");if(eH)return eH;if(eL.ifInBrowser){let t=await fetch(e);return eH=await t.text()}return eN.default.readFileSync(e,"utf8")};function eV(e){return new Promise(t=>setTimeout(t,e))}var ez=class{constructor(e){this.pageType="chrome-extension-proxy",this.version="0.17.5",this.activeTabId=null,this.tabIdOfDebuggerAttached=null,this.attachingDebugger=null,this.destroyed=!1,this.isMobileEmulation=null,this.latestMouseX=100,this.latestMouseY=100,this.mouse={click:async(e,t,a)=>{let{button:i="left",count:r=1}=a||{};if(await this.mouse.move(e,t),null===this.isMobileEmulation){let e=await this.sendCommandToDebugger("Runtime.evaluate",{expression:`(() => {
            return /Android|iPhone|iPad|iPod|Mobile/i.test(navigator.userAgent);
          })()`,returnByValue:!0});this.isMobileEmulation=e?.result?.value}if(this.isMobileEmulation&&"left"===i){let a=[{x:Math.round(e),y:Math.round(t)}];await this.sendCommandToDebugger("Input.dispatchTouchEvent",{type:"touchStart",touchPoints:a,modifiers:0}),await this.sendCommandToDebugger("Input.dispatchTouchEvent",{type:"touchEnd",touchPoints:[],modifiers:0})}else await this.sendCommandToDebugger("Input.dispatchMouseEvent",{type:"mousePressed",x:e,y:t,button:i,clickCount:r}),await this.sendCommandToDebugger("Input.dispatchMouseEvent",{type:"mouseReleased",x:e,y:t,button:i,clickCount:r})},wheel:async(e,t,a,i)=>{let r=a||this.latestMouseX,s=i||this.latestMouseY;await this.showMousePointer(r,s),await this.sendCommandToDebugger("Input.dispatchMouseEvent",{type:"mouseWheel",x:r,y:s,deltaX:e,deltaY:t}),this.latestMouseX=r,this.latestMouseY=s},move:async(e,t)=>{await this.showMousePointer(e,t),await this.sendCommandToDebugger("Input.dispatchMouseEvent",{type:"mouseMoved",x:e,y:t}),this.latestMouseX=e,this.latestMouseY=t},drag:async(e,t)=>{await this.mouse.move(e.x,e.y),await this.sendCommandToDebugger("Input.dispatchMouseEvent",{type:"mousePressed",x:e.x,y:e.y,button:"left",clickCount:1}),await this.mouse.move(t.x,t.y),await this.sendCommandToDebugger("Input.dispatchMouseEvent",{type:"mouseReleased",x:t.x,y:t.y,button:"left",clickCount:1})}},this.keyboard={type:async e=>{let t=new eR({send:this.sendCommandToDebugger.bind(this)});await t.type(e,{delay:0})},press:async e=>{let t=new eR({send:this.sendCommandToDebugger.bind(this)}),a=Array.isArray(e)?e:[e];for(let e of a){let a=e.command?[e.command]:[];await t.down(e.key,{commands:a})}for(let e of[...a].reverse())await t.up(e.key)}},this.forceSameTabNavigation=e}async setActiveTabId(e){if(this.activeTabId)throw Error(`Active tab id is already set, which is ${this.activeTabId}, cannot set it to ${e}`);await chrome.tabs.update(e,{active:!0}),this.activeTabId=e}async getActiveTabId(){return this.activeTabId}async getBrowserTabList(){return(await chrome.tabs.query({currentWindow:!0})).map(e=>({id:`${e.id}`,title:e.title,url:e.url,currentActiveTab:e.active})).filter(e=>e.id&&e.title&&e.url)}async getTabIdOrConnectToCurrentTab(){if(this.activeTabId)return this.activeTabId;let e=await chrome.tabs.query({active:!0,currentWindow:!0}).then(e=>e[0]?.id);return this.activeTabId=e||0,this.activeTabId}async attachDebugger(){if((0,e_.assert)(!this.destroyed,"Page is destroyed"),this.attachingDebugger){await this.attachingDebugger;return}this.attachingDebugger=(async()=>{let e=await this.url(),t=null;if(e.startsWith("chrome://"))throw Error("Cannot attach debugger to chrome:// pages, please use Midscene in a normal page with http://, https:// or file://");try{let e=await this.getTabIdOrConnectToCurrentTab();if(this.tabIdOfDebuggerAttached===e)return;if(this.tabIdOfDebuggerAttached&&this.tabIdOfDebuggerAttached!==e){console.log("detach the previous tab",this.tabIdOfDebuggerAttached,"->",e);try{await this.detachDebugger(this.tabIdOfDebuggerAttached)}catch(e){console.error("Failed to detach debugger",e)}}console.log("attaching debugger",e),await chrome.debugger.attach({tabId:e},"1.3"),await eV(500),this.tabIdOfDebuggerAttached=e,await this.enableWaterFlowAnimation()}catch(e){console.error("Failed to attach debugger",e),t=e}finally{this.attachingDebugger=null}if(t)throw t})(),await this.attachingDebugger}async showMousePointer(e,t){let a=`(() => {
      if(typeof window.midsceneWaterFlowAnimation !== 'undefined') {
        window.midsceneWaterFlowAnimation.enable();
        window.midsceneWaterFlowAnimation.showMousePointer(${e}, ${t});
      } else {
        console.log('midsceneWaterFlowAnimation is not defined');
      }
    })()`;await this.sendCommandToDebugger("Runtime.evaluate",{expression:`${a}`})}async hideMousePointer(){await this.sendCommandToDebugger("Runtime.evaluate",{expression:`(() => {
        if(typeof window.midsceneWaterFlowAnimation !== 'undefined') {
          window.midsceneWaterFlowAnimation.hideMousePointer();
        }
      })()`})}async detachDebugger(e){let t=e||this.tabIdOfDebuggerAttached;if(console.log("detaching debugger",t),!t){console.warn("No tab id to detach");return}try{await this.disableWaterFlowAnimation(t),await eV(200)}catch(e){console.warn("Failed to disable water flow animation",e)}try{await chrome.debugger.detach({tabId:t})}catch(e){console.warn("Failed to detach debugger",e)}this.tabIdOfDebuggerAttached=null}async enableWaterFlowAnimation(){this.forceSameTabNavigation&&await chrome.debugger.sendCommand({tabId:this.tabIdOfDebuggerAttached},"Runtime.evaluate",{expression:en});let e=await eU();await chrome.debugger.sendCommand({tabId:this.tabIdOfDebuggerAttached},"Runtime.evaluate",{expression:e})}async disableWaterFlowAnimation(e){let t=await eK();await chrome.debugger.sendCommand({tabId:e},"Runtime.evaluate",{expression:t})}async sendCommandToDebugger(e,t){return await this.attachDebugger(),(0,e_.assert)(this.tabIdOfDebuggerAttached,"Debugger is not attached"),this.enableWaterFlowAnimation(),await chrome.debugger.sendCommand({tabId:this.tabIdOfDebuggerAttached},e,t)}async getPageContentByCDP(){let e=await eB();await this.sendCommandToDebugger("Runtime.evaluate",{expression:e});let t=await this.sendCommandToDebugger("Runtime.evaluate",{expression:`(${(()=>(window.midscene_element_inspector.setNodeHashCacheListOnWindow(),{tree:window.midscene_element_inspector.webExtractNodeTree(),size:{width:document.documentElement.clientWidth,height:document.documentElement.clientHeight,dpr:window.devicePixelRatio}})).toString()})()`,returnByValue:!0});if(!t.result.value){let e=t.exceptionDetails?.exception?.description||"";throw e||console.error("returnValue from cdp",t),Error(`Failed to get page content from page, error: ${e}`)}return t.result.value}async evaluateJavaScript(e){return this.sendCommandToDebugger("Runtime.evaluate",{expression:e})}async waitUntilNetworkIdle(){let e=Date.now(),t="";for(;Date.now()-e<1e4;){if("complete"===(t=(await this.sendCommandToDebugger("Runtime.evaluate",{expression:"document.readyState"})).result.value)){await new Promise(e=>setTimeout(e,300));return}await new Promise(e=>setTimeout(e,300))}throw Error(`Failed to wait until network idle, last readyState: ${t}`)}async getElementsInfo(){let e=await this.getElementsNodeTree();return(0,eP.treeToList)(e)}async getXpathsById(e){let t=await eB();return await this.sendCommandToDebugger("Runtime.evaluate",{expression:t}),(await this.sendCommandToDebugger("Runtime.evaluate",{expression:`window.midscene_element_inspector.getXpathsById('${e}')`,returnByValue:!0})).result.value}async getElementInfoByXpath(e){let t=await eB();return await this.sendCommandToDebugger("Runtime.evaluate",{expression:t}),(await this.sendCommandToDebugger("Runtime.evaluate",{expression:`window.midscene_element_inspector.getElementInfoByXpath('${e}')`,returnByValue:!0})).result.value}async getElementsNodeTree(){await this.hideMousePointer();let e=await this.getPageContentByCDP();return e?.size&&(this.viewportSize=e.size),e?.tree||{node:null,children:[]}}async size(){return this.viewportSize?this.viewportSize:(await this.getPageContentByCDP()).size}async screenshotBase64(){await this.hideMousePointer();let e=await this.sendCommandToDebugger("Page.captureScreenshot",{format:"jpeg",quality:90});return`data:image/jpeg;base64,${e.data}`}async url(){let e=await this.getTabIdOrConnectToCurrentTab();return await chrome.tabs.get(e).then(e=>e.url)||""}async scrollUntilTop(e){return e&&await this.mouse.move(e.left,e.top),this.mouse.wheel(0,-9999999)}async scrollUntilBottom(e){return e&&await this.mouse.move(e.left,e.top),this.mouse.wheel(0,9999999)}async scrollUntilLeft(e){return e&&await this.mouse.move(e.left,e.top),this.mouse.wheel(-9999999,0)}async scrollUntilRight(e){return e&&await this.mouse.move(e.left,e.top),this.mouse.wheel(9999999,0)}async scrollUp(e,t){let{height:a}=await this.size();return this.mouse.wheel(0,-(e||.7*a),t?.left,t?.top)}async scrollDown(e,t){let{height:a}=await this.size();return this.mouse.wheel(0,e||.7*a,t?.left,t?.top)}async scrollLeft(e,t){let{width:a}=await this.size();return this.mouse.wheel(-(e||.7*a),0,t?.left,t?.top)}async scrollRight(e,t){let{width:a}=await this.size();return this.mouse.wheel(e||.7*a,0,t?.left,t?.top)}async clearInput(e){if(!e){console.warn("No element to clear input");return}await this.mouse.click(e.center[0],e.center[1]),await this.sendCommandToDebugger("Input.dispatchKeyEvent",{type:"keyDown",commands:["selectAll"]}),await this.sendCommandToDebugger("Input.dispatchKeyEvent",{type:"keyUp",commands:["selectAll"]}),await eV(100),await this.keyboard.press({key:"Backspace"})}async destroy(){this.activeTabId=null,await this.detachDebugger(),this.destroyed=!0}},eY=a(27966)},47080:function(e,t,a){"use strict";a(31549),a(4589),a(44194),a(51606),a(95297),a(6020),a(11778),a(60342),a(43436)},98377:function(e,t,a){"use strict";a.d(t,{F:()=>function e(t){if(s in t)return e(t[s]);let a=i.S.operationRequestMap.get(t);return a||(a={},i.S.operationRequestMap.set(t,a)),a},L:()=>function e(t,a,i){let s,n=a.parameterPath,o=a.mapper;if("string"==typeof n&&(n=[n]),Array.isArray(n)){if(n.length>0)if(o.isConstant)s=o.defaultValue;else{let e=r(t,n);!e.propertyFound&&i&&(e=r(i,n));let a=!1;e.propertyFound||(a=o.required||"options"===n[0]&&2===n.length),s=a?o.defaultValue:e.propertyValue}}else for(let a in o.required&&(s={}),n){let r=o.type.modelProperties[a],c=e(t,{parameterPath:n[a],mapper:r},i);void 0!==c&&(s||(s={}),s[a]=c)}return s}});var i=a(79250);function r(e,t){let a={propertyFound:!1},i=0;for(;i<t.length;++i){let a=t[i];if(e&&a in e)e=e[a];else break}return i===t.length&&(a.propertyValue=e,a.propertyFound=!0),a}let s=Symbol.for("@azure/core-client original request")},30942:function(e,t,a){"use strict";a.d(t,{AL:()=>i.A,WA:()=>r.W,dC:()=>n.d,kO:()=>o.k,oY:()=>c.o,tn:()=>s.t,vA:()=>l.vA});var i=a(88376),r=a(55141),s=a(33561),n=a(67697),o=a(6326),c=a(87644);a(54964),a(95145),a(13481),a(51804),a(3),a(61376),a(20011),a(74711),a(67064),a(47926);var l=a(52146);a(29170),a(48286)},39556:function(e,t,a){"use strict";a.d(t,{AO:()=>r,dz:()=>i});let i={span:Symbol.for("@azure/core-tracing span"),namespace:Symbol.for("@azure/core-tracing namespace")};function r(e={}){let t=new s(e.parentContext);return e.span&&(t=t.setValue(i.span,e.span)),e.namespace&&(t=t.setValue(i.namespace,e.namespace)),t}class s{constructor(e){this._contextMap=e instanceof s?new Map(e._contextMap):new Map}setValue(e,t){let a=new s(this);return a._contextMap.set(e,t),a}getValue(e){return this._contextMap.get(e)}deleteValue(e){let t=new s(this);return t._contextMap.delete(e),t}}}},t={};function a(i){var r=t[i];if(void 0!==r)return r.exports;var s=t[i]={id:i,loaded:!1,exports:{}};return e[i].call(s.exports,s,s.exports,a),s.loaded=!0,s.exports}a.m=e,a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;a.t=function(i,r){if(1&r&&(i=this(i)),8&r||"object"==typeof i&&i&&(4&r&&i.__esModule||16&r&&"function"==typeof i.then))return i;var s=Object.create(null);a.r(s);var n={};e=e||[null,t({}),t([]),t(t)];for(var o=2&r&&i;"object"==typeof o&&!~e.indexOf(o);o=t(o))Object.getOwnPropertyNames(o).forEach(e=>{n[e]=()=>i[e]});return n.default=()=>i,a.d(s,n),s}})(),a.d=(e,t)=>{for(var i in t)a.o(t,i)&&!a.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},a.f={},a.e=e=>Promise.all(Object.keys(a.f).reduce((t,i)=>(a.f[i](e,t),t),[])),a.u=e=>"static/js/async/"+e+"."+({18:"f4dc3b35",251:"dd1be928",450:"41f8b458"})[e]+".js",a.g=(()=>{if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(e){if("object"==typeof window)return window}})(),a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="chrome-extension:";a.l=function(i,r,s,n){if(e[i]){e[i].push(r);return}if(void 0!==s)for(var o,c,l=document.getElementsByTagName("script"),u=0;u<l.length;u++){var h=l[u];if(h.getAttribute("src")==i||h.getAttribute("data-webpack")==t+s){o=h;break}}o||(c=!0,(o=document.createElement("script")).charset="utf-8",o.timeout=120,a.nc&&o.setAttribute("nonce",a.nc),o.setAttribute("data-webpack",t+s),o.src=i),e[i]=[r];var d=function(t,a){o.onerror=o.onload=null,clearTimeout(p);var r=e[i];if(delete e[i],o.parentNode&&o.parentNode.removeChild(o),r&&r.forEach(function(e){return e(a)}),t)return t(a)},p=setTimeout(d.bind(null,void 0,{type:"timeout",target:o}),12e4);o.onerror=d.bind(null,o.onerror),o.onload=d.bind(null,o.onload),c&&document.head.appendChild(o)}})(),a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),a.nc=void 0,(()=>{var e=[];a.O=(t,i,r,s)=>{if(i){s=s||0;for(var n=e.length;n>0&&e[n-1][2]>s;n--)e[n]=e[n-1];e[n]=[i,r,s];return}for(var o=1/0,n=0;n<e.length;n++){for(var[i,r,s]=e[n],c=!0,l=0;l<i.length;l++)(!1&s||o>=s)&&Object.keys(a.O).every(e=>a.O[e](i[l]))?i.splice(l--,1):(c=!1,s<o&&(o=s));if(c){e.splice(n--,1);var u=r();void 0!==u&&(t=u)}}return t}})(),a.p="/",a.rv=()=>"1.3.0",(()=>{var e={533:0};a.f.j=function(t,i){var r=a.o(e,t)?e[t]:void 0;if(0!==r)if(r)i.push(r[2]);else{var s=new Promise((a,i)=>r=e[t]=[a,i]);i.push(r[2]=s);var n=a.p+a.u(t),o=Error();a.l(n,function(i){if(a.o(e,t)&&(0!==(r=e[t])&&(e[t]=void 0),r)){var s=i&&("load"===i.type?"missing":i.type),n=i&&i.target&&i.target.src;o.message="Loading chunk "+t+" failed.\n("+s+": "+n+")",o.name="ChunkLoadError",o.type=s,o.request=n,r[1](o)}},"chunk-"+t,t)}},a.O.j=t=>0===e[t];var t=(t,i)=>{var r,s,[n,o,c]=i,l=0;if(n.some(t=>0!==e[t])){for(r in o)a.o(o,r)&&(a.m[r]=o[r]);if(c)var u=c(a)}for(t&&t(i);l<n.length;l++)s=n[l],a.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return a.O(u)},i=self.webpackChunkchrome_extension=self.webpackChunkchrome_extension||[];i.forEach(t.bind(null,0)),i.push=t.bind(null,i.push.bind(i))})(),a.ruid="bundler=rspack@1.3.0",a.O(void 0,["126","361","337","487"],function(){return a(76762)});var i=a.O(void 0,["126","361","337","487"],function(){return a(47080)});i=a.O(i)})();
//# sourceMappingURL=popup.6311459a.js.map