declare const TEXT_SIZE_THRESHOLD = 9;
declare const TEXT_MAX_SIZE = 40;
declare const CONTAINER_MINI_HEIGHT = 3;
declare const CONTAINER_MINI_WIDTH = 3;
declare enum NodeType {
    CONTAINER = "CONTAINER Node",
    FORM_ITEM = "FORM_ITEM Node",
    BUTTON = "BUTTON Node",
    A = "Anchor Node",
    IMG = "IMG Node",
    TEXT = "TEXT Node",
    POSITION = "POSITION Node"
}
declare const PLAYGROUND_SERVER_PORT = 5800;
declare const SCRCPY_SERVER_PORT = 5700;
declare const DEFAULT_WAIT_FOR_NAVIGATION_TIMEOUT = 5000;
declare const DEFAULT_WAIT_FOR_NETWORK_IDLE_TIMEOUT = 2000;
declare const DEFAULT_WAIT_FOR_NETWORK_IDLE_TIME = 300;
declare const DEFAULT_WAIT_FOR_NETWORK_IDLE_CONCURRENCY = 2;

export { CONTAINER_MINI_HEIGHT, CONTAINER_MINI_WIDTH, DEFAULT_WAIT_FOR_NAVIGATION_TIMEOUT, DEFAULT_WAIT_FOR_NETWORK_IDLE_CONCURRENCY, DEFAULT_WAIT_FOR_NETWORK_IDLE_TIME, DEFAULT_WAIT_FOR_NETWORK_IDLE_TIMEOUT, NodeType, PLAYGROUND_SERVER_PORT, SCRCPY_SERVER_PORT, TEXT_MAX_SIZE, TEXT_SIZE_THRESHOLD };
