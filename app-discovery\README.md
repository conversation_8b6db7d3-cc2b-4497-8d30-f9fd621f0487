# App Discovery - 页面节点管理系统

基于ChromaDB的应用页面跳转关系存储和检索系统。

## 功能特性

- **页面节点存储**: 使用ChromaDB存储页面信息，包括截图、UI元素、语义描述等
- **向量化搜索**: 基于语义相似度的页面搜索功能
- **页面关系管理**: 支持父子页面关系和导航路径追踪
- **高性能检索**: 利用ChromaDB的向量数据库优势，实现快速相似性搜索

## 核心组件

### 1. PageTreeNode (models.py)
页面树节点类，包含：
- 页面ID和状态数据
- UI元素信息（按钮、输入框等）
- 语义描述和向量表示
- 父子关系管理

### 2. PageNodeManager (page_manager.py)
页面节点管理器，提供：
- 添加页面节点到ChromaDB
- 基于语义相似度的页面搜索
- 页面关系查询（父子节点、导航路径）
- 存储统计信息

## 使用示例

### 基础用法

```python
from models import PageTreeNode
from page_manager import PageNodeManager

# 创建管理器
manager = PageNodeManager()

# 创建页面节点
page = PageTreeNode("login_001", {
    "screenshot": "screenshots/login.png",
    "elements": [
        {"type": "input", "hint": "用户名", "bounds": [100, 200, 300, 240]},
        {"type": "button", "text": "登录", "bounds": [150, 320, 250, 360]}
    ],
    "semantic_desc": "登录页面，包含用户名和密码输入框",
    "depth": 0,
    "is_goal": False
})

# 添加到数据库
manager.add_page_node(page)

# 搜索相似页面
results = manager.find_similar_pages("用户登录", top_k=5)
for page, similarity in results:
    print(f"{page.id}: {similarity:.3f}")
```

### 高级功能

```python
# 获取子页面
children = manager.get_children("home_001")

# 获取导航路径
path = manager.get_page_path("target_page_001")

# 获取统计信息
stats = manager.get_stats()
```

## 运行演示

### 基础演示
```bash
uv run python main.py
```

### 高级演示（复杂应用结构）
```bash
uv run python demo_advanced.py
```

## 依赖项

- ChromaDB: 向量数据库
- NumPy: 数值计算
- scikit-learn: 机器学习工具

## 安装

```bash
uv add chromadb numpy scikit-learn
```

## 数据结构

### 页面节点数据格式
```python
{
    "id": "page_001",
    "state_data": {
        "screenshot": "path/to/screenshot.png",
        "elements": [
            {"type": "button", "text": "登录", "bounds": [x1,y1,x2,y2]},
            {"type": "input", "hint": "用户名", "bounds": [x1,y1,x2,y2]}
        ],
        "semantic_desc": "页面的语义描述",
        "depth": 0,
        "is_goal": False
    },
    "parent_id": "parent_page_id",
    "action_from_parent": "点击登录按钮",
    "children_ids": ["child1", "child2"]
}
```

## ChromaDB优势

1. **自动向量化**: 自动将文本转换为向量表示
2. **高效搜索**: 基于余弦相似度的快速检索
3. **持久化存储**: 数据自动持久化到磁盘
4. **可扩展性**: 支持大规模数据存储和查询
5. **易于使用**: 简单的API接口

## 应用场景

- 移动应用UI自动化测试
- 网页爬虫路径规划
- 应用页面关系分析
- 用户行为路径追踪
- 智能导航系统

## 项目结构

```
app-discovery/
├── models.py              # 页面节点数据模型
├── page_manager.py        # ChromaDB页面管理器
├── main.py               # 基础演示
├── demo_advanced.py      # 高级演示
├── simple_vectorizer.py  # 简单向量化器（备用）
├── README.md            # 项目文档
└── chroma_db/           # ChromaDB数据存储目录
```