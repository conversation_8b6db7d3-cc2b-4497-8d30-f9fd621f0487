Changes to Libsvm

This is here mainly as checklist for incorporation of new versions of libsvm.

  * Add copyright to files svm.cpp and svm.h
  * Add random_seed support and call to srand in fit function
  * Improved random number generator (fix on windows, enhancement on other
    platforms). See <https://github.com/scikit-learn/scikit-learn/pull/13511#issuecomment-481729756>
  * invoke scipy blas api for svm kernel function to improve performance with speedup rate of 1.5X to 2X for dense data only. See <https://github.com/scikit-learn/scikit-learn/pull/16530>
  * Expose the number of iterations run in optimization. See <https://github.com/scikit-learn/scikit-learn/pull/21408>
The changes made with respect to upstream are detailed in the heading of svm.cpp
