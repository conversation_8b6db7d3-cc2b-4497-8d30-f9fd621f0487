{"version": 3, "file": "static/js/popup.6311459a.js", "sources": ["webpack://chrome-extension/../../node_modules/.pnpm/dayjs@1.11.11/node_modules/dayjs/dayjs.min.js", "webpack://chrome-extension/../../packages/web-integration/dist/lib/bridge-mode-browser.js", "webpack://chrome-extension/../../packages/web-integration/dist/lib/chrome-extension.js", "webpack://chrome-extension/../../node_modules/.pnpm/@azure+core-client@1.9.2/node_modules/@azure/core-client/dist/browser/operationHelpers.js", "webpack://chrome-extension/../../node_modules/.pnpm/@azure+core-tracing@1.2.0/node_modules/@azure/core-tracing/dist/browser/tracingContext.js"], "sourcesContent": ["!function(t,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e():\"function\"==typeof define&&define.amd?define(e):(t=\"undefined\"!=typeof globalThis?globalThis:t||self).dayjs=e()}(this,(function(){\"use strict\";var t=1e3,e=6e4,n=36e5,r=\"millisecond\",i=\"second\",s=\"minute\",u=\"hour\",a=\"day\",o=\"week\",c=\"month\",f=\"quarter\",h=\"year\",d=\"date\",l=\"Invalid Date\",$=/^(\\d{4})[-/]?(\\d{1,2})?[-/]?(\\d{0,2})[Tt\\s]*(\\d{1,2})?:?(\\d{1,2})?:?(\\d{1,2})?[.:]?(\\d+)?$/,y=/\\[([^\\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,M={name:\"en\",weekdays:\"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday\".split(\"_\"),months:\"January_February_March_April_May_June_July_August_September_October_November_December\".split(\"_\"),ordinal:function(t){var e=[\"th\",\"st\",\"nd\",\"rd\"],n=t%100;return\"[\"+t+(e[(n-20)%10]||e[n]||e[0])+\"]\"}},m=function(t,e,n){var r=String(t);return!r||r.length>=e?t:\"\"+Array(e+1-r.length).join(n)+t},v={s:m,z:function(t){var e=-t.utcOffset(),n=Math.abs(e),r=Math.floor(n/60),i=n%60;return(e<=0?\"+\":\"-\")+m(r,2,\"0\")+\":\"+m(i,2,\"0\")},m:function t(e,n){if(e.date()<n.date())return-t(n,e);var r=12*(n.year()-e.year())+(n.month()-e.month()),i=e.clone().add(r,c),s=n-i<0,u=e.clone().add(r+(s?-1:1),c);return+(-(r+(n-i)/(s?i-u:u-i))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(t){return{M:c,y:h,w:o,d:a,D:d,h:u,m:s,s:i,ms:r,Q:f}[t]||String(t||\"\").toLowerCase().replace(/s$/,\"\")},u:function(t){return void 0===t}},g=\"en\",D={};D[g]=M;var p=\"$isDayjsObject\",S=function(t){return t instanceof _||!(!t||!t[p])},w=function t(e,n,r){var i;if(!e)return g;if(\"string\"==typeof e){var s=e.toLowerCase();D[s]&&(i=s),n&&(D[s]=n,i=s);var u=e.split(\"-\");if(!i&&u.length>1)return t(u[0])}else{var a=e.name;D[a]=e,i=a}return!r&&i&&(g=i),i||!r&&g},O=function(t,e){if(S(t))return t.clone();var n=\"object\"==typeof e?e:{};return n.date=t,n.args=arguments,new _(n)},b=v;b.l=w,b.i=S,b.w=function(t,e){return O(t,{locale:e.$L,utc:e.$u,x:e.$x,$offset:e.$offset})};var _=function(){function M(t){this.$L=w(t.locale,null,!0),this.parse(t),this.$x=this.$x||t.x||{},this[p]=!0}var m=M.prototype;return m.parse=function(t){this.$d=function(t){var e=t.date,n=t.utc;if(null===e)return new Date(NaN);if(b.u(e))return new Date;if(e instanceof Date)return new Date(e);if(\"string\"==typeof e&&!/Z$/i.test(e)){var r=e.match($);if(r){var i=r[2]-1||0,s=(r[7]||\"0\").substring(0,3);return n?new Date(Date.UTC(r[1],i,r[3]||1,r[4]||0,r[5]||0,r[6]||0,s)):new Date(r[1],i,r[3]||1,r[4]||0,r[5]||0,r[6]||0,s)}}return new Date(e)}(t),this.init()},m.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},m.$utils=function(){return b},m.isValid=function(){return!(this.$d.toString()===l)},m.isSame=function(t,e){var n=O(t);return this.startOf(e)<=n&&n<=this.endOf(e)},m.isAfter=function(t,e){return O(t)<this.startOf(e)},m.isBefore=function(t,e){return this.endOf(e)<O(t)},m.$g=function(t,e,n){return b.u(t)?this[e]:this.set(n,t)},m.unix=function(){return Math.floor(this.valueOf()/1e3)},m.valueOf=function(){return this.$d.getTime()},m.startOf=function(t,e){var n=this,r=!!b.u(e)||e,f=b.p(t),l=function(t,e){var i=b.w(n.$u?Date.UTC(n.$y,e,t):new Date(n.$y,e,t),n);return r?i:i.endOf(a)},$=function(t,e){return b.w(n.toDate()[t].apply(n.toDate(\"s\"),(r?[0,0,0,0]:[23,59,59,999]).slice(e)),n)},y=this.$W,M=this.$M,m=this.$D,v=\"set\"+(this.$u?\"UTC\":\"\");switch(f){case h:return r?l(1,0):l(31,11);case c:return r?l(1,M):l(0,M+1);case o:var g=this.$locale().weekStart||0,D=(y<g?y+7:y)-g;return l(r?m-D:m+(6-D),M);case a:case d:return $(v+\"Hours\",0);case u:return $(v+\"Minutes\",1);case s:return $(v+\"Seconds\",2);case i:return $(v+\"Milliseconds\",3);default:return this.clone()}},m.endOf=function(t){return this.startOf(t,!1)},m.$set=function(t,e){var n,o=b.p(t),f=\"set\"+(this.$u?\"UTC\":\"\"),l=(n={},n[a]=f+\"Date\",n[d]=f+\"Date\",n[c]=f+\"Month\",n[h]=f+\"FullYear\",n[u]=f+\"Hours\",n[s]=f+\"Minutes\",n[i]=f+\"Seconds\",n[r]=f+\"Milliseconds\",n)[o],$=o===a?this.$D+(e-this.$W):e;if(o===c||o===h){var y=this.clone().set(d,1);y.$d[l]($),y.init(),this.$d=y.set(d,Math.min(this.$D,y.daysInMonth())).$d}else l&&this.$d[l]($);return this.init(),this},m.set=function(t,e){return this.clone().$set(t,e)},m.get=function(t){return this[b.p(t)]()},m.add=function(r,f){var d,l=this;r=Number(r);var $=b.p(f),y=function(t){var e=O(l);return b.w(e.date(e.date()+Math.round(t*r)),l)};if($===c)return this.set(c,this.$M+r);if($===h)return this.set(h,this.$y+r);if($===a)return y(1);if($===o)return y(7);var M=(d={},d[s]=e,d[u]=n,d[i]=t,d)[$]||1,m=this.$d.getTime()+r*M;return b.w(m,this)},m.subtract=function(t,e){return this.add(-1*t,e)},m.format=function(t){var e=this,n=this.$locale();if(!this.isValid())return n.invalidDate||l;var r=t||\"YYYY-MM-DDTHH:mm:ssZ\",i=b.z(this),s=this.$H,u=this.$m,a=this.$M,o=n.weekdays,c=n.months,f=n.meridiem,h=function(t,n,i,s){return t&&(t[n]||t(e,r))||i[n].slice(0,s)},d=function(t){return b.s(s%12||12,t,\"0\")},$=f||function(t,e,n){var r=t<12?\"AM\":\"PM\";return n?r.toLowerCase():r};return r.replace(y,(function(t,r){return r||function(t){switch(t){case\"YY\":return String(e.$y).slice(-2);case\"YYYY\":return b.s(e.$y,4,\"0\");case\"M\":return a+1;case\"MM\":return b.s(a+1,2,\"0\");case\"MMM\":return h(n.monthsShort,a,c,3);case\"MMMM\":return h(c,a);case\"D\":return e.$D;case\"DD\":return b.s(e.$D,2,\"0\");case\"d\":return String(e.$W);case\"dd\":return h(n.weekdaysMin,e.$W,o,2);case\"ddd\":return h(n.weekdaysShort,e.$W,o,3);case\"dddd\":return o[e.$W];case\"H\":return String(s);case\"HH\":return b.s(s,2,\"0\");case\"h\":return d(1);case\"hh\":return d(2);case\"a\":return $(s,u,!0);case\"A\":return $(s,u,!1);case\"m\":return String(u);case\"mm\":return b.s(u,2,\"0\");case\"s\":return String(e.$s);case\"ss\":return b.s(e.$s,2,\"0\");case\"SSS\":return b.s(e.$ms,3,\"0\");case\"Z\":return i}return null}(t)||i.replace(\":\",\"\")}))},m.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},m.diff=function(r,d,l){var $,y=this,M=b.p(d),m=O(r),v=(m.utcOffset()-this.utcOffset())*e,g=this-m,D=function(){return b.m(y,m)};switch(M){case h:$=D()/12;break;case c:$=D();break;case f:$=D()/3;break;case o:$=(g-v)/6048e5;break;case a:$=(g-v)/864e5;break;case u:$=g/n;break;case s:$=g/e;break;case i:$=g/t;break;default:$=g}return l?$:b.a($)},m.daysInMonth=function(){return this.endOf(c).$D},m.$locale=function(){return D[this.$L]},m.locale=function(t,e){if(!t)return this.$L;var n=this.clone(),r=w(t,e,!0);return r&&(n.$L=r),n},m.clone=function(){return b.w(this.$d,this)},m.toDate=function(){return new Date(this.valueOf())},m.toJSON=function(){return this.isValid()?this.toISOString():null},m.toISOString=function(){return this.$d.toISOString()},m.toString=function(){return this.$d.toUTCString()},M}(),k=_.prototype;return O.prototype=k,[[\"$ms\",r],[\"$s\",i],[\"$m\",s],[\"$H\",u],[\"$W\",a],[\"$M\",c],[\"$y\",h],[\"$D\",d]].forEach((function(t){k[t[1]]=function(e){return this.$g(e,t[0],t[1])}})),O.extend=function(t,e){return t.$i||(t(e,_,O),t.$i=!0),O},O.locale=w,O.isDayjs=S,O.unix=function(t){return O(1e3*t)},O.en=D[g],O.Ls=D,O.p={},O}));", "\"use strict\";\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __accessCheck = (obj, member, msg) => {\n  if (!member.has(obj))\n    throw TypeError(\"Cannot \" + msg);\n};\nvar __privateGet = (obj, member, getter) => {\n  __accessCheck(obj, member, \"read from private field\");\n  return getter ? getter.call(obj) : member.get(obj);\n};\nvar __privateAdd = (obj, member, value) => {\n  if (member.has(obj))\n    throw TypeError(\"Cannot add the same private member more than once\");\n  member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\n};\nvar __privateSet = (obj, member, value, setter) => {\n  __accessCheck(obj, member, \"write to private field\");\n  setter ? setter.call(obj, value) : member.set(obj, value);\n  return value;\n};\nvar __privateMethod = (obj, member, method) => {\n  __accessCheck(obj, member, \"access private method\");\n  return method;\n};\n\n// src/bridge-mode/browser.ts\nvar browser_exports = {};\n__export(browser_exports, {\n  ExtensionBridgePageBrowserSide: () => ExtensionBridgePageBrowserSide\n});\nmodule.exports = __toCommonJS(browser_exports);\n\n// src/bridge-mode/page-browser-side.ts\nvar import_utils5 = require(\"@midscene/shared/utils\");\n\n// src/common/ui-utils.ts\nvar limitOpenNewTabScript = `\nif (!window.__MIDSCENE_NEW_TAB_INTERCEPTOR_INITIALIZED__) {\n  window.__MIDSCENE_NEW_TAB_INTERCEPTOR_INITIALIZED__ = true;\n\n  // Intercept the window.open method (only once)\n  window.open = function(url) {\n    console.log('Blocked window.open:', url);\n    window.location.href = url;\n    return null;\n  };\n\n  // Block all a tag clicks with target=\"_blank\" (only once)\n  document.addEventListener('click', function(e) {\n    const target = e.target.closest('a');\n    if (target && target.target === '_blank') {\n      e.preventDefault();\n      console.log('Blocked new tab:', target.href);\n      window.location.href = target.href;\n      target.removeAttribute('target');\n    }\n  }, true);\n}\n`;\n\n// src/chrome-extension/page.ts\nvar import_extractor = require(\"@midscene/shared/extractor\");\nvar import_utils3 = require(\"@midscene/shared/utils\");\n\n// src/chrome-extension/cdpInput.ts\nvar import_keyboard_layout = require(\"@midscene/shared/keyboard-layout\");\nvar import_utils = require(\"@midscene/shared/utils\");\nvar _pressedKeys, _client, _modifierBit, modifierBit_fn, _keyDescriptionForString, keyDescriptionForString_fn;\nvar CdpKeyboard = class {\n  constructor(client) {\n    __privateAdd(this, _modifierBit);\n    __privateAdd(this, _keyDescriptionForString);\n    __privateAdd(this, _pressedKeys, /* @__PURE__ */ new Set());\n    __privateAdd(this, _client, void 0);\n    this._modifiers = 0;\n    __privateSet(this, _client, client);\n  }\n  updateClient(client) {\n    __privateSet(this, _client, client);\n  }\n  async down(key, options = {\n    text: void 0,\n    commands: []\n  }) {\n    const description = __privateMethod(this, _keyDescriptionForString, keyDescriptionForString_fn).call(this, key);\n    const autoRepeat = __privateGet(this, _pressedKeys).has(description.code);\n    __privateGet(this, _pressedKeys).add(description.code);\n    this._modifiers |= __privateMethod(this, _modifierBit, modifierBit_fn).call(this, description.key);\n    const text = options.text === void 0 ? description.text : options.text;\n    await __privateGet(this, _client).send(\"Input.dispatchKeyEvent\", {\n      type: text ? \"keyDown\" : \"rawKeyDown\",\n      modifiers: this._modifiers,\n      windowsVirtualKeyCode: description.keyCode,\n      code: description.code,\n      key: description.key,\n      text,\n      unmodifiedText: text,\n      autoRepeat,\n      location: description.location,\n      isKeypad: description.location === 3,\n      commands: options.commands\n    });\n  }\n  async up(key) {\n    const description = __privateMethod(this, _keyDescriptionForString, keyDescriptionForString_fn).call(this, key);\n    this._modifiers &= ~__privateMethod(this, _modifierBit, modifierBit_fn).call(this, description.key);\n    __privateGet(this, _pressedKeys).delete(description.code);\n    await __privateGet(this, _client).send(\"Input.dispatchKeyEvent\", {\n      type: \"keyUp\",\n      modifiers: this._modifiers,\n      key: description.key,\n      windowsVirtualKeyCode: description.keyCode,\n      code: description.code,\n      location: description.location\n    });\n  }\n  async sendCharacter(char) {\n    await __privateGet(this, _client).send(\"Input.insertText\", { text: char });\n  }\n  charIsKey(char) {\n    return !!import_keyboard_layout._keyDefinitions[char];\n  }\n  async type(text, options = {}) {\n    const delay = options.delay || void 0;\n    for (const char of text) {\n      if (this.charIsKey(char)) {\n        await this.press(char, { delay });\n      } else {\n        if (delay) {\n          await new Promise((f) => {\n            return setTimeout(f, delay);\n          });\n        }\n        await this.sendCharacter(char);\n      }\n    }\n  }\n  async press(key, options = {}) {\n    const { delay = null } = options;\n    const keys = Array.isArray(key) ? key : [key];\n    for (const k of keys) {\n      await this.down(k, options);\n    }\n    if (delay) {\n      await new Promise((f) => {\n        return setTimeout(f, options.delay);\n      });\n    }\n    for (const k of [...keys].reverse()) {\n      await this.up(k);\n    }\n  }\n};\n_pressedKeys = new WeakMap();\n_client = new WeakMap();\n_modifierBit = new WeakSet();\nmodifierBit_fn = function(key) {\n  if (key === \"Alt\") {\n    return 1;\n  }\n  if (key === \"Control\") {\n    return 2;\n  }\n  if (key === \"Meta\") {\n    return 4;\n  }\n  if (key === \"Shift\") {\n    return 8;\n  }\n  return 0;\n};\n_keyDescriptionForString = new WeakSet();\nkeyDescriptionForString_fn = function(keyString) {\n  const shift = this._modifiers & 8;\n  const description = {\n    key: \"\",\n    keyCode: 0,\n    code: \"\",\n    text: \"\",\n    location: 0\n  };\n  const definition = import_keyboard_layout._keyDefinitions[keyString];\n  (0, import_utils.assert)(definition, `Unknown key: \"${keyString}\"`);\n  if (definition.key) {\n    description.key = definition.key;\n  }\n  if (shift && definition.shiftKey) {\n    description.key = definition.shiftKey;\n  }\n  if (definition.keyCode) {\n    description.keyCode = definition.keyCode;\n  }\n  if (shift && definition.shiftKeyCode) {\n    description.keyCode = definition.shiftKeyCode;\n  }\n  if (definition.code) {\n    description.code = definition.code;\n  }\n  if (definition.location) {\n    description.location = definition.location;\n  }\n  if (description.key.length === 1) {\n    description.text = description.key;\n  }\n  if (definition.text) {\n    description.text = definition.text;\n  }\n  if (shift && definition.shiftText) {\n    description.text = definition.shiftText;\n  }\n  if (this._modifiers & ~8) {\n    description.text = \"\";\n  }\n  return description;\n};\n\n// src/chrome-extension/dynamic-scripts.ts\nvar import_node_fs = __toESM(require(\"fs\"));\nvar import_utils2 = require(\"@midscene/shared/utils\");\nvar scriptFileContentCache = null;\nvar getHtmlElementScript = async () => {\n  const scriptFileToRetrieve = chrome.runtime.getURL(\"scripts/htmlElement.js\");\n  if (scriptFileContentCache)\n    return scriptFileContentCache;\n  if (import_utils2.ifInBrowser) {\n    const script = await fetch(scriptFileToRetrieve);\n    scriptFileContentCache = await script.text();\n    return scriptFileContentCache;\n  }\n  return import_node_fs.default.readFileSync(scriptFileToRetrieve, \"utf8\");\n};\nvar waterFlowScriptFileContentCache = null;\nvar injectWaterFlowAnimation = async () => {\n  const waterFlowScriptFileToRetrieve = chrome.runtime.getURL(\n    \"scripts/water-flow.js\"\n  );\n  if (waterFlowScriptFileContentCache)\n    return waterFlowScriptFileContentCache;\n  if (import_utils2.ifInBrowser) {\n    const script = await fetch(waterFlowScriptFileToRetrieve);\n    waterFlowScriptFileContentCache = await script.text();\n    return waterFlowScriptFileContentCache;\n  }\n  return import_node_fs.default.readFileSync(waterFlowScriptFileToRetrieve, \"utf8\");\n};\nvar stopWaterFlowScriptFileContentCache = null;\nvar injectStopWaterFlowAnimation = async () => {\n  const stopWaterFlowScriptFileToRetrieve = chrome.runtime.getURL(\n    \"scripts/stop-water-flow.js\"\n  );\n  if (stopWaterFlowScriptFileContentCache)\n    return stopWaterFlowScriptFileContentCache;\n  if (import_utils2.ifInBrowser) {\n    const script = await fetch(stopWaterFlowScriptFileToRetrieve);\n    stopWaterFlowScriptFileContentCache = await script.text();\n    return stopWaterFlowScriptFileContentCache;\n  }\n  return import_node_fs.default.readFileSync(stopWaterFlowScriptFileToRetrieve, \"utf8\");\n};\n\n// src/chrome-extension/page.ts\nfunction sleep(ms) {\n  return new Promise((resolve) => setTimeout(resolve, ms));\n}\nvar ChromeExtensionProxyPage = class {\n  constructor(forceSameTabNavigation) {\n    this.pageType = \"chrome-extension-proxy\";\n    this.version = \"0.17.5\";\n    this.activeTabId = null;\n    this.tabIdOfDebuggerAttached = null;\n    this.attachingDebugger = null;\n    this.destroyed = false;\n    this.isMobileEmulation = null;\n    this.latestMouseX = 100;\n    this.latestMouseY = 100;\n    this.mouse = {\n      click: async (x, y, options) => {\n        const { button = \"left\", count = 1 } = options || {};\n        await this.mouse.move(x, y);\n        if (this.isMobileEmulation === null) {\n          const result = await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n            expression: `(() => {\n            return /Android|iPhone|iPad|iPod|Mobile/i.test(navigator.userAgent);\n          })()`,\n            returnByValue: true\n          });\n          this.isMobileEmulation = result?.result?.value;\n        }\n        if (this.isMobileEmulation && button === \"left\") {\n          const touchPoints = [{ x: Math.round(x), y: Math.round(y) }];\n          await this.sendCommandToDebugger(\"Input.dispatchTouchEvent\", {\n            type: \"touchStart\",\n            touchPoints,\n            modifiers: 0\n          });\n          await this.sendCommandToDebugger(\"Input.dispatchTouchEvent\", {\n            type: \"touchEnd\",\n            touchPoints: [],\n            modifiers: 0\n          });\n        } else {\n          await this.sendCommandToDebugger(\"Input.dispatchMouseEvent\", {\n            type: \"mousePressed\",\n            x,\n            y,\n            button,\n            clickCount: count\n          });\n          await this.sendCommandToDebugger(\"Input.dispatchMouseEvent\", {\n            type: \"mouseReleased\",\n            x,\n            y,\n            button,\n            clickCount: count\n          });\n        }\n      },\n      wheel: async (deltaX, deltaY, startX, startY) => {\n        const finalX = startX || this.latestMouseX;\n        const finalY = startY || this.latestMouseY;\n        await this.showMousePointer(finalX, finalY);\n        await this.sendCommandToDebugger(\"Input.dispatchMouseEvent\", {\n          type: \"mouseWheel\",\n          x: finalX,\n          y: finalY,\n          deltaX,\n          deltaY\n        });\n        this.latestMouseX = finalX;\n        this.latestMouseY = finalY;\n      },\n      move: async (x, y) => {\n        await this.showMousePointer(x, y);\n        await this.sendCommandToDebugger(\"Input.dispatchMouseEvent\", {\n          type: \"mouseMoved\",\n          x,\n          y\n        });\n        this.latestMouseX = x;\n        this.latestMouseY = y;\n      },\n      drag: async (from, to) => {\n        await this.mouse.move(from.x, from.y);\n        await this.sendCommandToDebugger(\"Input.dispatchMouseEvent\", {\n          type: \"mousePressed\",\n          x: from.x,\n          y: from.y,\n          button: \"left\",\n          clickCount: 1\n        });\n        await this.mouse.move(to.x, to.y);\n        await this.sendCommandToDebugger(\"Input.dispatchMouseEvent\", {\n          type: \"mouseReleased\",\n          x: to.x,\n          y: to.y,\n          button: \"left\",\n          clickCount: 1\n        });\n      }\n    };\n    this.keyboard = {\n      type: async (text) => {\n        const cdpKeyboard = new CdpKeyboard({\n          send: this.sendCommandToDebugger.bind(this)\n        });\n        await cdpKeyboard.type(text, { delay: 0 });\n      },\n      press: async (action) => {\n        const cdpKeyboard = new CdpKeyboard({\n          send: this.sendCommandToDebugger.bind(this)\n        });\n        const keys = Array.isArray(action) ? action : [action];\n        for (const k of keys) {\n          const commands = k.command ? [k.command] : [];\n          await cdpKeyboard.down(k.key, { commands });\n        }\n        for (const k of [...keys].reverse()) {\n          await cdpKeyboard.up(k.key);\n        }\n      }\n    };\n    this.forceSameTabNavigation = forceSameTabNavigation;\n  }\n  async setActiveTabId(tabId) {\n    if (this.activeTabId) {\n      throw new Error(\n        `Active tab id is already set, which is ${this.activeTabId}, cannot set it to ${tabId}`\n      );\n    }\n    await chrome.tabs.update(tabId, { active: true });\n    this.activeTabId = tabId;\n  }\n  async getActiveTabId() {\n    return this.activeTabId;\n  }\n  /**\n   * Get a list of current tabs\n   * @returns {Promise<Array<{id: number, title: string, url: string}>>}\n   */\n  async getBrowserTabList() {\n    const tabs = await chrome.tabs.query({ currentWindow: true });\n    return tabs.map((tab) => ({\n      id: `${tab.id}`,\n      title: tab.title,\n      url: tab.url,\n      currentActiveTab: tab.active\n    })).filter((tab) => tab.id && tab.title && tab.url);\n  }\n  async getTabIdOrConnectToCurrentTab() {\n    if (this.activeTabId) {\n      return this.activeTabId;\n    }\n    const tabId = await chrome.tabs.query({ active: true, currentWindow: true }).then((tabs) => tabs[0]?.id);\n    this.activeTabId = tabId || 0;\n    return this.activeTabId;\n  }\n  async attachDebugger() {\n    (0, import_utils3.assert)(!this.destroyed, \"Page is destroyed\");\n    if (this.attachingDebugger) {\n      await this.attachingDebugger;\n      return;\n    }\n    this.attachingDebugger = (async () => {\n      const url = await this.url();\n      let error = null;\n      if (url.startsWith(\"chrome://\")) {\n        throw new Error(\n          \"Cannot attach debugger to chrome:// pages, please use Midscene in a normal page with http://, https:// or file://\"\n        );\n      }\n      try {\n        const currentTabId = await this.getTabIdOrConnectToCurrentTab();\n        if (this.tabIdOfDebuggerAttached === currentTabId) {\n          return;\n        }\n        if (this.tabIdOfDebuggerAttached && this.tabIdOfDebuggerAttached !== currentTabId) {\n          console.log(\n            \"detach the previous tab\",\n            this.tabIdOfDebuggerAttached,\n            \"->\",\n            currentTabId\n          );\n          try {\n            await this.detachDebugger(this.tabIdOfDebuggerAttached);\n          } catch (error2) {\n            console.error(\"Failed to detach debugger\", error2);\n          }\n        }\n        console.log(\"attaching debugger\", currentTabId);\n        await chrome.debugger.attach({ tabId: currentTabId }, \"1.3\");\n        await sleep(500);\n        this.tabIdOfDebuggerAttached = currentTabId;\n        await this.enableWaterFlowAnimation();\n      } catch (e) {\n        console.error(\"Failed to attach debugger\", e);\n        error = e;\n      } finally {\n        this.attachingDebugger = null;\n      }\n      if (error) {\n        throw error;\n      }\n    })();\n    await this.attachingDebugger;\n  }\n  async showMousePointer(x, y) {\n    const pointerScript = `(() => {\n      if(typeof window.midsceneWaterFlowAnimation !== 'undefined') {\n        window.midsceneWaterFlowAnimation.enable();\n        window.midsceneWaterFlowAnimation.showMousePointer(${x}, ${y});\n      } else {\n        console.log('midsceneWaterFlowAnimation is not defined');\n      }\n    })()`;\n    await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: `${pointerScript}`\n    });\n  }\n  async hideMousePointer() {\n    await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: `(() => {\n        if(typeof window.midsceneWaterFlowAnimation !== 'undefined') {\n          window.midsceneWaterFlowAnimation.hideMousePointer();\n        }\n      })()`\n    });\n  }\n  async detachDebugger(tabId) {\n    const tabIdToDetach = tabId || this.tabIdOfDebuggerAttached;\n    console.log(\"detaching debugger\", tabIdToDetach);\n    if (!tabIdToDetach) {\n      console.warn(\"No tab id to detach\");\n      return;\n    }\n    try {\n      await this.disableWaterFlowAnimation(tabIdToDetach);\n      await sleep(200);\n    } catch (error) {\n      console.warn(\"Failed to disable water flow animation\", error);\n    }\n    try {\n      await chrome.debugger.detach({ tabId: tabIdToDetach });\n    } catch (error) {\n      console.warn(\"Failed to detach debugger\", error);\n    }\n    this.tabIdOfDebuggerAttached = null;\n  }\n  async enableWaterFlowAnimation() {\n    if (this.forceSameTabNavigation) {\n      await chrome.debugger.sendCommand(\n        { tabId: this.tabIdOfDebuggerAttached },\n        \"Runtime.evaluate\",\n        {\n          expression: limitOpenNewTabScript\n        }\n      );\n    }\n    const script = await injectWaterFlowAnimation();\n    await chrome.debugger.sendCommand(\n      { tabId: this.tabIdOfDebuggerAttached },\n      \"Runtime.evaluate\",\n      {\n        expression: script\n      }\n    );\n  }\n  async disableWaterFlowAnimation(tabId) {\n    const script = await injectStopWaterFlowAnimation();\n    await chrome.debugger.sendCommand({ tabId }, \"Runtime.evaluate\", {\n      expression: script\n    });\n  }\n  async sendCommandToDebugger(command, params) {\n    await this.attachDebugger();\n    (0, import_utils3.assert)(this.tabIdOfDebuggerAttached, \"Debugger is not attached\");\n    this.enableWaterFlowAnimation();\n    return await chrome.debugger.sendCommand(\n      { tabId: this.tabIdOfDebuggerAttached },\n      command,\n      params\n    );\n  }\n  async getPageContentByCDP() {\n    const script = await getHtmlElementScript();\n    await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: script\n    });\n    const expression = () => {\n      window.midscene_element_inspector.setNodeHashCacheListOnWindow();\n      return {\n        tree: window.midscene_element_inspector.webExtractNodeTree(),\n        size: {\n          width: document.documentElement.clientWidth,\n          height: document.documentElement.clientHeight,\n          dpr: window.devicePixelRatio\n        }\n      };\n    };\n    const returnValue = await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: `(${expression.toString()})()`,\n      returnByValue: true\n    });\n    if (!returnValue.result.value) {\n      const errorDescription = returnValue.exceptionDetails?.exception?.description || \"\";\n      if (!errorDescription) {\n        console.error(\"returnValue from cdp\", returnValue);\n      }\n      throw new Error(\n        `Failed to get page content from page, error: ${errorDescription}`\n      );\n    }\n    return returnValue.result.value;\n  }\n  async evaluateJavaScript(script) {\n    return this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: script\n    });\n  }\n  // current implementation is wait until domReadyState is complete\n  async waitUntilNetworkIdle() {\n    const timeout = 1e4;\n    const startTime = Date.now();\n    let lastReadyState = \"\";\n    while (Date.now() - startTime < timeout) {\n      const result = await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n        expression: \"document.readyState\"\n      });\n      lastReadyState = result.result.value;\n      if (lastReadyState === \"complete\") {\n        await new Promise((resolve) => setTimeout(resolve, 300));\n        return;\n      }\n      await new Promise((resolve) => setTimeout(resolve, 300));\n    }\n    throw new Error(\n      `Failed to wait until network idle, last readyState: ${lastReadyState}`\n    );\n  }\n  async getElementsInfo() {\n    const tree = await this.getElementsNodeTree();\n    return (0, import_extractor.treeToList)(tree);\n  }\n  async getXpathsById(id) {\n    const script = await getHtmlElementScript();\n    await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: script\n    });\n    const result = await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: `window.midscene_element_inspector.getXpathsById('${id}')`,\n      returnByValue: true\n    });\n    return result.result.value;\n  }\n  async getElementInfoByXpath(xpath) {\n    const script = await getHtmlElementScript();\n    await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: script\n    });\n    const result = await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: `window.midscene_element_inspector.getElementInfoByXpath('${xpath}')`,\n      returnByValue: true\n    });\n    return result.result.value;\n  }\n  async getElementsNodeTree() {\n    await this.hideMousePointer();\n    const content = await this.getPageContentByCDP();\n    if (content?.size) {\n      this.viewportSize = content.size;\n    }\n    return content?.tree || { node: null, children: [] };\n  }\n  async size() {\n    if (this.viewportSize)\n      return this.viewportSize;\n    const content = await this.getPageContentByCDP();\n    return content.size;\n  }\n  async screenshotBase64() {\n    await this.hideMousePointer();\n    const base64 = await this.sendCommandToDebugger(\"Page.captureScreenshot\", {\n      format: \"jpeg\",\n      quality: 90\n    });\n    return `data:image/jpeg;base64,${base64.data}`;\n  }\n  async url() {\n    const tabId = await this.getTabIdOrConnectToCurrentTab();\n    const url = await chrome.tabs.get(tabId).then((tab) => tab.url);\n    return url || \"\";\n  }\n  async scrollUntilTop(startingPoint) {\n    if (startingPoint) {\n      await this.mouse.move(startingPoint.left, startingPoint.top);\n    }\n    return this.mouse.wheel(0, -9999999);\n  }\n  async scrollUntilBottom(startingPoint) {\n    if (startingPoint) {\n      await this.mouse.move(startingPoint.left, startingPoint.top);\n    }\n    return this.mouse.wheel(0, 9999999);\n  }\n  async scrollUntilLeft(startingPoint) {\n    if (startingPoint) {\n      await this.mouse.move(startingPoint.left, startingPoint.top);\n    }\n    return this.mouse.wheel(-9999999, 0);\n  }\n  async scrollUntilRight(startingPoint) {\n    if (startingPoint) {\n      await this.mouse.move(startingPoint.left, startingPoint.top);\n    }\n    return this.mouse.wheel(9999999, 0);\n  }\n  async scrollUp(distance, startingPoint) {\n    const { height } = await this.size();\n    const scrollDistance = distance || height * 0.7;\n    return this.mouse.wheel(\n      0,\n      -scrollDistance,\n      startingPoint?.left,\n      startingPoint?.top\n    );\n  }\n  async scrollDown(distance, startingPoint) {\n    const { height } = await this.size();\n    const scrollDistance = distance || height * 0.7;\n    return this.mouse.wheel(\n      0,\n      scrollDistance,\n      startingPoint?.left,\n      startingPoint?.top\n    );\n  }\n  async scrollLeft(distance, startingPoint) {\n    const { width } = await this.size();\n    const scrollDistance = distance || width * 0.7;\n    return this.mouse.wheel(\n      -scrollDistance,\n      0,\n      startingPoint?.left,\n      startingPoint?.top\n    );\n  }\n  async scrollRight(distance, startingPoint) {\n    const { width } = await this.size();\n    const scrollDistance = distance || width * 0.7;\n    return this.mouse.wheel(\n      scrollDistance,\n      0,\n      startingPoint?.left,\n      startingPoint?.top\n    );\n  }\n  async clearInput(element) {\n    if (!element) {\n      console.warn(\"No element to clear input\");\n      return;\n    }\n    await this.mouse.click(element.center[0], element.center[1]);\n    await this.sendCommandToDebugger(\"Input.dispatchKeyEvent\", {\n      type: \"keyDown\",\n      commands: [\"selectAll\"]\n    });\n    await this.sendCommandToDebugger(\"Input.dispatchKeyEvent\", {\n      type: \"keyUp\",\n      commands: [\"selectAll\"]\n    });\n    await sleep(100);\n    await this.keyboard.press({\n      key: \"Backspace\"\n    });\n  }\n  async destroy() {\n    this.activeTabId = null;\n    await this.detachDebugger();\n    this.destroyed = true;\n  }\n};\n\n// src/bridge-mode/common.ts\nvar DefaultBridgeServerPort = 3766;\nvar DefaultLocalEndpoint = `http://127.0.0.1:${DefaultBridgeServerPort}`;\n\n// src/bridge-mode/io-client.ts\nvar import_utils4 = require(\"@midscene/shared/utils\");\nvar import_socket = require(\"socket.io-client\");\nvar BridgeClient = class {\n  constructor(endpoint, onBridgeCall, onDisconnect) {\n    this.endpoint = endpoint;\n    this.onBridgeCall = onBridgeCall;\n    this.onDisconnect = onDisconnect;\n    this.socket = null;\n    this.serverVersion = null;\n  }\n  async connect() {\n    return new Promise((resolve, reject) => {\n      this.socket = (0, import_socket.io)(this.endpoint, {\n        reconnection: false,\n        query: {\n          version: \"0.17.5\"\n        }\n      });\n      const timeout = setTimeout(() => {\n        try {\n          this.socket?.offAny();\n          this.socket?.close();\n        } catch (e) {\n          console.warn(\"got error when offing socket\", e);\n        }\n        this.socket = null;\n        reject(new Error(\"failed to connect to bridge server after timeout\"));\n      }, 1 * 1e3);\n      this.socket.on(\"disconnect\", (reason) => {\n        this.socket = null;\n        this.onDisconnect?.();\n      });\n      this.socket.on(\"connect_error\", (e) => {\n        console.error(\"bridge-connect-error\", e);\n        reject(new Error(e || \"bridge connect error\"));\n      });\n      this.socket.on(\n        \"bridge-connected\" /* Connected */,\n        (payload) => {\n          clearTimeout(timeout);\n          this.serverVersion = payload?.version || \"unknown\";\n          resolve(this.socket);\n        }\n      );\n      this.socket.on(\"bridge-refused\" /* Refused */, (e) => {\n        console.error(\"bridge-refused\", e);\n        try {\n          this.socket?.disconnect();\n        } catch (e2) {\n        }\n        reject(new Error(e || \"bridge refused\"));\n      });\n      this.socket.on(\"bridge-call\" /* Call */, (call) => {\n        const id = call.id;\n        (0, import_utils4.assert)(typeof id !== \"undefined\", \"call id is required\");\n        (async () => {\n          let response;\n          try {\n            response = await this.onBridgeCall(call.method, call.args);\n          } catch (e) {\n            const errorContent = `Error from bridge client when calling, method: ${call.method}, args: ${call.args}, error: ${e?.message || e}\n${e?.stack || \"\"}`;\n            console.error(errorContent);\n            return this.socket?.emit(\"bridge-call-response\" /* CallResponse */, {\n              id,\n              error: errorContent\n            });\n          }\n          this.socket?.emit(\"bridge-call-response\" /* CallResponse */, {\n            id,\n            response\n          });\n        })();\n      });\n    });\n  }\n  disconnect() {\n    this.socket?.disconnect();\n    this.socket = null;\n  }\n};\n\n// src/bridge-mode/page-browser-side.ts\nvar ExtensionBridgePageBrowserSide = class extends ChromeExtensionProxyPage {\n  constructor(onDisconnect = () => {\n  }, onLogMessage = () => {\n  }, forceSameTabNavigation = true) {\n    super(forceSameTabNavigation);\n    this.onDisconnect = onDisconnect;\n    this.onLogMessage = onLogMessage;\n    this.bridgeClient = null;\n    this.newlyCreatedTabIds = [];\n  }\n  async setupBridgeClient() {\n    this.bridgeClient = new BridgeClient(\n      `ws://localhost:${DefaultBridgeServerPort}`,\n      async (method, args) => {\n        console.log(\"bridge call from cli side\", method, args);\n        if (method === \"connectNewTabWithUrl\" /* ConnectNewTabWithUrl */) {\n          return this.connectNewTabWithUrl.apply(\n            this,\n            args\n          );\n        }\n        if (method === \"getBrowserTabList\" /* GetBrowserTabList */) {\n          return this.getBrowserTabList.apply(this, args);\n        }\n        if (method === \"setActiveTabId\" /* SetActiveTabId */) {\n          return this.setActiveTabId.apply(this, args);\n        }\n        if (method === \"connectCurrentTab\" /* ConnectCurrentTab */) {\n          return this.connectCurrentTab.apply(this, args);\n        }\n        if (method === \"bridge-update-agent-status\" /* UpdateAgentStatus */) {\n          return this.onLogMessage(args[0], \"status\");\n        }\n        const tabId = await this.getActiveTabId();\n        if (!tabId || tabId === 0) {\n          throw new Error(\"no tab is connected\");\n        }\n        if (method.startsWith(\"mouse.\" /* PREFIX */)) {\n          const actionName = method.split(\".\")[1];\n          if (actionName === \"drag\") {\n            return this.mouse[actionName].apply(this.mouse, args);\n          }\n          return this.mouse[actionName].apply(this.mouse, args);\n        }\n        if (method.startsWith(\"keyboard.\" /* PREFIX */)) {\n          const actionName = method.split(\".\")[1];\n          if (actionName === \"press\") {\n            return this.keyboard[actionName].apply(this.keyboard, args);\n          }\n          return this.keyboard[actionName].apply(this.keyboard, args);\n        }\n        try {\n          const result = await this[method](\n            ...args\n          );\n          return result;\n        } catch (e) {\n          const errorMessage = e instanceof Error ? e.message : \"Unknown error\";\n          console.error(\"error calling method\", method, args, e);\n          this.onLogMessage(\n            `Error calling method: ${method}, ${errorMessage}`,\n            \"log\"\n          );\n          throw new Error(errorMessage, { cause: e });\n        }\n      },\n      // on disconnect\n      () => {\n        return this.destroy();\n      }\n    );\n    await this.bridgeClient.connect();\n    this.onLogMessage(\n      `Bridge connected, cli-side version v${this.bridgeClient.serverVersion}, browser-side version v${\"0.17.5\"}`,\n      \"log\"\n    );\n  }\n  async connect() {\n    return await this.setupBridgeClient();\n  }\n  async connectNewTabWithUrl(url, options = {\n    forceSameTabNavigation: true\n  }) {\n    const tab = await chrome.tabs.create({ url });\n    const tabId = tab.id;\n    (0, import_utils5.assert)(tabId, \"failed to get tabId after creating a new tab\");\n    this.onLogMessage(`Creating new tab: ${url}`, \"log\");\n    this.newlyCreatedTabIds.push(tabId);\n    if (options?.forceSameTabNavigation) {\n      this.forceSameTabNavigation = true;\n    }\n    await this.setActiveTabId(tabId);\n  }\n  async connectCurrentTab(options = {\n    forceSameTabNavigation: true\n  }) {\n    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });\n    const tabId = tabs[0]?.id;\n    (0, import_utils5.assert)(tabId, \"failed to get tabId\");\n    this.onLogMessage(`Connected to current tab: ${tabs[0]?.url}`, \"log\");\n    if (options?.forceSameTabNavigation) {\n      this.forceSameTabNavigation = true;\n    }\n    await this.setActiveTabId(tabId);\n  }\n  async setDestroyOptions(options) {\n    this.destroyOptions = options;\n  }\n  async destroy() {\n    if (this.destroyOptions?.closeTab && this.newlyCreatedTabIds.length > 0) {\n      this.onLogMessage(\"Closing all newly created tabs by bridge...\", \"log\");\n      for (const tabId of this.newlyCreatedTabIds) {\n        await chrome.tabs.remove(tabId);\n      }\n      this.newlyCreatedTabIds = [];\n    }\n    await super.destroy();\n    if (this.bridgeClient) {\n      this.bridgeClient.disconnect();\n      this.bridgeClient = null;\n      this.onDisconnect();\n    }\n  }\n};\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  ExtensionBridgePageBrowserSide\n});\n/**\n * @license\n * Copyright 2017 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\n\n//# sourceMappingURL=bridge-mode-browser.js.map", "\"use strict\";\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __accessCheck = (obj, member, msg) => {\n  if (!member.has(obj))\n    throw TypeError(\"Cannot \" + msg);\n};\nvar __privateGet = (obj, member, getter) => {\n  __accessCheck(obj, member, \"read from private field\");\n  return getter ? getter.call(obj) : member.get(obj);\n};\nvar __privateAdd = (obj, member, value) => {\n  if (member.has(obj))\n    throw TypeError(\"Cannot add the same private member more than once\");\n  member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\n};\nvar __privateSet = (obj, member, value, setter) => {\n  __accessCheck(obj, member, \"write to private field\");\n  setter ? setter.call(obj, value) : member.set(obj, value);\n  return value;\n};\nvar __privateMethod = (obj, member, method) => {\n  __accessCheck(obj, member, \"access private method\");\n  return method;\n};\n\n// src/chrome-extension/index.ts\nvar chrome_extension_exports = {};\n__export(chrome_extension_exports, {\n  ChromeExtensionProxyPage: () => ChromeExtensionProxyPage,\n  ChromeExtensionProxyPageAgent: () => ChromeExtensionProxyPageAgent,\n  ERROR_CODE_NOT_IMPLEMENTED_AS_DESIGNED: () => ERROR_CODE_NOT_IMPLEMENTED_AS_DESIGNED,\n  overrideAIConfig: () => import_env3.overrideAIConfig\n});\nmodule.exports = __toCommonJS(chrome_extension_exports);\n\n// src/common/utils.ts\nvar import_ai_model = require(\"@midscene/core/ai-model\");\nvar import_utils = require(\"@midscene/core/utils\");\nvar import_env = require(\"@midscene/shared/env\");\nvar import_extractor = require(\"@midscene/shared/extractor\");\nvar import_img = require(\"@midscene/shared/img\");\nvar import_utils2 = require(\"@midscene/shared/utils\");\nvar import_dayjs = __toESM(require(\"dayjs\"));\n\n// src/web-element.ts\nvar WebElementInfo = class {\n  constructor({\n    content,\n    rect,\n    // page,\n    locator,\n    id,\n    attributes,\n    indexId,\n    xpaths,\n    isVisible\n  }) {\n    this.content = content;\n    this.rect = rect;\n    this.center = [\n      Math.floor(rect.left + rect.width / 2),\n      Math.floor(rect.top + rect.height / 2)\n    ];\n    this.locator = locator;\n    this.id = id;\n    this.attributes = attributes;\n    this.indexId = indexId;\n    this.xpaths = xpaths;\n    this.isVisible = isVisible;\n  }\n};\n\n// src/common/utils.ts\nasync function parseContextFromWebPage(page, _opt) {\n  (0, import_utils2.assert)(page, \"page is required\");\n  if (page._forceUsePageContext) {\n    return await page._forceUsePageContext();\n  }\n  const url = await page.url();\n  (0, import_utils.uploadTestInfoToServer)({ testUrl: url });\n  let screenshotBase64;\n  let tree;\n  await Promise.all([\n    page.screenshotBase64().then((base64) => {\n      screenshotBase64 = base64;\n    }),\n    page.getElementsNodeTree().then(async (treeRoot) => {\n      tree = treeRoot;\n    })\n  ]);\n  const webTree = (0, import_extractor.traverseTree)(tree, (elementInfo) => {\n    const { rect, id, content, attributes, locator, indexId, isVisible } = elementInfo;\n    return new WebElementInfo({\n      rect,\n      locator,\n      id,\n      content,\n      attributes,\n      indexId,\n      isVisible\n    });\n  });\n  (0, import_utils2.assert)(screenshotBase64, \"screenshotBase64 is required\");\n  const elementsInfo = (0, import_extractor.treeToList)(webTree);\n  const size = await page.size();\n  if (size.dpr && size.dpr > 1) {\n    screenshotBase64 = await (0, import_img.resizeImgBase64)(screenshotBase64, {\n      width: size.width,\n      height: size.height\n    });\n  }\n  return {\n    content: elementsInfo,\n    tree: webTree,\n    size,\n    screenshotBase64,\n    url\n  };\n}\nfunction reportFileName(tag = \"web\") {\n  const reportTagName = (0, import_env.getAIConfig)(import_env.MIDSCENE_REPORT_TAG_NAME);\n  const dateTimeInFileName = (0, import_dayjs.default)().format(\"YYYY-MM-DD_HH-mm-ss\");\n  const uniqueId = (0, import_utils2.uuid)().substring(0, 8);\n  return `${reportTagName || tag}-${dateTimeInFileName}-${uniqueId}`;\n}\nfunction printReportMsg(filepath) {\n  (0, import_utils2.logMsg)(`Midscene - report file updated: ${filepath}`);\n}\nvar ERROR_CODE_NOT_IMPLEMENTED_AS_DESIGNED = \"NOT_IMPLEMENTED_AS_DESIGNED\";\nfunction replaceIllegalPathCharsAndSpace(str) {\n  return str.replace(/[:*?\"<>| ]/g, \"-\");\n}\nfunction matchElementFromPlan(planLocateParam, tree) {\n  if (!planLocateParam) {\n    return void 0;\n  }\n  if (planLocateParam.id) {\n    return (0, import_extractor.getNodeFromCacheList)(planLocateParam.id);\n  }\n  if (planLocateParam.bbox) {\n    const centerPosition = {\n      x: Math.floor((planLocateParam.bbox[0] + planLocateParam.bbox[2]) / 2),\n      y: Math.floor((planLocateParam.bbox[1] + planLocateParam.bbox[3]) / 2)\n    };\n    let element = (0, import_ai_model.elementByPositionWithElementInfo)(tree, centerPosition);\n    if (!element) {\n      element = (0, import_extractor.generateElementByPosition)(centerPosition);\n    }\n    return element;\n  }\n  return void 0;\n}\n\n// src/common/agent.ts\nvar import_core2 = require(\"@midscene/core\");\nvar import_js_yaml4 = __toESM(require(\"js-yaml\"));\n\n// src/yaml/player.ts\nvar import_node_fs = require(\"fs\");\nvar import_node_path = require(\"path\");\nvar import_utils3 = require(\"@midscene/shared/utils\");\nvar import_common = require(\"@midscene/shared/common\");\nvar ScriptPlayer = class {\n  constructor(script, setupAgent, onTaskStatusChange) {\n    this.script = script;\n    this.setupAgent = setupAgent;\n    this.onTaskStatusChange = onTaskStatusChange;\n    this.taskStatusList = [];\n    this.status = \"init\";\n    this.unnamedResultIndex = 0;\n    this.pageAgent = null;\n    this.result = {};\n    const target = script.target || script.web || script.android;\n    if (import_utils3.ifInBrowser) {\n      this.output = void 0;\n    } else if (target?.output) {\n      this.output = (0, import_node_path.resolve)(process.cwd(), target.output);\n    } else {\n      this.output = (0, import_node_path.join)((0, import_common.getMidsceneRunSubDir)(\"output\"), `${process.pid}.json`);\n    }\n    this.taskStatusList = (script.tasks || []).map((task, taskIndex) => ({\n      ...task,\n      index: taskIndex,\n      status: \"init\",\n      totalSteps: task.flow?.length || 0\n    }));\n  }\n  setResult(key, value) {\n    const keyToUse = key || this.unnamedResultIndex++;\n    if (this.result[keyToUse]) {\n      console.warn(`result key ${keyToUse} already exists, will overwrite`);\n    }\n    this.result[keyToUse] = value;\n    this.flushResult();\n  }\n  setPlayerStatus(status, error) {\n    this.status = status;\n    this.errorInSetup = error;\n  }\n  notifyCurrentTaskStatusChange(taskIndex) {\n    const taskIndexToNotify = typeof taskIndex === \"number\" ? taskIndex : this.currentTaskIndex;\n    if (typeof taskIndexToNotify !== \"number\") {\n      return;\n    }\n    const taskStatus = this.taskStatusList[taskIndexToNotify];\n    if (this.onTaskStatusChange) {\n      this.onTaskStatusChange(taskStatus);\n    }\n  }\n  async setTaskStatus(index, statusValue, error) {\n    this.taskStatusList[index].status = statusValue;\n    if (error) {\n      this.taskStatusList[index].error = error;\n    }\n    this.notifyCurrentTaskStatusChange(index);\n  }\n  setTaskIndex(taskIndex) {\n    this.currentTaskIndex = taskIndex;\n  }\n  flushResult() {\n    if (Object.keys(this.result).length && this.output) {\n      const output = (0, import_node_path.resolve)(process.cwd(), this.output);\n      const outputDir = (0, import_node_path.dirname)(output);\n      if (!(0, import_node_fs.existsSync)(outputDir)) {\n        (0, import_node_fs.mkdirSync)(outputDir, { recursive: true });\n      }\n      (0, import_node_fs.writeFileSync)(output, JSON.stringify(this.result, void 0, 2));\n    }\n  }\n  async playTask(taskStatus, agent) {\n    const { flow } = taskStatus;\n    (0, import_utils3.assert)(flow, \"missing flow in task\");\n    for (const flowItemIndex in flow) {\n      const currentStep = Number.parseInt(flowItemIndex, 10);\n      taskStatus.currentStep = currentStep;\n      const flowItem = flow[flowItemIndex];\n      if (\"aiAction\" in flowItem || \"ai\" in flowItem) {\n        const actionTask = flowItem;\n        const prompt = actionTask.aiAction || actionTask.ai;\n        (0, import_utils3.assert)(prompt, \"missing prompt for ai (aiAction)\");\n        (0, import_utils3.assert)(\n          typeof prompt === \"string\",\n          \"prompt for aiAction must be a string\"\n        );\n        await agent.aiAction(prompt, {\n          cacheable: actionTask.cacheable\n        });\n      } else if (\"aiAssert\" in flowItem) {\n        const assertTask = flowItem;\n        const prompt = assertTask.aiAssert;\n        const msg = assertTask.errorMessage;\n        (0, import_utils3.assert)(prompt, \"missing prompt for aiAssert\");\n        (0, import_utils3.assert)(\n          typeof prompt === \"string\",\n          \"prompt for aiAssert must be a string\"\n        );\n        await agent.aiAssert(prompt, msg);\n      } else if (\"aiQuery\" in flowItem) {\n        const queryTask = flowItem;\n        const prompt = queryTask.aiQuery;\n        const options = {\n          domIncluded: queryTask.domIncluded,\n          screenshotIncluded: queryTask.screenshotIncluded\n        };\n        (0, import_utils3.assert)(prompt, \"missing prompt for aiQuery\");\n        (0, import_utils3.assert)(\n          typeof prompt === \"string\",\n          \"prompt for aiQuery must be a string\"\n        );\n        const queryResult = await agent.aiQuery(prompt, options);\n        this.setResult(queryTask.name, queryResult);\n      } else if (\"aiNumber\" in flowItem) {\n        const numberTask = flowItem;\n        const prompt = numberTask.aiNumber;\n        const options = {\n          domIncluded: numberTask.domIncluded,\n          screenshotIncluded: numberTask.screenshotIncluded\n        };\n        (0, import_utils3.assert)(prompt, \"missing prompt for number\");\n        (0, import_utils3.assert)(\n          typeof prompt === \"string\",\n          \"prompt for number must be a string\"\n        );\n        const numberResult = await agent.aiNumber(prompt, options);\n        this.setResult(numberTask.name, numberResult);\n      } else if (\"aiString\" in flowItem) {\n        const stringTask = flowItem;\n        const prompt = stringTask.aiString;\n        const options = {\n          domIncluded: stringTask.domIncluded,\n          screenshotIncluded: stringTask.screenshotIncluded\n        };\n        (0, import_utils3.assert)(prompt, \"missing prompt for string\");\n        (0, import_utils3.assert)(\n          typeof prompt === \"string\",\n          \"prompt for string must be a string\"\n        );\n        const stringResult = await agent.aiString(prompt, options);\n        this.setResult(stringTask.name, stringResult);\n      } else if (\"aiBoolean\" in flowItem) {\n        const booleanTask = flowItem;\n        const prompt = booleanTask.aiBoolean;\n        const options = {\n          domIncluded: booleanTask.domIncluded,\n          screenshotIncluded: booleanTask.screenshotIncluded\n        };\n        (0, import_utils3.assert)(prompt, \"missing prompt for boolean\");\n        (0, import_utils3.assert)(\n          typeof prompt === \"string\",\n          \"prompt for boolean must be a string\"\n        );\n        const booleanResult = await agent.aiBoolean(prompt, options);\n        this.setResult(booleanTask.name, booleanResult);\n      } else if (\"aiLocate\" in flowItem) {\n        const locateTask = flowItem;\n        const prompt = locateTask.aiLocate;\n        (0, import_utils3.assert)(prompt, \"missing prompt for aiLocate\");\n        (0, import_utils3.assert)(\n          typeof prompt === \"string\",\n          \"prompt for aiLocate must be a string\"\n        );\n        const locateResult = await agent.aiLocate(prompt);\n        this.setResult(locateTask.name, locateResult);\n      } else if (\"aiWaitFor\" in flowItem) {\n        const waitForTask = flowItem;\n        const prompt = waitForTask.aiWaitFor;\n        (0, import_utils3.assert)(prompt, \"missing prompt for aiWaitFor\");\n        (0, import_utils3.assert)(\n          typeof prompt === \"string\",\n          \"prompt for aiWaitFor must be a string\"\n        );\n        const timeout = waitForTask.timeout;\n        await agent.aiWaitFor(prompt, { timeoutMs: timeout });\n      } else if (\"sleep\" in flowItem) {\n        const sleepTask = flowItem;\n        const ms = sleepTask.sleep;\n        let msNumber = ms;\n        if (typeof ms === \"string\") {\n          msNumber = Number.parseInt(ms, 10);\n        }\n        (0, import_utils3.assert)(\n          msNumber && msNumber > 0,\n          `ms for sleep must be greater than 0, but got ${ms}`\n        );\n        await new Promise((resolve2) => setTimeout(resolve2, msNumber));\n      } else if (\"aiTap\" in flowItem) {\n        const tapTask = flowItem;\n        await agent.aiTap(tapTask.aiTap, tapTask);\n      } else if (\"aiRightClick\" in flowItem) {\n        const rightClickTask = flowItem;\n        await agent.aiRightClick(rightClickTask.aiRightClick, rightClickTask);\n      } else if (\"aiHover\" in flowItem) {\n        const hoverTask = flowItem;\n        await agent.aiHover(hoverTask.aiHover, hoverTask);\n      } else if (\"aiInput\" in flowItem) {\n        const inputTask = flowItem;\n        await agent.aiInput(inputTask.aiInput, inputTask.locate, inputTask);\n      } else if (\"aiKeyboardPress\" in flowItem) {\n        const keyboardPressTask = flowItem;\n        await agent.aiKeyboardPress(\n          keyboardPressTask.aiKeyboardPress,\n          keyboardPressTask.locate,\n          keyboardPressTask\n        );\n      } else if (\"aiScroll\" in flowItem) {\n        const scrollTask = flowItem;\n        await agent.aiScroll(scrollTask, scrollTask.locate, scrollTask);\n      } else if (\"javascript\" in flowItem) {\n        const evaluateJavaScriptTask = flowItem;\n        const result = await agent.evaluateJavaScript(\n          evaluateJavaScriptTask.javascript\n        );\n        this.setResult(evaluateJavaScriptTask.name, result);\n      } else if (\"logScreenshot\" in flowItem) {\n        const logScreenshotTask = flowItem;\n        await agent.logScreenshot(logScreenshotTask.logScreenshot, {\n          content: logScreenshotTask.content || \"\"\n        });\n      } else {\n        throw new Error(`unknown flowItem: ${JSON.stringify(flowItem)}`);\n      }\n    }\n    this.reportFile = agent.reportFile;\n  }\n  async run() {\n    const { target, web, android, tasks } = this.script;\n    const webEnv = web || target;\n    const androidEnv = android;\n    const platform = webEnv || androidEnv;\n    this.setPlayerStatus(\"running\");\n    let agent = null;\n    let freeFn = [];\n    try {\n      const { agent: newAgent, freeFn: newFreeFn } = await this.setupAgent(\n        platform\n      );\n      agent = newAgent;\n      const originalOnTaskStartTip = agent.onTaskStartTip;\n      agent.onTaskStartTip = (tip) => {\n        if (this.status === \"running\") {\n          this.agentStatusTip = tip;\n        }\n        originalOnTaskStartTip?.(tip);\n      };\n      freeFn = [\n        ...newFreeFn || [],\n        {\n          name: \"restore-agent-onTaskStartTip\",\n          fn: () => {\n            if (agent) {\n              agent.onTaskStartTip = originalOnTaskStartTip;\n            }\n          }\n        }\n      ];\n    } catch (e) {\n      this.setPlayerStatus(\"error\", e);\n      return;\n    }\n    this.pageAgent = agent;\n    let taskIndex = 0;\n    this.setPlayerStatus(\"running\");\n    let errorFlag = false;\n    while (taskIndex < tasks.length) {\n      const taskStatus = this.taskStatusList[taskIndex];\n      this.setTaskStatus(taskIndex, \"running\");\n      this.setTaskIndex(taskIndex);\n      try {\n        await this.playTask(taskStatus, this.pageAgent);\n        this.setTaskStatus(taskIndex, \"done\");\n      } catch (e) {\n        this.setTaskStatus(taskIndex, \"error\", e);\n        if (taskStatus.continueOnError) {\n        } else {\n          this.reportFile = agent.reportFile;\n          errorFlag = true;\n          break;\n        }\n      }\n      this.reportFile = agent.reportFile;\n      taskIndex++;\n    }\n    if (errorFlag) {\n      this.setPlayerStatus(\"error\");\n    } else {\n      this.setPlayerStatus(\"done\");\n    }\n    this.agentStatusTip = \"\";\n    for (const fn of freeFn) {\n      try {\n        await fn.fn();\n      } catch (e) {\n      }\n    }\n  }\n};\n\n// src/yaml/builder.ts\nvar import_js_yaml = __toESM(require(\"js-yaml\"));\n\n// src/yaml/utils.ts\nvar import_utils4 = require(\"@midscene/shared/utils\");\nvar import_js_yaml2 = __toESM(require(\"js-yaml\"));\nfunction interpolateEnvVars(content) {\n  return content.replace(/\\$\\{([^}]+)\\}/g, (_, envVar) => {\n    const value = process.env[envVar.trim()];\n    if (value === void 0) {\n      throw new Error(`Environment variable \"${envVar.trim()}\" is not defined`);\n    }\n    return value;\n  });\n}\nfunction parseYamlScript(content, filePath, ignoreCheckingTarget) {\n  let processedContent = content;\n  if (content.indexOf(\"android\") !== -1 && content.match(/deviceId:\\s*(\\d+)/)) {\n    let matchedDeviceId;\n    processedContent = content.replace(\n      /deviceId:\\s*(\\d+)/g,\n      (match, deviceId) => {\n        matchedDeviceId = deviceId;\n        return `deviceId: '${deviceId}'`;\n      }\n    );\n    console.warn(\n      `please use string-style deviceId in yaml script, for example: deviceId: \"${matchedDeviceId}\"`\n    );\n  }\n  const interpolatedContent = interpolateEnvVars(processedContent);\n  const obj = import_js_yaml2.default.load(interpolatedContent, {\n    schema: import_js_yaml2.default.JSON_SCHEMA\n  });\n  const pathTip = filePath ? `, failed to load ${filePath}` : \"\";\n  const android = typeof obj.android !== \"undefined\" ? Object.assign({}, obj.android || {}) : void 0;\n  const webConfig = obj.web || obj.target;\n  const web = typeof webConfig !== \"undefined\" ? Object.assign({}, webConfig || {}) : void 0;\n  if (!ignoreCheckingTarget) {\n    (0, import_utils4.assert)(\n      web || android,\n      `at least one of \"target\", \"web\", or \"android\" properties is required in yaml script${pathTip}`\n    );\n    (0, import_utils4.assert)(\n      web && !android || !web && android,\n      `only one of \"target\", \"web\", or \"android\" properties is allowed in yaml script${pathTip}`\n    );\n    if (web || android) {\n      (0, import_utils4.assert)(\n        typeof web === \"object\" || typeof android === \"object\",\n        `property \"target/web/android\" must be an object${pathTip}`\n      );\n    }\n  }\n  (0, import_utils4.assert)(obj.tasks, `property \"tasks\" is required in yaml script ${pathTip}`);\n  (0, import_utils4.assert)(\n    Array.isArray(obj.tasks),\n    `property \"tasks\" must be an array in yaml script, but got ${obj.tasks}`\n  );\n  return obj;\n}\n\n// src/common/agent.ts\nvar import_utils11 = require(\"@midscene/core/utils\");\nvar import_constants2 = require(\"@midscene/shared/constants\");\nvar import_env2 = require(\"@midscene/shared/env\");\nvar import_logger4 = require(\"@midscene/shared/logger\");\nvar import_utils12 = require(\"@midscene/shared/utils\");\n\n// src/common/tasks.ts\nvar import_core = require(\"@midscene/core\");\nvar import_ai_model2 = require(\"@midscene/core/ai-model\");\nvar import_utils5 = require(\"@midscene/core/utils\");\nvar import_constants = require(\"@midscene/shared/constants\");\nvar import_logger = require(\"@midscene/shared/logger\");\nvar import_utils6 = require(\"@midscene/shared/utils\");\n\n// src/common/ui-utils.ts\nfunction typeStr(task) {\n  return task.subType && task.subType !== \"Plan\" ? `${task.type} / ${task.subType || \"\"}` : task.type;\n}\nfunction getKeyCommands(value) {\n  const keys = Array.isArray(value) ? value : [value];\n  return keys.reduce((acc, k) => {\n    const includeMeta = keys.includes(\"Meta\") || keys.includes(\"Control\");\n    if (includeMeta && (k === \"a\" || k === \"A\")) {\n      return acc.concat([{ key: k, command: \"SelectAll\" }]);\n    }\n    if (includeMeta && (k === \"c\" || k === \"C\")) {\n      return acc.concat([{ key: k, command: \"Copy\" }]);\n    }\n    if (includeMeta && (k === \"v\" || k === \"V\")) {\n      return acc.concat([{ key: k, command: \"Paste\" }]);\n    }\n    return acc.concat([{ key: k }]);\n  }, []);\n}\nfunction locateParamStr(locate) {\n  if (!locate) {\n    return \"\";\n  }\n  if (typeof locate === \"string\") {\n    return locate;\n  }\n  return locate.prompt;\n}\nfunction scrollParamStr(scrollParam) {\n  if (!scrollParam) {\n    return \"\";\n  }\n  return `${scrollParam.direction || \"down\"}, ${scrollParam.scrollType || \"once\"}, ${scrollParam.distance || \"distance-not-set\"}`;\n}\nfunction taskTitleStr(type, prompt) {\n  if (prompt) {\n    return `${type} - ${prompt}`;\n  }\n  return type;\n}\nfunction paramStr(task) {\n  let value;\n  if (task.type === \"Planning\") {\n    value = task?.param?.userInstruction;\n  }\n  if (task.type === \"Insight\") {\n    value = task?.param?.prompt || task?.param?.id || task?.param?.dataDemand || task?.param?.assertion;\n  }\n  if (task.type === \"Action\") {\n    const locate = task?.locate;\n    const locateStr = locate ? locateParamStr(locate) : \"\";\n    value = task.thought || \"\";\n    if (typeof task?.param?.timeMs === \"number\") {\n      value = `${task?.param?.timeMs}ms`;\n    } else if (typeof task?.param?.scrollType === \"string\") {\n      value = scrollParamStr(task?.param);\n    } else if (typeof task?.param?.value !== \"undefined\") {\n      value = task?.param?.value;\n    }\n    if (locateStr) {\n      if (value) {\n        value = `${locateStr} - ${value}`;\n      } else {\n        value = locateStr;\n      }\n    }\n  }\n  if (typeof value === \"undefined\")\n    return \"\";\n  return typeof value === \"string\" ? value : JSON.stringify(value, void 0, 2);\n}\nvar limitOpenNewTabScript = `\nif (!window.__MIDSCENE_NEW_TAB_INTERCEPTOR_INITIALIZED__) {\n  window.__MIDSCENE_NEW_TAB_INTERCEPTOR_INITIALIZED__ = true;\n\n  // Intercept the window.open method (only once)\n  window.open = function(url) {\n    console.log('Blocked window.open:', url);\n    window.location.href = url;\n    return null;\n  };\n\n  // Block all a tag clicks with target=\"_blank\" (only once)\n  document.addEventListener('click', function(e) {\n    const target = e.target.closest('a');\n    if (target && target.target === '_blank') {\n      e.preventDefault();\n      console.log('Blocked new tab:', target.href);\n      window.location.href = target.href;\n      target.removeAttribute('target');\n    }\n  }, true);\n}\n`;\n\n// src/common/tasks.ts\nvar debug = (0, import_logger.getDebug)(\"page-task-executor\");\nvar replanningCountLimit = 10;\nvar isAndroidPage = (page) => {\n  return page.pageType === \"android\";\n};\nvar PageTaskExecutor = class {\n  constructor(page, insight, opts) {\n    this.conversationHistory = [];\n    this.page = page;\n    this.insight = insight;\n    this.taskCache = opts.taskCache;\n    this.onTaskStartCallback = opts?.onTaskStart;\n  }\n  async recordScreenshot(timing) {\n    const base64 = await this.page.screenshotBase64();\n    const item = {\n      type: \"screenshot\",\n      ts: Date.now(),\n      screenshot: base64,\n      timing\n    };\n    return item;\n  }\n  async getElementXpath(pageContext, element) {\n    let elementId = element?.id;\n    if (element?.attributes?.nodeType === import_constants.NodeType.POSITION) {\n      await this.insight.contextRetrieverFn(\"locate\");\n      const info = (0, import_ai_model2.elementByPositionWithElementInfo)(\n        pageContext.tree,\n        {\n          x: element.center[0],\n          y: element.center[1]\n        },\n        {\n          requireStrictDistance: false,\n          filterPositionElements: true\n        }\n      );\n      if (info?.id) {\n        elementId = info.id;\n      } else {\n        debug(\n          \"no element id found for position node, will not update cache\",\n          element\n        );\n      }\n    }\n    if (!elementId) {\n      return void 0;\n    }\n    try {\n      const result = await this.page.getXpathsById(elementId);\n      return result;\n    } catch (error) {\n      debug(\"getXpathsById error: \", error);\n    }\n  }\n  prependExecutorWithScreenshot(taskApply, appendAfterExecution = false) {\n    const taskWithScreenshot = {\n      ...taskApply,\n      executor: async (param, context, ...args) => {\n        const recorder = [];\n        const { task } = context;\n        task.recorder = recorder;\n        const shot = await this.recordScreenshot(`before ${task.type}`);\n        recorder.push(shot);\n        const result = await taskApply.executor(param, context, ...args);\n        if (taskApply.type === \"Action\") {\n          await Promise.all([\n            (async () => {\n              await (0, import_utils5.sleep)(100);\n              if (this.page.waitUntilNetworkIdle) {\n                try {\n                  await this.page.waitUntilNetworkIdle();\n                } catch (error) {\n                }\n              }\n            })(),\n            (0, import_utils5.sleep)(200)\n          ]);\n        }\n        if (appendAfterExecution) {\n          const shot2 = await this.recordScreenshot(\"after Action\");\n          recorder.push(shot2);\n        }\n        return result;\n      }\n    };\n    return taskWithScreenshot;\n  }\n  async convertPlanToExecutable(plans, opts) {\n    const tasks = [];\n    plans.forEach((plan2) => {\n      if (plan2.type === \"Locate\") {\n        if (plan2.locate === null || plan2.locate?.id === null || plan2.locate?.id === \"null\") {\n          return;\n        }\n        const taskFind = {\n          type: \"Insight\",\n          subType: \"Locate\",\n          param: plan2.locate ? {\n            ...plan2.locate,\n            cacheable: opts?.cacheable\n          } : void 0,\n          thought: plan2.thought,\n          locate: plan2.locate,\n          executor: async (param, taskContext) => {\n            const { task } = taskContext;\n            (0, import_utils6.assert)(\n              param?.prompt || param?.id || param?.bbox,\n              \"No prompt or id or position or bbox to locate\"\n            );\n            let insightDump;\n            let usage;\n            const dumpCollector = (dump) => {\n              insightDump = dump;\n              usage = dump?.taskInfo?.usage;\n              task.log = {\n                dump: insightDump\n              };\n              task.usage = usage;\n            };\n            this.insight.onceDumpUpdatedFn = dumpCollector;\n            const shotTime = Date.now();\n            const pageContext = await this.insight.contextRetrieverFn(\"locate\");\n            task.pageContext = pageContext;\n            const recordItem = {\n              type: \"screenshot\",\n              ts: shotTime,\n              screenshot: pageContext.screenshotBase64,\n              timing: \"before locate\"\n            };\n            task.recorder = [recordItem];\n            let cacheHitFlag = false;\n            const cachePrompt = param.prompt;\n            const locateCacheRecord = this.taskCache?.matchLocateCache(cachePrompt);\n            const xpaths = locateCacheRecord?.cacheContent?.xpaths;\n            let elementFromCache = null;\n            try {\n              if (xpaths?.length && this.taskCache?.isCacheResultUsed && param?.cacheable !== false) {\n                for (let i = 0; i < xpaths.length; i++) {\n                  const element2 = await this.page.getElementInfoByXpath(\n                    xpaths[i]\n                  );\n                  if (element2?.id) {\n                    elementFromCache = element2;\n                    debug(\"cache hit, prompt: %s\", cachePrompt);\n                    cacheHitFlag = true;\n                    debug(\n                      \"found a new new element with same xpath, xpath: %s, id: %s\",\n                      xpaths[i],\n                      element2?.id\n                    );\n                    break;\n                  }\n                }\n              }\n            } catch (error) {\n              debug(\"get element info by xpath error: \", error);\n            }\n            const startTime = Date.now();\n            const element = elementFromCache || // try to match element from cache\n            matchElementFromPlan(param, pageContext.tree) || // try to match element from plan\n            (await this.insight.locate(param, {\n              context: pageContext\n            })).element;\n            const aiCost = Date.now() - startTime;\n            let currentXpaths;\n            if (element && this.taskCache && !cacheHitFlag && param?.cacheable !== false) {\n              const elementXpaths = await this.getElementXpath(\n                pageContext,\n                element\n              );\n              if (elementXpaths?.length) {\n                currentXpaths = elementXpaths;\n                this.taskCache.updateOrAppendCacheRecord(\n                  {\n                    type: \"locate\",\n                    prompt: cachePrompt,\n                    xpaths: elementXpaths\n                  },\n                  locateCacheRecord\n                );\n              } else {\n                debug(\n                  \"no xpaths found, will not update cache\",\n                  cachePrompt,\n                  elementXpaths\n                );\n              }\n            }\n            if (!element) {\n              throw new Error(`Element not found: ${param.prompt}`);\n            }\n            return {\n              output: {\n                element\n              },\n              pageContext,\n              cache: {\n                hit: cacheHitFlag,\n                originalXpaths: xpaths,\n                currentXpaths\n              },\n              aiCost\n            };\n          }\n        };\n        tasks.push(taskFind);\n      } else if (plan2.type === \"Assert\" || plan2.type === \"AssertWithoutThrow\") {\n        const assertPlan = plan2;\n        const taskAssert = {\n          type: \"Insight\",\n          subType: \"Assert\",\n          param: assertPlan.param,\n          thought: assertPlan.thought,\n          locate: assertPlan.locate,\n          executor: async (param, taskContext) => {\n            const { task } = taskContext;\n            let insightDump;\n            const dumpCollector = (dump) => {\n              insightDump = dump;\n            };\n            this.insight.onceDumpUpdatedFn = dumpCollector;\n            const assertion = await this.insight.assert(\n              assertPlan.param.assertion\n            );\n            if (!assertion.pass) {\n              if (plan2.type === \"Assert\") {\n                task.output = assertion;\n                task.log = {\n                  dump: insightDump\n                };\n                throw new Error(\n                  assertion.thought || \"Assertion failed without reason\"\n                );\n              }\n              task.error = assertion.thought;\n            }\n            return {\n              output: assertion,\n              log: {\n                dump: insightDump\n              },\n              usage: assertion.usage\n            };\n          }\n        };\n        tasks.push(taskAssert);\n      } else if (plan2.type === \"Input\") {\n        const taskActionInput = {\n          type: \"Action\",\n          subType: \"Input\",\n          param: plan2.param,\n          thought: plan2.thought,\n          locate: plan2.locate,\n          executor: async (taskParam, { element }) => {\n            if (element) {\n              await this.page.clearInput(element);\n              if (!taskParam || !taskParam.value) {\n                return;\n              }\n            }\n            await this.page.keyboard.type(taskParam.value, {\n              autoDismissKeyboard: taskParam.autoDismissKeyboard\n            });\n          }\n        };\n        tasks.push(taskActionInput);\n      } else if (plan2.type === \"KeyboardPress\") {\n        const taskActionKeyboardPress = {\n          type: \"Action\",\n          subType: \"KeyboardPress\",\n          param: plan2.param,\n          thought: plan2.thought,\n          locate: plan2.locate,\n          executor: async (taskParam) => {\n            const keys = getKeyCommands(taskParam.value);\n            await this.page.keyboard.press(keys);\n          }\n        };\n        tasks.push(taskActionKeyboardPress);\n      } else if (plan2.type === \"Tap\") {\n        const taskActionTap = {\n          type: \"Action\",\n          subType: \"Tap\",\n          thought: plan2.thought,\n          locate: plan2.locate,\n          executor: async (param, { element }) => {\n            (0, import_utils6.assert)(element, \"Element not found, cannot tap\");\n            await this.page.mouse.click(element.center[0], element.center[1]);\n          }\n        };\n        tasks.push(taskActionTap);\n      } else if (plan2.type === \"RightClick\") {\n        const taskActionRightClick = {\n          type: \"Action\",\n          subType: \"RightClick\",\n          thought: plan2.thought,\n          locate: plan2.locate,\n          executor: async (param, { element }) => {\n            (0, import_utils6.assert)(element, \"Element not found, cannot right click\");\n            await this.page.mouse.click(\n              element.center[0],\n              element.center[1],\n              { button: \"right\" }\n            );\n          }\n        };\n        tasks.push(taskActionRightClick);\n      } else if (plan2.type === \"Drag\") {\n        const taskActionDrag = {\n          type: \"Action\",\n          subType: \"Drag\",\n          param: plan2.param,\n          thought: plan2.thought,\n          locate: plan2.locate,\n          executor: async (taskParam) => {\n            (0, import_utils6.assert)(\n              taskParam?.start_box && taskParam?.end_box,\n              \"No start_box or end_box to drag\"\n            );\n            await this.page.mouse.drag(taskParam.start_box, taskParam.end_box);\n          }\n        };\n        tasks.push(taskActionDrag);\n      } else if (plan2.type === \"Hover\") {\n        const taskActionHover = {\n          type: \"Action\",\n          subType: \"Hover\",\n          thought: plan2.thought,\n          locate: plan2.locate,\n          executor: async (param, { element }) => {\n            (0, import_utils6.assert)(element, \"Element not found, cannot hover\");\n            await this.page.mouse.move(element.center[0], element.center[1]);\n          }\n        };\n        tasks.push(taskActionHover);\n      } else if (plan2.type === \"Scroll\") {\n        const taskActionScroll = {\n          type: \"Action\",\n          subType: \"Scroll\",\n          param: plan2.param,\n          thought: plan2.thought,\n          locate: plan2.locate,\n          executor: async (taskParam, { element }) => {\n            const startingPoint = element ? {\n              left: element.center[0],\n              top: element.center[1]\n            } : void 0;\n            const scrollToEventName = taskParam?.scrollType;\n            if (scrollToEventName === \"untilTop\") {\n              await this.page.scrollUntilTop(startingPoint);\n            } else if (scrollToEventName === \"untilBottom\") {\n              await this.page.scrollUntilBottom(startingPoint);\n            } else if (scrollToEventName === \"untilRight\") {\n              await this.page.scrollUntilRight(startingPoint);\n            } else if (scrollToEventName === \"untilLeft\") {\n              await this.page.scrollUntilLeft(startingPoint);\n            } else if (scrollToEventName === \"once\" || !scrollToEventName) {\n              if (taskParam?.direction === \"down\" || !taskParam || !taskParam.direction) {\n                await this.page.scrollDown(\n                  taskParam?.distance || void 0,\n                  startingPoint\n                );\n              } else if (taskParam.direction === \"up\") {\n                await this.page.scrollUp(\n                  taskParam.distance || void 0,\n                  startingPoint\n                );\n              } else if (taskParam.direction === \"left\") {\n                await this.page.scrollLeft(\n                  taskParam.distance || void 0,\n                  startingPoint\n                );\n              } else if (taskParam.direction === \"right\") {\n                await this.page.scrollRight(\n                  taskParam.distance || void 0,\n                  startingPoint\n                );\n              } else {\n                throw new Error(\n                  `Unknown scroll direction: ${taskParam.direction}`\n                );\n              }\n              await (0, import_utils5.sleep)(500);\n            } else {\n              throw new Error(\n                `Unknown scroll event type: ${scrollToEventName}, taskParam: ${JSON.stringify(\n                  taskParam\n                )}`\n              );\n            }\n          }\n        };\n        tasks.push(taskActionScroll);\n      } else if (plan2.type === \"Sleep\") {\n        const taskActionSleep = {\n          type: \"Action\",\n          subType: \"Sleep\",\n          param: plan2.param,\n          thought: plan2.thought,\n          locate: plan2.locate,\n          executor: async (taskParam) => {\n            await (0, import_utils5.sleep)(taskParam?.timeMs || 3e3);\n          }\n        };\n        tasks.push(taskActionSleep);\n      } else if (plan2.type === \"Error\") {\n        const taskActionError = {\n          type: \"Action\",\n          subType: \"Error\",\n          param: plan2.param,\n          thought: plan2.thought || plan2.param?.thought,\n          locate: plan2.locate,\n          executor: async () => {\n            throw new Error(\n              plan2?.thought || plan2.param?.thought || \"error without thought\"\n            );\n          }\n        };\n        tasks.push(taskActionError);\n      } else if (plan2.type === \"ExpectedFalsyCondition\") {\n        const taskActionFalsyConditionStatement = {\n          type: \"Action\",\n          subType: \"ExpectedFalsyCondition\",\n          param: null,\n          thought: plan2.param?.reason,\n          locate: plan2.locate,\n          executor: async () => {\n          }\n        };\n        tasks.push(taskActionFalsyConditionStatement);\n      } else if (plan2.type === \"Finished\") {\n        const taskActionFinished = {\n          type: \"Action\",\n          subType: \"Finished\",\n          param: null,\n          thought: plan2.thought,\n          locate: plan2.locate,\n          executor: async (param) => {\n          }\n        };\n        tasks.push(taskActionFinished);\n      } else if (plan2.type === \"AndroidHomeButton\") {\n        const taskActionAndroidHomeButton = {\n          type: \"Action\",\n          subType: \"AndroidHomeButton\",\n          param: null,\n          thought: plan2.thought,\n          locate: plan2.locate,\n          executor: async (param) => {\n            (0, import_utils6.assert)(\n              isAndroidPage(this.page),\n              \"Cannot use home button on non-Android devices\"\n            );\n            await this.page.home();\n          }\n        };\n        tasks.push(taskActionAndroidHomeButton);\n      } else if (plan2.type === \"AndroidBackButton\") {\n        const taskActionAndroidBackButton = {\n          type: \"Action\",\n          subType: \"AndroidBackButton\",\n          param: null,\n          thought: plan2.thought,\n          locate: plan2.locate,\n          executor: async (param) => {\n            (0, import_utils6.assert)(\n              isAndroidPage(this.page),\n              \"Cannot use back button on non-Android devices\"\n            );\n            await this.page.back();\n          }\n        };\n        tasks.push(taskActionAndroidBackButton);\n      } else if (plan2.type === \"AndroidRecentAppsButton\") {\n        const taskActionAndroidRecentAppsButton = {\n          type: \"Action\",\n          subType: \"AndroidRecentAppsButton\",\n          param: null,\n          thought: plan2.thought,\n          locate: plan2.locate,\n          executor: async (param) => {\n            (0, import_utils6.assert)(\n              isAndroidPage(this.page),\n              \"Cannot use recent apps button on non-Android devices\"\n            );\n            await this.page.recentApps();\n          }\n        };\n        tasks.push(taskActionAndroidRecentAppsButton);\n      } else {\n        throw new Error(`Unknown or unsupported task type: ${plan2.type}`);\n      }\n    });\n    const wrappedTasks = tasks.map(\n      (task, index) => {\n        if (task.type === \"Action\") {\n          return this.prependExecutorWithScreenshot(\n            task,\n            index === tasks.length - 1\n          );\n        }\n        return task;\n      }\n    );\n    return {\n      tasks: wrappedTasks\n    };\n  }\n  async setupPlanningContext(executorContext) {\n    const shotTime = Date.now();\n    const pageContext = await this.insight.contextRetrieverFn(\"locate\");\n    const recordItem = {\n      type: \"screenshot\",\n      ts: shotTime,\n      screenshot: pageContext.screenshotBase64,\n      timing: \"before planning\"\n    };\n    executorContext.task.recorder = [recordItem];\n    executorContext.task.pageContext = pageContext;\n    return {\n      pageContext\n    };\n  }\n  async loadYamlFlowAsPlanning(userInstruction, yamlString) {\n    const taskExecutor = new import_core.Executor(taskTitleStr(\"Action\", userInstruction), {\n      onTaskStart: this.onTaskStartCallback\n    });\n    const task = {\n      type: \"Planning\",\n      subType: \"LoadYaml\",\n      locate: null,\n      param: {\n        userInstruction\n      },\n      executor: async (param, executorContext) => {\n        await this.setupPlanningContext(executorContext);\n        return {\n          output: {\n            actions: [],\n            more_actions_needed_by_instruction: false,\n            log: \"\",\n            yamlString\n          },\n          cache: {\n            hit: true\n          }\n        };\n      }\n    };\n    await taskExecutor.append(task);\n    await taskExecutor.flush();\n    return {\n      executor: taskExecutor\n    };\n  }\n  planningTaskFromPrompt(userInstruction, log, actionContext) {\n    const task = {\n      type: \"Planning\",\n      subType: \"Plan\",\n      locate: null,\n      param: {\n        userInstruction,\n        log\n      },\n      executor: async (param, executorContext) => {\n        const startTime = Date.now();\n        const { pageContext } = await this.setupPlanningContext(executorContext);\n        const planResult = await (0, import_core.plan)(param.userInstruction, {\n          context: pageContext,\n          log: param.log,\n          actionContext,\n          pageType: this.page.pageType\n        });\n        const {\n          actions,\n          log: log2,\n          more_actions_needed_by_instruction,\n          error,\n          usage,\n          rawResponse,\n          sleep: sleep3\n        } = planResult;\n        executorContext.task.log = {\n          ...executorContext.task.log || {},\n          rawResponse\n        };\n        executorContext.task.usage = usage;\n        let stopCollecting = false;\n        let bboxCollected = false;\n        let planParsingError = \"\";\n        const finalActions = (actions || []).reduce(\n          (acc, planningAction) => {\n            if (stopCollecting) {\n              return acc;\n            }\n            if (planningAction.locate) {\n              if (bboxCollected && planningAction.locate.bbox) {\n                delete planningAction.locate.bbox;\n              }\n              if (planningAction.locate.bbox) {\n                bboxCollected = true;\n              }\n              acc.push({\n                type: \"Locate\",\n                locate: planningAction.locate,\n                param: null,\n                thought: planningAction.locate.prompt\n              });\n            } else if ([\"Tap\", \"Hover\", \"Input\"].includes(planningAction.type)) {\n              planParsingError = `invalid planning response: ${JSON.stringify(planningAction)}`;\n              stopCollecting = true;\n              return acc;\n            }\n            acc.push(planningAction);\n            return acc;\n          },\n          []\n        );\n        if (sleep3) {\n          const timeNow = Date.now();\n          const timeRemaining = sleep3 - (timeNow - startTime);\n          if (timeRemaining > 0) {\n            finalActions.push({\n              type: \"Sleep\",\n              param: {\n                timeMs: timeRemaining\n              },\n              locate: null\n            });\n          }\n        }\n        if (finalActions.length === 0) {\n          (0, import_utils6.assert)(\n            !more_actions_needed_by_instruction || sleep3,\n            error ? `Failed to plan: ${error}` : planParsingError || \"No plan found\"\n          );\n        }\n        return {\n          output: {\n            actions: finalActions,\n            more_actions_needed_by_instruction,\n            log: log2,\n            yamlFlow: planResult.yamlFlow\n          },\n          cache: {\n            hit: false\n          },\n          pageContext\n        };\n      }\n    };\n    return task;\n  }\n  planningTaskToGoal(userInstruction) {\n    const task = {\n      type: \"Planning\",\n      subType: \"Plan\",\n      locate: null,\n      param: {\n        userInstruction\n      },\n      executor: async (param, executorContext) => {\n        const { pageContext } = await this.setupPlanningContext(executorContext);\n        const imagePayload = await (0, import_ai_model2.resizeImageForUiTars)(\n          pageContext.screenshotBase64,\n          pageContext.size\n        );\n        this.appendConversationHistory({\n          role: \"user\",\n          content: [\n            {\n              type: \"image_url\",\n              image_url: {\n                url: imagePayload\n              }\n            }\n          ]\n        });\n        const startTime = Date.now();\n        const planResult = await (0, import_ai_model2.vlmPlanning)({\n          userInstruction: param.userInstruction,\n          conversationHistory: this.conversationHistory,\n          size: pageContext.size\n        });\n        const aiCost = Date.now() - startTime;\n        const { actions, action_summary } = planResult;\n        this.appendConversationHistory({\n          role: \"assistant\",\n          content: action_summary\n        });\n        return {\n          output: {\n            actions,\n            thought: actions[0]?.thought,\n            actionType: actions[0].type,\n            more_actions_needed_by_instruction: true,\n            log: \"\",\n            yamlFlow: planResult.yamlFlow\n          },\n          cache: {\n            hit: false\n          },\n          aiCost\n        };\n      }\n    };\n    return task;\n  }\n  async runPlans(title, plans, opts) {\n    const taskExecutor = new import_core.Executor(title, {\n      onTaskStart: this.onTaskStartCallback\n    });\n    const { tasks } = await this.convertPlanToExecutable(plans, opts);\n    await taskExecutor.append(tasks);\n    const result = await taskExecutor.flush();\n    return {\n      output: result,\n      executor: taskExecutor\n    };\n  }\n  async action(userPrompt, actionContext, opts) {\n    const taskExecutor = new import_core.Executor(taskTitleStr(\"Action\", userPrompt), {\n      onTaskStart: this.onTaskStartCallback\n    });\n    let planningTask = this.planningTaskFromPrompt(userPrompt, void 0, actionContext);\n    let replanCount = 0;\n    const logList = [];\n    const yamlFlow = [];\n    while (planningTask) {\n      if (replanCount > replanningCountLimit) {\n        const errorMsg = \"Replanning too many times, please split the task into multiple steps\";\n        return this.appendErrorPlan(taskExecutor, errorMsg);\n      }\n      await taskExecutor.append(planningTask);\n      const planResult = await taskExecutor.flush();\n      if (taskExecutor.isInErrorState()) {\n        return {\n          output: planResult,\n          executor: taskExecutor\n        };\n      }\n      const plans = planResult.actions || [];\n      yamlFlow.push(...planResult.yamlFlow || []);\n      let executables;\n      try {\n        executables = await this.convertPlanToExecutable(plans, opts);\n        taskExecutor.append(executables.tasks);\n      } catch (error) {\n        return this.appendErrorPlan(\n          taskExecutor,\n          `Error converting plans to executable tasks: ${error}, plans: ${JSON.stringify(\n            plans\n          )}`\n        );\n      }\n      await taskExecutor.flush();\n      if (taskExecutor.isInErrorState()) {\n        return {\n          output: void 0,\n          executor: taskExecutor\n        };\n      }\n      if (planResult?.log) {\n        logList.push(planResult.log);\n      }\n      if (!planResult.more_actions_needed_by_instruction) {\n        planningTask = null;\n        break;\n      }\n      planningTask = this.planningTaskFromPrompt(\n        userPrompt,\n        logList.length > 0 ? `- ${logList.join(\"\\n- \")}` : void 0,\n        actionContext\n      );\n      replanCount++;\n    }\n    return {\n      output: {\n        yamlFlow\n      },\n      executor: taskExecutor\n    };\n  }\n  async actionToGoal(userPrompt, opts) {\n    const taskExecutor = new import_core.Executor(taskTitleStr(\"Action\", userPrompt), {\n      onTaskStart: this.onTaskStartCallback\n    });\n    this.conversationHistory = [];\n    const isCompleted = false;\n    let currentActionNumber = 0;\n    const maxActionNumber = 40;\n    const yamlFlow = [];\n    while (!isCompleted && currentActionNumber < maxActionNumber) {\n      currentActionNumber++;\n      const planningTask = this.planningTaskToGoal(userPrompt);\n      await taskExecutor.append(planningTask);\n      const output = await taskExecutor.flush();\n      if (taskExecutor.isInErrorState()) {\n        return {\n          output: void 0,\n          executor: taskExecutor\n        };\n      }\n      const plans = output.actions;\n      yamlFlow.push(...output.yamlFlow || []);\n      let executables;\n      try {\n        executables = await this.convertPlanToExecutable(plans, opts);\n        taskExecutor.append(executables.tasks);\n      } catch (error) {\n        return this.appendErrorPlan(\n          taskExecutor,\n          `Error converting plans to executable tasks: ${error}, plans: ${JSON.stringify(\n            plans\n          )}`\n        );\n      }\n      await taskExecutor.flush();\n      if (taskExecutor.isInErrorState()) {\n        return {\n          output: void 0,\n          executor: taskExecutor\n        };\n      }\n      if (plans[0].type === \"Finished\") {\n        break;\n      }\n    }\n    return {\n      output: {\n        yamlFlow\n      },\n      executor: taskExecutor\n    };\n  }\n  async createTypeQueryTask(type, demand, opt) {\n    const taskExecutor = new import_core.Executor(\n      taskTitleStr(\n        type,\n        typeof demand === \"string\" ? demand : JSON.stringify(demand)\n      ),\n      {\n        onTaskStart: this.onTaskStartCallback\n      }\n    );\n    const queryTask = {\n      type: \"Insight\",\n      subType: type,\n      locate: null,\n      param: {\n        dataDemand: demand\n        // for user param presentation in report right sidebar\n      },\n      executor: async (param) => {\n        let insightDump;\n        const dumpCollector = (dump) => {\n          insightDump = dump;\n        };\n        this.insight.onceDumpUpdatedFn = dumpCollector;\n        const ifTypeRestricted = type !== \"Query\";\n        let demandInput = demand;\n        if (ifTypeRestricted) {\n          demandInput = {\n            result: `${type}, ${demand}`\n          };\n        }\n        const { data, usage } = await this.insight.extract(\n          demandInput,\n          opt\n        );\n        let outputResult = data;\n        if (ifTypeRestricted) {\n          (0, import_utils6.assert)(data?.result !== void 0, \"No result in query data\");\n          outputResult = data.result;\n        }\n        return {\n          output: outputResult,\n          log: { dump: insightDump },\n          usage\n        };\n      }\n    };\n    await taskExecutor.append(this.prependExecutorWithScreenshot(queryTask));\n    const output = await taskExecutor.flush();\n    return {\n      output,\n      executor: taskExecutor\n    };\n  }\n  async query(demand, opt) {\n    return this.createTypeQueryTask(\"Query\", demand, opt);\n  }\n  async boolean(prompt, opt) {\n    return this.createTypeQueryTask(\"Boolean\", prompt, opt);\n  }\n  async number(prompt, opt) {\n    return this.createTypeQueryTask(\"Number\", prompt, opt);\n  }\n  async string(prompt, opt) {\n    return this.createTypeQueryTask(\"String\", prompt, opt);\n  }\n  async assert(assertion) {\n    const description = `assert: ${assertion}`;\n    const taskExecutor = new import_core.Executor(taskTitleStr(\"Assert\", description), {\n      onTaskStart: this.onTaskStartCallback\n    });\n    const assertionPlan = {\n      type: \"Assert\",\n      param: {\n        assertion\n      },\n      locate: null\n    };\n    const { tasks } = await this.convertPlanToExecutable([assertionPlan]);\n    await taskExecutor.append(this.prependExecutorWithScreenshot(tasks[0]));\n    const output = await taskExecutor.flush();\n    return {\n      output,\n      executor: taskExecutor\n    };\n  }\n  /**\n   * Append a message to the conversation history\n   * For user messages with images:\n   * - Keep max 4 user image messages in history\n   * - Remove oldest user image message when limit reached\n   * For assistant messages:\n   * - Simply append to history\n   * @param conversationHistory Message to append\n   */\n  appendConversationHistory(conversationHistory) {\n    if (conversationHistory.role === \"user\") {\n      const userImgItems = this.conversationHistory.filter(\n        (item) => item.role === \"user\"\n      );\n      if (userImgItems.length >= 4 && conversationHistory.role === \"user\") {\n        const firstUserImgIndex = this.conversationHistory.findIndex(\n          (item) => item.role === \"user\"\n        );\n        if (firstUserImgIndex >= 0) {\n          this.conversationHistory.splice(firstUserImgIndex, 1);\n        }\n      }\n    }\n    this.conversationHistory.push(conversationHistory);\n  }\n  async appendErrorPlan(taskExecutor, errorMsg) {\n    const errorPlan = {\n      type: \"Error\",\n      param: {\n        thought: errorMsg\n      },\n      locate: null\n    };\n    const { tasks } = await this.convertPlanToExecutable([errorPlan]);\n    await taskExecutor.append(this.prependExecutorWithScreenshot(tasks[0]));\n    await taskExecutor.flush();\n    return {\n      output: void 0,\n      executor: taskExecutor\n    };\n  }\n  async waitFor(assertion, opt) {\n    const description = `waitFor: ${assertion}`;\n    const taskExecutor = new import_core.Executor(taskTitleStr(\"WaitFor\", description), {\n      onTaskStart: this.onTaskStartCallback\n    });\n    const { timeoutMs, checkIntervalMs } = opt;\n    (0, import_utils6.assert)(assertion, \"No assertion for waitFor\");\n    (0, import_utils6.assert)(timeoutMs, \"No timeoutMs for waitFor\");\n    (0, import_utils6.assert)(checkIntervalMs, \"No checkIntervalMs for waitFor\");\n    const overallStartTime = Date.now();\n    let startTime = Date.now();\n    let errorThought = \"\";\n    while (Date.now() - overallStartTime < timeoutMs) {\n      startTime = Date.now();\n      const assertPlan = {\n        type: \"AssertWithoutThrow\",\n        param: {\n          assertion\n        },\n        locate: null\n      };\n      const { tasks: assertTasks } = await this.convertPlanToExecutable([\n        assertPlan\n      ]);\n      await taskExecutor.append(\n        this.prependExecutorWithScreenshot(assertTasks[0])\n      );\n      const output = await taskExecutor.flush();\n      if (output?.pass) {\n        return {\n          output: void 0,\n          executor: taskExecutor\n        };\n      }\n      errorThought = output?.thought || `unknown error when waiting for assertion: ${assertion}`;\n      const now = Date.now();\n      if (now - startTime < checkIntervalMs) {\n        const timeRemaining = checkIntervalMs - (now - startTime);\n        const sleepPlan = {\n          type: \"Sleep\",\n          param: {\n            timeMs: timeRemaining\n          },\n          locate: null\n        };\n        const { tasks: sleepTasks } = await this.convertPlanToExecutable([\n          sleepPlan\n        ]);\n        await taskExecutor.append(\n          this.prependExecutorWithScreenshot(sleepTasks[0])\n        );\n        await taskExecutor.flush();\n      }\n    }\n    return this.appendErrorPlan(\n      taskExecutor,\n      `waitFor timeout: ${errorThought}`\n    );\n  }\n};\n\n// src/common/plan-builder.ts\nvar import_logger2 = require(\"@midscene/shared/logger\");\nvar import_utils8 = require(\"@midscene/shared/utils\");\nvar debug2 = (0, import_logger2.getDebug)(\"plan-builder\");\nfunction buildPlans(type, locateParam, param) {\n  let returnPlans = [];\n  const locatePlan = locateParam ? {\n    type: \"Locate\",\n    locate: locateParam,\n    param: locateParam,\n    thought: \"\"\n  } : null;\n  if (type === \"Tap\" || type === \"Hover\" || type === \"RightClick\") {\n    (0, import_utils8.assert)(locateParam, `missing locate info for action \"${type}\"`);\n    (0, import_utils8.assert)(locatePlan, `missing locate info for action \"${type}\"`);\n    const tapPlan = {\n      type,\n      param: null,\n      thought: \"\",\n      locate: locateParam\n    };\n    returnPlans = [locatePlan, tapPlan];\n  }\n  if (type === \"Input\" || type === \"KeyboardPress\") {\n    if (type === \"Input\") {\n      (0, import_utils8.assert)(locateParam, `missing locate info for action \"${type}\"`);\n    }\n    (0, import_utils8.assert)(param, `missing param for action \"${type}\"`);\n    const inputPlan = {\n      type,\n      param,\n      thought: \"\",\n      locate: locateParam\n    };\n    if (locatePlan) {\n      returnPlans = [locatePlan, inputPlan];\n    } else {\n      returnPlans = [inputPlan];\n    }\n  }\n  if (type === \"Scroll\") {\n    (0, import_utils8.assert)(param, `missing param for action \"${type}\"`);\n    const scrollPlan = {\n      type,\n      param,\n      thought: \"\",\n      locate: locateParam\n    };\n    if (locatePlan) {\n      returnPlans = [locatePlan, scrollPlan];\n    } else {\n      returnPlans = [scrollPlan];\n    }\n  }\n  if (type === \"Sleep\") {\n    (0, import_utils8.assert)(param, `missing param for action \"${type}\"`);\n    const sleepPlan = {\n      type,\n      param,\n      thought: \"\",\n      locate: null\n    };\n    returnPlans = [sleepPlan];\n  }\n  if (type === \"Locate\") {\n    (0, import_utils8.assert)(locateParam, `missing locate info for action \"${type}\"`);\n    const locatePlan2 = {\n      type,\n      param: locateParam,\n      locate: locateParam,\n      thought: \"\"\n    };\n    returnPlans = [locatePlan2];\n  }\n  if (returnPlans) {\n    debug2(\"buildPlans\", returnPlans);\n    return returnPlans;\n  }\n  throw new Error(`Not supported type: ${type}`);\n}\n\n// src/common/task-cache.ts\nvar import_node_assert = __toESM(require(\"assert\"));\nvar import_node_fs2 = require(\"fs\");\nvar import_node_path2 = require(\"path\");\nvar import_common2 = require(\"@midscene/shared/common\");\nvar import_logger3 = require(\"@midscene/shared/logger\");\nvar import_utils9 = require(\"@midscene/shared/utils\");\nvar import_js_yaml3 = __toESM(require(\"js-yaml\"));\nvar import_semver = __toESM(require(\"semver\"));\n\n// package.json\nvar version = \"0.17.5\";\n\n// src/common/task-cache.ts\nvar debug3 = (0, import_logger3.getDebug)(\"cache\");\nvar lowestSupportedMidsceneVersion = \"0.16.10\";\nvar cacheFileExt = \".cache.yaml\";\nvar TaskCache = class {\n  // Track matched records\n  constructor(cacheId, isCacheResultUsed, cacheFilePath) {\n    this.matchedCacheIndices = /* @__PURE__ */ new Set();\n    (0, import_node_assert.default)(cacheId, \"cacheId is required\");\n    this.cacheId = replaceIllegalPathCharsAndSpace(cacheId);\n    this.cacheFilePath = import_utils9.ifInBrowser ? void 0 : cacheFilePath || (0, import_node_path2.join)((0, import_common2.getMidsceneRunSubDir)(\"cache\"), `${this.cacheId}${cacheFileExt}`);\n    this.isCacheResultUsed = isCacheResultUsed;\n    let cacheContent;\n    if (this.cacheFilePath) {\n      cacheContent = this.loadCacheFromFile();\n    }\n    if (!cacheContent) {\n      cacheContent = {\n        midsceneVersion: version,\n        cacheId: this.cacheId,\n        caches: []\n      };\n    }\n    this.cache = cacheContent;\n    this.cacheOriginalLength = this.cache.caches.length;\n  }\n  matchCache(prompt, type) {\n    for (let i = 0; i < this.cacheOriginalLength; i++) {\n      const item = this.cache.caches[i];\n      const key = `${type}:${prompt}:${i}`;\n      if (item.type === type && item.prompt === prompt && !this.matchedCacheIndices.has(key)) {\n        this.matchedCacheIndices.add(key);\n        debug3(\n          \"cache found and marked as used, type: %s, prompt: %s, index: %d\",\n          type,\n          prompt,\n          i\n        );\n        return {\n          cacheContent: item,\n          updateFn: (cb) => {\n            debug3(\n              \"will call updateFn to update cache, type: %s, prompt: %s, index: %d\",\n              type,\n              prompt,\n              i\n            );\n            cb(item);\n            debug3(\n              \"cache updated, will flush to file, type: %s, prompt: %s, index: %d\",\n              type,\n              prompt,\n              i\n            );\n            this.flushCacheToFile();\n          }\n        };\n      }\n    }\n    debug3(\"no unused cache found, type: %s, prompt: %s\", type, prompt);\n    return void 0;\n  }\n  matchPlanCache(prompt) {\n    return this.matchCache(prompt, \"plan\");\n  }\n  matchLocateCache(prompt) {\n    return this.matchCache(prompt, \"locate\");\n  }\n  appendCache(cache) {\n    debug3(\"will append cache\", cache);\n    this.cache.caches.push(cache);\n    this.flushCacheToFile();\n  }\n  loadCacheFromFile() {\n    const cacheFile = this.cacheFilePath;\n    (0, import_node_assert.default)(cacheFile, \"cache file path is required\");\n    if (!(0, import_node_fs2.existsSync)(cacheFile)) {\n      debug3(\"no cache file found, path: %s\", cacheFile);\n      return void 0;\n    }\n    const jsonTypeCacheFile = cacheFile.replace(cacheFileExt, \".json\");\n    if ((0, import_node_fs2.existsSync)(jsonTypeCacheFile) && this.isCacheResultUsed) {\n      console.warn(\n        `An outdated cache file from an earlier version of Midscene has been detected. Since version 0.17, we have implemented an improved caching strategy. Please delete the old file located at: ${jsonTypeCacheFile}.`\n      );\n      return void 0;\n    }\n    try {\n      const data = (0, import_node_fs2.readFileSync)(cacheFile, \"utf8\");\n      const jsonData = import_js_yaml3.default.load(data);\n      if (!version) {\n        debug3(\"no midscene version info, will not read cache from file\");\n        return void 0;\n      }\n      if (import_semver.default.lt(jsonData.midsceneVersion, lowestSupportedMidsceneVersion) && !jsonData.midsceneVersion.includes(\"beta\")) {\n        console.warn(\n          `You are using an old version of Midscene cache file, and we cannot match any info from it. Starting from Midscene v0.17, we changed our strategy to use xpath for cache info, providing better performance.\nPlease delete the existing cache and rebuild it. Sorry for the inconvenience.\ncache file: ${cacheFile}`\n        );\n        return void 0;\n      }\n      debug3(\n        \"cache loaded from file, path: %s, cache version: %s, record length: %s\",\n        cacheFile,\n        jsonData.midsceneVersion,\n        jsonData.caches.length\n      );\n      jsonData.midsceneVersion = version;\n      return jsonData;\n    } catch (err) {\n      debug3(\n        \"cache file exists but load failed, path: %s, error: %s\",\n        cacheFile,\n        err\n      );\n      return void 0;\n    }\n  }\n  flushCacheToFile() {\n    if (!version) {\n      debug3(\"no midscene version info, will not write cache to file\");\n      return;\n    }\n    if (!this.cacheFilePath) {\n      debug3(\"no cache file path, will not write cache to file\");\n      return;\n    }\n    try {\n      const dir = (0, import_node_path2.dirname)(this.cacheFilePath);\n      if (!(0, import_node_fs2.existsSync)(dir)) {\n        (0, import_node_fs2.mkdirSync)(dir, { recursive: true });\n        debug3(\"created cache directory: %s\", dir);\n      }\n      const yamlData = import_js_yaml3.default.dump(this.cache);\n      (0, import_node_fs2.writeFileSync)(this.cacheFilePath, yamlData);\n      debug3(\"cache flushed to file: %s\", this.cacheFilePath);\n    } catch (err) {\n      debug3(\n        \"write cache to file failed, path: %s, error: %s\",\n        this.cacheFilePath,\n        err\n      );\n    }\n  }\n  updateOrAppendCacheRecord(newRecord, cachedRecord) {\n    if (cachedRecord) {\n      if (newRecord.type === \"plan\") {\n        cachedRecord.updateFn((cache) => {\n          cache.yamlWorkflow = newRecord.yamlWorkflow;\n        });\n      } else {\n        cachedRecord.updateFn((cache) => {\n          cache.xpaths = newRecord.xpaths;\n        });\n      }\n    } else {\n      this.appendCache(newRecord);\n    }\n  }\n};\n\n// src/common/agent.ts\nvar debug4 = (0, import_logger4.getDebug)(\"web-integration\");\nvar distanceOfTwoPoints = (p1, p2) => {\n  const [x1, y1] = p1;\n  const [x2, y2] = p2;\n  return Math.round(Math.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2));\n};\nvar includedInRect = (point, rect) => {\n  const [x, y] = point;\n  const { left, top, width, height } = rect;\n  return x >= left && x <= left + width && y >= top && y <= top + height;\n};\nvar defaultInsightExtractOption = {\n  domIncluded: false,\n  screenshotIncluded: true\n};\nvar PageAgent = class {\n  constructor(page, opts) {\n    /**\n     * If true, the agent will not perform any actions\n     */\n    this.dryMode = false;\n    this.page = page;\n    this.opts = Object.assign(\n      {\n        generateReport: true,\n        autoPrintReportMsg: true,\n        groupName: \"Midscene Report\",\n        groupDescription: \"\"\n      },\n      opts || {}\n    );\n    if (this.page.pageType === \"puppeteer\" || this.page.pageType === \"playwright\") {\n      this.page.waitForNavigationTimeout = this.opts.waitForNavigationTimeout || import_constants2.DEFAULT_WAIT_FOR_NAVIGATION_TIMEOUT;\n      this.page.waitForNetworkIdleTimeout = this.opts.waitForNetworkIdleTimeout || import_constants2.DEFAULT_WAIT_FOR_NETWORK_IDLE_TIMEOUT;\n    }\n    this.onTaskStartTip = this.opts.onTaskStartTip;\n    this.insight = new import_core2.Insight(\n      async (action) => {\n        return this.getUIContext(action);\n      }\n    );\n    if (opts?.cacheId && this.page.pageType !== \"android\") {\n      this.taskCache = new TaskCache(\n        opts.cacheId,\n        (0, import_env2.getAIConfigInBoolean)(\"MIDSCENE_CACHE\")\n        // if we should use cache to match the element\n      );\n    }\n    this.taskExecutor = new PageTaskExecutor(this.page, this.insight, {\n      taskCache: this.taskCache,\n      onTaskStart: this.callbackOnTaskStartTip.bind(this)\n    });\n    this.dump = this.resetDump();\n    this.reportFileName = reportFileName(\n      opts?.testId || this.page.pageType || \"web\"\n    );\n  }\n  async getUIContext(action) {\n    if (action && (action === \"extract\" || action === \"assert\")) {\n      return await parseContextFromWebPage(this.page, {\n        ignoreMarker: true\n      });\n    }\n    return await parseContextFromWebPage(this.page, {\n      ignoreMarker: !!(0, import_env2.vlLocateMode)()\n    });\n  }\n  async setAIActionContext(prompt) {\n    this.opts.aiActionContext = prompt;\n  }\n  resetDump() {\n    this.dump = {\n      groupName: this.opts.groupName,\n      groupDescription: this.opts.groupDescription,\n      executions: []\n    };\n    return this.dump;\n  }\n  appendExecutionDump(execution) {\n    const currentDump = this.dump;\n    currentDump.executions.push(execution);\n  }\n  dumpDataString() {\n    this.dump.groupName = this.opts.groupName;\n    this.dump.groupDescription = this.opts.groupDescription;\n    return (0, import_utils11.stringifyDumpData)(this.dump);\n  }\n  reportHTMLString() {\n    return (0, import_utils11.reportHTMLContent)(this.dumpDataString());\n  }\n  writeOutActionDumps() {\n    const { generateReport, autoPrintReportMsg } = this.opts;\n    this.reportFile = (0, import_utils11.writeLogFile)({\n      fileName: this.reportFileName,\n      fileExt: import_utils11.groupedActionDumpFileExt,\n      fileContent: this.dumpDataString(),\n      type: \"dump\",\n      generateReport\n    });\n    debug4(\"writeOutActionDumps\", this.reportFile);\n    if (generateReport && autoPrintReportMsg && this.reportFile) {\n      printReportMsg(this.reportFile);\n    }\n  }\n  async callbackOnTaskStartTip(task) {\n    const param = paramStr(task);\n    const tip = param ? `${typeStr(task)} - ${param}` : typeStr(task);\n    if (this.onTaskStartTip) {\n      await this.onTaskStartTip(tip);\n    }\n  }\n  afterTaskRunning(executor, doNotThrowError = false) {\n    this.appendExecutionDump(executor.dump());\n    this.writeOutActionDumps();\n    if (executor.isInErrorState() && !doNotThrowError) {\n      const errorTask = executor.latestErrorTask();\n      throw new Error(`${errorTask?.error}\n${errorTask?.errorStack}`);\n    }\n  }\n  buildDetailedLocateParam(locatePrompt, opt) {\n    (0, import_utils12.assert)(locatePrompt, \"missing locate prompt\");\n    if (typeof opt === \"object\") {\n      const prompt = opt.prompt ?? locatePrompt;\n      const deepThink = opt.deepThink ?? false;\n      const cacheable = opt.cacheable ?? true;\n      return {\n        prompt,\n        deepThink,\n        cacheable\n      };\n    }\n    return {\n      prompt: locatePrompt\n    };\n  }\n  async aiTap(locatePrompt, opt) {\n    const detailedLocateParam = this.buildDetailedLocateParam(\n      locatePrompt,\n      opt\n    );\n    const plans = buildPlans(\"Tap\", detailedLocateParam);\n    const { executor, output } = await this.taskExecutor.runPlans(\n      taskTitleStr(\"Tap\", locateParamStr(detailedLocateParam)),\n      plans,\n      { cacheable: opt?.cacheable }\n    );\n    this.afterTaskRunning(executor);\n    return output;\n  }\n  async aiRightClick(locatePrompt, opt) {\n    const detailedLocateParam = this.buildDetailedLocateParam(\n      locatePrompt,\n      opt\n    );\n    const plans = buildPlans(\"RightClick\", detailedLocateParam);\n    const { executor, output } = await this.taskExecutor.runPlans(\n      taskTitleStr(\"RightClick\", locateParamStr(detailedLocateParam)),\n      plans,\n      { cacheable: opt?.cacheable }\n    );\n    this.afterTaskRunning(executor);\n    return output;\n  }\n  async aiHover(locatePrompt, opt) {\n    const detailedLocateParam = this.buildDetailedLocateParam(\n      locatePrompt,\n      opt\n    );\n    const plans = buildPlans(\"Hover\", detailedLocateParam);\n    const { executor, output } = await this.taskExecutor.runPlans(\n      taskTitleStr(\"Hover\", locateParamStr(detailedLocateParam)),\n      plans,\n      { cacheable: opt?.cacheable }\n    );\n    this.afterTaskRunning(executor);\n    return output;\n  }\n  async aiInput(value, locatePrompt, opt) {\n    (0, import_utils12.assert)(\n      typeof value === \"string\",\n      \"input value must be a string, use empty string if you want to clear the input\"\n    );\n    (0, import_utils12.assert)(locatePrompt, \"missing locate prompt for input\");\n    const detailedLocateParam = this.buildDetailedLocateParam(\n      locatePrompt,\n      opt\n    );\n    const plans = buildPlans(\"Input\", detailedLocateParam, {\n      value,\n      autoDismissKeyboard: opt?.autoDismissKeyboard\n    });\n    const { executor, output } = await this.taskExecutor.runPlans(\n      taskTitleStr(\"Input\", locateParamStr(detailedLocateParam)),\n      plans,\n      {\n        cacheable: opt?.cacheable\n      }\n    );\n    this.afterTaskRunning(executor);\n    return output;\n  }\n  async aiKeyboardPress(keyName, locatePrompt, opt) {\n    (0, import_utils12.assert)(keyName, \"missing keyName for keyboard press\");\n    const detailedLocateParam = locatePrompt ? this.buildDetailedLocateParam(locatePrompt, opt) : void 0;\n    const plans = buildPlans(\"KeyboardPress\", detailedLocateParam, {\n      value: keyName\n    });\n    const { executor, output } = await this.taskExecutor.runPlans(\n      taskTitleStr(\"KeyboardPress\", locateParamStr(detailedLocateParam)),\n      plans,\n      { cacheable: opt?.cacheable }\n    );\n    this.afterTaskRunning(executor);\n    return output;\n  }\n  async aiScroll(scrollParam, locatePrompt, opt) {\n    const detailedLocateParam = locatePrompt ? this.buildDetailedLocateParam(locatePrompt, opt) : void 0;\n    const plans = buildPlans(\"Scroll\", detailedLocateParam, scrollParam);\n    const paramInTitle = locatePrompt ? `${locateParamStr(detailedLocateParam)} - ${scrollParamStr(scrollParam)}` : scrollParamStr(scrollParam);\n    const { executor, output } = await this.taskExecutor.runPlans(\n      taskTitleStr(\"Scroll\", paramInTitle),\n      plans,\n      { cacheable: opt?.cacheable }\n    );\n    this.afterTaskRunning(executor);\n    return output;\n  }\n  async aiAction(taskPrompt, opt) {\n    const cacheable = opt?.cacheable;\n    const isVlmUiTars = (0, import_env2.vlLocateMode)() === \"vlm-ui-tars\";\n    const matchedCache = isVlmUiTars || cacheable === false ? void 0 : this.taskCache?.matchPlanCache(taskPrompt);\n    if (matchedCache && this.taskCache?.isCacheResultUsed) {\n      const { executor: executor2 } = await this.taskExecutor.loadYamlFlowAsPlanning(\n        taskPrompt,\n        matchedCache.cacheContent?.yamlWorkflow\n      );\n      await this.afterTaskRunning(executor2);\n      debug4(\"matched cache, will call .runYaml to run the action\");\n      const yaml5 = matchedCache.cacheContent?.yamlWorkflow;\n      return this.runYaml(yaml5);\n    }\n    const { output, executor } = await (isVlmUiTars ? this.taskExecutor.actionToGoal(taskPrompt, { cacheable }) : this.taskExecutor.action(taskPrompt, this.opts.aiActionContext, {\n      cacheable\n    }));\n    if (this.taskCache && output?.yamlFlow && cacheable !== false) {\n      const yamlContent = {\n        tasks: [\n          {\n            name: taskPrompt,\n            flow: output.yamlFlow\n          }\n        ]\n      };\n      const yamlFlowStr = import_js_yaml4.default.dump(yamlContent);\n      this.taskCache.updateOrAppendCacheRecord(\n        {\n          type: \"plan\",\n          prompt: taskPrompt,\n          yamlWorkflow: yamlFlowStr\n        },\n        matchedCache\n      );\n    }\n    this.afterTaskRunning(executor);\n    return output;\n  }\n  async aiQuery(demand, opt = defaultInsightExtractOption) {\n    const { output, executor } = await this.taskExecutor.query(demand, opt);\n    this.afterTaskRunning(executor);\n    return output;\n  }\n  async aiBoolean(prompt, opt = defaultInsightExtractOption) {\n    const { output, executor } = await this.taskExecutor.boolean(prompt, opt);\n    this.afterTaskRunning(executor);\n    return output;\n  }\n  async aiNumber(prompt, opt = defaultInsightExtractOption) {\n    const { output, executor } = await this.taskExecutor.number(prompt, opt);\n    this.afterTaskRunning(executor);\n    return output;\n  }\n  async aiString(prompt, opt = defaultInsightExtractOption) {\n    const { output, executor } = await this.taskExecutor.string(prompt, opt);\n    this.afterTaskRunning(executor);\n    return output;\n  }\n  async describeElementAtPoint(center, opt) {\n    const { verifyPrompt = true, retryLimit = 3 } = opt || {};\n    let success = false;\n    let retryCount = 0;\n    let resultPrompt = \"\";\n    let deepThink = opt?.deepThink || false;\n    let verifyResult;\n    while (!success && retryCount < retryLimit) {\n      if (retryCount >= 2) {\n        deepThink = true;\n      }\n      debug4(\n        \"aiDescribe\",\n        center,\n        \"verifyPrompt\",\n        verifyPrompt,\n        \"retryCount\",\n        retryCount,\n        \"deepThink\",\n        deepThink\n      );\n      const text = await this.insight.describe(center, { deepThink });\n      debug4(\"aiDescribe text\", text);\n      (0, import_utils12.assert)(text.description, `failed to describe element at [${center}]`);\n      resultPrompt = text.description;\n      verifyResult = await this.verifyLocator(\n        resultPrompt,\n        deepThink ? { deepThink: true } : void 0,\n        center,\n        opt\n      );\n      if (verifyResult.pass) {\n        success = true;\n      } else {\n        retryCount++;\n      }\n    }\n    return {\n      prompt: resultPrompt,\n      deepThink,\n      verifyResult\n    };\n  }\n  async verifyLocator(prompt, locateOpt, expectCenter, verifyLocateOption) {\n    debug4(\"verifyLocator\", prompt, locateOpt, expectCenter, verifyLocateOption);\n    const { center: verifyCenter, rect: verifyRect } = await this.aiLocate(\n      prompt,\n      locateOpt\n    );\n    const distance = distanceOfTwoPoints(expectCenter, verifyCenter);\n    const included = includedInRect(expectCenter, verifyRect);\n    const pass = distance <= (verifyLocateOption?.centerDistanceThreshold || 20) || included;\n    const verifyResult = {\n      pass,\n      rect: verifyRect,\n      center: verifyCenter,\n      centerDistance: distance\n    };\n    debug4(\"aiDescribe verifyResult\", verifyResult);\n    return verifyResult;\n  }\n  async aiLocate(prompt, opt) {\n    const detailedLocateParam = this.buildDetailedLocateParam(prompt, opt);\n    const plans = buildPlans(\"Locate\", detailedLocateParam);\n    const { executor, output } = await this.taskExecutor.runPlans(\n      taskTitleStr(\"Locate\", locateParamStr(detailedLocateParam)),\n      plans,\n      { cacheable: opt?.cacheable }\n    );\n    this.afterTaskRunning(executor);\n    const { element } = output;\n    return {\n      rect: element?.rect,\n      center: element?.center\n    };\n  }\n  async aiAssert(assertion, msg, opt) {\n    const { output, executor } = await this.taskExecutor.assert(assertion);\n    this.afterTaskRunning(executor, true);\n    if (output && opt?.keepRawResponse) {\n      return output;\n    }\n    if (!output?.pass) {\n      const errMsg = msg || `Assertion failed: ${assertion}`;\n      const reasonMsg = `Reason: ${output?.thought || executor.latestErrorTask()?.error || \"(no_reason)\"}`;\n      throw new Error(`${errMsg}\n${reasonMsg}`);\n    }\n  }\n  async aiWaitFor(assertion, opt) {\n    const { executor } = await this.taskExecutor.waitFor(assertion, {\n      timeoutMs: opt?.timeoutMs || 15 * 1e3,\n      checkIntervalMs: opt?.checkIntervalMs || 3 * 1e3,\n      assertion\n    });\n    this.appendExecutionDump(executor.dump());\n    this.writeOutActionDumps();\n    if (executor.isInErrorState()) {\n      const errorTask = executor.latestErrorTask();\n      throw new Error(`${errorTask?.error}\n${errorTask?.errorStack}`);\n    }\n  }\n  async ai(taskPrompt, type = \"action\") {\n    if (type === \"action\") {\n      return this.aiAction(taskPrompt);\n    }\n    if (type === \"query\") {\n      return this.aiQuery(taskPrompt);\n    }\n    if (type === \"assert\") {\n      return this.aiAssert(taskPrompt);\n    }\n    if (type === \"tap\") {\n      return this.aiTap(taskPrompt);\n    }\n    if (type === \"rightClick\") {\n      return this.aiRightClick(taskPrompt);\n    }\n    throw new Error(\n      `Unknown type: ${type}, only support 'action', 'query', 'assert', 'tap', 'rightClick'`\n    );\n  }\n  async runYaml(yamlScriptContent) {\n    const script = parseYamlScript(yamlScriptContent, \"yaml\", true);\n    const player = new ScriptPlayer(script, async (target) => {\n      return { agent: this, freeFn: [] };\n    });\n    await player.run();\n    if (player.status === \"error\") {\n      const errors = player.taskStatusList.filter((task) => task.status === \"error\").map((task) => {\n        return `task - ${task.name}: ${task.error?.message}`;\n      }).join(\"\\n\");\n      throw new Error(`Error(s) occurred in running yaml script:\n${errors}`);\n    }\n    return {\n      result: player.result\n    };\n  }\n  async evaluateJavaScript(script) {\n    (0, import_utils12.assert)(\n      this.page.evaluateJavaScript,\n      \"evaluateJavaScript is not supported in current agent\"\n    );\n    return this.page.evaluateJavaScript(script);\n  }\n  async destroy() {\n    await this.page.destroy();\n  }\n  async logScreenshot(title, opt) {\n    const base64 = await this.page.screenshotBase64();\n    const now = Date.now();\n    const recorder = [\n      {\n        type: \"screenshot\",\n        ts: now,\n        screenshot: base64\n      }\n    ];\n    const task = {\n      type: \"Log\",\n      subType: \"Screenshot\",\n      status: \"finished\",\n      recorder,\n      timing: {\n        start: now,\n        end: now,\n        cost: 0\n      },\n      param: {\n        content: opt?.content || \"\"\n      },\n      executor: async () => {\n      }\n    };\n    const executionDump = {\n      sdkVersion: \"\",\n      logTime: now,\n      model_name: \"\",\n      model_description: \"\",\n      name: `Log - ${title || \"untitled\"}`,\n      description: opt?.content || \"\",\n      tasks: [task]\n    };\n    this.appendExecutionDump(executionDump);\n    this.writeOutActionDumps();\n  }\n};\n\n// src/chrome-extension/agent.ts\nvar ChromeExtensionProxyPageAgent = class extends PageAgent {\n  // biome-ignore lint/complexity/noUselessConstructor: <explanation>\n  constructor(page, opts) {\n    super(page, opts);\n  }\n};\n\n// src/chrome-extension/page.ts\nvar import_extractor2 = require(\"@midscene/shared/extractor\");\nvar import_utils17 = require(\"@midscene/shared/utils\");\n\n// src/chrome-extension/cdpInput.ts\nvar import_keyboard_layout = require(\"@midscene/shared/keyboard-layout\");\nvar import_utils15 = require(\"@midscene/shared/utils\");\nvar _pressedKeys, _client, _modifierBit, modifierBit_fn, _keyDescriptionForString, keyDescriptionForString_fn;\nvar CdpKeyboard = class {\n  constructor(client) {\n    __privateAdd(this, _modifierBit);\n    __privateAdd(this, _keyDescriptionForString);\n    __privateAdd(this, _pressedKeys, /* @__PURE__ */ new Set());\n    __privateAdd(this, _client, void 0);\n    this._modifiers = 0;\n    __privateSet(this, _client, client);\n  }\n  updateClient(client) {\n    __privateSet(this, _client, client);\n  }\n  async down(key, options = {\n    text: void 0,\n    commands: []\n  }) {\n    const description = __privateMethod(this, _keyDescriptionForString, keyDescriptionForString_fn).call(this, key);\n    const autoRepeat = __privateGet(this, _pressedKeys).has(description.code);\n    __privateGet(this, _pressedKeys).add(description.code);\n    this._modifiers |= __privateMethod(this, _modifierBit, modifierBit_fn).call(this, description.key);\n    const text = options.text === void 0 ? description.text : options.text;\n    await __privateGet(this, _client).send(\"Input.dispatchKeyEvent\", {\n      type: text ? \"keyDown\" : \"rawKeyDown\",\n      modifiers: this._modifiers,\n      windowsVirtualKeyCode: description.keyCode,\n      code: description.code,\n      key: description.key,\n      text,\n      unmodifiedText: text,\n      autoRepeat,\n      location: description.location,\n      isKeypad: description.location === 3,\n      commands: options.commands\n    });\n  }\n  async up(key) {\n    const description = __privateMethod(this, _keyDescriptionForString, keyDescriptionForString_fn).call(this, key);\n    this._modifiers &= ~__privateMethod(this, _modifierBit, modifierBit_fn).call(this, description.key);\n    __privateGet(this, _pressedKeys).delete(description.code);\n    await __privateGet(this, _client).send(\"Input.dispatchKeyEvent\", {\n      type: \"keyUp\",\n      modifiers: this._modifiers,\n      key: description.key,\n      windowsVirtualKeyCode: description.keyCode,\n      code: description.code,\n      location: description.location\n    });\n  }\n  async sendCharacter(char) {\n    await __privateGet(this, _client).send(\"Input.insertText\", { text: char });\n  }\n  charIsKey(char) {\n    return !!import_keyboard_layout._keyDefinitions[char];\n  }\n  async type(text, options = {}) {\n    const delay = options.delay || void 0;\n    for (const char of text) {\n      if (this.charIsKey(char)) {\n        await this.press(char, { delay });\n      } else {\n        if (delay) {\n          await new Promise((f) => {\n            return setTimeout(f, delay);\n          });\n        }\n        await this.sendCharacter(char);\n      }\n    }\n  }\n  async press(key, options = {}) {\n    const { delay = null } = options;\n    const keys = Array.isArray(key) ? key : [key];\n    for (const k of keys) {\n      await this.down(k, options);\n    }\n    if (delay) {\n      await new Promise((f) => {\n        return setTimeout(f, options.delay);\n      });\n    }\n    for (const k of [...keys].reverse()) {\n      await this.up(k);\n    }\n  }\n};\n_pressedKeys = new WeakMap();\n_client = new WeakMap();\n_modifierBit = new WeakSet();\nmodifierBit_fn = function(key) {\n  if (key === \"Alt\") {\n    return 1;\n  }\n  if (key === \"Control\") {\n    return 2;\n  }\n  if (key === \"Meta\") {\n    return 4;\n  }\n  if (key === \"Shift\") {\n    return 8;\n  }\n  return 0;\n};\n_keyDescriptionForString = new WeakSet();\nkeyDescriptionForString_fn = function(keyString) {\n  const shift = this._modifiers & 8;\n  const description = {\n    key: \"\",\n    keyCode: 0,\n    code: \"\",\n    text: \"\",\n    location: 0\n  };\n  const definition = import_keyboard_layout._keyDefinitions[keyString];\n  (0, import_utils15.assert)(definition, `Unknown key: \"${keyString}\"`);\n  if (definition.key) {\n    description.key = definition.key;\n  }\n  if (shift && definition.shiftKey) {\n    description.key = definition.shiftKey;\n  }\n  if (definition.keyCode) {\n    description.keyCode = definition.keyCode;\n  }\n  if (shift && definition.shiftKeyCode) {\n    description.keyCode = definition.shiftKeyCode;\n  }\n  if (definition.code) {\n    description.code = definition.code;\n  }\n  if (definition.location) {\n    description.location = definition.location;\n  }\n  if (description.key.length === 1) {\n    description.text = description.key;\n  }\n  if (definition.text) {\n    description.text = definition.text;\n  }\n  if (shift && definition.shiftText) {\n    description.text = definition.shiftText;\n  }\n  if (this._modifiers & ~8) {\n    description.text = \"\";\n  }\n  return description;\n};\n\n// src/chrome-extension/dynamic-scripts.ts\nvar import_node_fs3 = __toESM(require(\"fs\"));\nvar import_utils16 = require(\"@midscene/shared/utils\");\nvar scriptFileContentCache = null;\nvar getHtmlElementScript = async () => {\n  const scriptFileToRetrieve = chrome.runtime.getURL(\"scripts/htmlElement.js\");\n  if (scriptFileContentCache)\n    return scriptFileContentCache;\n  if (import_utils16.ifInBrowser) {\n    const script = await fetch(scriptFileToRetrieve);\n    scriptFileContentCache = await script.text();\n    return scriptFileContentCache;\n  }\n  return import_node_fs3.default.readFileSync(scriptFileToRetrieve, \"utf8\");\n};\nvar waterFlowScriptFileContentCache = null;\nvar injectWaterFlowAnimation = async () => {\n  const waterFlowScriptFileToRetrieve = chrome.runtime.getURL(\n    \"scripts/water-flow.js\"\n  );\n  if (waterFlowScriptFileContentCache)\n    return waterFlowScriptFileContentCache;\n  if (import_utils16.ifInBrowser) {\n    const script = await fetch(waterFlowScriptFileToRetrieve);\n    waterFlowScriptFileContentCache = await script.text();\n    return waterFlowScriptFileContentCache;\n  }\n  return import_node_fs3.default.readFileSync(waterFlowScriptFileToRetrieve, \"utf8\");\n};\nvar stopWaterFlowScriptFileContentCache = null;\nvar injectStopWaterFlowAnimation = async () => {\n  const stopWaterFlowScriptFileToRetrieve = chrome.runtime.getURL(\n    \"scripts/stop-water-flow.js\"\n  );\n  if (stopWaterFlowScriptFileContentCache)\n    return stopWaterFlowScriptFileContentCache;\n  if (import_utils16.ifInBrowser) {\n    const script = await fetch(stopWaterFlowScriptFileToRetrieve);\n    stopWaterFlowScriptFileContentCache = await script.text();\n    return stopWaterFlowScriptFileContentCache;\n  }\n  return import_node_fs3.default.readFileSync(stopWaterFlowScriptFileToRetrieve, \"utf8\");\n};\n\n// src/chrome-extension/page.ts\nfunction sleep2(ms) {\n  return new Promise((resolve2) => setTimeout(resolve2, ms));\n}\nvar ChromeExtensionProxyPage = class {\n  constructor(forceSameTabNavigation) {\n    this.pageType = \"chrome-extension-proxy\";\n    this.version = \"0.17.5\";\n    this.activeTabId = null;\n    this.tabIdOfDebuggerAttached = null;\n    this.attachingDebugger = null;\n    this.destroyed = false;\n    this.isMobileEmulation = null;\n    this.latestMouseX = 100;\n    this.latestMouseY = 100;\n    this.mouse = {\n      click: async (x, y, options) => {\n        const { button = \"left\", count = 1 } = options || {};\n        await this.mouse.move(x, y);\n        if (this.isMobileEmulation === null) {\n          const result = await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n            expression: `(() => {\n            return /Android|iPhone|iPad|iPod|Mobile/i.test(navigator.userAgent);\n          })()`,\n            returnByValue: true\n          });\n          this.isMobileEmulation = result?.result?.value;\n        }\n        if (this.isMobileEmulation && button === \"left\") {\n          const touchPoints = [{ x: Math.round(x), y: Math.round(y) }];\n          await this.sendCommandToDebugger(\"Input.dispatchTouchEvent\", {\n            type: \"touchStart\",\n            touchPoints,\n            modifiers: 0\n          });\n          await this.sendCommandToDebugger(\"Input.dispatchTouchEvent\", {\n            type: \"touchEnd\",\n            touchPoints: [],\n            modifiers: 0\n          });\n        } else {\n          await this.sendCommandToDebugger(\"Input.dispatchMouseEvent\", {\n            type: \"mousePressed\",\n            x,\n            y,\n            button,\n            clickCount: count\n          });\n          await this.sendCommandToDebugger(\"Input.dispatchMouseEvent\", {\n            type: \"mouseReleased\",\n            x,\n            y,\n            button,\n            clickCount: count\n          });\n        }\n      },\n      wheel: async (deltaX, deltaY, startX, startY) => {\n        const finalX = startX || this.latestMouseX;\n        const finalY = startY || this.latestMouseY;\n        await this.showMousePointer(finalX, finalY);\n        await this.sendCommandToDebugger(\"Input.dispatchMouseEvent\", {\n          type: \"mouseWheel\",\n          x: finalX,\n          y: finalY,\n          deltaX,\n          deltaY\n        });\n        this.latestMouseX = finalX;\n        this.latestMouseY = finalY;\n      },\n      move: async (x, y) => {\n        await this.showMousePointer(x, y);\n        await this.sendCommandToDebugger(\"Input.dispatchMouseEvent\", {\n          type: \"mouseMoved\",\n          x,\n          y\n        });\n        this.latestMouseX = x;\n        this.latestMouseY = y;\n      },\n      drag: async (from, to) => {\n        await this.mouse.move(from.x, from.y);\n        await this.sendCommandToDebugger(\"Input.dispatchMouseEvent\", {\n          type: \"mousePressed\",\n          x: from.x,\n          y: from.y,\n          button: \"left\",\n          clickCount: 1\n        });\n        await this.mouse.move(to.x, to.y);\n        await this.sendCommandToDebugger(\"Input.dispatchMouseEvent\", {\n          type: \"mouseReleased\",\n          x: to.x,\n          y: to.y,\n          button: \"left\",\n          clickCount: 1\n        });\n      }\n    };\n    this.keyboard = {\n      type: async (text) => {\n        const cdpKeyboard = new CdpKeyboard({\n          send: this.sendCommandToDebugger.bind(this)\n        });\n        await cdpKeyboard.type(text, { delay: 0 });\n      },\n      press: async (action) => {\n        const cdpKeyboard = new CdpKeyboard({\n          send: this.sendCommandToDebugger.bind(this)\n        });\n        const keys = Array.isArray(action) ? action : [action];\n        for (const k of keys) {\n          const commands = k.command ? [k.command] : [];\n          await cdpKeyboard.down(k.key, { commands });\n        }\n        for (const k of [...keys].reverse()) {\n          await cdpKeyboard.up(k.key);\n        }\n      }\n    };\n    this.forceSameTabNavigation = forceSameTabNavigation;\n  }\n  async setActiveTabId(tabId) {\n    if (this.activeTabId) {\n      throw new Error(\n        `Active tab id is already set, which is ${this.activeTabId}, cannot set it to ${tabId}`\n      );\n    }\n    await chrome.tabs.update(tabId, { active: true });\n    this.activeTabId = tabId;\n  }\n  async getActiveTabId() {\n    return this.activeTabId;\n  }\n  /**\n   * Get a list of current tabs\n   * @returns {Promise<Array<{id: number, title: string, url: string}>>}\n   */\n  async getBrowserTabList() {\n    const tabs = await chrome.tabs.query({ currentWindow: true });\n    return tabs.map((tab) => ({\n      id: `${tab.id}`,\n      title: tab.title,\n      url: tab.url,\n      currentActiveTab: tab.active\n    })).filter((tab) => tab.id && tab.title && tab.url);\n  }\n  async getTabIdOrConnectToCurrentTab() {\n    if (this.activeTabId) {\n      return this.activeTabId;\n    }\n    const tabId = await chrome.tabs.query({ active: true, currentWindow: true }).then((tabs) => tabs[0]?.id);\n    this.activeTabId = tabId || 0;\n    return this.activeTabId;\n  }\n  async attachDebugger() {\n    (0, import_utils17.assert)(!this.destroyed, \"Page is destroyed\");\n    if (this.attachingDebugger) {\n      await this.attachingDebugger;\n      return;\n    }\n    this.attachingDebugger = (async () => {\n      const url = await this.url();\n      let error = null;\n      if (url.startsWith(\"chrome://\")) {\n        throw new Error(\n          \"Cannot attach debugger to chrome:// pages, please use Midscene in a normal page with http://, https:// or file://\"\n        );\n      }\n      try {\n        const currentTabId = await this.getTabIdOrConnectToCurrentTab();\n        if (this.tabIdOfDebuggerAttached === currentTabId) {\n          return;\n        }\n        if (this.tabIdOfDebuggerAttached && this.tabIdOfDebuggerAttached !== currentTabId) {\n          console.log(\n            \"detach the previous tab\",\n            this.tabIdOfDebuggerAttached,\n            \"->\",\n            currentTabId\n          );\n          try {\n            await this.detachDebugger(this.tabIdOfDebuggerAttached);\n          } catch (error2) {\n            console.error(\"Failed to detach debugger\", error2);\n          }\n        }\n        console.log(\"attaching debugger\", currentTabId);\n        await chrome.debugger.attach({ tabId: currentTabId }, \"1.3\");\n        await sleep2(500);\n        this.tabIdOfDebuggerAttached = currentTabId;\n        await this.enableWaterFlowAnimation();\n      } catch (e) {\n        console.error(\"Failed to attach debugger\", e);\n        error = e;\n      } finally {\n        this.attachingDebugger = null;\n      }\n      if (error) {\n        throw error;\n      }\n    })();\n    await this.attachingDebugger;\n  }\n  async showMousePointer(x, y) {\n    const pointerScript = `(() => {\n      if(typeof window.midsceneWaterFlowAnimation !== 'undefined') {\n        window.midsceneWaterFlowAnimation.enable();\n        window.midsceneWaterFlowAnimation.showMousePointer(${x}, ${y});\n      } else {\n        console.log('midsceneWaterFlowAnimation is not defined');\n      }\n    })()`;\n    await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: `${pointerScript}`\n    });\n  }\n  async hideMousePointer() {\n    await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: `(() => {\n        if(typeof window.midsceneWaterFlowAnimation !== 'undefined') {\n          window.midsceneWaterFlowAnimation.hideMousePointer();\n        }\n      })()`\n    });\n  }\n  async detachDebugger(tabId) {\n    const tabIdToDetach = tabId || this.tabIdOfDebuggerAttached;\n    console.log(\"detaching debugger\", tabIdToDetach);\n    if (!tabIdToDetach) {\n      console.warn(\"No tab id to detach\");\n      return;\n    }\n    try {\n      await this.disableWaterFlowAnimation(tabIdToDetach);\n      await sleep2(200);\n    } catch (error) {\n      console.warn(\"Failed to disable water flow animation\", error);\n    }\n    try {\n      await chrome.debugger.detach({ tabId: tabIdToDetach });\n    } catch (error) {\n      console.warn(\"Failed to detach debugger\", error);\n    }\n    this.tabIdOfDebuggerAttached = null;\n  }\n  async enableWaterFlowAnimation() {\n    if (this.forceSameTabNavigation) {\n      await chrome.debugger.sendCommand(\n        { tabId: this.tabIdOfDebuggerAttached },\n        \"Runtime.evaluate\",\n        {\n          expression: limitOpenNewTabScript\n        }\n      );\n    }\n    const script = await injectWaterFlowAnimation();\n    await chrome.debugger.sendCommand(\n      { tabId: this.tabIdOfDebuggerAttached },\n      \"Runtime.evaluate\",\n      {\n        expression: script\n      }\n    );\n  }\n  async disableWaterFlowAnimation(tabId) {\n    const script = await injectStopWaterFlowAnimation();\n    await chrome.debugger.sendCommand({ tabId }, \"Runtime.evaluate\", {\n      expression: script\n    });\n  }\n  async sendCommandToDebugger(command, params) {\n    await this.attachDebugger();\n    (0, import_utils17.assert)(this.tabIdOfDebuggerAttached, \"Debugger is not attached\");\n    this.enableWaterFlowAnimation();\n    return await chrome.debugger.sendCommand(\n      { tabId: this.tabIdOfDebuggerAttached },\n      command,\n      params\n    );\n  }\n  async getPageContentByCDP() {\n    const script = await getHtmlElementScript();\n    await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: script\n    });\n    const expression = () => {\n      window.midscene_element_inspector.setNodeHashCacheListOnWindow();\n      return {\n        tree: window.midscene_element_inspector.webExtractNodeTree(),\n        size: {\n          width: document.documentElement.clientWidth,\n          height: document.documentElement.clientHeight,\n          dpr: window.devicePixelRatio\n        }\n      };\n    };\n    const returnValue = await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: `(${expression.toString()})()`,\n      returnByValue: true\n    });\n    if (!returnValue.result.value) {\n      const errorDescription = returnValue.exceptionDetails?.exception?.description || \"\";\n      if (!errorDescription) {\n        console.error(\"returnValue from cdp\", returnValue);\n      }\n      throw new Error(\n        `Failed to get page content from page, error: ${errorDescription}`\n      );\n    }\n    return returnValue.result.value;\n  }\n  async evaluateJavaScript(script) {\n    return this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: script\n    });\n  }\n  // current implementation is wait until domReadyState is complete\n  async waitUntilNetworkIdle() {\n    const timeout = 1e4;\n    const startTime = Date.now();\n    let lastReadyState = \"\";\n    while (Date.now() - startTime < timeout) {\n      const result = await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n        expression: \"document.readyState\"\n      });\n      lastReadyState = result.result.value;\n      if (lastReadyState === \"complete\") {\n        await new Promise((resolve2) => setTimeout(resolve2, 300));\n        return;\n      }\n      await new Promise((resolve2) => setTimeout(resolve2, 300));\n    }\n    throw new Error(\n      `Failed to wait until network idle, last readyState: ${lastReadyState}`\n    );\n  }\n  async getElementsInfo() {\n    const tree = await this.getElementsNodeTree();\n    return (0, import_extractor2.treeToList)(tree);\n  }\n  async getXpathsById(id) {\n    const script = await getHtmlElementScript();\n    await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: script\n    });\n    const result = await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: `window.midscene_element_inspector.getXpathsById('${id}')`,\n      returnByValue: true\n    });\n    return result.result.value;\n  }\n  async getElementInfoByXpath(xpath) {\n    const script = await getHtmlElementScript();\n    await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: script\n    });\n    const result = await this.sendCommandToDebugger(\"Runtime.evaluate\", {\n      expression: `window.midscene_element_inspector.getElementInfoByXpath('${xpath}')`,\n      returnByValue: true\n    });\n    return result.result.value;\n  }\n  async getElementsNodeTree() {\n    await this.hideMousePointer();\n    const content = await this.getPageContentByCDP();\n    if (content?.size) {\n      this.viewportSize = content.size;\n    }\n    return content?.tree || { node: null, children: [] };\n  }\n  async size() {\n    if (this.viewportSize)\n      return this.viewportSize;\n    const content = await this.getPageContentByCDP();\n    return content.size;\n  }\n  async screenshotBase64() {\n    await this.hideMousePointer();\n    const base64 = await this.sendCommandToDebugger(\"Page.captureScreenshot\", {\n      format: \"jpeg\",\n      quality: 90\n    });\n    return `data:image/jpeg;base64,${base64.data}`;\n  }\n  async url() {\n    const tabId = await this.getTabIdOrConnectToCurrentTab();\n    const url = await chrome.tabs.get(tabId).then((tab) => tab.url);\n    return url || \"\";\n  }\n  async scrollUntilTop(startingPoint) {\n    if (startingPoint) {\n      await this.mouse.move(startingPoint.left, startingPoint.top);\n    }\n    return this.mouse.wheel(0, -9999999);\n  }\n  async scrollUntilBottom(startingPoint) {\n    if (startingPoint) {\n      await this.mouse.move(startingPoint.left, startingPoint.top);\n    }\n    return this.mouse.wheel(0, 9999999);\n  }\n  async scrollUntilLeft(startingPoint) {\n    if (startingPoint) {\n      await this.mouse.move(startingPoint.left, startingPoint.top);\n    }\n    return this.mouse.wheel(-9999999, 0);\n  }\n  async scrollUntilRight(startingPoint) {\n    if (startingPoint) {\n      await this.mouse.move(startingPoint.left, startingPoint.top);\n    }\n    return this.mouse.wheel(9999999, 0);\n  }\n  async scrollUp(distance, startingPoint) {\n    const { height } = await this.size();\n    const scrollDistance = distance || height * 0.7;\n    return this.mouse.wheel(\n      0,\n      -scrollDistance,\n      startingPoint?.left,\n      startingPoint?.top\n    );\n  }\n  async scrollDown(distance, startingPoint) {\n    const { height } = await this.size();\n    const scrollDistance = distance || height * 0.7;\n    return this.mouse.wheel(\n      0,\n      scrollDistance,\n      startingPoint?.left,\n      startingPoint?.top\n    );\n  }\n  async scrollLeft(distance, startingPoint) {\n    const { width } = await this.size();\n    const scrollDistance = distance || width * 0.7;\n    return this.mouse.wheel(\n      -scrollDistance,\n      0,\n      startingPoint?.left,\n      startingPoint?.top\n    );\n  }\n  async scrollRight(distance, startingPoint) {\n    const { width } = await this.size();\n    const scrollDistance = distance || width * 0.7;\n    return this.mouse.wheel(\n      scrollDistance,\n      0,\n      startingPoint?.left,\n      startingPoint?.top\n    );\n  }\n  async clearInput(element) {\n    if (!element) {\n      console.warn(\"No element to clear input\");\n      return;\n    }\n    await this.mouse.click(element.center[0], element.center[1]);\n    await this.sendCommandToDebugger(\"Input.dispatchKeyEvent\", {\n      type: \"keyDown\",\n      commands: [\"selectAll\"]\n    });\n    await this.sendCommandToDebugger(\"Input.dispatchKeyEvent\", {\n      type: \"keyUp\",\n      commands: [\"selectAll\"]\n    });\n    await sleep2(100);\n    await this.keyboard.press({\n      key: \"Backspace\"\n    });\n  }\n  async destroy() {\n    this.activeTabId = null;\n    await this.detachDebugger();\n    this.destroyed = true;\n  }\n};\n\n// src/chrome-extension/index.ts\nvar import_env3 = require(\"@midscene/shared/env\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  ChromeExtensionProxyPage,\n  ChromeExtensionProxyPageAgent,\n  ERROR_CODE_NOT_IMPLEMENTED_AS_DESIGNED,\n  overrideAIConfig\n});\n/**\n * @license\n * Copyright 2017 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\n\n//# sourceMappingURL=chrome-extension.js.map", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\nimport { state } from \"./state.js\";\n/**\n * @internal\n * Retrieves the value to use for a given operation argument\n * @param operationArguments - The arguments passed from the generated client\n * @param parameter - The parameter description\n * @param fallbackObject - If something isn't found in the arguments bag, look here.\n *  Generally used to look at the service client properties.\n */\nexport function getOperationArgumentValueFromParameter(operationArguments, parameter, fallbackObject) {\n    let parameterPath = parameter.parameterPath;\n    const parameterMapper = parameter.mapper;\n    let value;\n    if (typeof parameterPath === \"string\") {\n        parameterPath = [parameterPath];\n    }\n    if (Array.isArray(parameterPath)) {\n        if (parameterPath.length > 0) {\n            if (parameterMapper.isConstant) {\n                value = parameterMapper.defaultValue;\n            }\n            else {\n                let propertySearchResult = getPropertyFromParameterPath(operationArguments, parameterPath);\n                if (!propertySearchResult.propertyFound && fallbackObject) {\n                    propertySearchResult = getPropertyFromParameterPath(fallbackObject, parameterPath);\n                }\n                let useDefaultValue = false;\n                if (!propertySearchResult.propertyFound) {\n                    useDefaultValue =\n                        parameterMapper.required ||\n                            (parameterPath[0] === \"options\" && parameterPath.length === 2);\n                }\n                value = useDefaultValue ? parameterMapper.defaultValue : propertySearchResult.propertyValue;\n            }\n        }\n    }\n    else {\n        if (parameterMapper.required) {\n            value = {};\n        }\n        for (const propertyName in parameterPath) {\n            const propertyMapper = parameterMapper.type.modelProperties[propertyName];\n            const propertyPath = parameterPath[propertyName];\n            const propertyValue = getOperationArgumentValueFromParameter(operationArguments, {\n                parameterPath: propertyPath,\n                mapper: propertyMapper,\n            }, fallbackObject);\n            if (propertyValue !== undefined) {\n                if (!value) {\n                    value = {};\n                }\n                value[propertyName] = propertyValue;\n            }\n        }\n    }\n    return value;\n}\nfunction getPropertyFromParameterPath(parent, parameterPath) {\n    const result = { propertyFound: false };\n    let i = 0;\n    for (; i < parameterPath.length; ++i) {\n        const parameterPathPart = parameterPath[i];\n        // Make sure to check inherited properties too, so don't use hasOwnProperty().\n        if (parent && parameterPathPart in parent) {\n            parent = parent[parameterPathPart];\n        }\n        else {\n            break;\n        }\n    }\n    if (i === parameterPath.length) {\n        result.propertyValue = parent;\n        result.propertyFound = true;\n    }\n    return result;\n}\nconst originalRequestSymbol = Symbol.for(\"@azure/core-client original request\");\nfunction hasOriginalRequest(request) {\n    return originalRequestSymbol in request;\n}\nexport function getOperationRequestInfo(request) {\n    if (hasOriginalRequest(request)) {\n        return getOperationRequestInfo(request[originalRequestSymbol]);\n    }\n    let info = state.operationRequestMap.get(request);\n    if (!info) {\n        info = {};\n        state.operationRequestMap.set(request, info);\n    }\n    return info;\n}\n//# sourceMappingURL=operationHelpers.js.map", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n/** @internal */\nexport const knownContextKeys = {\n    span: Symbol.for(\"@azure/core-tracing span\"),\n    namespace: Symbol.for(\"@azure/core-tracing namespace\"),\n};\n/**\n * Creates a new {@link TracingContext} with the given options.\n * @param options - A set of known keys that may be set on the context.\n * @returns A new {@link TracingContext} with the given options.\n *\n * @internal\n */\nexport function createTracingContext(options = {}) {\n    let context = new TracingContextImpl(options.parentContext);\n    if (options.span) {\n        context = context.setValue(knownContextKeys.span, options.span);\n    }\n    if (options.namespace) {\n        context = context.setValue(knownContextKeys.namespace, options.namespace);\n    }\n    return context;\n}\n/** @internal */\nexport class TracingContextImpl {\n    constructor(initialContext) {\n        this._contextMap =\n            initialContext instanceof TracingContextImpl\n                ? new Map(initialContext._contextMap)\n                : new Map();\n    }\n    setValue(key, value) {\n        const newContext = new TracingContextImpl(this);\n        newContext._contextMap.set(key, value);\n        return newContext;\n    }\n    getValue(key) {\n        return this._contextMap.get(key);\n    }\n    deleteValue(key) {\n        const newContext = new TracingContextImpl(this);\n        newContext._contextMap.delete(key);\n        return newContext;\n    }\n}\n//# sourceMappingURL=tracingContext.js.map"], "names": ["module", "e", "r", "i", "s", "u", "o", "c", "f", "h", "d", "l", "$", "y", "m", "t", "n", "String", "Array", "g", "D", "p", "S", "_", "w", "a", "O", "arguments", "b", "Math", "M", "Date", "NaN", "v", "Number", "k", "mod", "isNodeMode", "target", "_pressed<PERSON><PERSON>s", "_client", "_modifierBit", "modifierBit_fn", "_keyDescriptionForString", "keyDescriptionForString_fn", "__create", "Object", "__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__getProtoOf", "__hasOwnProp", "__copyProps", "to", "from", "except", "desc", "key", "__access<PERSON>heck", "obj", "member", "msg", "TypeError", "__privateGet", "getter", "__privateAdd", "value", "WeakSet", "__privateSet", "setter", "__privateMethod", "method", "browser_exports", "all", "ExtensionBridgePageBrowserSide", "name", "import_utils5", "limitOpenNewTabScript", "import_extractor", "import_utils3", "import_keyboard_layout", "import_utils", "CdpKeyboard", "client", "Set", "options", "description", "autoRepeat", "text", "char", "delay", "Promise", "setTimeout", "keys", "WeakMap", "keyString", "shift", "definition", "import_node_fs", "import_utils2", "scriptFileContentCache", "getHtmlElementScript", "scriptFileToRetrieve", "chrome", "script", "fetch", "waterFlowScriptFileContentCache", "injectWaterFlowAnimation", "waterFlowScriptFileToRetrieve", "stopWaterFlowScriptFileContentCache", "injectStopWaterFlowAnimation", "stopWaterFlowScriptFileToRetrieve", "sleep", "ms", "resolve", "ChromeExtensionProxyPage", "forceSameTabNavigation", "x", "button", "count", "result", "touchPoints", "deltaX", "deltaY", "startX", "startY", "finalX", "finalY", "cdpKeyboard", "action", "commands", "tabId", "Error", "tabs", "tab", "url", "error", "currentTabId", "console", "error2", "pointerScript", "tabIdToDetach", "command", "params", "returnValue", "expression", "window", "document", "errorDescription", "startTime", "lastReadyState", "tree", "id", "xpath", "content", "base64", "startingPoint", "distance", "height", "width", "element", "import_utils4", "import_socket", "BridgeClient", "endpoint", "onBridgeCall", "onDisconnect", "reject", "timeout", "reason", "payload", "clearTimeout", "e2", "call", "response", "errorContent", "onLogMessage", "args", "actionName", "errorMessage", "__toESM", "chrome_extension_exports", "ChromeExtensionProxyPageAgent", "ERROR_CODE_NOT_IMPLEMENTED_AS_DESIGNED", "import_env3", "import_ai_model", "import_env", "import_img", "import_dayjs", "WebElementInfo", "rect", "locator", "attributes", "indexId", "xpaths", "isVisible", "parseContextFromWebPage", "page", "_opt", "screenshotBase64", "treeRoot", "webTree", "elementInfo", "elementsInfo", "size", "import_core2", "import_js_yaml4", "import_node_path", "import_common", "ScriptPlayer", "setupAgent", "onTaskStatusChange", "process", "task", "taskIndex", "keyToUse", "status", "taskIndexToNotify", "taskStatus", "index", "statusValue", "output", "outputDir", "JSON", "agent", "flow", "flowItemIndex", "flowItem", "prompt", "actionTask", "assertTask", "queryTask", "query<PERSON><PERSON>ult", "numberTask", "numberResult", "stringTask", "stringResult", "booleanTask", "booleanResult", "locateTask", "locateResult", "waitForTask", "sleepTask", "msNumber", "resolve2", "tapTask", "rightClickTask", "hoverTask", "inputTask", "keyboardPressTask", "scrollTask", "evaluateJavaScriptTask", "logScreenshotTask", "web", "android", "tasks", "freeFn", "newAgent", "newFreeFn", "webEnv", "originalOnTaskStartTip", "tip", "errorFlag", "fn", "import_js_yaml2", "import_utils11", "import_constants2", "import_env2", "import_logger4", "import_utils12", "import_core", "import_ai_model2", "import_constants", "import_logger", "import_utils6", "typeStr", "locateParamStr", "locate", "scrollParamStr", "scrollParam", "taskTitleStr", "type", "debug", "isAndroidPage", "PageTaskExecutor", "insight", "opts", "timing", "pageContext", "elementId", "info", "taskApply", "appendAfterExecution", "param", "context", "recorder", "shot", "shot2", "plans", "plan2", "taskFind", "taskContext", "insightDump", "usage", "currentXpaths", "dump", "shotTime", "cacheHitFlag", "cachePrompt", "locateCacheRecord", "elementFromCache", "element2", "matchElementFromPlan", "planLocateParam", "centerPosition", "aiCost", "elementXpaths", "taskAssert", "assertPlan", "assertion", "taskActionInput", "taskParam", "taskActionKeyboardPress", "getKeyCommands", "acc", "includeMeta", "taskActionTap", "taskActionRightClick", "taskActionDrag", "taskActionHover", "taskActionScroll", "scrollToEventName", "taskActionSleep", "taskActionError", "taskActionFalsyConditionStatement", "taskActionFinished", "taskActionAndroidHomeButton", "taskActionAndroidBackButton", "taskActionAndroidRecentAppsButton", "executorContext", "recordItem", "userInstruction", "yamlString", "taskExecutor", "log", "actionContext", "planResult", "actions", "log2", "more_actions_needed_by_instruction", "rawResponse", "sleep3", "stopCollecting", "bboxCollected", "planParsingError", "finalActions", "planningAction", "timeRemaining", "timeNow", "imagePayload", "action_summary", "title", "userPrompt", "planningTask", "replanCount", "logList", "yamlFlow", "executables", "currentActionNumber", "demand", "opt", "ifTypeRestricted", "demandInput", "data", "outputResult", "conversationHistory", "userImgItems", "item", "firstUserImgIndex", "errorMsg", "timeoutMs", "checkIntervalMs", "overallStartTime", "errorThought", "assertTasks", "now", "sleepPlan", "sleepTasks", "import_logger2", "import_utils8", "debug2", "buildPlans", "locateParam", "returnPlans", "locatePlan", "inputPlan", "scrollPlan", "import_node_assert", "import_node_fs2", "import_node_path2", "import_common2", "import_logger3", "import_utils9", "import_js_yaml3", "import_semver", "version", "debug3", "cacheFileExt", "TaskCache", "cacheId", "isCacheResultUsed", "cacheFilePath", "cacheContent", "str", "cb", "cache", "cacheFile", "jsonTypeCacheFile", "jsonData", "err", "dir", "yamlData", "newRecord", "cachedRecord", "debug4", "distanceOfTwoPoints", "p1", "p2", "x1", "y1", "x2", "y2", "includedInRect", "point", "left", "top", "defaultInsightExtractOption", "PageAgent", "reportFileName", "tag", "reportTagName", "dateTimeInFileName", "uniqueId", "execution", "currentDump", "generateReport", "autoPrintReportMsg", "printReportMsg", "filepath", "paramStr", "locateStr", "executor", "doNotThrowError", "errorTask", "locatePrompt", "deepThink", "cacheable", "detailedLocateParam", "keyName", "paramInTitle", "taskPrompt", "isVlmUiTars", "matchedCache", "executor2", "yaml5", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yamlFlowStr", "center", "verifyResult", "verifyPrompt", "retryLimit", "success", "retryCount", "resultPrompt", "locateOpt", "expectCenter", "verifyLocateOption", "verifyCenter", "verifyRect", "included", "pass", "errMsg", "reasonMsg", "yamlScriptContent", "player", "parseYamlScript", "filePath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "processedContent", "matchedDeviceId", "match", "deviceId", "interpolated<PERSON><PERSON><PERSON>", "envVar", "pathTip", "webConfig", "errors", "executionDump", "import_extractor2", "import_utils17", "import_utils15", "import_node_fs3", "import_utils16", "sleep2", "getOperationRequestInfo", "request", "originalRequestSymbol", "getOperationArgumentValueFromParameter", "operationArguments", "parameter", "fallbackObject", "parameterPath", "parameterMapper", "propertySearchResult", "getPropertyFromParameterPath", "useDefaultValue", "propertyName", "propertyMapper", "propertyValue", "undefined", "parent", "parameterPathPart", "Symbol", "knownContextKeys", "createTracingContext", "TracingContextImpl", "initialContext", "Map", "newContext"], "mappings": ";8BAAoEA,EAAO,OAAO,CAACC,AAA2H,WAAW,aAAa,IAAuBC,EAAE,cAAcC,EAAE,SAASC,EAAE,SAASC,EAAE,OAAeC,EAAE,OAAOC,EAAE,QAAQC,EAAE,UAAUC,EAAE,OAAOC,EAAE,OAAOC,EAAE,eAAeC,EAAE,6FAA6FC,EAAE,sFAAiYC,EAAE,SAASC,CAAC,CAACd,CAAC,CAACe,CAAC,EAAE,IAAId,EAAEe,OAAOF,GAAG,MAAM,CAACb,GAAGA,EAAE,MAAM,EAAED,EAAEc,EAAE,GAAGG,MAAMjB,EAAE,EAAEC,EAAE,MAAM,EAAE,IAAI,CAACc,GAAGD,CAAC,EAAshBI,EAAE,KAAKC,EAAE,CAAC,CAAEA,CAAAA,CAAC,CAACD,EAAE,CAAz6B,CAAC,KAAK,KAAK,SAAS,2DAA2D,KAAK,CAAC,KAAK,OAAO,wFAAwF,KAAK,CAAC,KAAK,QAAQ,SAASJ,CAAC,EAAE,IAAId,EAAE,CAAC,KAAK,KAAK,KAAK,KAAK,CAACe,EAAED,EAAE,IAAI,MAAM,IAAIA,EAAGd,CAAAA,CAAC,CAAC,AAACe,CAAAA,EAAE,EAAC,EAAG,GAAG,EAAEf,CAAC,CAACe,EAAE,EAAEf,CAAC,CAAC,EAAE,AAAD,EAAG,GAAG,CAAC,EAAqoB,IAAIoB,EAAE,iBAAiBC,EAAE,SAASP,CAAC,EAAE,OAAOA,aAAaQ,GAAG,CAAE,EAACR,GAAG,CAACA,CAAC,CAACM,EAAE,AAAD,CAAE,EAAEG,EAAE,SAAST,EAAEd,CAAC,CAACe,CAAC,CAACd,CAAC,EAAE,IAAIC,EAAE,GAAG,CAACF,EAAE,OAAOkB,EAAE,GAAG,UAAU,OAAOlB,EAAE,CAAC,IAAIG,EAAEH,EAAE,WAAW,EAAGmB,CAAAA,CAAC,CAAChB,EAAE,EAAGD,CAAAA,EAAEC,CAAAA,EAAGY,GAAII,CAAAA,CAAC,CAAChB,EAAE,CAACY,EAAEb,EAAEC,CAAAA,EAAG,IAAIC,EAAEJ,EAAE,KAAK,CAAC,KAAK,GAAG,CAACE,GAAGE,EAAE,MAAM,CAAC,EAAE,OAAOU,EAAEV,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAIoB,EAAExB,EAAE,IAAI,AAACmB,CAAAA,CAAC,CAACK,EAAE,CAACxB,EAAEE,EAAEsB,CAAC,CAAC,MAAM,CAACvB,GAAGC,GAAIgB,CAAAA,EAAEhB,CAAAA,EAAGA,GAAG,CAACD,GAAGiB,CAAC,EAAEO,EAAE,SAASX,CAAC,CAACd,CAAC,EAAE,GAAGqB,EAAEP,GAAG,OAAOA,EAAE,KAAK,GAAG,IAAIC,EAAE,UAAU,OAAOf,EAAEA,EAAE,CAAC,EAAE,OAAOe,EAAE,IAAI,CAACD,EAAEC,EAAE,IAAI,CAACW,UAAU,IAAIJ,EAAEP,EAAE,EAAEY,EAAj8B,CAAC,EAAEd,EAAE,EAAE,SAASC,CAAC,EAAE,IAAId,EAAE,CAACc,EAAE,SAAS,GAAGC,EAAEa,KAAK,GAAG,CAAC5B,GAA6B,MAAM,AAACA,CAAAA,GAAG,EAAE,IAAI,GAAE,EAAGa,EAA7Ce,KAAK,KAAK,CAACb,EAAE,IAAoC,EAAE,KAAK,IAAIF,EAAzCE,EAAE,GAA2C,EAAE,IAAI,EAAE,EAAE,SAASD,EAAEd,CAAC,CAACe,CAAC,EAAE,GAAGf,EAAE,IAAI,GAAGe,EAAE,IAAI,GAAG,MAAM,CAACD,EAAEC,EAAEf,GAAG,IAAIC,EAAE,GAAIc,CAAAA,EAAE,IAAI,GAAGf,EAAE,IAAI,EAAC,EAAIe,CAAAA,EAAE,KAAK,GAAGf,EAAE,KAAK,EAAC,EAAGE,EAAEF,EAAE,KAAK,GAAG,GAAG,CAACC,EAAEK,GAAGH,EAAEY,EAAEb,EAAE,EAAEE,EAAEJ,EAAE,KAAK,GAAG,GAAG,CAACC,EAAGE,CAAAA,EAAE,GAAG,GAAGG,GAAG,MAAM,CAAE,EAAEL,CAAAA,EAAE,AAACc,CAAAA,EAAEb,CAAAA,EAAIC,CAAAA,EAAED,EAAEE,EAAEA,EAAEF,CAAAA,CAAC,GAAI,EAAE,EAAE,EAAE,SAASY,CAAC,EAAE,OAAOA,EAAE,EAAEc,KAAK,IAAI,CAACd,IAAI,EAAEc,KAAK,KAAK,CAACd,EAAE,EAAE,EAAE,SAASA,CAAC,EAAE,MAAM,EAAC,EAAER,EAAE,EAAEE,EAAE,EAAEH,EAAE,EAAxiC,MAA4iC,EAAEI,EAAE,EAAEL,EAAE,EAAED,EAAE,EAAED,EAAE,GAAGD,EAAE,EAAEM,CAAC,EAAC,CAACO,EAAE,EAAEE,OAAOF,GAAG,IAAI,WAAW,GAAG,OAAO,CAAC,KAAK,GAAG,EAAE,EAAE,SAASA,CAAC,EAAE,OAAO,KAAK,IAAIA,CAAC,CAAC,CAAqba,CAAAA,EAAE,CAAC,CAACJ,EAAEI,EAAE,CAAC,CAACN,EAAEM,EAAE,CAAC,CAAC,SAASb,CAAC,CAACd,CAAC,EAAE,OAAOyB,EAAEX,EAAE,CAAC,OAAOd,EAAE,EAAE,CAAC,IAAIA,EAAE,EAAE,CAAC,EAAEA,EAAE,EAAE,CAAC,QAAQA,EAAE,OAAO,EAAE,EAAE,IAAIsB,EAAE,WAAW,SAASO,EAAEf,CAAC,EAAE,IAAI,CAAC,EAAE,CAACS,EAAET,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAACA,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAEA,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAACM,EAAE,CAAC,CAAC,CAAC,CAAC,IAAIP,EAAEgB,EAAE,SAAS,CAAC,OAAOhB,EAAE,KAAK,CAAC,SAASC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,SAASA,CAAC,EAAE,IAAId,EAAEc,EAAE,IAAI,CAACC,EAAED,EAAE,GAAG,CAAC,GAAG,OAAOd,EAAE,OAAO,IAAI8B,KAAKC,KAAK,GAAGJ,EAAE,CAAC,CAAC3B,GAAG,OAAO,IAAI8B,KAAK,GAAG9B,aAAa8B,KAAK,OAAO,IAAIA,KAAK9B,GAAG,GAAG,UAAU,OAAOA,GAAG,CAAC,MAAM,IAAI,CAACA,GAAG,CAAC,IAAIC,EAAED,EAAE,KAAK,CAACW,GAAG,GAAGV,EAAE,CAAC,IAAIC,EAAED,CAAC,CAAC,EAAE,CAAC,GAAG,EAAEE,EAAE,AAACF,CAAAA,CAAC,CAAC,EAAE,EAAE,GAAE,EAAG,SAAS,CAAC,EAAE,GAAG,OAAOc,EAAE,IAAIe,KAAKA,KAAK,GAAG,CAAC7B,CAAC,CAAC,EAAE,CAACC,EAAED,CAAC,CAAC,EAAE,EAAE,EAAEA,CAAC,CAAC,EAAE,EAAE,EAAEA,CAAC,CAAC,EAAE,EAAE,EAAEA,CAAC,CAAC,EAAE,EAAE,EAAEE,IAAI,IAAI2B,KAAK7B,CAAC,CAAC,EAAE,CAACC,EAAED,CAAC,CAAC,EAAE,EAAE,EAAEA,CAAC,CAAC,EAAE,EAAE,EAAEA,CAAC,CAAC,EAAE,EAAE,EAAEA,CAAC,CAAC,EAAE,EAAE,EAAEE,EAAE,CAAC,CAAC,OAAO,IAAI2B,KAAK9B,EAAE,EAAEc,GAAG,IAAI,CAAC,IAAI,EAAE,EAAED,EAAE,IAAI,CAAC,WAAW,IAAIC,EAAE,IAAI,CAAC,EAAE,AAAC,KAAI,CAAC,EAAE,CAACA,EAAE,WAAW,GAAG,IAAI,CAAC,EAAE,CAACA,EAAE,QAAQ,GAAG,IAAI,CAAC,EAAE,CAACA,EAAE,OAAO,GAAG,IAAI,CAAC,EAAE,CAACA,EAAE,MAAM,GAAG,IAAI,CAAC,EAAE,CAACA,EAAE,QAAQ,GAAG,IAAI,CAAC,EAAE,CAACA,EAAE,UAAU,GAAG,IAAI,CAAC,EAAE,CAACA,EAAE,UAAU,GAAG,IAAI,CAAC,GAAG,CAACA,EAAE,eAAe,EAAE,EAAED,EAAE,MAAM,CAAC,WAAW,OAAOc,CAAC,EAAEd,EAAE,OAAO,CAAC,WAAW,OAAQ,IAAI,CAAC,EAAE,CAAC,QAAQ,KAAKH,CAAE,EAAEG,EAAE,MAAM,CAAC,SAASC,CAAC,CAACd,CAAC,EAAE,IAAIe,EAAEU,EAAEX,GAAG,OAAO,IAAI,CAAC,OAAO,CAACd,IAAIe,GAAGA,GAAG,IAAI,CAAC,KAAK,CAACf,EAAE,EAAEa,EAAE,OAAO,CAAC,SAASC,CAAC,CAACd,CAAC,EAAE,OAAOyB,EAAEX,GAAG,IAAI,CAAC,OAAO,CAACd,EAAE,EAAEa,EAAE,QAAQ,CAAC,SAASC,CAAC,CAACd,CAAC,EAAE,OAAO,IAAI,CAAC,KAAK,CAACA,GAAGyB,EAAEX,EAAE,EAAED,EAAE,EAAE,CAAC,SAASC,CAAC,CAACd,CAAC,CAACe,CAAC,EAAE,OAAOY,EAAE,CAAC,CAACb,GAAG,IAAI,CAACd,EAAE,CAAC,IAAI,CAAC,GAAG,CAACe,EAAED,EAAE,EAAED,EAAE,IAAI,CAAC,WAAW,OAAOe,KAAK,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,EAAEf,EAAE,OAAO,CAAC,WAAW,OAAO,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,EAAEA,EAAE,OAAO,CAAC,SAASC,CAAC,CAACd,CAAC,EAAE,IAAIe,EAAE,IAAI,CAACd,EAAE,CAAC,CAAC0B,EAAE,CAAC,CAAC3B,IAAIA,EAAEO,EAAEoB,EAAE,CAAC,CAACb,GAAGJ,EAAE,SAASI,CAAC,CAACd,CAAC,EAAE,IAAIE,EAAEyB,EAAE,CAAC,CAACZ,EAAE,EAAE,CAACe,KAAK,GAAG,CAACf,EAAE,EAAE,CAACf,EAAEc,GAAG,IAAIgB,KAAKf,EAAE,EAAE,CAACf,EAAEc,GAAGC,GAAG,OAAOd,EAAEC,EAAEA,EAAE,KAAK,CAA9+F,MAAi/F,EAAES,EAAE,SAASG,CAAC,CAACd,CAAC,EAAE,OAAO2B,EAAE,CAAC,CAACZ,EAAE,MAAM,EAAE,CAACD,EAAE,CAAC,KAAK,CAACC,EAAE,MAAM,CAAC,KAAK,AAACd,CAAAA,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,AAAD,EAAG,KAAK,CAACD,IAAIe,EAAE,EAAEH,EAAE,IAAI,CAAC,EAAE,CAACiB,EAAE,IAAI,CAAC,EAAE,CAAChB,EAAE,IAAI,CAAC,EAAE,CAACmB,EAAE,MAAO,KAAI,CAAC,EAAE,CAAC,MAAM,EAAC,EAAG,OAAOzB,GAAG,KAAKC,EAAE,OAAOP,EAAES,EAAE,EAAE,GAAGA,EAAE,GAAG,GAAI,MAAKJ,EAAE,OAAOL,EAAES,EAAE,EAAEmB,GAAGnB,EAAE,EAAEmB,EAAE,EAAG,MAAKxB,EAAE,IAAIa,EAAE,IAAI,CAAC,OAAO,GAAG,SAAS,EAAE,EAAEC,EAAE,AAACP,CAAAA,EAAEM,EAAEN,EAAE,EAAEA,CAAAA,EAAGM,EAAE,OAAOR,EAAET,EAAEY,EAAEM,EAAEN,EAAG,GAAEM,CAAAA,EAAGU,EAAG,KAAjzG,MAAwzG,KAAKpB,EAAE,OAAOE,EAAEqB,EAAE,QAAQ,EAAG,MAAK5B,EAAE,OAAOO,EAAEqB,EAAE,UAAU,EAAG,MAAK7B,EAAE,OAAOQ,EAAEqB,EAAE,UAAU,EAAG,MAAK9B,EAAE,OAAOS,EAAEqB,EAAE,eAAe,EAAG,SAAQ,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,EAAEnB,EAAE,KAAK,CAAC,SAASC,CAAC,EAAE,OAAO,IAAI,CAAC,OAAO,CAACA,EAAE,CAAC,EAAE,EAAED,EAAE,IAAI,CAAC,SAASC,CAAC,CAACd,CAAC,EAAE,IAAIe,EAAEV,EAAEsB,EAAE,CAAC,CAACb,GAAGP,EAAE,MAAO,KAAI,CAAC,EAAE,CAAC,MAAM,EAAC,EAAGG,EAAE,AAACK,CAAAA,AAAKA,CAALA,EAAE,CAAC,GAAzkH,GAA+kH,CAACR,EAAE,OAAOQ,CAAC,CAACN,EAAE,CAACF,EAAE,OAAOQ,CAAC,CAACT,EAAE,CAACC,EAAE,QAAQQ,CAAC,CAACP,EAAE,CAACD,EAAE,WAAWQ,CAAC,CAACX,EAAE,CAACG,EAAE,QAAQQ,CAAC,CAACZ,EAAE,CAACI,EAAE,UAAUQ,CAAC,CAACb,EAAE,CAACK,EAAE,UAAUQ,CAAC,CAACd,EAAE,CAACM,EAAE,eAAeQ,CAAAA,CAAE,CAACV,EAAE,CAACM,EAAEN,AAAvtH,QAAutHA,EAAM,IAAI,CAAC,EAAE,CAAEL,CAAAA,EAAE,IAAI,CAAC,EAAE,AAAD,EAAGA,EAAE,GAAGK,IAAIC,GAAGD,IAAIG,EAAE,CAAC,IAAII,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,CAACH,EAAE,EAAGG,CAAAA,EAAE,EAAE,CAACF,EAAE,CAACC,GAAGC,EAAE,IAAI,GAAG,IAAI,CAAC,EAAE,CAACA,EAAE,GAAG,CAACH,EAAEmB,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,CAAChB,EAAE,WAAW,KAAK,EAAE,MAAMF,GAAG,IAAI,CAAC,EAAE,CAACA,EAAE,CAACC,GAAG,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,EAAEE,EAAE,GAAG,CAAC,SAASC,CAAC,CAACd,CAAC,EAAE,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAACc,EAAEd,EAAE,EAAEa,EAAE,GAAG,CAAC,SAASC,CAAC,EAAE,OAAO,IAAI,CAACa,EAAE,CAAC,CAACb,GAAG,EAAE,EAAED,EAAE,GAAG,CAAC,SAASZ,CAAC,CAACM,CAAC,EAAE,IAAIE,EAAEC,EAAE,IAAI,CAACT,EAAEgC,OAAOhC,GAAG,IAAIU,EAAEgB,EAAE,CAAC,CAACpB,GAAGK,EAAE,SAASE,CAAC,EAAE,IAAId,EAAEyB,EAAEf,GAAG,OAAOiB,EAAE,CAAC,CAAC3B,EAAE,IAAI,CAACA,EAAE,IAAI,GAAG4B,KAAK,KAAK,CAACd,EAAEb,IAAIS,EAAE,EAAE,GAAGC,IAAIL,EAAE,OAAO,IAAI,CAAC,GAAG,CAACA,EAAE,IAAI,CAAC,EAAE,CAACL,GAAG,GAAGU,IAAIH,EAAE,OAAO,IAAI,CAAC,GAAG,CAACA,EAAE,IAAI,CAAC,EAAE,CAACP,GAAG,GAAGU,AAAvsI,QAAusIA,EAAM,OAAOC,EAAE,GAAG,GAAGD,IAAIN,EAAE,OAAOO,EAAE,GAAG,IAAIiB,EAAE,AAACpB,CAAAA,AAAKA,CAALA,EAAE,CAAC,EAAG,CAACN,EAAE,CAA1zI,IAA6zIM,CAAC,CAACL,EAAE,CAA3zI,KAA8zIK,CAAC,CAACP,EAAE,CAA90I,IAAi1IO,CAAAA,CAAE,CAACE,EAAE,EAAE,EAAEE,EAAE,IAAI,CAAC,EAAE,CAAC,OAAO,GAAGZ,EAAE4B,EAAE,OAAOF,EAAE,CAAC,CAACd,EAAE,IAAI,CAAC,EAAEA,EAAE,QAAQ,CAAC,SAASC,CAAC,CAACd,CAAC,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAGc,EAAEd,EAAE,EAAEa,EAAE,MAAM,CAAC,SAASC,CAAC,EAAE,IAAId,EAAE,IAAI,CAACe,EAAE,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,GAAG,OAAOA,EAAE,WAAW,EAAEL,EAAE,IAAIT,EAAEa,GAAG,uBAAuBZ,EAAEyB,EAAE,CAAC,CAAC,IAAI,EAAExB,EAAE,IAAI,CAAC,EAAE,CAACC,EAAE,IAAI,CAAC,EAAE,CAACoB,EAAE,IAAI,CAAC,EAAE,CAACnB,EAAEU,EAAE,QAAQ,CAACT,EAAES,EAAE,MAAM,CAACR,EAAEQ,EAAE,QAAQ,CAACP,EAAE,SAASM,CAAC,CAACC,CAAC,CAACb,CAAC,CAACC,CAAC,EAAE,OAAOW,GAAIA,CAAAA,CAAC,CAACC,EAAE,EAAED,EAAEd,EAAEC,EAAC,GAAIC,CAAC,CAACa,EAAE,CAAC,KAAK,CAAC,EAAEZ,EAAE,EAAEM,EAAE,SAASK,CAAC,EAAE,OAAOa,EAAE,CAAC,CAACxB,EAAE,IAAI,GAAGW,EAAE,IAAI,EAAEH,EAAEJ,GAAG,SAASO,CAAC,CAACd,CAAC,CAACe,CAAC,EAAE,IAAId,EAAEa,EAAE,GAAG,KAAK,KAAK,OAAOC,EAAEd,EAAE,WAAW,GAAGA,CAAC,EAAE,OAAOA,EAAE,OAAO,CAACW,EAAG,SAASE,CAAC,CAACb,CAAC,EAAE,OAAOA,GAAG,SAASa,CAAC,EAAE,OAAOA,GAAG,IAAI,KAAK,OAAOE,OAAOhB,EAAE,EAAE,EAAE,KAAK,CAAC,GAAI,KAAI,OAAO,OAAO2B,EAAE,CAAC,CAAC3B,EAAE,EAAE,CAAC,EAAE,IAAK,KAAI,IAAI,OAAOwB,EAAE,CAAE,KAAI,KAAK,OAAOG,EAAE,CAAC,CAACH,EAAE,EAAE,EAAE,IAAK,KAAI,MAAM,OAAOhB,EAAEO,EAAE,WAAW,CAACS,EAAElB,EAAE,EAAG,KAAI,OAAO,OAAOE,EAAEF,EAAEkB,EAAG,KAAI,IAAI,OAAOxB,EAAE,EAAE,AAAC,KAAI,KAAK,OAAO2B,EAAE,CAAC,CAAC3B,EAAE,EAAE,CAAC,EAAE,IAAK,KAAI,IAAI,OAAOgB,OAAOhB,EAAE,EAAE,CAAE,KAAI,KAAK,OAAOQ,EAAEO,EAAE,WAAW,CAACf,EAAE,EAAE,CAACK,EAAE,EAAG,KAAI,MAAM,OAAOG,EAAEO,EAAE,aAAa,CAACf,EAAE,EAAE,CAACK,EAAE,EAAG,KAAI,OAAO,OAAOA,CAAC,CAACL,EAAE,EAAE,CAAC,AAAC,KAAI,IAAI,OAAOgB,OAAOb,EAAG,KAAI,KAAK,OAAOwB,EAAE,CAAC,CAACxB,EAAE,EAAE,IAAK,KAAI,IAAI,OAAOM,EAAE,EAAG,KAAI,KAAK,OAAOA,EAAE,EAAG,KAAI,IAAI,OAAOE,EAAER,EAAEC,EAAE,CAAC,EAAG,KAAI,IAAI,OAAOO,EAAER,EAAEC,EAAE,CAAC,EAAG,KAAI,IAAI,OAAOY,OAAOZ,EAAG,KAAI,KAAK,OAAOuB,EAAE,CAAC,CAACvB,EAAE,EAAE,IAAK,KAAI,IAAI,OAAOY,OAAOhB,EAAE,EAAE,CAAE,KAAI,KAAK,OAAO2B,EAAE,CAAC,CAAC3B,EAAE,EAAE,CAAC,EAAE,IAAK,KAAI,MAAM,OAAO2B,EAAE,CAAC,CAAC3B,EAAE,GAAG,CAAC,EAAE,IAAK,KAAI,IAAI,OAAOE,CAAC,CAAC,OAAO,IAAI,EAAEY,IAAIZ,EAAE,OAAO,CAAC,IAAI,GAAG,EAAG,EAAEW,EAAE,SAAS,CAAC,WAAW,MAAO,KAAIe,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,iBAAiB,GAAG,GAAE,CAAC,EAAEf,EAAE,IAAI,CAAC,SAASZ,CAAC,CAACQ,CAAC,CAACC,CAAC,EAAE,IAAIC,EAAEC,EAAE,IAAI,CAACiB,EAAEF,EAAE,CAAC,CAAClB,GAAGI,EAAEY,EAAExB,GAAG+B,EAAE,AAACnB,CAAAA,EAAE,SAAS,GAAG,IAAI,CAAC,SAAS,EAAC,EAA1uL,IAA+uLK,EAAE,IAAI,CAACL,EAAEM,EAAE,WAAW,OAAOQ,EAAE,CAAC,CAACf,EAAEC,EAAE,EAAE,OAAOgB,GAAG,KAAKrB,EAAEG,EAAEQ,IAAI,GAAG,KAAM,MAAKb,EAAEK,EAAEQ,IAAI,KAAM,MAAKZ,EAAEI,EAAEQ,IAAI,EAAE,KAAM,MAAKd,EAAEM,EAAE,AAACO,CAAAA,EAAEc,CAAAA,EAAG,OAAO,KAAM,KAA9zL,MAAq0LrB,EAAE,AAACO,CAAAA,EAAEc,CAAAA,EAAG,MAAM,KAAM,MAAK5B,EAAEO,EAAEO,EAAx5L,KAA45L,KAAM,MAAKf,EAAEQ,EAAEO,EAAj7L,IAAq7L,KAAM,MAAKhB,EAAES,EAAEO,EAA18L,IAA88L,KAAM,SAAQP,EAAEO,CAAC,CAAC,OAAOR,EAAEC,EAAEgB,EAAE,CAAC,CAAChB,EAAE,EAAEE,EAAE,WAAW,CAAC,WAAW,OAAO,IAAI,CAAC,KAAK,CAACP,GAAG,EAAE,EAAEO,EAAE,OAAO,CAAC,WAAW,OAAOM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAEN,EAAE,MAAM,CAAC,SAASC,CAAC,CAACd,CAAC,EAAE,GAAG,CAACc,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,IAAIC,EAAE,IAAI,CAAC,KAAK,GAAGd,EAAEsB,EAAET,EAAEd,EAAE,CAAC,GAAG,OAAOC,GAAIc,CAAAA,EAAE,EAAE,CAACd,CAAAA,EAAGc,CAAC,EAAEF,EAAE,KAAK,CAAC,WAAW,OAAOc,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAEd,EAAE,MAAM,CAAC,WAAW,OAAO,IAAIiB,KAAK,IAAI,CAAC,OAAO,GAAG,EAAEjB,EAAE,MAAM,CAAC,WAAW,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,EAAEA,EAAE,WAAW,CAAC,WAAW,OAAO,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,EAAEA,EAAE,QAAQ,CAAC,WAAW,OAAO,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,EAAEgB,CAAC,IAAIK,EAAEZ,EAAE,SAAS,CAAC,OAAOG,EAAE,SAAS,CAACS,EAAE,CAAC,CAAC,MAAMjC,EAAE,CAAC,CAAC,KAAKC,EAAE,CAAC,CAAC,KAAKC,EAAE,CAAC,CAAC,KAAKC,EAAE,CAAC,CAAC,KAA38M,MAAk9M,CAAC,CAAC,KAAKE,EAAE,CAAC,CAAC,KAAKE,EAAE,CAAC,CAAC,KAAKC,EAAE,CAAC,CAAC,OAAO,CAAE,SAASK,CAAC,EAAEoB,CAAC,CAACpB,CAAC,CAAC,EAAE,CAAC,CAAC,SAASd,CAAC,EAAE,OAAO,IAAI,CAAC,EAAE,CAACA,EAAEc,CAAC,CAAC,EAAE,CAACA,CAAC,CAAC,EAAE,CAAC,CAAC,GAAIW,EAAE,MAAM,CAAC,SAASX,CAAC,CAACd,CAAC,EAAE,OAAOc,EAAE,EAAE,EAAGA,CAAAA,EAAEd,EAAEsB,EAAEG,GAAGX,EAAE,EAAE,CAAC,CAAC,GAAGW,CAAC,EAAEA,EAAE,MAAM,CAACF,EAAEE,EAAE,OAAO,CAACJ,EAAEI,EAAE,IAAI,CAAC,SAASX,CAAC,EAAE,OAAOW,EAAE,IAAIX,EAAE,EAAEW,EAAE,EAAE,CAACN,CAAC,CAACD,EAAE,CAACO,EAAE,EAAE,CAACN,EAAEM,EAAE,CAAC,CAAC,CAAC,EAAEA,CAAC,4CCmBr+NU,EAAKC,EAAYC,EAlBhC,IA4FIC,EAAcC,EAASC,EAAcC,EAAgBC,EAA0BC,EA5F/EC,EAAWC,OAAO,MAAM,CACxBC,EAAYD,OAAO,cAAc,CACjCE,EAAmBF,OAAO,wBAAwB,CAClDG,EAAoBH,OAAO,mBAAmB,CAC9CI,EAAeJ,OAAO,cAAc,CACpCK,EAAeL,OAAO,SAAS,CAAC,cAAc,CAK9CM,EAAc,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAQ,AAAgB,UAAhB,OAAOA,GAAqB,AAAgB,YAAhB,OAAOA,EAC7C,IAAK,IAAIG,KAAOR,EAAkBK,GAC3BH,EAAa,IAAI,CAACE,EAAII,IAAQA,IAAQF,GACzCR,EAAUM,EAAII,EAAK,CAAE,IAAK,IAAMH,CAAI,CAACG,EAAI,CAAE,WAAY,CAAED,CAAAA,EAAOR,EAAiBM,EAAMG,EAAG,GAAMD,EAAK,UAAU,AAAC,GAEtH,OAAOH,CACT,EAUIK,EAAgB,CAACC,EAAKC,EAAQC,KAChC,GAAI,CAACD,EAAO,GAAG,CAACD,GACd,MAAMG,UAAU,UAAYD,EAChC,EACIE,EAAe,CAACJ,EAAKC,EAAQI,KAC/BN,EAAcC,EAAKC,EAAQ,2BACpBI,EAASA,EAAO,IAAI,CAACL,GAAOC,EAAO,GAAG,CAACD,IAE5CM,EAAe,CAACN,EAAKC,EAAQM,KAC/B,GAAIN,EAAO,GAAG,CAACD,GACb,MAAMG,UAAU,oDAClBF,CAAAA,aAAkBO,QAAUP,EAAO,GAAG,CAACD,GAAOC,EAAO,GAAG,CAACD,EAAKO,EAChE,EACIE,EAAe,CAACT,EAAKC,EAAQM,EAAOG,KACtCX,EAAcC,EAAKC,EAAQ,0BAC3BS,EAASA,EAAO,IAAI,CAACV,EAAKO,GAASN,EAAO,GAAG,CAACD,EAAKO,GAC5CA,GAELI,EAAkB,CAACX,EAAKC,EAAQW,KAClCb,EAAcC,EAAKC,EAAQ,yBACpBW,GAILC,EAAkB,CAAC,EA7CCC,EA8CE,CACxB,+BAAgC,IAAMC,CACxC,EA/CE,IAAK,IAAIC,KAAQF,EACf1B,EA4CKyB,EA5CaG,EAAM,CAAE,IAAKF,CAAG,CAACE,EAAK,CAAE,WAAY,EAAK,EA+C/D3E,CAAAA,EAAO,OAAO,CA7BcoD,EAAYL,EAAU,CAAC,EAAG,aAAc,CAAE,MAAO,EAAK,GA6BpDyB,GAG9B,IAAII,EAAgB,EAAQ,MAGxBC,EAAwB;AAC5B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAGGC,EAAmB,EAAQ,OAC3BC,EAAgB,EAAQ,MAGxBC,EAAyB,EAAQ,OACjCC,EAAe,EAAQ,MAEvBC,EAAc,MAChB,YAAYC,CAAM,CAAE,CAClBlB,EAAa,IAAI,CAAExB,GACnBwB,EAAa,IAAI,CAAEtB,GACnBsB,EAAa,IAAI,CAAE1B,EAA8B,IAAI6C,KACrDnB,EAAa,IAAI,CAAEzB,EAAS,KAAK,GACjC,IAAI,CAAC,UAAU,CAAG,EAClB4B,EAAa,IAAI,CAAE5B,EAAS2C,EAC9B,CACA,aAAaA,CAAM,CAAE,CACnBf,EAAa,IAAI,CAAE5B,EAAS2C,EAC9B,CACA,MAAM,KAAK1B,CAAG,CAAE4B,EAAU,CACxB,KAAM,KAAK,EACX,SAAU,EAAE,AACd,CAAC,CAAE,CACD,IAAMC,EAAchB,EAAgB,IAAI,CAAE3B,EAA0BC,GAA4B,IAAI,CAAC,IAAI,CAAEa,GACrG8B,EAAaxB,EAAa,IAAI,CAAExB,GAAc,GAAG,CAAC+C,EAAY,IAAI,EACxEvB,EAAa,IAAI,CAAExB,GAAc,GAAG,CAAC+C,EAAY,IAAI,EACrD,IAAI,CAAC,UAAU,EAAIhB,EAAgB,IAAI,CAAE7B,EAAcC,GAAgB,IAAI,CAAC,IAAI,CAAE4C,EAAY,GAAG,EACjG,IAAME,EAAOH,AAAiB,KAAK,IAAtBA,EAAQ,IAAI,CAAcC,EAAY,IAAI,CAAGD,EAAQ,IAAI,AACtE,OAAMtB,EAAa,IAAI,CAAEvB,GAAS,IAAI,CAAC,yBAA0B,CAC/D,KAAMgD,EAAO,UAAY,aACzB,UAAW,IAAI,CAAC,UAAU,CAC1B,sBAAuBF,EAAY,OAAO,CAC1C,KAAMA,EAAY,IAAI,CACtB,IAAKA,EAAY,GAAG,CACpBE,KAAAA,EACA,eAAgBA,EAChBD,WAAAA,EACA,SAAUD,EAAY,QAAQ,CAC9B,SAAUA,AAAyB,IAAzBA,EAAY,QAAQ,CAC9B,SAAUD,EAAQ,QAAQ,AAC5B,EACF,CACA,MAAM,GAAG5B,CAAG,CAAE,CACZ,IAAM6B,EAAchB,EAAgB,IAAI,CAAE3B,EAA0BC,GAA4B,IAAI,CAAC,IAAI,CAAEa,EAC3G,KAAI,CAAC,UAAU,EAAI,CAACa,EAAgB,IAAI,CAAE7B,EAAcC,GAAgB,IAAI,CAAC,IAAI,CAAE4C,EAAY,GAAG,EAClGvB,EAAa,IAAI,CAAExB,GAAc,MAAM,CAAC+C,EAAY,IAAI,EACxD,MAAMvB,EAAa,IAAI,CAAEvB,GAAS,IAAI,CAAC,yBAA0B,CAC/D,KAAM,QACN,UAAW,IAAI,CAAC,UAAU,CAC1B,IAAK8C,EAAY,GAAG,CACpB,sBAAuBA,EAAY,OAAO,CAC1C,KAAMA,EAAY,IAAI,CACtB,SAAUA,EAAY,QAAQ,AAChC,EACF,CACA,MAAM,cAAcG,CAAI,CAAE,CACxB,MAAM1B,EAAa,IAAI,CAAEvB,GAAS,IAAI,CAAC,mBAAoB,CAAE,KAAMiD,CAAK,EAC1E,CACA,UAAUA,CAAI,CAAE,CACd,MAAO,CAAC,CAACT,EAAuB,eAAe,CAACS,EAAK,AACvD,CACA,MAAM,KAAKD,CAAI,CAAEH,EAAU,CAAC,CAAC,CAAE,CAC7B,IAAMK,EAAQL,EAAQ,KAAK,EAAI,KAAK,EACpC,IAAK,IAAMI,KAAQD,EACb,IAAI,CAAC,SAAS,CAACC,GACjB,MAAM,IAAI,CAAC,KAAK,CAACA,EAAM,CAAEC,MAAAA,CAAM,IAE3BA,GACF,MAAM,IAAIC,QAAQ,AAACnF,GACVoF,WAAWpF,EAAGkF,IAGzB,MAAM,IAAI,CAAC,aAAa,CAACD,GAG/B,CACA,MAAM,MAAMhC,CAAG,CAAE4B,EAAU,CAAC,CAAC,CAAE,CAC7B,GAAM,CAAEK,MAAAA,EAAQ,IAAI,CAAE,CAAGL,EACnBQ,EAAO3E,MAAM,OAAO,CAACuC,GAAOA,EAAM,CAACA,EAAI,CAC7C,IAAK,IAAMtB,KAAK0D,EACd,MAAM,IAAI,CAAC,IAAI,CAAC1D,EAAGkD,GAOrB,IAAK,IAAMlD,KALPuD,GACF,MAAM,IAAIC,QAAQ,AAACnF,GACVoF,WAAWpF,EAAG6E,EAAQ,KAAK,GAGtB,IAAIQ,EAAK,CAAC,OAAO,IAC/B,MAAM,IAAI,CAAC,EAAE,CAAC1D,EAElB,CACF,EACAI,EAAe,IAAIuD,QACnBtD,EAAU,IAAIsD,QACdrD,EAAe,IAAI0B,QACnBzB,EAAiB,SAASe,CAAG,QAC3B,AAAIA,AAAQ,QAARA,EACK,EAELA,AAAQ,YAARA,EACK,EAELA,AAAQ,SAARA,EACK,EAGA,EADLA,CAAAA,AAAQ,UAARA,CAAc,CAIpB,EACAd,EAA2B,IAAIwB,QAC/BvB,EAA6B,SAASmD,CAAS,EAC7C,IAAMC,EAAQ,AAAkB,EAAlB,IAAI,CAAC,UAAU,CACvBV,EAAc,CAClB,IAAK,GACL,QAAS,EACT,KAAM,GACN,KAAM,GACN,SAAU,CACZ,EACMW,EAAajB,EAAuB,eAAe,CAACe,EAAU,CAgCpE,MA/BA,AAAC,GAAGd,EAAa,MAAM,AAAD,EAAGgB,EAAY,CAAC,cAAc,EAAEF,EAAU,CAAC,CAAC,EAC9DE,EAAW,GAAG,EAChBX,CAAAA,EAAY,GAAG,CAAGW,EAAW,GAAG,AAAD,EAE7BD,GAASC,EAAW,QAAQ,EAC9BX,CAAAA,EAAY,GAAG,CAAGW,EAAW,QAAQ,AAAD,EAElCA,EAAW,OAAO,EACpBX,CAAAA,EAAY,OAAO,CAAGW,EAAW,OAAO,AAAD,EAErCD,GAASC,EAAW,YAAY,EAClCX,CAAAA,EAAY,OAAO,CAAGW,EAAW,YAAY,AAAD,EAE1CA,EAAW,IAAI,EACjBX,CAAAA,EAAY,IAAI,CAAGW,EAAW,IAAI,AAAD,EAE/BA,EAAW,QAAQ,EACrBX,CAAAA,EAAY,QAAQ,CAAGW,EAAW,QAAQ,AAAD,EAEZ,IAA3BX,EAAY,GAAG,CAAC,MAAM,EACxBA,CAAAA,EAAY,IAAI,CAAGA,EAAY,GAAG,AAAD,EAE/BW,EAAW,IAAI,EACjBX,CAAAA,EAAY,IAAI,CAAGW,EAAW,IAAI,AAAD,EAE/BD,GAASC,EAAW,SAAS,EAC/BX,CAAAA,EAAY,IAAI,CAAGW,EAAW,SAAS,AAAD,EAElB,GAAlB,IAAI,CAAC,UAAU,EACjBX,CAAAA,EAAY,IAAI,CAAG,EAAC,EAEfA,CACT,EAGA,IAAIY,GAhOwC5D,EAASF,AAAO,OAA7CA,EAgOc,EAAQ,QAhO8BS,EAASK,EAAad,IAAQ,CAAC,EAAGgB,EAKnGf,CAAAA,GAAeD,GAAQA,EAAI,UAAU,CAAoEE,EAAjES,EAAUT,EAAQ,UAAW,CAAE,MAAOF,EAAK,WAAY,EAAK,GACpGA,IA2NE+D,EAAgB,EAAQ,MACxBC,EAAyB,KACzBC,EAAuB,UACzB,IAAMC,EAAuBC,OAAO,OAAO,CAAC,MAAM,CAAC,0BACnD,GAAIH,EACF,OAAOA,EACT,GAAID,EAAc,WAAW,CAAE,CAC7B,IAAMK,EAAS,MAAMC,MAAMH,GAE3B,OADAF,EAAyB,MAAMI,EAAO,IAAI,EAE5C,CACA,OAAON,EAAe,OAAO,CAAC,YAAY,CAACI,EAAsB,OACnE,EACII,EAAkC,KAClCC,EAA2B,UAC7B,IAAMC,EAAgCL,OAAO,OAAO,CAAC,MAAM,CACzD,yBAEF,GAAIG,EACF,OAAOA,EACT,GAAIP,EAAc,WAAW,CAAE,CAC7B,IAAMK,EAAS,MAAMC,MAAMG,GAE3B,OADAF,EAAkC,MAAMF,EAAO,IAAI,EAErD,CACA,OAAON,EAAe,OAAO,CAAC,YAAY,CAACU,EAA+B,OAC5E,EACIC,EAAsC,KACtCC,EAA+B,UACjC,IAAMC,EAAoCR,OAAO,OAAO,CAAC,MAAM,CAC7D,8BAEF,GAAIM,EACF,OAAOA,EACT,GAAIV,EAAc,WAAW,CAAE,CAC7B,IAAMK,EAAS,MAAMC,MAAMM,GAE3B,OADAF,EAAsC,MAAML,EAAO,IAAI,EAEzD,CACA,OAAON,EAAe,OAAO,CAAC,YAAY,CAACa,EAAmC,OAChF,EAGA,SAASC,EAAMC,CAAE,EACf,OAAO,IAAItB,QAAQ,AAACuB,GAAYtB,WAAWsB,EAASD,GACtD,CACA,IAAIE,EAA2B,MAC7B,YAAYC,CAAsB,CAAE,CAClC,IAAI,CAAC,QAAQ,CAAG,yBAChB,IAAI,CAAC,OAAO,CAAG,SACf,IAAI,CAAC,WAAW,CAAG,KACnB,IAAI,CAAC,uBAAuB,CAAG,KAC/B,IAAI,CAAC,iBAAiB,CAAG,KACzB,IAAI,CAAC,SAAS,CAAG,GACjB,IAAI,CAAC,iBAAiB,CAAG,KACzB,IAAI,CAAC,YAAY,CAAG,IACpB,IAAI,CAAC,YAAY,CAAG,IACpB,IAAI,CAAC,KAAK,CAAG,CACX,MAAO,MAAOC,EAAGxG,EAAGwE,KAClB,GAAM,CAAEiC,OAAAA,EAAS,MAAM,CAAEC,MAAAA,EAAQ,CAAC,CAAE,CAAGlC,GAAW,CAAC,EAEnD,GADA,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAACgC,EAAGxG,GACrB,AAA2B,OAA3B,IAAI,CAAC,iBAAiB,CAAW,CACnC,IAAM2G,EAAS,MAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CAClE,WAAY,CAAC;AACzB;AACA,cAAc,CAAC,CACH,cAAe,EACjB,EACA,KAAI,CAAC,iBAAiB,CAAGA,GAAQ,QAAQ,KAC3C,CACA,GAAI,IAAI,CAAC,iBAAiB,EAAIF,AAAW,SAAXA,EAAmB,CAC/C,IAAMG,EAAc,CAAC,CAAE,EAAG5F,KAAK,KAAK,CAACwF,GAAI,EAAGxF,KAAK,KAAK,CAAChB,EAAG,EAAE,AAC5D,OAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA4B,CAC3D,KAAM,aACN4G,YAAAA,EACA,UAAW,CACb,GACA,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA4B,CAC3D,KAAM,WACN,YAAa,EAAE,CACf,UAAW,CACb,EACF,MACE,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA4B,CAC3D,KAAM,eACNJ,EAAAA,EACAxG,EAAAA,EACAyG,OAAAA,EACA,WAAYC,CACd,GACA,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA4B,CAC3D,KAAM,gBACNF,EAAAA,EACAxG,EAAAA,EACAyG,OAAAA,EACA,WAAYC,CACd,EAEJ,EACA,MAAO,MAAOG,EAAQC,EAAQC,EAAQC,KACpC,IAAMC,EAASF,GAAU,IAAI,CAAC,YAAY,CACpCG,EAASF,GAAU,IAAI,CAAC,YAAY,AAC1C,OAAM,IAAI,CAAC,gBAAgB,CAACC,EAAQC,GACpC,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA4B,CAC3D,KAAM,aACN,EAAGD,EACH,EAAGC,EACHL,OAAAA,EACAC,OAAAA,CACF,GACA,IAAI,CAAC,YAAY,CAAGG,EACpB,IAAI,CAAC,YAAY,CAAGC,CACtB,EACA,KAAM,MAAOV,EAAGxG,KACd,MAAM,IAAI,CAAC,gBAAgB,CAACwG,EAAGxG,GAC/B,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA4B,CAC3D,KAAM,aACNwG,EAAAA,EACAxG,EAAAA,CACF,GACA,IAAI,CAAC,YAAY,CAAGwG,EACpB,IAAI,CAAC,YAAY,CAAGxG,CACtB,EACA,KAAM,MAAOyC,EAAMD,KACjB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAACC,EAAK,CAAC,CAAEA,EAAK,CAAC,EACpC,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA4B,CAC3D,KAAM,eACN,EAAGA,EAAK,CAAC,CACT,EAAGA,EAAK,CAAC,CACT,OAAQ,OACR,WAAY,CACd,GACA,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAACD,EAAG,CAAC,CAAEA,EAAG,CAAC,EAChC,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA4B,CAC3D,KAAM,gBACN,EAAGA,EAAG,CAAC,CACP,EAAGA,EAAG,CAAC,CACP,OAAQ,OACR,WAAY,CACd,EACF,CACF,EACA,IAAI,CAAC,QAAQ,CAAG,CACd,KAAM,MAAOmC,IACX,IAAMwC,EAAc,IAAI9C,EAAY,CAClC,KAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAC5C,EACA,OAAM8C,EAAY,IAAI,CAACxC,EAAM,CAAE,MAAO,CAAE,EAC1C,EACA,MAAO,MAAOyC,IACZ,IAAMD,EAAc,IAAI9C,EAAY,CAClC,KAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAC5C,GACMW,EAAO3E,MAAM,OAAO,CAAC+G,GAAUA,EAAS,CAACA,EAAO,CACtD,IAAK,IAAM9F,KAAK0D,EAAM,CACpB,IAAMqC,EAAW/F,EAAE,OAAO,CAAG,CAACA,EAAE,OAAO,CAAC,CAAG,EAAE,AAC7C,OAAM6F,EAAY,IAAI,CAAC7F,EAAE,GAAG,CAAE,CAAE+F,SAAAA,CAAS,EAC3C,CACA,IAAK,IAAM/F,IAAK,IAAI0D,EAAK,CAAC,OAAO,GAC/B,MAAMmC,EAAY,EAAE,CAAC7F,EAAE,GAAG,CAE9B,CACF,EACA,IAAI,CAAC,sBAAsB,CAAGiF,CAChC,CACA,MAAM,eAAee,CAAK,CAAE,CAC1B,GAAI,IAAI,CAAC,WAAW,CAClB,MAAM,AAAIC,MACR,CAAC,uCAAuC,EAAE,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAED,EAAM,CAAC,CAG3F,OAAM5B,OAAO,IAAI,CAAC,MAAM,CAAC4B,EAAO,CAAE,OAAQ,EAAK,GAC/C,IAAI,CAAC,WAAW,CAAGA,CACrB,CACA,MAAM,gBAAiB,CACrB,OAAO,IAAI,CAAC,WAAW,AACzB,CAKA,MAAM,mBAAoB,CAExB,MAAOE,AADM,OAAM9B,OAAO,IAAI,CAAC,KAAK,CAAC,CAAE,cAAe,EAAK,EAAC,EAChD,GAAG,CAAC,AAAC+B,GAAS,EACxB,GAAI,CAAC,EAAEA,EAAI,EAAE,CAAC,CAAC,CACf,MAAOA,EAAI,KAAK,CAChB,IAAKA,EAAI,GAAG,CACZ,iBAAkBA,EAAI,MAAM,AAC9B,IAAI,MAAM,CAAC,AAACA,GAAQA,EAAI,EAAE,EAAIA,EAAI,KAAK,EAAIA,EAAI,GAAG,CACpD,CACA,MAAM,+BAAgC,CACpC,GAAI,IAAI,CAAC,WAAW,CAClB,OAAO,IAAI,CAAC,WAAW,CAEzB,IAAMH,EAAQ,MAAM5B,OAAO,IAAI,CAAC,KAAK,CAAC,CAAE,OAAQ,GAAM,cAAe,EAAK,GAAG,IAAI,CAAC,AAAC8B,GAASA,CAAI,CAAC,EAAE,EAAE,IAErG,OADA,IAAI,CAAC,WAAW,CAAGF,GAAS,EACrB,IAAI,CAAC,WAAW,AACzB,CACA,MAAM,gBAAiB,CAErB,GADA,AAAC,GAAGpD,EAAc,MAAM,AAAD,EAAG,CAAC,IAAI,CAAC,SAAS,CAAE,qBACvC,IAAI,CAAC,iBAAiB,CAAE,CAC1B,MAAM,IAAI,CAAC,iBAAiB,CAC5B,MACF,CACA,IAAI,CAAC,iBAAiB,CAAG,AAAC,WACxB,IAAMwD,EAAM,MAAM,IAAI,CAAC,GAAG,GACtBC,EAAQ,KACZ,GAAID,EAAI,UAAU,CAAC,aACjB,MAAM,AAAIH,MACR,qHAGJ,GAAI,CACF,IAAMK,EAAe,MAAM,IAAI,CAAC,6BAA6B,GAC7D,GAAI,IAAI,CAAC,uBAAuB,GAAKA,EACnC,OAEF,GAAI,IAAI,CAAC,uBAAuB,EAAI,IAAI,CAAC,uBAAuB,GAAKA,EAAc,CACjFC,QAAQ,GAAG,CACT,0BACA,IAAI,CAAC,uBAAuB,CAC5B,KACAD,GAEF,GAAI,CACF,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,uBAAuB,CACxD,CAAE,MAAOE,EAAQ,CACfD,QAAQ,KAAK,CAAC,4BAA6BC,EAC7C,CACF,CACAD,QAAQ,GAAG,CAAC,qBAAsBD,GAClC,MAAMlC,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAE,MAAOkC,CAAa,EAAG,OACtD,MAAMzB,EAAM,KACZ,IAAI,CAAC,uBAAuB,CAAGyB,EAC/B,MAAM,IAAI,CAAC,wBAAwB,EACrC,CAAE,MAAOxI,EAAG,CACVyI,QAAQ,KAAK,CAAC,4BAA6BzI,GAC3CuI,EAAQvI,CACV,QAAU,CACR,IAAI,CAAC,iBAAiB,CAAG,IAC3B,CACA,GAAIuI,EACF,MAAMA,CAEV,KACA,MAAM,IAAI,CAAC,iBAAiB,AAC9B,CACA,MAAM,iBAAiBnB,CAAC,CAAExG,CAAC,CAAE,CAC3B,IAAM+H,EAAgB,CAAC;AAC3B;AACA;AACA,2DAA2D,EAAEvB,EAAE,EAAE,EAAExG,EAAE;AACrE;AACA;AACA;AACA,QAAQ,CAAC,AACL,OAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CACnD,WAAY,CAAC,EAAE+H,EAAc,CAAC,AAChC,EACF,CACA,MAAM,kBAAmB,CACvB,MAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CACnD,WAAY,CAAC;AACnB;AACA;AACA;AACA,UAAU,CAAC,AACP,EACF,CACA,MAAM,eAAeT,CAAK,CAAE,CAC1B,IAAMU,EAAgBV,GAAS,IAAI,CAAC,uBAAuB,CAE3D,GADAO,QAAQ,GAAG,CAAC,qBAAsBG,GAC9B,CAACA,EAAe,CAClBH,QAAQ,IAAI,CAAC,uBACb,MACF,CACA,GAAI,CACF,MAAM,IAAI,CAAC,yBAAyB,CAACG,GACrC,MAAM7B,EAAM,IACd,CAAE,MAAOwB,EAAO,CACdE,QAAQ,IAAI,CAAC,yCAA0CF,EACzD,CACA,GAAI,CACF,MAAMjC,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAE,MAAOsC,CAAc,EACtD,CAAE,MAAOL,EAAO,CACdE,QAAQ,IAAI,CAAC,4BAA6BF,EAC5C,CACA,IAAI,CAAC,uBAAuB,CAAG,IACjC,CACA,MAAM,0BAA2B,CAC3B,IAAI,CAAC,sBAAsB,EAC7B,MAAMjC,OAAO,QAAQ,CAAC,WAAW,CAC/B,CAAE,MAAO,IAAI,CAAC,uBAAuB,AAAC,EACtC,mBACA,CACE,WAAY1B,CACd,GAGJ,IAAM2B,EAAS,MAAMG,GACrB,OAAMJ,OAAO,QAAQ,CAAC,WAAW,CAC/B,CAAE,MAAO,IAAI,CAAC,uBAAuB,AAAC,EACtC,mBACA,CACE,WAAYC,CACd,EAEJ,CACA,MAAM,0BAA0B2B,CAAK,CAAE,CACrC,IAAM3B,EAAS,MAAMM,GACrB,OAAMP,OAAO,QAAQ,CAAC,WAAW,CAAC,CAAE4B,MAAAA,CAAM,EAAG,mBAAoB,CAC/D,WAAY3B,CACd,EACF,CACA,MAAM,sBAAsBsC,CAAO,CAAEC,CAAM,CAAE,CAI3C,OAHA,MAAM,IAAI,CAAC,cAAc,GACzB,AAAC,GAAGhE,EAAc,MAAM,AAAD,EAAG,IAAI,CAAC,uBAAuB,CAAE,4BACxD,IAAI,CAAC,wBAAwB,GACtB,MAAMwB,OAAO,QAAQ,CAAC,WAAW,CACtC,CAAE,MAAO,IAAI,CAAC,uBAAuB,AAAC,EACtCuC,EACAC,EAEJ,CACA,MAAM,qBAAsB,CAC1B,IAAMvC,EAAS,MAAMH,GACrB,OAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CACnD,WAAYG,CACd,GAYA,IAAMwC,EAAc,MAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CACvE,WAAY,CAAC,CAAC,EAAEC,AAZC,MACjBC,OAAO,0BAA0B,CAAC,4BAA4B,GACvD,CACL,KAAMA,OAAO,0BAA0B,CAAC,kBAAkB,GAC1D,KAAM,CACJ,MAAOC,SAAS,eAAe,CAAC,WAAW,CAC3C,OAAQA,SAAS,eAAe,CAAC,YAAY,CAC7C,IAAKD,OAAO,gBAAgB,AAC9B,CACF,EACF,EAE6B,QAAQ,GAAG,GAAG,CAAC,CAC1C,cAAe,EACjB,GACA,GAAI,CAACF,EAAY,MAAM,CAAC,KAAK,CAAE,CAC7B,IAAMI,EAAmBJ,EAAY,gBAAgB,EAAE,WAAW,aAAe,EAIjF,OAHKI,GACHV,QAAQ,KAAK,CAAC,uBAAwBM,GAElC,AAAIZ,MACR,CAAC,6CAA6C,EAAEgB,EAAiB,CAAC,CAEtE,CACA,OAAOJ,EAAY,MAAM,CAAC,KAAK,AACjC,CACA,MAAM,mBAAmBxC,CAAM,CAAE,CAC/B,OAAO,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CACpD,WAAYA,CACd,EACF,CAEA,MAAM,sBAAuB,CAE3B,IAAM6C,EAAYtH,KAAK,GAAG,GACtBuH,EAAiB,GACrB,KAAOvH,KAAK,GAAG,GAAKsH,EAHJ,KAGyB,CAKvC,GAAIC,AAAmB,aADvBA,CAAAA,EAAiB9B,AAHF,OAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CAClE,WAAY,qBACd,EAAC,EACuB,MAAM,CAAC,KAAK,AAAD,EACA,CACjC,MAAM,IAAI7B,QAAQ,AAACuB,GAAYtB,WAAWsB,EAAS,MACnD,MACF,CACA,MAAM,IAAIvB,QAAQ,AAACuB,GAAYtB,WAAWsB,EAAS,KACrD,CACA,MAAM,AAAIkB,MACR,CAAC,oDAAoD,EAAEkB,EAAe,CAAC,CAE3E,CACA,MAAM,iBAAkB,CACtB,IAAMC,EAAO,MAAM,IAAI,CAAC,mBAAmB,GAC3C,MAAO,AAAC,GAAGzE,EAAiB,UAAU,AAAD,EAAGyE,EAC1C,CACA,MAAM,cAAcC,CAAE,CAAE,CACtB,IAAMhD,EAAS,MAAMH,IAQrB,OAPA,MAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CACnD,WAAYG,CACd,GAKOgB,AAJQ,OAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CAClE,WAAY,CAAC,iDAAiD,EAAEgC,EAAG,EAAE,CAAC,CACtE,cAAe,EACjB,EAAC,EACa,MAAM,CAAC,KAAK,AAC5B,CACA,MAAM,sBAAsBC,CAAK,CAAE,CACjC,IAAMjD,EAAS,MAAMH,IAQrB,OAPA,MAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CACnD,WAAYG,CACd,GAKOgB,AAJQ,OAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CAClE,WAAY,CAAC,yDAAyD,EAAEiC,EAAM,EAAE,CAAC,CACjF,cAAe,EACjB,EAAC,EACa,MAAM,CAAC,KAAK,AAC5B,CACA,MAAM,qBAAsB,CAC1B,MAAM,IAAI,CAAC,gBAAgB,GAC3B,IAAMC,EAAU,MAAM,IAAI,CAAC,mBAAmB,GAI9C,OAHIA,GAAS,MACX,KAAI,CAAC,YAAY,CAAGA,EAAQ,IAAI,AAAD,EAE1BA,GAAS,MAAQ,CAAE,KAAM,KAAM,SAAU,EAAE,AAAC,CACrD,CACA,MAAM,MAAO,QACX,AAAI,IAAI,CAAC,YAAY,CACZ,IAAI,CAAC,YAAY,CAEnBA,AADS,OAAM,IAAI,CAAC,mBAAmB,EAAC,EAChC,IAAI,AACrB,CACA,MAAM,kBAAmB,CACvB,MAAM,IAAI,CAAC,gBAAgB,GAC3B,IAAMC,EAAS,MAAM,IAAI,CAAC,qBAAqB,CAAC,yBAA0B,CACxE,OAAQ,OACR,QAAS,EACX,GACA,MAAO,CAAC,uBAAuB,EAAEA,EAAO,IAAI,CAAC,CAAC,AAChD,CACA,MAAM,KAAM,CACV,IAAMxB,EAAQ,MAAM,IAAI,CAAC,6BAA6B,GAEtD,OAAOI,AADK,MAAMhC,OAAO,IAAI,CAAC,GAAG,CAAC4B,GAAO,IAAI,CAAC,AAACG,GAAQA,EAAI,GAAG,GAChD,EAChB,CACA,MAAM,eAAesB,CAAa,CAAE,CAIlC,OAHIA,GACF,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAACA,EAAc,IAAI,CAAEA,EAAc,GAAG,EAEtD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAG,SAC7B,CACA,MAAM,kBAAkBA,CAAa,CAAE,CAIrC,OAHIA,GACF,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAACA,EAAc,IAAI,CAAEA,EAAc,GAAG,EAEtD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAG,QAC7B,CACA,MAAM,gBAAgBA,CAAa,CAAE,CAInC,OAHIA,GACF,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAACA,EAAc,IAAI,CAAEA,EAAc,GAAG,EAEtD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAU,EACpC,CACA,MAAM,iBAAiBA,CAAa,CAAE,CAIpC,OAHIA,GACF,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAACA,EAAc,IAAI,CAAEA,EAAc,GAAG,EAEtD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAS,EACnC,CACA,MAAM,SAASC,CAAQ,CAAED,CAAa,CAAE,CACtC,GAAM,CAAEE,OAAAA,CAAM,CAAE,CAAG,MAAM,IAAI,CAAC,IAAI,GAElC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CACrB,EACA,CAHqBD,CAAAA,GAAYC,AAAS,GAATA,CAAW,EAI5CF,GAAe,KACfA,GAAe,IAEnB,CACA,MAAM,WAAWC,CAAQ,CAAED,CAAa,CAAE,CACxC,GAAM,CAAEE,OAAAA,CAAM,CAAE,CAAG,MAAM,IAAI,CAAC,IAAI,GAElC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CACrB,EAFqBD,GAAYC,AAAS,GAATA,EAIjCF,GAAe,KACfA,GAAe,IAEnB,CACA,MAAM,WAAWC,CAAQ,CAAED,CAAa,CAAE,CACxC,GAAM,CAAEG,MAAAA,CAAK,CAAE,CAAG,MAAM,IAAI,CAAC,IAAI,GAEjC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CACrB,CAFqBF,CAAAA,GAAYE,AAAQ,GAARA,CAAU,EAG3C,EACAH,GAAe,KACfA,GAAe,IAEnB,CACA,MAAM,YAAYC,CAAQ,CAAED,CAAa,CAAE,CACzC,GAAM,CAAEG,MAAAA,CAAK,CAAE,CAAG,MAAM,IAAI,CAAC,IAAI,GAEjC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CADAF,GAAYE,AAAQ,GAARA,EAGjC,EACAH,GAAe,KACfA,GAAe,IAEnB,CACA,MAAM,WAAWI,CAAO,CAAE,CACxB,GAAI,CAACA,EAAS,CACZtB,QAAQ,IAAI,CAAC,6BACb,MACF,CACA,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAACsB,EAAQ,MAAM,CAAC,EAAE,CAAEA,EAAQ,MAAM,CAAC,EAAE,EAC3D,MAAM,IAAI,CAAC,qBAAqB,CAAC,yBAA0B,CACzD,KAAM,UACN,SAAU,CAAC,YAAY,AACzB,GACA,MAAM,IAAI,CAAC,qBAAqB,CAAC,yBAA0B,CACzD,KAAM,QACN,SAAU,CAAC,YAAY,AACzB,GACA,MAAMhD,EAAM,KACZ,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CACxB,IAAK,WACP,EACF,CACA,MAAM,SAAU,CACd,IAAI,CAAC,WAAW,CAAG,KACnB,MAAM,IAAI,CAAC,cAAc,GACzB,IAAI,CAAC,SAAS,CAAG,EACnB,CACF,EAOIiD,EAAgB,EAAQ,MACxBC,EAAgB,EAAQ,OACxBC,EAAe,MACjB,YAAYC,CAAQ,CAAEC,CAAY,CAAEC,CAAY,CAAE,CAChD,IAAI,CAAC,QAAQ,CAAGF,EAChB,IAAI,CAAC,YAAY,CAAGC,EACpB,IAAI,CAAC,YAAY,CAAGC,EACpB,IAAI,CAAC,MAAM,CAAG,KACd,IAAI,CAAC,aAAa,CAAG,IACvB,CACA,MAAM,SAAU,CACd,OAAO,IAAI3E,QAAQ,CAACuB,EAASqD,KAC3B,IAAI,CAAC,MAAM,CAAG,AAAC,GAAGL,EAAc,EAAE,AAAD,EAAG,IAAI,CAAC,QAAQ,CAAE,CACjD,aAAc,GACd,MAAO,CACL,QAAS,QACX,CACF,GACA,IAAMM,EAAU5E,WAAW,KACzB,GAAI,CACF,IAAI,CAAC,MAAM,EAAE,SACb,IAAI,CAAC,MAAM,EAAE,OACf,CAAE,MAAO3F,EAAG,CACVyI,QAAQ,IAAI,CAAC,+BAAgCzI,EAC/C,CACA,IAAI,CAAC,MAAM,CAAG,KACdsK,EAAO,AAAInC,MAAM,oDACnB,EAAG,KACH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,aAAc,AAACqC,IAC5B,IAAI,CAAC,MAAM,CAAG,KACd,IAAI,CAAC,YAAY,IACnB,GACA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,gBAAiB,AAACxK,IAC/ByI,QAAQ,KAAK,CAAC,uBAAwBzI,GACtCsK,EAAO,AAAInC,MAAMnI,GAAK,wBACxB,GACA,IAAI,CAAC,MAAM,CAAC,EAAE,CACZ,mBACA,AAACyK,IACCC,aAAaH,GACb,IAAI,CAAC,aAAa,CAAGE,GAAS,SAAW,UACzCxD,EAAQ,IAAI,CAAC,MAAM,CACrB,GAEF,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAgC,AAACjH,IAC9CyI,QAAQ,KAAK,CAAC,iBAAkBzI,GAChC,GAAI,CACF,IAAI,CAAC,MAAM,EAAE,YACf,CAAE,MAAO2K,EAAI,CACb,CACAL,EAAO,AAAInC,MAAMnI,GAAK,kBACxB,GACA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAA0B,AAAC4K,IACxC,IAAMrB,EAAKqB,EAAK,EAAE,CAClB,AAAC,GAAGZ,EAAc,MAAM,AAAD,EAAG,AAAc,SAAPT,EAAoB,uBACrD,AAAC,WACC,IAAIsB,EACJ,GAAI,CACFA,EAAW,MAAM,IAAI,CAAC,YAAY,CAACD,EAAK,MAAM,CAAEA,EAAK,IAAI,CAC3D,CAAE,MAAO5K,EAAG,CACV,IAAM8K,EAAe,CAAC,+CAA+C,EAAEF,EAAK,MAAM,CAAC,QAAQ,EAAEA,EAAK,IAAI,CAAC,SAAS,EAAE5K,GAAG,SAAWA;AAC5I,EAAEA,GAAG,OAAS,GAAG,CAAC,CAEN,OADAyI,QAAQ,KAAK,CAACqC,GACP,IAAI,CAAC,MAAM,EAAE,KAAK,uBAA2C,CAClEvB,GAAAA,EACA,MAAOuB,CACT,EACF,CACA,IAAI,CAAC,MAAM,EAAE,KAAK,uBAA2C,CAC3DvB,GAAAA,EACAsB,SAAAA,CACF,EACF,IACF,EACF,EACF,CACA,YAAa,CACX,IAAI,CAAC,MAAM,EAAE,aACb,IAAI,CAAC,MAAM,CAAG,IAChB,CACF,EAGIpG,EAAiC,cAAcyC,EACjD,YAAYmD,EAAe,KAC3B,CAAC,CAAEU,EAAe,KAClB,CAAC,CAAE5D,EAAyB,EAAI,CAAE,CAChC,KAAK,CAACA,GACN,IAAI,CAAC,YAAY,CAAGkD,EACpB,IAAI,CAAC,YAAY,CAAGU,EACpB,IAAI,CAAC,YAAY,CAAG,KACpB,IAAI,CAAC,kBAAkB,CAAG,EAAE,AAC9B,CACA,MAAM,mBAAoB,CACxB,IAAI,CAAC,YAAY,CAAG,IAAIb,EACtB,sBACA,MAAO5F,EAAQ0G,KAEb,GADAvC,QAAQ,GAAG,CAAC,4BAA6BnE,EAAQ0G,GAC7C1G,AAAW,yBAAXA,EACF,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,CACpC,IAAI,CACJ0G,GAGJ,GAAI1G,AAAW,sBAAXA,EACF,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAE0G,GAE5C,GAAI1G,AAAW,mBAAXA,EACF,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAE0G,GAEzC,GAAI1G,AAAW,sBAAXA,EACF,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAE0G,GAE5C,GAAI1G,AAAW,+BAAXA,EACF,OAAO,IAAI,CAAC,YAAY,CAAC0G,CAAI,CAAC,EAAE,CAAE,UAEpC,IAAM9C,EAAQ,MAAM,IAAI,CAAC,cAAc,GACvC,GAAI,CAACA,GAASA,AAAU,IAAVA,EACZ,MAAM,AAAIC,MAAM,uBAElB,GAAI7D,EAAO,UAAU,CAAC,UAAwB,CAC5C,IAAM2G,EAAa3G,EAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAIvC,OAAO,IAAI,CAAC,KAAK,CAAC2G,EAAW,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAED,EAClD,CACA,GAAI1G,EAAO,UAAU,CAAC,aAA2B,CAC/C,IAAM2G,EAAa3G,EAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAIvC,OAAO,IAAI,CAAC,QAAQ,CAAC2G,EAAW,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAED,EACxD,CACA,GAAI,CAIF,OAHe,MAAM,IAAI,CAAC1G,EAAO,IAC5B0G,EAGP,CAAE,MAAOhL,EAAG,CACV,IAAMkL,EAAelL,aAAamI,MAAQnI,EAAE,OAAO,CAAG,eAMtD,OALAyI,QAAQ,KAAK,CAAC,uBAAwBnE,EAAQ0G,EAAMhL,GACpD,IAAI,CAAC,YAAY,CACf,CAAC,sBAAsB,EAAEsE,EAAO,EAAE,EAAE4G,EAAa,CAAC,CAClD,OAEI,AAAI/C,MAAM+C,EAAc,CAAE,MAAOlL,CAAE,EAC3C,CACF,EAEA,IACS,IAAI,CAAC,OAAO,IAGvB,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,GAC/B,IAAI,CAAC,YAAY,CACf,uCAAuC,IAAI,CAAC,YAAY,CAAC,aAAa,gCAAqC,CAC3G,MAEJ,CACA,MAAM,SAAU,CACd,OAAO,MAAM,IAAI,CAAC,iBAAiB,EACrC,CACA,MAAM,qBAAqBsI,CAAG,CAAElD,EAAU,CACxC,uBAAwB,EAC1B,CAAC,CAAE,CAED,IAAM8C,EAAQG,AADF,OAAM/B,OAAO,IAAI,CAAC,MAAM,CAAC,CAAEgC,IAAAA,CAAI,EAAC,EAC1B,EAAE,CACpB,AAAC,GAAG3D,EAAc,MAAM,AAAD,EAAGuD,EAAO,gDACjC,IAAI,CAAC,YAAY,CAAC,CAAC,kBAAkB,EAAEI,EAAI,CAAC,CAAE,OAC9C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAACJ,GACzB9C,GAAS,wBACX,KAAI,CAAC,sBAAsB,CAAG,EAAG,EAEnC,MAAM,IAAI,CAAC,cAAc,CAAC8C,EAC5B,CACA,MAAM,kBAAkB9C,EAAU,CAChC,uBAAwB,EAC1B,CAAC,CAAE,CACD,IAAMgD,EAAO,MAAM9B,OAAO,IAAI,CAAC,KAAK,CAAC,CAAE,OAAQ,GAAM,cAAe,EAAK,GACnE4B,EAAQE,CAAI,CAAC,EAAE,EAAE,GACvB,AAAC,GAAGzD,EAAc,MAAM,AAAD,EAAGuD,EAAO,uBACjC,IAAI,CAAC,YAAY,CAAC,CAAC,0BAA0B,EAAEE,CAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAE,OAC3DhD,GAAS,wBACX,KAAI,CAAC,sBAAsB,CAAG,EAAG,EAEnC,MAAM,IAAI,CAAC,cAAc,CAAC8C,EAC5B,CACA,MAAM,kBAAkB9C,CAAO,CAAE,CAC/B,IAAI,CAAC,cAAc,CAAGA,CACxB,CACA,MAAM,SAAU,CACd,GAAI,IAAI,CAAC,cAAc,EAAE,UAAY,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAG,EAAG,CAEvE,IAAK,IAAM8C,KADX,IAAI,CAAC,YAAY,CAAC,8CAA+C,OAC7C,IAAI,CAAC,kBAAkB,EACzC,MAAM5B,OAAO,IAAI,CAAC,MAAM,CAAC4B,EAE3B,KAAI,CAAC,kBAAkB,CAAG,EAAE,AAC9B,CACA,MAAM,KAAK,CAAC,UACR,IAAI,CAAC,YAAY,GACnB,IAAI,CAAC,YAAY,CAAC,UAAU,GAC5B,IAAI,CAAC,YAAY,CAAG,KACpB,IAAI,CAAC,YAAY,GAErB,CACF,0CC86CI5F,EAAcC,EAASC,EAAcC,EAAgBC,EAA0BC,aA/3E/EC,EAAWC,OAAO,MAAM,CACxBC,EAAYD,OAAO,cAAc,CACjCE,EAAmBF,OAAO,wBAAwB,CAClDG,EAAoBH,OAAO,mBAAmB,CAC9CI,EAAeJ,OAAO,cAAc,CACpCK,EAAeL,OAAO,SAAS,CAAC,cAAc,CAK9CM,EAAc,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAQ,AAAgB,UAAhB,OAAOA,GAAqB,AAAgB,YAAhB,OAAOA,EAC7C,IAAK,IAAIG,KAAOR,EAAkBK,GAC3BH,EAAa,IAAI,CAACE,EAAII,IAAQA,IAAQF,GACzCR,EAAUM,EAAII,EAAK,CAAE,IAAK,IAAMH,CAAI,CAACG,EAAI,CAAE,WAAY,CAAED,CAAAA,EAAOR,EAAiBM,EAAMG,EAAG,GAAMD,EAAK,UAAU,AAAC,GAEtH,OAAOH,CACT,EACI+H,EAAU,CAAChJ,EAAKC,EAAYC,IAAYA,CAAAA,EAASF,AAAO,MAAPA,EAAcS,EAASK,EAAad,IAAQ,CAAC,EAAGgB,EAKnGf,CAAAA,GAAeD,GAAQA,EAAI,UAAU,CAAoEE,EAAjES,EAAUT,EAAQ,UAAW,CAAE,MAAOF,EAAK,WAAY,EAAK,GACpGA,EACF,EAEIsB,EAAgB,CAACC,EAAKC,EAAQC,KAChC,GAAI,CAACD,EAAO,GAAG,CAACD,GACd,MAAMG,UAAU,UAAYD,EAChC,EACIE,EAAe,CAACJ,EAAKC,EAAQI,KAC/BN,EAAcC,EAAKC,EAAQ,2BACpBI,EAASA,EAAO,IAAI,CAACL,GAAOC,EAAO,GAAG,CAACD,IAE5CM,EAAe,CAACN,EAAKC,EAAQM,KAC/B,GAAIN,EAAO,GAAG,CAACD,GACb,MAAMG,UAAU,oDAClBF,CAAAA,aAAkBO,QAAUP,EAAO,GAAG,CAACD,GAAOC,EAAO,GAAG,CAACD,EAAKO,EAChE,EACIE,EAAe,CAACT,EAAKC,EAAQM,EAAOG,KACtCX,EAAcC,EAAKC,EAAQ,0BAC3BS,EAASA,EAAO,IAAI,CAACV,EAAKO,GAASN,EAAO,GAAG,CAACD,EAAKO,GAC5CA,GAELI,EAAkB,CAACX,EAAKC,EAAQW,KAClCb,EAAcC,EAAKC,EAAQ,yBACpBW,GAIL8G,EAA2B,CAAC,EA7CR5G,EA8CW,CACjC,yBAA0B,IAAM0C,GAChC,8BAA+B,IAAMmE,GACrC,uCAAwC,IAAMC,EAC9C,iBAAkB,IAAMC,GAAY,gBAAgB,AACtD,EAlDE,IAAK,IAAI7G,KAAQF,EACf1B,EA4CKsI,EA5Ca1G,EAAM,CAAE,IAAKF,CAAG,CAACE,EAAK,CAAE,WAAY,EAAK,EAkD/D3E,CAAAA,EAAO,OAAO,CAhCcoD,EAAYL,EAAU,CAAC,EAAG,aAAc,CAAE,MAAO,EAAK,GAgCpDsI,GAG9B,IAAII,EAAkB,EAAQ,OAC1BxG,EAAe,EAAQ,OACvByG,EAAa,EAAQ,OACrB5G,EAAmB,EAAQ,OAC3B6G,EAAa,EAAQ,OACrBxF,EAAgB,EAAQ,MACxByF,EAAeR,EAAQ,EAAQ,OAG/BS,EAAiB,MACnB,YAAY,CACVnC,QAAAA,CAAO,CACPoC,KAAAA,CAAI,CAEJC,QAAAA,CAAO,CACPvC,GAAAA,CAAE,CACFwC,WAAAA,CAAU,CACVC,QAAAA,CAAO,CACPC,OAAAA,CAAM,CACNC,UAAAA,CAAS,CACV,CAAE,CACD,IAAI,CAAC,OAAO,CAAGzC,EACf,IAAI,CAAC,IAAI,CAAGoC,EACZ,IAAI,CAAC,MAAM,CAAG,CACZjK,KAAK,KAAK,CAACiK,EAAK,IAAI,CAAGA,EAAK,KAAK,CAAG,GACpCjK,KAAK,KAAK,CAACiK,EAAK,GAAG,CAAGA,EAAK,MAAM,CAAG,GACrC,CACD,IAAI,CAAC,OAAO,CAAGC,EACf,IAAI,CAAC,EAAE,CAAGvC,EACV,IAAI,CAAC,UAAU,CAAGwC,EAClB,IAAI,CAAC,OAAO,CAAGC,EACf,IAAI,CAAC,MAAM,CAAGC,EACd,IAAI,CAAC,SAAS,CAAGC,CACnB,CACF,EAGA,eAAeC,EAAwBC,CAAI,CAAEC,CAAI,MAO3CC,EACAhD,EANJ,GADA,AAAC,GAAGpD,EAAc,MAAM,AAAD,EAAGkG,EAAM,oBAC5BA,EAAK,oBAAoB,CAC3B,OAAO,MAAMA,EAAK,oBAAoB,GAExC,IAAM9D,EAAM,MAAM8D,EAAK,GAAG,GAC1B,AAAC,GAAGpH,EAAa,sBAAsB,AAAD,EAAG,CAAE,QAASsD,CAAI,GAGxD,MAAM5C,QAAQ,GAAG,CAAC,CAChB0G,EAAK,gBAAgB,GAAG,IAAI,CAAC,AAAC1C,IAC5B4C,EAAmB5C,CACrB,GACA0C,EAAK,mBAAmB,GAAG,IAAI,CAAC,MAAOG,IACrCjD,EAAOiD,CACT,GACD,EACD,IAAMC,EAAU,AAAC,GAAG3H,EAAiB,YAAY,AAAD,EAAGyE,EAAM,AAACmD,IACxD,GAAM,CAAEZ,KAAAA,CAAI,CAAEtC,GAAAA,CAAE,CAAEE,QAAAA,CAAO,CAAEsC,WAAAA,CAAU,CAAED,QAAAA,CAAO,CAAEE,QAAAA,CAAO,CAAEE,UAAAA,CAAS,CAAE,CAAGO,EACvE,OAAO,IAAIb,EAAe,CACxBC,KAAAA,EACAC,QAAAA,EACAvC,GAAAA,EACAE,QAAAA,EACAsC,WAAAA,EACAC,QAAAA,EACAE,UAAAA,CACF,EACF,GACA,AAAC,GAAGhG,EAAc,MAAM,AAAD,EAAGoG,EAAkB,gCAC5C,IAAMI,EAAe,AAAC,GAAG7H,EAAiB,UAAU,AAAD,EAAG2H,GAChDG,EAAO,MAAMP,EAAK,IAAI,GAO5B,OANIO,EAAK,GAAG,EAAIA,EAAK,GAAG,CAAG,GACzBL,CAAAA,EAAmB,MAAM,AAAC,GAAGZ,EAAW,eAAe,AAAD,EAAGY,EAAkB,CACzE,MAAOK,EAAK,KAAK,CACjB,OAAQA,EAAK,MAAM,AACrB,EAAC,EAEI,CACL,QAASD,EACT,KAAMF,EACNG,KAAAA,EACAL,iBAAAA,EACAhE,IAAAA,CACF,CACF,CAUA,IAAIgD,EAAyC,8BA0BzCsB,EAAe,EAAQ,OACvBC,EAAkB1B,EAAQ,EAAQ,QAGlClF,EAAiB,EAAQ,OACzB6G,EAAmB,EAAQ,OAC3BhI,EAAgB,EAAQ,MACxBiI,EAAgB,EAAQ,OACxBC,EAAe,MACjB,YAAYzG,CAAM,CAAE0G,CAAU,CAAEC,CAAkB,CAAE,CAClD,IAAI,CAAC,MAAM,CAAG3G,EACd,IAAI,CAAC,UAAU,CAAG0G,EAClB,IAAI,CAAC,kBAAkB,CAAGC,EAC1B,IAAI,CAAC,cAAc,CAAG,EAAE,CACxB,IAAI,CAAC,MAAM,CAAG,OACd,IAAI,CAAC,kBAAkB,CAAG,EAC1B,IAAI,CAAC,SAAS,CAAG,KACjB,IAAI,CAAC,MAAM,CAAG,CAAC,EACf,IAAM7K,EAASkE,EAAO,MAAM,EAAIA,EAAO,GAAG,EAAIA,EAAO,OAAO,AACxDzB,CAAAA,EAAc,WAAW,CAC3B,IAAI,CAAC,MAAM,CAAG,KAAK,EACVzC,GAAQ,OACjB,IAAI,CAAC,MAAM,CAAG,AAAC,GAAGyK,EAAiB,OAAO,AAAD,EAAGK,EAAQ,GAAG,GAAI9K,EAAO,MAAM,EAExE,IAAI,CAAC,MAAM,CAAG,AAAC,GAAGyK,EAAiB,IAAI,AAAD,EAAG,AAAC,GAAGC,EAAc,oBAAoB,AAAD,EAAG,UAAW,CAAC,EAAEI,EAAQ,GAAG,CAAC,KAAK,CAAC,EAEnH,IAAI,CAAC,cAAc,CAAG,AAAC5G,CAAAA,EAAO,KAAK,EAAI,EAAE,AAAD,EAAG,GAAG,CAAC,CAAC6G,EAAMC,IAAe,EACnE,GAAGD,CAAI,CACP,MAAOC,EACP,OAAQ,OACR,WAAYD,EAAK,IAAI,EAAE,QAAU,CACnC,GACF,CACA,UAAU5J,CAAG,CAAES,CAAK,CAAE,CACpB,IAAMqJ,EAAW9J,GAAO,IAAI,CAAC,kBAAkB,EAC3C,KAAI,CAAC,MAAM,CAAC8J,EAAS,EACvB7E,QAAQ,IAAI,CAAC,CAAC,WAAW,EAAE6E,EAAS,+BAA+B,CAAC,EAEtE,IAAI,CAAC,MAAM,CAACA,EAAS,CAAGrJ,EACxB,IAAI,CAAC,WAAW,EAClB,CACA,gBAAgBsJ,CAAM,CAAEhF,CAAK,CAAE,CAC7B,IAAI,CAAC,MAAM,CAAGgF,EACd,IAAI,CAAC,YAAY,CAAGhF,CACtB,CACA,8BAA8B8E,CAAS,CAAE,CACvC,IAAMG,EAAoB,AAAqB,UAArB,OAAOH,EAAyBA,EAAY,IAAI,CAAC,gBAAgB,CAC3F,GAAI,AAA6B,UAA7B,OAAOG,EACT,OAEF,IAAMC,EAAa,IAAI,CAAC,cAAc,CAACD,EAAkB,AACrD,KAAI,CAAC,kBAAkB,EACzB,IAAI,CAAC,kBAAkB,CAACC,EAE5B,CACA,MAAM,cAAcC,CAAK,CAAEC,CAAW,CAAEpF,CAAK,CAAE,CAC7C,IAAI,CAAC,cAAc,CAACmF,EAAM,CAAC,MAAM,CAAGC,EAChCpF,GACF,KAAI,CAAC,cAAc,CAACmF,EAAM,CAAC,KAAK,CAAGnF,CAAI,EAEzC,IAAI,CAAC,6BAA6B,CAACmF,EACrC,CACA,aAAaL,CAAS,CAAE,CACtB,IAAI,CAAC,gBAAgB,CAAGA,CAC1B,CACA,aAAc,CACZ,GAAIxK,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAI,IAAI,CAAC,MAAM,CAAE,CAClD,IAAM+K,EAAS,AAAC,GAAGd,EAAiB,OAAO,AAAD,EAAGK,EAAQ,GAAG,GAAI,IAAI,CAAC,MAAM,EACjEU,EAAY,AAAC,GAAGf,EAAiB,OAAO,AAAD,EAAGc,GAC3C,AAAC,GAAG3H,EAAe,UAAU,AAAD,EAAG4H,IAClC,AAAC,GAAG5H,EAAe,SAAS,AAAD,EAAG4H,EAAW,CAAE,UAAW,EAAK,GAE7D,AAAC,GAAG5H,EAAe,aAAa,AAAD,EAAG2H,EAAQE,KAAK,SAAS,CAAC,IAAI,CAAC,MAAM,CAAE,KAAK,EAAG,GAChF,CACF,CACA,MAAM,SAASL,CAAU,CAAEM,CAAK,CAAE,CAChC,GAAM,CAAEC,KAAAA,CAAI,CAAE,CAAGP,EAEjB,IAAK,IAAMQ,IADX,AAAC,GAAGnJ,EAAc,MAAM,AAAD,EAAGkJ,EAAM,wBACJA,EAAM,CAEhCP,EAAW,WAAW,CADFxL,OAAO,QAAQ,CAACgM,EAAe,IAEnD,IAAMC,EAAWF,CAAI,CAACC,EAAc,CACpC,GAAI,aAAcC,GAAY,OAAQA,EAAU,CAE9C,IAAMC,EAASC,AADIF,EACO,QAAQ,EAAIE,AADnBF,EAC8B,EAAE,CACnD,AAAC,GAAGpJ,EAAc,MAAM,AAAD,EAAGqJ,EAAQ,oCAClC,AAAC,GAAGrJ,EAAc,MAAM,AAAD,EACrB,AAAkB,UAAlB,OAAOqJ,EACP,wCAEF,MAAMJ,EAAM,QAAQ,CAACI,EAAQ,CAC3B,UAAWC,AARMF,EAQK,SAAS,AACjC,EACF,MAAO,GAAI,aAAcA,EAAU,CAEjC,IAAMC,EAASE,AADIH,EACO,QAAQ,CAC5BtK,EAAMyK,AAFOH,EAEI,YAAY,CACnC,AAAC,GAAGpJ,EAAc,MAAM,AAAD,EAAGqJ,EAAQ,+BAClC,AAAC,GAAGrJ,EAAc,MAAM,AAAD,EACrB,AAAkB,UAAlB,OAAOqJ,EACP,wCAEF,MAAMJ,EAAM,QAAQ,CAACI,EAAQvK,EAC/B,MAAO,GAAI,YAAasK,EAAU,CAEhC,IAAMC,EAASG,AADGJ,EACO,OAAO,CAC1B9I,EAAU,CACd,YAAakJ,AAHGJ,EAGO,WAAW,CAClC,mBAAoBI,AAJJJ,EAIc,kBAAkB,AAClD,EACA,AAAC,GAAGpJ,EAAc,MAAM,AAAD,EAAGqJ,EAAQ,8BAClC,AAAC,GAAGrJ,EAAc,MAAM,AAAD,EACrB,AAAkB,UAAlB,OAAOqJ,EACP,uCAEF,IAAMI,EAAc,MAAMR,EAAM,OAAO,CAACI,EAAQ/I,GAChD,IAAI,CAAC,SAAS,CAACkJ,AAZGJ,EAYO,IAAI,CAAEK,EACjC,MAAO,GAAI,aAAcL,EAAU,CAEjC,IAAMC,EAASK,AADIN,EACO,QAAQ,CAC5B9I,EAAU,CACd,YAAaoJ,AAHIN,EAGO,WAAW,CACnC,mBAAoBM,AAJHN,EAIc,kBAAkB,AACnD,EACA,AAAC,GAAGpJ,EAAc,MAAM,AAAD,EAAGqJ,EAAQ,6BAClC,AAAC,GAAGrJ,EAAc,MAAM,AAAD,EACrB,AAAkB,UAAlB,OAAOqJ,EACP,sCAEF,IAAMM,EAAe,MAAMV,EAAM,QAAQ,CAACI,EAAQ/I,GAClD,IAAI,CAAC,SAAS,CAACoJ,AAZIN,EAYO,IAAI,CAAEO,EAClC,MAAO,GAAI,aAAcP,EAAU,CAEjC,IAAMC,EAASO,AADIR,EACO,QAAQ,CAC5B9I,EAAU,CACd,YAAasJ,AAHIR,EAGO,WAAW,CACnC,mBAAoBQ,AAJHR,EAIc,kBAAkB,AACnD,EACA,AAAC,GAAGpJ,EAAc,MAAM,AAAD,EAAGqJ,EAAQ,6BAClC,AAAC,GAAGrJ,EAAc,MAAM,AAAD,EACrB,AAAkB,UAAlB,OAAOqJ,EACP,sCAEF,IAAMQ,EAAe,MAAMZ,EAAM,QAAQ,CAACI,EAAQ/I,GAClD,IAAI,CAAC,SAAS,CAACsJ,AAZIR,EAYO,IAAI,CAAES,EAClC,MAAO,GAAI,cAAeT,EAAU,CAElC,IAAMC,EAASS,AADKV,EACO,SAAS,CAC9B9I,EAAU,CACd,YAAawJ,AAHKV,EAGO,WAAW,CACpC,mBAAoBU,AAJFV,EAIc,kBAAkB,AACpD,EACA,AAAC,GAAGpJ,EAAc,MAAM,AAAD,EAAGqJ,EAAQ,8BAClC,AAAC,GAAGrJ,EAAc,MAAM,AAAD,EACrB,AAAkB,UAAlB,OAAOqJ,EACP,uCAEF,IAAMU,EAAgB,MAAMd,EAAM,SAAS,CAACI,EAAQ/I,GACpD,IAAI,CAAC,SAAS,CAACwJ,AAZKV,EAYO,IAAI,CAAEW,EACnC,MAAO,GAAI,aAAcX,EAAU,CAEjC,IAAMC,EAASW,AADIZ,EACO,QAAQ,CAClC,AAAC,GAAGpJ,EAAc,MAAM,AAAD,EAAGqJ,EAAQ,+BAClC,AAAC,GAAGrJ,EAAc,MAAM,AAAD,EACrB,AAAkB,UAAlB,OAAOqJ,EACP,wCAEF,IAAMY,EAAe,MAAMhB,EAAM,QAAQ,CAACI,GAC1C,IAAI,CAAC,SAAS,CAACW,AARIZ,EAQO,IAAI,CAAEa,EAClC,MAAO,GAAI,cAAeb,EAAU,CAElC,IAAMC,EAASa,AADKd,EACO,SAAS,CACpC,AAAC,GAAGpJ,EAAc,MAAM,AAAD,EAAGqJ,EAAQ,gCAClC,AAAC,GAAGrJ,EAAc,MAAM,AAAD,EACrB,AAAkB,UAAlB,OAAOqJ,EACP,yCAEF,IAAM5D,EAAUyE,AAPId,EAOQ,OAAO,AACnC,OAAMH,EAAM,SAAS,CAACI,EAAQ,CAAE,UAAW5D,CAAQ,EACrD,MAAO,GAAI,UAAW2D,EAAU,CAE9B,IAAMlH,EAAKiI,AADOf,EACG,KAAK,CACtBgB,EAAWlI,CACG,WAAd,OAAOA,GACTkI,CAAAA,EAAWjN,OAAO,QAAQ,CAAC+E,EAAI,GAAE,EAEnC,AAAC,GAAGlC,EAAc,MAAM,AAAD,EACrBoK,GAAYA,EAAW,EACvB,CAAC,6CAA6C,EAAElI,EAAG,CAAC,EAEtD,MAAM,IAAItB,QAAQ,AAACyJ,GAAaxJ,WAAWwJ,EAAUD,GACvD,MAAO,GAAI,UAAWhB,EAEpB,MAAMH,EAAM,KAAK,CAACqB,AADFlB,EACU,KAAK,CADfA,QAEX,GAAI,iBAAkBA,EAE3B,MAAMH,EAAM,YAAY,CAACsB,AADFnB,EACiB,YAAY,CAD7BA,QAElB,GAAI,YAAaA,EAEtB,MAAMH,EAAM,OAAO,CAACuB,AADFpB,EACY,OAAO,CADnBA,QAEb,GAAI,YAAaA,EAEtB,MAAMH,EAAM,OAAO,CAACwB,AADFrB,EACY,OAAO,CAAEqB,AADrBrB,EAC+B,MAAM,CADrCA,QAEb,GAAI,oBAAqBA,EAE9B,MAAMH,EAAM,eAAe,CACzByB,AAFwBtB,EAEN,eAAe,CACjCsB,AAHwBtB,EAGN,MAAM,CAHAA,QAMrB,GAAI,aAAcA,EAEvB,MAAMH,EAAM,QAAQ,CADDG,EACcuB,AADdvB,EACyB,MAAM,CAD/BA,QAEd,GAAI,eAAgBA,EAAU,CAEnC,IAAM3G,EAAS,MAAMwG,EAAM,kBAAkB,CAC3C2B,AAF6BxB,EAEN,UAAU,EAEnC,IAAI,CAAC,SAAS,CAACwB,AAJgBxB,EAIO,IAAI,CAAE3G,EAC9C,MAAO,GAAI,kBAAmB2G,EAE5B,MAAMH,EAAM,aAAa,CAAC4B,AADAzB,EACkB,aAAa,CAAE,CACzD,QAASyB,AAFezB,EAEG,OAAO,EAAI,EACxC,QAEA,MAAM,AAAI/F,MAAM,CAAC,kBAAkB,EAAE2F,KAAK,SAAS,CAACI,GAAU,CAAC,CAEnE,CACA,IAAI,CAAC,UAAU,CAAGH,EAAM,UAAU,AACpC,CACA,MAAM,KAAM,CACV,GAAM,CAAE1L,OAAAA,CAAM,CAAEuN,IAAAA,CAAG,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAE,CAAG,IAAI,CAAC,MAAM,CAInD,IAAI,CAAC,eAAe,CAAC,WACrB,IAAI/B,EAAQ,KACRgC,EAAS,EAAE,CACf,GAAI,CACF,GAAM,CAAE,MAAOC,CAAQ,CAAE,OAAQC,CAAS,CAAE,CAAG,MAAM,IAAI,CAAC,UAAU,CALrDC,AAFFN,GAAOvN,GACHwN,GAUXM,EAAyBpC,AAD/BA,CAAAA,EAAQiC,CAAO,EACsB,cAAc,AACnDjC,CAAAA,EAAM,cAAc,CAAG,AAACqC,IACF,YAAhB,IAAI,CAAC,MAAM,EACb,KAAI,CAAC,cAAc,CAAGA,CAAE,EAE1BD,IAAyBC,EAC3B,EACAL,EAAS,IACJE,GAAa,EAAE,CAClB,CACE,KAAM,+BACN,GAAI,KACElC,GACFA,CAAAA,EAAM,cAAc,CAAGoC,CAAqB,CAEhD,CACF,EACD,AACH,CAAE,MAAOnQ,EAAG,CACV,IAAI,CAAC,eAAe,CAAC,QAASA,GAC9B,MACF,CACA,IAAI,CAAC,SAAS,CAAG+N,EACjB,IAAIV,EAAY,EAChB,IAAI,CAAC,eAAe,CAAC,WACrB,IAAIgD,EAAY,GAChB,KAAOhD,EAAYyC,EAAM,MAAM,EAAE,CAC/B,IAAMrC,EAAa,IAAI,CAAC,cAAc,CAACJ,EAAU,CACjD,IAAI,CAAC,aAAa,CAACA,EAAW,WAC9B,IAAI,CAAC,YAAY,CAACA,GAClB,GAAI,CACF,MAAM,IAAI,CAAC,QAAQ,CAACI,EAAY,IAAI,CAAC,SAAS,EAC9C,IAAI,CAAC,aAAa,CAACJ,EAAW,OAChC,CAAE,MAAOrN,EAAG,CAEV,GADA,IAAI,CAAC,aAAa,CAACqN,EAAW,QAASrN,GACnCyN,EAAW,eAAe,MACvB,CACL,IAAI,CAAC,UAAU,CAAGM,EAAM,UAAU,CAClCsC,EAAY,GACZ,KACF,CACF,CACA,IAAI,CAAC,UAAU,CAAGtC,EAAM,UAAU,CAClCV,GACF,CAOA,IAAK,IAAMiD,KANPD,EACF,IAAI,CAAC,eAAe,CAAC,SAErB,IAAI,CAAC,eAAe,CAAC,QAEvB,IAAI,CAAC,cAAc,CAAG,GACLN,GACf,GAAI,CACF,MAAMO,EAAG,EAAE,EACb,CAAE,MAAOtQ,EAAG,CACZ,CAEJ,CACF,EAGqBmL,EAAQ,EAAQ,QAGrC,IAAInB,EAAgB,EAAQ,MACxBuG,EAAkBpF,EAAQ,EAAQ,QA0DlCqF,EAAiB,EAAQ,OACzBC,EAAoB,EAAQ,OAC5BC,EAAc,EAAQ,OACtBC,EAAiB,EAAQ,OACzBC,EAAiB,EAAQ,MAGzBC,EAAc,EAAQ,OACtBC,EAAmB,EAAQ,OAC3BnM,EAAgB,EAAQ,OACxBoM,EAAmB,EAAQ,OAC3BC,GAAgB,EAAQ,OACxBC,GAAgB,EAAQ,MAG5B,SAASC,GAAQ9D,CAAI,EACnB,OAAOA,EAAK,OAAO,EAAIA,AAAiB,SAAjBA,EAAK,OAAO,CAAc,CAAC,EAAEA,EAAK,IAAI,CAAC,GAAG,EAAEA,EAAK,OAAO,EAAI,GAAG,CAAC,CAAGA,EAAK,IAAI,AACrG,CAiBA,SAAS+D,GAAeC,CAAM,SAC5B,AAAKA,EAGD,AAAkB,UAAlB,OAAOA,EACFA,EAEFA,EAAO,MAAM,CALX,EAMX,CACA,SAASC,GAAeC,CAAW,SACjC,AAAKA,EAGE,CAAC,EAAEA,EAAY,SAAS,EAAI,OAAO,EAAE,EAAEA,EAAY,UAAU,EAAI,OAAO,EAAE,EAAEA,EAAY,QAAQ,EAAI,mBAAmB,CAAC,CAFtH,EAGX,CACA,SAASC,GAAaC,CAAI,CAAErD,CAAM,SAChC,AAAIA,EACK,CAAC,EAAEqD,EAAK,GAAG,EAAErD,EAAO,CAAC,CAEvBqD,CACT,CAgCA,IAAI5M,GAAwB;AAC5B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAGG6M,GAAQ,AAAC,GAAGT,GAAc,QAAQ,AAAD,EAAG,sBAEpCU,GAAgB,AAACtF,GACZA,AAAkB,YAAlBA,EAAK,QAAQ,CAElBuF,GAAmB,MACrB,YAAYvF,CAAI,CAAEwF,CAAO,CAAEC,CAAI,CAAE,CAC/B,IAAI,CAAC,mBAAmB,CAAG,EAAE,CAC7B,IAAI,CAAC,IAAI,CAAGzF,EACZ,IAAI,CAAC,OAAO,CAAGwF,EACf,IAAI,CAAC,SAAS,CAAGC,EAAK,SAAS,CAC/B,IAAI,CAAC,mBAAmB,CAAGA,GAAM,WACnC,CACA,MAAM,iBAAiBC,CAAM,CAAE,CAC7B,IAAMpI,EAAS,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAO/C,MANa,CACX,KAAM,aACN,GAAI5H,KAAK,GAAG,GACZ,WAAY4H,EACZoI,OAAAA,CACF,CAEF,CACA,MAAM,gBAAgBC,CAAW,CAAEhI,CAAO,CAAE,CAC1C,IAAIiI,EAAYjI,GAAS,GACzB,GAAIA,GAAS,YAAY,WAAagH,EAAiB,QAAQ,CAAC,QAAQ,CAAE,CACxE,MAAM,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,UACtC,IAAMkB,EAAO,AAAC,GAAGnB,EAAiB,gCAAgC,AAAD,EAC/DiB,EAAY,IAAI,CAChB,CACE,EAAGhI,EAAQ,MAAM,CAAC,EAAE,CACpB,EAAGA,EAAQ,MAAM,CAAC,EAAE,AACtB,EACA,CACE,sBAAuB,GACvB,uBAAwB,EAC1B,GAEEkI,GAAM,GACRD,EAAYC,EAAK,EAAE,CAEnBR,GACE,+DACA1H,EAGN,CACA,GAAKiI,EAGL,GAAI,CAEF,OADe,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAACA,EAE/C,CAAE,MAAOzJ,EAAO,CACdkJ,GAAM,wBAAyBlJ,EACjC,CACF,CACA,8BAA8B2J,CAAS,CAAEC,EAAuB,EAAK,CAAE,CA+BrE,MA9B2B,CACzB,GAAGD,CAAS,CACZ,SAAU,MAAOE,EAAOC,EAAS,GAAGrH,KAClC,IAAMsH,EAAW,EAAE,CACb,CAAElF,KAAAA,CAAI,CAAE,CAAGiF,CACjBjF,CAAAA,EAAK,QAAQ,CAAGkF,EAChB,IAAMC,EAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,CAAC,OAAO,EAAEnF,EAAK,IAAI,CAAC,CAAC,EAC9DkF,EAAS,IAAI,CAACC,GACd,IAAMhL,EAAS,MAAM2K,EAAU,QAAQ,CAACE,EAAOC,KAAYrH,GAe3D,GAduB,WAAnBkH,EAAU,IAAI,EAChB,MAAMxM,QAAQ,GAAG,CAAC,CAChB,AAAC,WAEC,GADA,MAAM,AAAC,GAAGf,EAAc,KAAK,AAAD,EAAG,KAC3B,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAChC,GAAI,CACF,MAAM,IAAI,CAAC,IAAI,CAAC,oBAAoB,EACtC,CAAE,MAAO4D,EAAO,CAChB,CAEJ,KACA,AAAC,GAAG5D,EAAc,KAAK,AAAD,EAAG,KAC1B,EAECwN,EAAsB,CACxB,IAAMK,EAAQ,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAC1CF,EAAS,IAAI,CAACE,EAChB,CACA,OAAOjL,CACT,CACF,CAEF,CACA,MAAM,wBAAwBkL,CAAK,CAAEZ,CAAI,CAAE,CACzC,IAAM/B,EAAQ,EAAE,CAgahB,OA/ZA2C,EAAM,OAAO,CAAC,AAACC,IACb,GAAIA,AAAe,WAAfA,EAAM,IAAI,CAAe,CAC3B,GAAIA,AAAiB,OAAjBA,EAAM,MAAM,EAAaA,EAAM,MAAM,EAAE,KAAO,MAAQA,EAAM,MAAM,EAAE,KAAO,OAC7E,OAEF,IAAMC,EAAW,CACf,KAAM,UACN,QAAS,SACT,MAAOD,EAAM,MAAM,CAAG,CACpB,GAAGA,EAAM,MAAM,CACf,UAAWb,GAAM,SACnB,EAAI,KAAK,EACT,QAASa,EAAM,OAAO,CACtB,OAAQA,EAAM,MAAM,CACpB,SAAU,MAAON,EAAOQ,KACtB,IAKIC,EACAC,EAsDAC,EA5DE,CAAE3F,KAAAA,CAAI,CAAE,CAAGwF,EACjB,AAAC,GAAG3B,GAAc,MAAM,AAAD,EACrBmB,GAAO,QAAUA,GAAO,IAAMA,GAAO,KACrC,iDAYF,IAAI,CAAC,OAAO,CAAC,iBAAiB,CARR,AAACY,IACrBH,EAAcG,EACdF,EAAQE,GAAM,UAAU,MACxB5F,EAAK,GAAG,CAAG,CACT,KAAMyF,CACR,EACAzF,EAAK,KAAK,CAAG0F,CACf,EAEA,IAAMG,EAAWnR,KAAK,GAAG,GACnBiQ,EAAc,MAAM,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,SAC1D3E,CAAAA,EAAK,WAAW,CAAG2E,EAOnB3E,EAAK,QAAQ,CAAG,CANG,CACjB,KAAM,aACN,GAAI6F,EACJ,WAAYlB,EAAY,gBAAgB,CACxC,OAAQ,eACV,EAC4B,CAC5B,IAAImB,EAAe,GACbC,EAAcf,EAAM,MAAM,CAC1BgB,EAAoB,IAAI,CAAC,SAAS,EAAE,iBAAiBD,GACrDlH,EAASmH,GAAmB,cAAc,OAC5CC,EAAmB,KACvB,GAAI,CACF,GAAIpH,GAAQ,QAAU,IAAI,CAAC,SAAS,EAAE,mBAAqBmG,GAAO,YAAc,GAC9E,IAAK,IAAIlS,EAAI,EAAGA,EAAI+L,EAAO,MAAM,CAAE/L,IAAK,CACtC,IAAMoT,EAAW,MAAM,IAAI,CAAC,IAAI,CAAC,qBAAqB,CACpDrH,CAAM,CAAC/L,EAAE,EAEX,GAAIoT,GAAU,GAAI,CAChBD,EAAmBC,EACnB7B,GAAM,wBAAyB0B,GAC/BD,EAAe,GACfzB,GACE,6DACAxF,CAAM,CAAC/L,EAAE,CACToT,GAAU,IAEZ,KACF,CACF,CAEJ,CAAE,MAAO/K,EAAO,CACdkJ,GAAM,oCAAqClJ,EAC7C,CACA,IAAMa,EAAYtH,KAAK,GAAG,GACpBiI,EAAUsJ,GAChBE,AArpBZ,SAA8BC,CAAe,CAAElK,CAAI,EACjD,GAAKkK,GAGL,GAAIA,EAAgB,EAAE,CACpB,MAAO,AAAC,GAAG3O,EAAiB,oBAAoB,AAAD,EAAG2O,EAAgB,EAAE,EAEtE,GAAIA,EAAgB,IAAI,CAAE,CACxB,IAAMC,EAAiB,CACrB,EAAG7R,KAAK,KAAK,CAAC,AAAC4R,CAAAA,EAAgB,IAAI,CAAC,EAAE,CAAGA,EAAgB,IAAI,CAAC,EAAE,AAAD,EAAK,GACpE,EAAG5R,KAAK,KAAK,CAAC,AAAC4R,CAAAA,EAAgB,IAAI,CAAC,EAAE,CAAGA,EAAgB,IAAI,CAAC,EAAE,AAAD,EAAK,EACtE,EACIzJ,EAAU,AAAC,GAAGyB,EAAgB,gCAAgC,AAAD,EAAGlC,EAAMmK,GAI1E,OAHK1J,GACHA,CAAAA,EAAU,AAAC,GAAGlF,EAAiB,yBAAyB,AAAD,EAAG4O,EAAc,EAEnE1J,CACT,EAEF,EAkoBiCqI,EAAOL,EAAY,IAAI,GAC5C,AAAC,OAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAACK,EAAO,CAChC,QAASL,CACX,EAAC,EAAG,OAAO,CACL2B,EAAS5R,KAAK,GAAG,GAAKsH,EAE5B,GAAIW,GAAW,IAAI,CAAC,SAAS,EAAI,CAACmJ,GAAgBd,GAAO,YAAc,GAAO,CAC5E,IAAMuB,EAAgB,MAAM,IAAI,CAAC,eAAe,CAC9C5B,EACAhI,GAEE4J,GAAe,QACjBZ,EAAgBY,EAChB,IAAI,CAAC,SAAS,CAAC,yBAAyB,CACtC,CACE,KAAM,SACN,OAAQR,EACR,OAAQQ,CACV,EACAP,IAGF3B,GACE,yCACA0B,EACAQ,EAGN,CACA,GAAI,CAAC5J,EACH,MAAM,AAAI5B,MAAM,CAAC,mBAAmB,EAAEiK,EAAM,MAAM,CAAC,CAAC,EAEtD,MAAO,CACL,OAAQ,CACNrI,QAAAA,CACF,EACAgI,YAAAA,EACA,MAAO,CACL,IAAKmB,EACL,eAAgBjH,EAChB8G,cAAAA,CACF,EACAW,OAAAA,CACF,CACF,CACF,EACA5D,EAAM,IAAI,CAAC6C,EACb,MAAO,GAAID,AAAe,WAAfA,EAAM,IAAI,EAAiBA,AAAe,uBAAfA,EAAM,IAAI,CAA2B,CAEzE,IAAMkB,EAAa,CACjB,KAAM,UACN,QAAS,SACT,MAAOC,AAJUnB,EAIC,KAAK,CACvB,QAASmB,AALQnB,EAKG,OAAO,CAC3B,OAAQmB,AANSnB,EAME,MAAM,CACzB,SAAU,MAAON,EAAOQ,KACtB,IACIC,EADE,CAAEzF,KAAAA,CAAI,CAAE,CAAGwF,CAKjB,KAAI,CAAC,OAAO,CAAC,iBAAiB,CAHR,AAACI,IACrBH,EAAcG,CAChB,EAEA,IAAMc,EAAY,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CACzCD,AAfanB,EAeF,KAAK,CAAC,SAAS,EAE5B,GAAI,CAACoB,EAAU,IAAI,CAAE,CACnB,GAAIpB,AAAe,WAAfA,EAAM,IAAI,CAKZ,MAJAtF,EAAK,MAAM,CAAG0G,EACd1G,EAAK,GAAG,CAAG,CACT,KAAMyF,CACR,EACM,AAAI1K,MACR2L,EAAU,OAAO,EAAI,kCAGzB1G,CAAAA,EAAK,KAAK,CAAG0G,EAAU,OAAO,AAChC,CACA,MAAO,CACL,OAAQA,EACR,IAAK,CACH,KAAMjB,CACR,EACA,MAAOiB,EAAU,KAAK,AACxB,CACF,CACF,EACAhE,EAAM,IAAI,CAAC8D,EACb,MAAO,GAAIlB,AAAe,UAAfA,EAAM,IAAI,CAAc,CACjC,IAAMqB,EAAkB,CACtB,KAAM,SACN,QAAS,QACT,MAAOrB,EAAM,KAAK,CAClB,QAASA,EAAM,OAAO,CACtB,OAAQA,EAAM,MAAM,CACpB,SAAU,MAAOsB,EAAW,CAAEjK,QAAAA,CAAO,CAAE,IACjCA,CAAAA,CAAAA,IACF,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAACA,GACvB,AAACiK,GAAcA,EAAU,KAAK,CAF1B,GAMV,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAACA,EAAU,KAAK,CAAE,CAC7C,oBAAqBA,EAAU,mBAAmB,AACpD,EACF,CACF,EACAlE,EAAM,IAAI,CAACiE,EACb,MAAO,GAAIrB,AAAe,kBAAfA,EAAM,IAAI,CAAsB,CACzC,IAAMuB,EAA0B,CAC9B,KAAM,SACN,QAAS,gBACT,MAAOvB,EAAM,KAAK,CAClB,QAASA,EAAM,OAAO,CACtB,OAAQA,EAAM,MAAM,CACpB,SAAU,MAAOsB,IACf,IAAMpO,EAAOsO,AAlXzB,SAAwBjQ,CAAK,EAC3B,IAAM2B,EAAO3E,MAAM,OAAO,CAACgD,GAASA,EAAQ,CAACA,EAAM,CACnD,OAAO2B,EAAK,MAAM,CAAC,CAACuO,EAAKjS,KACvB,IAAMkS,EAAcxO,EAAK,QAAQ,CAAC,SAAWA,EAAK,QAAQ,CAAC,kBAC3D,AAAIwO,GAAgBlS,CAAAA,AAAM,MAANA,GAAaA,AAAM,MAANA,CAAQ,EAChCiS,EAAI,MAAM,CAAC,CAAC,CAAE,IAAKjS,EAAG,QAAS,WAAY,EAAE,EAElDkS,GAAgBlS,CAAAA,AAAM,MAANA,GAAaA,AAAM,MAANA,CAAQ,EAChCiS,EAAI,MAAM,CAAC,CAAC,CAAE,IAAKjS,EAAG,QAAS,MAAO,EAAE,EAE7CkS,GAAgBlS,CAAAA,AAAM,MAANA,GAAaA,AAAM,MAANA,CAAQ,EAChCiS,EAAI,MAAM,CAAC,CAAC,CAAE,IAAKjS,EAAG,QAAS,OAAQ,EAAE,EAE3CiS,EAAI,MAAM,CAAC,CAAC,CAAE,IAAKjS,CAAE,EAAE,CAChC,EAAG,EAAE,CACP,EAmWwC8R,EAAU,KAAK,CAC3C,OAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAACpO,EACjC,CACF,EACAkK,EAAM,IAAI,CAACmE,EACb,MAAO,GAAIvB,AAAe,QAAfA,EAAM,IAAI,CAAY,CAC/B,IAAM2B,EAAgB,CACpB,KAAM,SACN,QAAS,MACT,QAAS3B,EAAM,OAAO,CACtB,OAAQA,EAAM,MAAM,CACpB,SAAU,MAAON,EAAO,CAAErI,QAAAA,CAAO,CAAE,IACjC,AAAC,GAAGkH,GAAc,MAAM,AAAD,EAAGlH,EAAS,iCACnC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAACA,EAAQ,MAAM,CAAC,EAAE,CAAEA,EAAQ,MAAM,CAAC,EAAE,CAClE,CACF,EACA+F,EAAM,IAAI,CAACuE,EACb,MAAO,GAAI3B,AAAe,eAAfA,EAAM,IAAI,CAAmB,CACtC,IAAM4B,EAAuB,CAC3B,KAAM,SACN,QAAS,aACT,QAAS5B,EAAM,OAAO,CACtB,OAAQA,EAAM,MAAM,CACpB,SAAU,MAAON,EAAO,CAAErI,QAAAA,CAAO,CAAE,IACjC,AAAC,GAAGkH,GAAc,MAAM,AAAD,EAAGlH,EAAS,yCACnC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CACzBA,EAAQ,MAAM,CAAC,EAAE,CACjBA,EAAQ,MAAM,CAAC,EAAE,CACjB,CAAE,OAAQ,OAAQ,EAEtB,CACF,EACA+F,EAAM,IAAI,CAACwE,EACb,MAAO,GAAI5B,AAAe,SAAfA,EAAM,IAAI,CAAa,CAChC,IAAM6B,EAAiB,CACrB,KAAM,SACN,QAAS,OACT,MAAO7B,EAAM,KAAK,CAClB,QAASA,EAAM,OAAO,CACtB,OAAQA,EAAM,MAAM,CACpB,SAAU,MAAOsB,IACf,AAAC,GAAG/C,GAAc,MAAM,AAAD,EACrB+C,GAAW,WAAaA,GAAW,QACnC,mCAEF,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAACA,EAAU,SAAS,CAAEA,EAAU,OAAO,CACnE,CACF,EACAlE,EAAM,IAAI,CAACyE,EACb,MAAO,GAAI7B,AAAe,UAAfA,EAAM,IAAI,CAAc,CACjC,IAAM8B,EAAkB,CACtB,KAAM,SACN,QAAS,QACT,QAAS9B,EAAM,OAAO,CACtB,OAAQA,EAAM,MAAM,CACpB,SAAU,MAAON,EAAO,CAAErI,QAAAA,CAAO,CAAE,IACjC,AAAC,GAAGkH,GAAc,MAAM,AAAD,EAAGlH,EAAS,mCACnC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAACA,EAAQ,MAAM,CAAC,EAAE,CAAEA,EAAQ,MAAM,CAAC,EAAE,CACjE,CACF,EACA+F,EAAM,IAAI,CAAC0E,EACb,MAAO,GAAI9B,AAAe,WAAfA,EAAM,IAAI,CAAe,CAClC,IAAM+B,EAAmB,CACvB,KAAM,SACN,QAAS,SACT,MAAO/B,EAAM,KAAK,CAClB,QAASA,EAAM,OAAO,CACtB,OAAQA,EAAM,MAAM,CACpB,SAAU,MAAOsB,EAAW,CAAEjK,QAAAA,CAAO,CAAE,IACrC,IAAMJ,EAAgBI,EAAU,CAC9B,KAAMA,EAAQ,MAAM,CAAC,EAAE,CACvB,IAAKA,EAAQ,MAAM,CAAC,EAAE,AACxB,EAAI,KAAK,EACH2K,EAAoBV,GAAW,WACrC,GAAIU,AAAsB,aAAtBA,EACF,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC/K,QAC1B,GAAI+K,AAAsB,gBAAtBA,EACT,MAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC/K,QAC7B,GAAI+K,AAAsB,eAAtBA,EACT,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC/K,QAC5B,GAAI+K,AAAsB,cAAtBA,EACT,MAAM,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC/K,QAC3B,GAAI+K,AAAsB,SAAtBA,GAAiCA,EA4B1C,MAAM,AAAIvM,MACR,CAAC,2BAA2B,EAAEuM,EAAkB,aAAa,EAAE5G,KAAK,SAAS,CAC3EkG,GACA,CAAC,MA/BwD,CAC7D,GAAIA,GAAW,YAAc,QAAWA,GAAcA,EAAU,SAAS,CAKlE,GAAIA,AAAwB,OAAxBA,EAAU,SAAS,CAC5B,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CACtBA,EAAU,QAAQ,EAAI,KAAK,EAC3BrK,QAEG,GAAIqK,AAAwB,SAAxBA,EAAU,SAAS,CAC5B,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CACxBA,EAAU,QAAQ,EAAI,KAAK,EAC3BrK,QAEG,GAAIqK,AAAwB,UAAxBA,EAAU,SAAS,CAC5B,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CACzBA,EAAU,QAAQ,EAAI,KAAK,EAC3BrK,QAGF,MAAM,AAAIxB,MACR,CAAC,0BAA0B,EAAE6L,EAAU,SAAS,CAAC,CAAC,OArBpD,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CACxBA,GAAW,UAAY,KAAK,EAC5BrK,EAsBJ,OAAM,AAAC,GAAGhF,EAAc,KAAK,AAAD,EAAG,IACjC,CAOF,CACF,EACAmL,EAAM,IAAI,CAAC2E,EACb,MAAO,GAAI/B,AAAe,UAAfA,EAAM,IAAI,CAAc,CACjC,IAAMiC,EAAkB,CACtB,KAAM,SACN,QAAS,QACT,MAAOjC,EAAM,KAAK,CAClB,QAASA,EAAM,OAAO,CACtB,OAAQA,EAAM,MAAM,CACpB,SAAU,MAAOsB,IACf,MAAM,AAAC,GAAGrP,EAAc,KAAK,AAAD,EAAGqP,GAAW,QAAU,IACtD,CACF,EACAlE,EAAM,IAAI,CAAC6E,EACb,MAAO,GAAIjC,AAAe,UAAfA,EAAM,IAAI,CAAc,CACjC,IAAMkC,EAAkB,CACtB,KAAM,SACN,QAAS,QACT,MAAOlC,EAAM,KAAK,CAClB,QAASA,EAAM,OAAO,EAAIA,EAAM,KAAK,EAAE,QACvC,OAAQA,EAAM,MAAM,CACpB,SAAU,UACR,MAAM,AAAIvK,MACRuK,GAAO,SAAWA,EAAM,KAAK,EAAE,SAAW,wBAE9C,CACF,EACA5C,EAAM,IAAI,CAAC8E,EACb,MAAO,GAAIlC,AAAe,2BAAfA,EAAM,IAAI,CAA+B,CAClD,IAAMmC,EAAoC,CACxC,KAAM,SACN,QAAS,yBACT,MAAO,KACP,QAASnC,EAAM,KAAK,EAAE,OACtB,OAAQA,EAAM,MAAM,CACpB,SAAU,UACV,CACF,EACA5C,EAAM,IAAI,CAAC+E,EACb,MAAO,GAAInC,AAAe,aAAfA,EAAM,IAAI,CAAiB,CACpC,IAAMoC,EAAqB,CACzB,KAAM,SACN,QAAS,WACT,MAAO,KACP,QAASpC,EAAM,OAAO,CACtB,OAAQA,EAAM,MAAM,CACpB,SAAU,MAAON,IACjB,CACF,EACAtC,EAAM,IAAI,CAACgF,EACb,MAAO,GAAIpC,AAAe,sBAAfA,EAAM,IAAI,CAA0B,CAC7C,IAAMqC,EAA8B,CAClC,KAAM,SACN,QAAS,oBACT,MAAO,KACP,QAASrC,EAAM,OAAO,CACtB,OAAQA,EAAM,MAAM,CACpB,SAAU,MAAON,IACf,AAAC,GAAGnB,GAAc,MAAM,AAAD,EACrBS,GAAc,IAAI,CAAC,IAAI,EACvB,iDAEF,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,EACtB,CACF,EACA5B,EAAM,IAAI,CAACiF,EACb,MAAO,GAAIrC,AAAe,sBAAfA,EAAM,IAAI,CAA0B,CAC7C,IAAMsC,EAA8B,CAClC,KAAM,SACN,QAAS,oBACT,MAAO,KACP,QAAStC,EAAM,OAAO,CACtB,OAAQA,EAAM,MAAM,CACpB,SAAU,MAAON,IACf,AAAC,GAAGnB,GAAc,MAAM,AAAD,EACrBS,GAAc,IAAI,CAAC,IAAI,EACvB,iDAEF,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,EACtB,CACF,EACA5B,EAAM,IAAI,CAACkF,EACb,MAAO,GAAItC,AAAe,4BAAfA,EAAM,IAAI,CAAgC,CACnD,IAAMuC,EAAoC,CACxC,KAAM,SACN,QAAS,0BACT,MAAO,KACP,QAASvC,EAAM,OAAO,CACtB,OAAQA,EAAM,MAAM,CACpB,SAAU,MAAON,IACf,AAAC,GAAGnB,GAAc,MAAM,AAAD,EACrBS,GAAc,IAAI,CAAC,IAAI,EACvB,wDAEF,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAC5B,CACF,EACA5B,EAAM,IAAI,CAACmF,EACb,MACE,MAAM,AAAI9M,MAAM,CAAC,kCAAkC,EAAEuK,EAAM,IAAI,CAAC,CAAC,CAErE,GAYO,CACL,MAZmB5C,EAAM,GAAG,CAC5B,CAAC1C,EAAMM,IACL,AAAIN,AAAc,WAAdA,EAAK,IAAI,CACJ,IAAI,CAAC,6BAA6B,CACvCA,EACAM,IAAUoC,EAAM,MAAM,CAAG,GAGtB1C,EAKX,CACF,CACA,MAAM,qBAAqB8H,CAAe,CAAE,CAC1C,IAAMjC,EAAWnR,KAAK,GAAG,GACnBiQ,EAAc,MAAM,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,UACpDoD,EAAa,CACjB,KAAM,aACN,GAAIlC,EACJ,WAAYlB,EAAY,gBAAgB,CACxC,OAAQ,iBACV,EAGA,OAFAmD,EAAgB,IAAI,CAAC,QAAQ,CAAG,CAACC,EAAW,CAC5CD,EAAgB,IAAI,CAAC,WAAW,CAAGnD,EAC5B,CACLA,YAAAA,CACF,CACF,CACA,MAAM,uBAAuBqD,CAAe,CAAEC,CAAU,CAAE,CACxD,IAAMC,EAAe,IAAIzE,EAAY,QAAQ,CAACU,GAAa,SAAU6D,GAAkB,CACrF,YAAa,IAAI,CAAC,mBAAmB,AACvC,GAyBA,OAFA,MAAME,EAAa,MAAM,CAtBZ,CACX,KAAM,WACN,QAAS,WACT,OAAQ,KACR,MAAO,CACLF,gBAAAA,CACF,EACA,SAAU,MAAOhD,EAAO8C,KACtB,MAAM,IAAI,CAAC,oBAAoB,CAACA,GACzB,CACL,OAAQ,CACN,QAAS,EAAE,CACX,mCAAoC,GACpC,IAAK,GACLG,WAAAA,CACF,EACA,MAAO,CACL,IAAK,EACP,CACF,EAEJ,GAEA,MAAMC,EAAa,KAAK,GACjB,CACL,SAAUA,CACZ,CACF,CACA,uBAAuBF,CAAe,CAAEG,CAAG,CAAEC,CAAa,CAAE,CAgG1D,MA/Fa,CACX,KAAM,WACN,QAAS,OACT,OAAQ,KACR,MAAO,CACLJ,gBAAAA,EACAG,IAAAA,CACF,EACA,SAAU,MAAOnD,EAAO8C,KACtB,IAAM9L,EAAYtH,KAAK,GAAG,GACpB,CAAEiQ,YAAAA,CAAW,CAAE,CAAG,MAAM,IAAI,CAAC,oBAAoB,CAACmD,GAClDO,EAAa,MAAM,AAAC,GAAG5E,EAAY,IAAI,AAAD,EAAGuB,EAAM,eAAe,CAAE,CACpE,QAASL,EACT,IAAKK,EAAM,GAAG,CACdoD,cAAAA,EACA,SAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,AAC9B,GACM,CACJE,QAAAA,CAAO,CACP,IAAKC,CAAI,CACTC,mCAAAA,CAAkC,CAClCrN,MAAAA,CAAK,CACLuK,MAAAA,CAAK,CACL+C,YAAAA,CAAW,CACX,MAAOC,CAAM,CACd,CAAGL,CACJP,CAAAA,EAAgB,IAAI,CAAC,GAAG,CAAG,CACzB,GAAGA,EAAgB,IAAI,CAAC,GAAG,EAAI,CAAC,CAAC,CACjCW,YAAAA,CACF,EACAX,EAAgB,IAAI,CAAC,KAAK,CAAGpC,EAC7B,IAAIiD,EAAiB,GACjBC,EAAgB,GAChBC,EAAmB,GACjBC,EAAe,AAACR,CAAAA,GAAW,EAAE,AAAD,EAAG,MAAM,CACzC,CAACvB,EAAKgC,KACJ,GAAIJ,EACF,OAAO5B,EAET,GAAIgC,EAAe,MAAM,CACnBH,GAAiBG,EAAe,MAAM,CAAC,IAAI,EAC7C,OAAOA,EAAe,MAAM,CAAC,IAAI,CAE/BA,EAAe,MAAM,CAAC,IAAI,EAC5BH,CAAAA,EAAgB,EAAG,EAErB7B,EAAI,IAAI,CAAC,CACP,KAAM,SACN,OAAQgC,EAAe,MAAM,CAC7B,MAAO,KACP,QAASA,EAAe,MAAM,CAAC,MAAM,AACvC,QACK,GAAI,CAAC,MAAO,QAAS,QAAQ,CAAC,QAAQ,CAACA,EAAe,IAAI,EAG/D,OAFAF,EAAmB,CAAC,2BAA2B,EAAEnI,KAAK,SAAS,CAACqI,GAAgB,CAAC,CACjFJ,EAAiB,GACV5B,EAGT,OADAA,EAAI,IAAI,CAACgC,GACFhC,CACT,EACA,EAAE,EAEJ,GAAI2B,EAAQ,CAEV,IAAMM,EAAgBN,EAAUO,CAAAA,AADhBvU,KAAK,GAAG,GACkBsH,CAAQ,EAC9CgN,EAAgB,GAClBF,EAAa,IAAI,CAAC,CAChB,KAAM,QACN,MAAO,CACL,OAAQE,CACV,EACA,OAAQ,IACV,EAEJ,CAOA,OAN4B,IAAxBF,EAAa,MAAM,EACrB,AAAC,GAAGjF,GAAc,MAAM,AAAD,EACrB,CAAC2E,GAAsCE,EACvCvN,EAAQ,CAAC,gBAAgB,EAAEA,EAAM,CAAC,CAAG0N,GAAoB,iBAGtD,CACL,OAAQ,CACN,QAASC,EACTN,mCAAAA,EACA,IAAKD,EACL,SAAUF,EAAW,QAAQ,AAC/B,EACA,MAAO,CACL,IAAK,EACP,EACA1D,YAAAA,CACF,CACF,CACF,CAEF,CACA,mBAAmBqD,CAAe,CAAE,CAqDlC,MApDa,CACX,KAAM,WACN,QAAS,OACT,OAAQ,KACR,MAAO,CACLA,gBAAAA,CACF,EACA,SAAU,MAAOhD,EAAO8C,KACtB,GAAM,CAAEnD,YAAAA,CAAW,CAAE,CAAG,MAAM,IAAI,CAAC,oBAAoB,CAACmD,GAClDoB,EAAe,MAAM,AAAC,GAAGxF,EAAiB,oBAAoB,AAAD,EACjEiB,EAAY,gBAAgB,CAC5BA,EAAY,IAAI,EAElB,IAAI,CAAC,yBAAyB,CAAC,CAC7B,KAAM,OACN,QAAS,CACP,CACE,KAAM,YACN,UAAW,CACT,IAAKuE,CACP,CACF,EACD,AACH,GACA,IAAMlN,EAAYtH,KAAK,GAAG,GACpB2T,EAAa,MAAM,AAAC,GAAG3E,EAAiB,WAAW,AAAD,EAAG,CACzD,gBAAiBsB,EAAM,eAAe,CACtC,oBAAqB,IAAI,CAAC,mBAAmB,CAC7C,KAAML,EAAY,IAAI,AACxB,GACM2B,EAAS5R,KAAK,GAAG,GAAKsH,EACtB,CAAEsM,QAAAA,CAAO,CAAEa,eAAAA,CAAc,CAAE,CAAGd,EAKpC,OAJA,IAAI,CAAC,yBAAyB,CAAC,CAC7B,KAAM,YACN,QAASc,CACX,GACO,CACL,OAAQ,CACNb,QAAAA,EACA,QAASA,CAAO,CAAC,EAAE,EAAE,QACrB,WAAYA,CAAO,CAAC,EAAE,CAAC,IAAI,CAC3B,mCAAoC,GACpC,IAAK,GACL,SAAUD,EAAW,QAAQ,AAC/B,EACA,MAAO,CACL,IAAK,EACP,EACA/B,OAAAA,CACF,CACF,CACF,CAEF,CACA,MAAM,SAAS8C,CAAK,CAAE/D,CAAK,CAAEZ,CAAI,CAAE,CACjC,IAAMyD,EAAe,IAAIzE,EAAY,QAAQ,CAAC2F,EAAO,CACnD,YAAa,IAAI,CAAC,mBAAmB,AACvC,GACM,CAAE1G,MAAAA,CAAK,CAAE,CAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC2C,EAAOZ,GAG5D,OAFA,MAAMyD,EAAa,MAAM,CAACxF,GAEnB,CACL,OAFa,MAAMwF,EAAa,KAAK,GAGrC,SAAUA,CACZ,CACF,CACA,MAAM,OAAOmB,CAAU,CAAEjB,CAAa,CAAE3D,CAAI,CAAE,CAC5C,IAAMyD,EAAe,IAAIzE,EAAY,QAAQ,CAACU,GAAa,SAAUkF,GAAa,CAChF,YAAa,IAAI,CAAC,mBAAmB,AACvC,GACIC,EAAe,IAAI,CAAC,sBAAsB,CAACD,EAAY,KAAK,EAAGjB,GAC/DmB,EAAc,EACZC,EAAU,EAAE,CACZC,EAAW,EAAE,CACnB,KAAOH,GAAc,KAefI,EAdJ,GAAIH,EA3tBiB,GA6tBnB,OAAO,IAAI,CAAC,eAAe,CAACrB,EADX,uEAGnB,OAAMA,EAAa,MAAM,CAACoB,GAC1B,IAAMjB,EAAa,MAAMH,EAAa,KAAK,GAC3C,GAAIA,EAAa,cAAc,GAC7B,MAAO,CACL,OAAQG,EACR,SAAUH,CACZ,EAEF,IAAM7C,EAAQgD,EAAW,OAAO,EAAI,EAAE,CACtCoB,EAAS,IAAI,IAAIpB,EAAW,QAAQ,EAAI,EAAE,EAE1C,GAAI,CACFqB,EAAc,MAAM,IAAI,CAAC,uBAAuB,CAACrE,EAAOZ,GACxDyD,EAAa,MAAM,CAACwB,EAAY,KAAK,CACvC,CAAE,MAAOvO,EAAO,CACd,OAAO,IAAI,CAAC,eAAe,CACzB+M,EACA,CAAC,4CAA4C,EAAE/M,EAAM,SAAS,EAAEuF,KAAK,SAAS,CAC5E2E,GACA,CAAC,CAEP,CAEA,GADA,MAAM6C,EAAa,KAAK,GACpBA,EAAa,cAAc,GAC7B,MAAO,CACL,OAAQ,KAAK,EACb,SAAUA,CACZ,EAKF,GAHIG,GAAY,KACdmB,EAAQ,IAAI,CAACnB,EAAW,GAAG,EAEzB,CAACA,EAAW,kCAAkC,CAAE,CAClDiB,EAAe,KACf,KACF,CACAA,EAAe,IAAI,CAAC,sBAAsB,CACxCD,EACAG,EAAQ,MAAM,CAAG,EAAI,CAAC,EAAE,EAAEA,EAAQ,IAAI,CAAC,QAAQ,CAAC,CAAG,KAAK,EACxDpB,GAEFmB,GACF,CACA,MAAO,CACL,OAAQ,CACNE,SAAAA,CACF,EACA,SAAUvB,CACZ,CACF,CACA,MAAM,aAAamB,CAAU,CAAE5E,CAAI,CAAE,CACnC,IAAMyD,EAAe,IAAIzE,EAAY,QAAQ,CAACU,GAAa,SAAUkF,GAAa,CAChF,YAAa,IAAI,CAAC,mBAAmB,AACvC,EACA,KAAI,CAAC,mBAAmB,CAAG,EAAE,CAE7B,IAAIM,EAAsB,EAEpBF,EAAW,EAAE,CACnB,KAAuBE,EAFC,IAEsC,KAaxDD,CAZJC,CAAAA,IACA,IAAML,EAAe,IAAI,CAAC,kBAAkB,CAACD,EAC7C,OAAMnB,EAAa,MAAM,CAACoB,GAC1B,IAAM9I,EAAS,MAAM0H,EAAa,KAAK,GACvC,GAAIA,EAAa,cAAc,GAC7B,MAAO,CACL,OAAQ,KAAK,EACb,SAAUA,CACZ,EAEF,IAAM7C,EAAQ7E,EAAO,OAAO,CAC5BiJ,EAAS,IAAI,IAAIjJ,EAAO,QAAQ,EAAI,EAAE,EAEtC,GAAI,CACFkJ,EAAc,MAAM,IAAI,CAAC,uBAAuB,CAACrE,EAAOZ,GACxDyD,EAAa,MAAM,CAACwB,EAAY,KAAK,CACvC,CAAE,MAAOvO,EAAO,CACd,OAAO,IAAI,CAAC,eAAe,CACzB+M,EACA,CAAC,4CAA4C,EAAE/M,EAAM,SAAS,EAAEuF,KAAK,SAAS,CAC5E2E,GACA,CAAC,CAEP,CAEA,GADA,MAAM6C,EAAa,KAAK,GACpBA,EAAa,cAAc,GAC7B,MAAO,CACL,OAAQ,KAAK,EACb,SAAUA,CACZ,EAEF,GAAI7C,AAAkB,aAAlBA,CAAK,CAAC,EAAE,CAAC,IAAI,CACf,KAEJ,CACA,MAAO,CACL,OAAQ,CACNoE,SAAAA,CACF,EACA,SAAUvB,CACZ,CACF,CACA,MAAM,oBAAoB9D,CAAI,CAAEwF,CAAM,CAAEC,CAAG,CAAE,CAC3C,IAAM3B,EAAe,IAAIzE,EAAY,QAAQ,CAC3CU,GACEC,EACA,AAAkB,UAAlB,OAAOwF,EAAsBA,EAASlJ,KAAK,SAAS,CAACkJ,IAEvD,CACE,YAAa,IAAI,CAAC,mBAAmB,AACvC,GAyCF,OAFA,MAAM1B,EAAa,MAAM,CAAC,IAAI,CAAC,6BAA6B,CArC1C,CAChB,KAAM,UACN,QAAS9D,EACT,OAAQ,KACR,MAAO,CACL,WAAYwF,CAEd,EACA,SAAU,MAAO5E,QACXS,CAIJ,KAAI,CAAC,OAAO,CAAC,iBAAiB,CAHR,AAACG,IACrBH,EAAcG,CAChB,EAEA,IAAMkE,EAAmB1F,AAAS,UAATA,EACrB2F,EAAcH,EACdE,GACFC,CAAAA,EAAc,CACZ,OAAQ,CAAC,EAAE3F,EAAK,EAAE,EAAEwF,EAAO,CAAC,AAC9B,GAEF,GAAM,CAAEI,KAAAA,CAAI,CAAEtE,MAAAA,CAAK,CAAE,CAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAChDqE,EACAF,GAEEI,EAAeD,EAKnB,OAJIF,IACF,AAAC,GAAGjG,GAAc,MAAM,AAAD,EAAGmG,GAAM,SAAW,KAAK,EAAG,2BACnDC,EAAeD,EAAK,MAAM,EAErB,CACL,OAAQC,EACR,IAAK,CAAE,KAAMxE,CAAY,EACzBC,MAAAA,CACF,CACF,CACF,IAGO,CACLlF,OAFa,MAAM0H,EAAa,KAAK,GAGrC,SAAUA,CACZ,CACF,CACA,MAAM,MAAM0B,CAAM,CAAEC,CAAG,CAAE,CACvB,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAASD,EAAQC,EACnD,CACA,MAAM,QAAQ9I,CAAM,CAAE8I,CAAG,CAAE,CACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC,UAAW9I,EAAQ8I,EACrD,CACA,MAAM,OAAO9I,CAAM,CAAE8I,CAAG,CAAE,CACxB,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAU9I,EAAQ8I,EACpD,CACA,MAAM,OAAO9I,CAAM,CAAE8I,CAAG,CAAE,CACxB,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAU9I,EAAQ8I,EACpD,CACA,MAAM,OAAOnD,CAAS,CAAE,CACtB,IAAMzO,EAAc,CAAC,QAAQ,EAAEyO,EAAU,CAAC,CACpCwB,EAAe,IAAIzE,EAAY,QAAQ,CAACU,GAAa,SAAUlM,GAAc,CACjF,YAAa,IAAI,CAAC,mBAAmB,AACvC,GAQM,CAAEyK,MAAAA,CAAK,CAAE,CAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,CAP/B,CACpB,KAAM,SACN,MAAO,CACLgE,UAAAA,CACF,EACA,OAAQ,IACV,EACoE,EAGpE,OAFA,MAAMwB,EAAa,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAACxF,CAAK,CAAC,EAAE,GAE9D,CACLlC,OAFa,MAAM0H,EAAa,KAAK,GAGrC,SAAUA,CACZ,CACF,CAUA,0BAA0BgC,CAAmB,CAAE,CAC7C,GAAIA,AAA6B,SAA7BA,EAAoB,IAAI,EAItBC,AAHiB,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAClD,AAACC,GAASA,AAAc,SAAdA,EAAK,IAAI,EAEJ,MAAM,EAAI,GAAKF,AAA6B,SAA7BA,EAAoB,IAAI,CAAa,CACnE,IAAMG,EAAoB,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAC1D,AAACD,GAASA,AAAc,SAAdA,EAAK,IAAI,EAEjBC,GAAqB,GACvB,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAACA,EAAmB,EAEvD,CAEF,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAACH,EAChC,CACA,MAAM,gBAAgBhC,CAAY,CAAEoC,CAAQ,CAAE,CAQ5C,GAAM,CAAE5H,MAAAA,CAAK,CAAE,CAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,CAPnC,CAChB,KAAM,QACN,MAAO,CACL,QAAS4H,CACX,EACA,OAAQ,IACV,EACgE,EAGhE,OAFA,MAAMpC,EAAa,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAACxF,CAAK,CAAC,EAAE,GACrE,MAAMwF,EAAa,KAAK,GACjB,CACL,OAAQ,KAAK,EACb,SAAUA,CACZ,CACF,CACA,MAAM,QAAQxB,CAAS,CAAEmD,CAAG,CAAE,CAC5B,IAAM5R,EAAc,CAAC,SAAS,EAAEyO,EAAU,CAAC,CACrCwB,EAAe,IAAIzE,EAAY,QAAQ,CAACU,GAAa,UAAWlM,GAAc,CAClF,YAAa,IAAI,CAAC,mBAAmB,AACvC,GACM,CAAEsS,UAAAA,CAAS,CAAEC,gBAAAA,CAAe,CAAE,CAAGX,EACvC,AAAC,GAAGhG,GAAc,MAAM,AAAD,EAAG6C,EAAW,4BACrC,AAAC,GAAG7C,GAAc,MAAM,AAAD,EAAG0G,EAAW,4BACrC,AAAC,GAAG1G,GAAc,MAAM,AAAD,EAAG2G,EAAiB,kCAC3C,IAAMC,EAAmB/V,KAAK,GAAG,GAC7BsH,EAAYtH,KAAK,GAAG,GACpBgW,EAAe,GACnB,KAAOhW,KAAK,GAAG,GAAK+V,EAAmBF,GAAW,CAChDvO,EAAYtH,KAAK,GAAG,GACpB,IAAM+R,EAAa,CACjB,KAAM,qBACN,MAAO,CACLC,UAAAA,CACF,EACA,OAAQ,IACV,EACM,CAAE,MAAOiE,CAAW,CAAE,CAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,CAChElE,EACD,CACD,OAAMyB,EAAa,MAAM,CACvB,IAAI,CAAC,6BAA6B,CAACyC,CAAW,CAAC,EAAE,GAEnD,IAAMnK,EAAS,MAAM0H,EAAa,KAAK,GACvC,GAAI1H,GAAQ,KACV,MAAO,CACL,OAAQ,KAAK,EACb,SAAU0H,CACZ,EAEFwC,EAAelK,GAAQ,SAAW,CAAC,0CAA0C,EAAEkG,EAAU,CAAC,CAC1F,IAAMkE,EAAMlW,KAAK,GAAG,GACpB,GAAIkW,EAAM5O,EAAYwO,EAAiB,CAErC,IAAMK,EAAY,CAChB,KAAM,QACN,MAAO,CACL,OAJkBL,EAAmBI,CAAAA,EAAM5O,CAAQ,CAKrD,EACA,OAAQ,IACV,EACM,CAAE,MAAO8O,CAAU,CAAE,CAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,CAC/DD,EACD,CACD,OAAM3C,EAAa,MAAM,CACvB,IAAI,CAAC,6BAA6B,CAAC4C,CAAU,CAAC,EAAE,GAElD,MAAM5C,EAAa,KAAK,EAC1B,CACF,CACA,OAAO,IAAI,CAAC,eAAe,CACzBA,EACA,CAAC,iBAAiB,EAAEwC,EAAa,CAAC,CAEtC,CACF,EAGIK,GAAiB,EAAQ,OACzBC,GAAgB,EAAQ,MACxBC,GAAS,AAAC,GAAGF,GAAe,QAAQ,AAAD,EAAG,gBAC1C,SAASG,GAAW9G,CAAI,CAAE+G,CAAW,CAAEnG,CAAK,EAC1C,IAAIoG,EAAc,EAAE,CACdC,EAAaF,EAAc,CAC/B,KAAM,SACN,OAAQA,EACR,MAAOA,EACP,QAAS,EACX,EAAI,KAYJ,GAXI/G,CAAAA,AAAS,QAATA,GAAkBA,AAAS,UAATA,GAAoBA,AAAS,eAATA,CAAoB,IAC5D,AAAC,GAAG4G,GAAc,MAAM,AAAD,EAAGG,EAAa,CAAC,gCAAgC,EAAE/G,EAAK,CAAC,CAAC,EACjF,AAAC,GAAG4G,GAAc,MAAM,AAAD,EAAGK,EAAY,CAAC,gCAAgC,EAAEjH,EAAK,CAAC,CAAC,EAOhFgH,EAAc,CAACC,EANC,CACdjH,KAAAA,EACA,MAAO,KACP,QAAS,GACT,OAAQ+G,CACV,EACmC,EAEjC/G,AAAS,UAATA,GAAoBA,AAAS,kBAATA,EAA0B,CACnC,UAATA,GACF,AAAC,GAAG4G,GAAc,MAAM,AAAD,EAAGG,EAAa,CAAC,gCAAgC,EAAE/G,EAAK,CAAC,CAAC,EAEnF,AAAC,GAAG4G,GAAc,MAAM,AAAD,EAAGhG,EAAO,CAAC,0BAA0B,EAAEZ,EAAK,CAAC,CAAC,EACrE,IAAMkH,EAAY,CAChBlH,KAAAA,EACAY,MAAAA,EACA,QAAS,GACT,OAAQmG,CACV,EAEEC,EADEC,EACY,CAACA,EAAYC,EAAU,CAEvB,CAACA,EAAU,AAE7B,CACA,GAAIlH,AAAS,WAATA,EAAmB,CACrB,AAAC,GAAG4G,GAAc,MAAM,AAAD,EAAGhG,EAAO,CAAC,0BAA0B,EAAEZ,EAAK,CAAC,CAAC,EACrE,IAAMmH,EAAa,CACjBnH,KAAAA,EACAY,MAAAA,EACA,QAAS,GACT,OAAQmG,CACV,EAEEC,EADEC,EACY,CAACA,EAAYE,EAAW,CAExB,CAACA,EAAW,AAE9B,CAqBA,GApBa,UAATnH,IACF,AAAC,GAAG4G,GAAc,MAAM,AAAD,EAAGhG,EAAO,CAAC,0BAA0B,EAAEZ,EAAK,CAAC,CAAC,EAOrEgH,EAAc,CANI,CAChBhH,KAAAA,EACAY,MAAAA,EACA,QAAS,GACT,OAAQ,IACV,EACyB,EAEd,WAATZ,IACF,AAAC,GAAG4G,GAAc,MAAM,AAAD,EAAGG,EAAa,CAAC,gCAAgC,EAAE/G,EAAK,CAAC,CAAC,EAOjFgH,EAAc,CANM,CAClBhH,KAAAA,EACA,MAAO+G,EACP,OAAQA,EACR,QAAS,EACX,EAC2B,EAEzBC,EAEF,OADAH,GAAO,aAAcG,GACdA,CAET,OAAM,AAAIrQ,MAAM,CAAC,oBAAoB,EAAEqJ,EAAK,CAAC,CAC/C,CAGA,IAAIoH,GAAqBzN,EAAQ,EAAQ,QACrC0N,GAAkB,EAAQ,OAC1BC,GAAoB,EAAQ,OAC5BC,GAAiB,EAAQ,OACzBC,GAAiB,EAAQ,OACzBC,GAAgB,EAAQ,MACxBC,GAAkB/N,EAAQ,EAAQ,QAClCgO,GAAgBhO,EAAQ,EAAQ,QAGhCiO,GAAU,SAGVC,GAAS,AAAC,GAAGL,GAAe,QAAQ,AAAD,EAAG,SAEtCM,GAAe,cACfC,GAAY,MAEd,YAAYC,CAAO,CAAEC,CAAiB,CAAEC,CAAa,CAAE,KAMjDC,CALJ,KAAI,CAAC,mBAAmB,CAAmB,IAAIxU,IAC/C,AAAC,GAAGyT,GAAmB,OAAO,AAAD,EAAGY,EAAS,uBACzC,IAAI,CAAC,OAAO,CA9lDPI,AA8lD0CJ,EA9lDtC,OAAO,CAAC,cAAe,KA+lDhC,IAAI,CAAC,aAAa,CAAGP,GAAc,WAAW,CAAG,KAAK,EAAIS,GAAiB,AAAC,GAAGZ,GAAkB,IAAI,AAAD,EAAG,AAAC,GAAGC,GAAe,oBAAoB,AAAD,EAAG,SAAU,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAEO,GAAa,CAAC,EAC1L,IAAI,CAAC,iBAAiB,CAAGG,EAErB,IAAI,CAAC,aAAa,EACpBE,CAAAA,EAAe,IAAI,CAAC,iBAAiB,EAAC,EAEnCA,GACHA,CAAAA,EAAe,CACb,gBAAiBP,GACjB,QAAS,IAAI,CAAC,OAAO,CACrB,OAAQ,EAAE,AACZ,GAEF,IAAI,CAAC,KAAK,CAAGO,EACb,IAAI,CAAC,mBAAmB,CAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,AACrD,CACA,WAAWxL,CAAM,CAAEqD,CAAI,CAAE,CACvB,IAAK,IAAItR,EAAI,EAAGA,EAAI,IAAI,CAAC,mBAAmB,CAAEA,IAAK,CACjD,IAAMsX,EAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAACtX,EAAE,CAC3BsD,EAAM,CAAC,EAAEgO,EAAK,CAAC,EAAErD,EAAO,CAAC,EAAEjO,EAAE,CAAC,CACpC,GAAIsX,EAAK,IAAI,GAAKhG,GAAQgG,EAAK,MAAM,GAAKrJ,GAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC3K,GAQhF,OAPA,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAACA,GAC7B6V,GACE,kEACA7H,EACArD,EACAjO,GAEK,CACL,aAAcsX,EACd,SAAU,AAACqC,IACTR,GACE,sEACA7H,EACArD,EACAjO,GAEF2Z,EAAGrC,GACH6B,GACE,qEACA7H,EACArD,EACAjO,GAEF,IAAI,CAAC,gBAAgB,EACvB,CACF,CAEJ,CACAmZ,GAAO,8CAA+C7H,EAAMrD,EAE9D,CACA,eAAeA,CAAM,CAAE,CACrB,OAAO,IAAI,CAAC,UAAU,CAACA,EAAQ,OACjC,CACA,iBAAiBA,CAAM,CAAE,CACvB,OAAO,IAAI,CAAC,UAAU,CAACA,EAAQ,SACjC,CACA,YAAY2L,CAAK,CAAE,CACjBT,GAAO,oBAAqBS,GAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAACA,GACvB,IAAI,CAAC,gBAAgB,EACvB,CACA,mBAAoB,CAClB,IAAMC,EAAY,IAAI,CAAC,aAAa,CAEpC,GADA,AAAC,GAAGnB,GAAmB,OAAO,AAAD,EAAGmB,EAAW,+BACvC,CAAC,AAAC,GAAGlB,GAAgB,UAAU,AAAD,EAAGkB,GAAY,CAC/CV,GAAO,gCAAiCU,GACxC,MACF,CACA,IAAMC,EAAoBD,EAAU,OAAO,CAACT,GAAc,SAC1D,GAAI,AAAC,GAAGT,GAAgB,UAAU,AAAD,EAAGmB,IAAsB,IAAI,CAAC,iBAAiB,CAAE,CAChFvR,QAAQ,IAAI,CACV,CAAC,2LAA2L,EAAEuR,EAAkB,CAAC,CAAC,EAEpN,MACF,CACA,GAAI,CACF,IAAM5C,EAAO,AAAC,GAAGyB,GAAgB,YAAY,AAAD,EAAGkB,EAAW,QACpDE,EAAWf,GAAgB,OAAO,CAAC,IAAI,CAAC9B,GAC9C,GAAI,CAACgC,GAAS,CACZC,GAAO,2DACP,MACF,CACA,GAAIF,GAAc,OAAO,CAAC,EAAE,CAACc,EAAS,eAAe,CA5FtB,YA4F2D,CAACA,EAAS,eAAe,CAAC,QAAQ,CAAC,QAAS,CACpIxR,QAAQ,IAAI,CACV,CAAC;AACX;AACA,YAAY,EAAEsR,EAAU,CAAC,EAEjB,MACF,CAQA,OAPAV,GACE,yEACAU,EACAE,EAAS,eAAe,CACxBA,EAAS,MAAM,CAAC,MAAM,EAExBA,EAAS,eAAe,CAAGb,GACpBa,CACT,CAAE,MAAOC,EAAK,CACZb,GACE,yDACAU,EACAG,GAEF,MACF,CACF,CACA,kBAAmB,CACjB,GAAI,CAACd,GAAS,CACZC,GAAO,0DACP,MACF,CACA,GAAI,CAAC,IAAI,CAAC,aAAa,CAAE,CACvBA,GAAO,oDACP,MACF,CACA,GAAI,CACF,IAAMc,EAAM,AAAC,GAAGrB,GAAkB,OAAO,AAAD,EAAG,IAAI,CAAC,aAAa,EACxD,AAAC,GAAGD,GAAgB,UAAU,AAAD,EAAGsB,KACnC,AAAC,GAAGtB,GAAgB,SAAS,AAAD,EAAGsB,EAAK,CAAE,UAAW,EAAK,GACtDd,GAAO,8BAA+Bc,IAExC,IAAMC,EAAWlB,GAAgB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EACxD,AAAC,GAAGL,GAAgB,aAAa,AAAD,EAAG,IAAI,CAAC,aAAa,CAAEuB,GACvDf,GAAO,4BAA6B,IAAI,CAAC,aAAa,CACxD,CAAE,MAAOa,EAAK,CACZb,GACE,kDACA,IAAI,CAAC,aAAa,CAClBa,EAEJ,CACF,CACA,0BAA0BG,CAAS,CAAEC,CAAY,CAAE,CAC7CA,EACED,AAAmB,SAAnBA,EAAU,IAAI,CAChBC,EAAa,QAAQ,CAAC,AAACR,IACrBA,EAAM,YAAY,CAAGO,EAAU,YAAY,AAC7C,GAEAC,EAAa,QAAQ,CAAC,AAACR,IACrBA,EAAM,MAAM,CAAGO,EAAU,MAAM,AACjC,GAGF,IAAI,CAAC,WAAW,CAACA,EAErB,CACF,EAGIE,GAAS,AAAC,GAAG5J,EAAe,QAAQ,AAAD,EAAG,mBACtC6J,GAAsB,CAACC,EAAIC,KAC7B,GAAM,CAACC,EAAIC,EAAG,CAAGH,EACX,CAACI,EAAIC,EAAG,CAAGJ,EACjB,OAAO9Y,KAAK,KAAK,CAACA,KAAK,IAAI,CAAC,AAAC+Y,CAAAA,EAAKE,CAAC,GAAM,EAAI,AAACD,CAAAA,EAAKE,CAAC,GAAM,GAC5D,EACIC,GAAiB,CAACC,EAAOnP,KAC3B,GAAM,CAACzE,EAAGxG,EAAE,CAAGoa,EACT,CAAEC,KAAAA,CAAI,CAAEC,IAAAA,CAAG,CAAEpR,MAAAA,CAAK,CAAED,OAAAA,CAAM,CAAE,CAAGgC,EACrC,OAAOzE,GAAK6T,GAAQ7T,GAAK6T,EAAOnR,GAASlJ,GAAKsa,GAAOta,GAAKsa,EAAMrR,CAClE,EACIsR,GAA8B,CAChC,YAAa,GACb,mBAAoB,EACtB,EACIC,GAAY,MACd,YAAYhP,CAAI,CAAEyF,CAAI,CAAE,CAItB,IAAI,CAAC,OAAO,CAAG,GACf,IAAI,CAAC,IAAI,CAAGzF,EACZ,IAAI,CAAC,IAAI,CAAGvJ,OAAO,MAAM,CACvB,CACE,eAAgB,GAChB,mBAAoB,GACpB,UAAW,kBACX,iBAAkB,EACpB,EACAgP,GAAQ,CAAC,GAEP,CAAuB,cAAvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAoB,AAAuB,eAAvB,IAAI,CAAC,IAAI,CAAC,QAAQ,AAAgB,IAC1E,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAG,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAIpB,EAAkB,mCAAmC,CAChI,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAG,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAIA,EAAkB,qCAAqC,EAEtI,IAAI,CAAC,cAAc,CAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAC9C,IAAI,CAAC,OAAO,CAAG,IAAI7D,EAAa,OAAO,CACrC,MAAO5E,GACE,IAAI,CAAC,YAAY,CAACA,IAGzB6J,GAAM,SAAW,AAAuB,YAAvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EACrC,KAAI,CAAC,SAAS,CAAG,IAAI0H,GACnB1H,EAAK,OAAO,CACZ,AAAC,GAAGnB,EAAY,oBAAoB,AAAD,EAAG,kBAExC,EAEF,IAAI,CAAC,YAAY,CAAG,IAAIiB,GAAiB,IAAI,CAAC,IAAI,CAAE,IAAI,CAAC,OAAO,CAAE,CAChE,UAAW,IAAI,CAAC,SAAS,CACzB,YAAa,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CACpD,GACA,IAAI,CAAC,IAAI,CAAG,IAAI,CAAC,SAAS,GAC1B,IAAI,CAAC,cAAc,CAAG0J,AAxzD1B,SAAwBC,EAAM,KAAK,EACjC,IAAMC,EAAgB,AAAC,GAAG9P,EAAW,WAAW,AAAD,EAAGA,EAAW,wBAAwB,EAC/E+P,EAAqB,AAAC,GAAG7P,EAAa,OAAO,AAAD,IAAK,MAAM,CAAC,uBACxD8P,EAAW,AAAC,GAAGvV,EAAc,IAAI,AAAD,IAAK,SAAS,CAAC,EAAG,GACxD,MAAO,CAAC,EAAEqV,GAAiBD,EAAI,CAAC,EAAEE,EAAmB,CAAC,EAAEC,EAAS,CAAC,AACpE,EAozDM5J,GAAM,QAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAI,MAE1C,CACA,MAAM,aAAa7J,CAAM,CAAE,QACzB,AAAIA,GAAWA,CAAAA,AAAW,YAAXA,GAAwBA,AAAW,WAAXA,CAAkB,EAChD,MAAMmE,EAAwB,IAAI,CAAC,IAAI,CAAE,CAC9C,aAAc,EAChB,GAEK,MAAMA,EAAwB,IAAI,CAAC,IAAI,CAAE,CAC9C,aAAc,CAAC,CAAC,AAAC,GAAGuE,EAAY,YAAY,AAAD,GAC7C,EACF,CACA,MAAM,mBAAmBvC,CAAM,CAAE,CAC/B,IAAI,CAAC,IAAI,CAAC,eAAe,CAAGA,CAC9B,CACA,WAAY,CAMV,OALA,IAAI,CAAC,IAAI,CAAG,CACV,UAAW,IAAI,CAAC,IAAI,CAAC,SAAS,CAC9B,iBAAkB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAC5C,WAAY,EAAE,AAChB,EACO,IAAI,CAAC,IAAI,AAClB,CACA,oBAAoBuN,CAAS,CAAE,CAE7BC,AADoB,IAAI,CAAC,IAAI,CACjB,UAAU,CAAC,IAAI,CAACD,EAC9B,CACA,gBAAiB,CAGf,OAFA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CACzC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAChD,AAAC,GAAGlL,EAAe,iBAAiB,AAAD,EAAG,IAAI,CAAC,IAAI,CACxD,CACA,kBAAmB,CACjB,MAAO,AAAC,GAAGA,EAAe,iBAAiB,AAAD,EAAG,IAAI,CAAC,cAAc,GAClE,CACA,qBAAsB,CACpB,GAAM,CAAEoL,eAAAA,CAAc,CAAEC,mBAAAA,CAAkB,CAAE,CAAG,IAAI,CAAC,IAAI,CASxD,GARA,IAAI,CAAC,UAAU,CAAG,AAAC,GAAGrL,EAAe,YAAY,AAAD,EAAG,CACjD,SAAU,IAAI,CAAC,cAAc,CAC7B,QAASA,EAAe,wBAAwB,CAChD,YAAa,IAAI,CAAC,cAAc,GAChC,KAAM,OACNoL,eAAAA,CACF,GACArB,GAAO,sBAAuB,IAAI,CAAC,UAAU,EACzCqB,GAAkBC,GAAsB,IAAI,CAAC,UAAU,CACzDC,KAl2DkBC,EAAAA,EAk2DH,IAAI,CAAC,UAAU,CAj2DlC,AAAC,GAAG7V,EAAc,MAAM,AAAD,EAAG,CAAC,gCAAgC,EAAE6V,EAAS,CAAC,CAi2DpC,CAEnC,CACA,MAAM,uBAAuB3O,CAAI,CAAE,CACjC,IAAMgF,EAAQ4J,AAp6ClB,SAAkB5O,CAAI,EACpB,IAAInJ,EAOJ,GANkB,aAAdmJ,EAAK,IAAI,EACXnJ,CAAAA,EAAQmJ,GAAM,OAAO,eAAc,EAEnB,YAAdA,EAAK,IAAI,EACXnJ,CAAAA,EAAQmJ,GAAM,OAAO,QAAUA,GAAM,OAAO,IAAMA,GAAM,OAAO,YAAcA,GAAM,OAAO,SAAQ,EAEhGA,AAAc,WAAdA,EAAK,IAAI,CAAe,CAC1B,IAAMgE,EAAShE,GAAM,OACf6O,EAAY7K,EAASD,GAAeC,GAAU,GACpDnN,EAAQmJ,EAAK,OAAO,EAAI,GACpB,AAA+B,UAA/B,OAAOA,GAAM,OAAO,OACtBnJ,EAAQ,CAAC,EAAEmJ,GAAM,OAAO,OAAO,EAAE,CAAC,CACzB,AAAmC,UAAnC,OAAOA,GAAM,OAAO,WAC7BnJ,EAAQoN,GAAejE,GAAM,OACU,SAAvBA,GAAM,OAAO,OAC7BnJ,CAAAA,EAAQmJ,GAAM,OAAO,KAAI,EAEvB6O,IAEAhY,EADEA,EACM,CAAC,EAAEgY,EAAU,GAAG,EAAEhY,EAAM,CAAC,CAEzBgY,EAGd,QACA,AAAI,AAAiB,SAAVhY,EACF,GACF,AAAiB,UAAjB,OAAOA,EAAqBA,EAAQ6J,KAAK,SAAS,CAAC7J,EAAO,KAAK,EAAG,EAC3E,EAs4C2BmJ,GACjBgD,EAAMgC,EAAQ,CAAC,EAAElB,GAAQ9D,GAAM,GAAG,EAAEgF,EAAM,CAAC,CAAGlB,GAAQ9D,EACxD,KAAI,CAAC,cAAc,EACrB,MAAM,IAAI,CAAC,cAAc,CAACgD,EAE9B,CACA,iBAAiB8L,CAAQ,CAAEC,EAAkB,EAAK,CAAE,CAGlD,GAFA,IAAI,CAAC,mBAAmB,CAACD,EAAS,IAAI,IACtC,IAAI,CAAC,mBAAmB,GACpBA,EAAS,cAAc,IAAM,CAACC,EAAiB,CACjD,IAAMC,EAAYF,EAAS,eAAe,EAC1C,OAAM,AAAI/T,MAAM,CAAC,EAAEiU,GAAW;AACpC,EAAEA,GAAW,WAAW,CAAC,CACrB,CACF,CACA,yBAAyBC,CAAY,CAAEpF,CAAG,CAAE,CAE1C,GADA,AAAC,GAAGrG,EAAe,MAAM,AAAD,EAAGyL,EAAc,yBACrC,AAAe,UAAf,OAAOpF,EAAkB,CAC3B,IAAM9I,EAAS8I,EAAI,MAAM,EAAIoF,EAG7B,MAAO,CACLlO,OAAAA,EACAmO,UAJgBrF,EAAI,SAAS,EAAI,GAKjCsF,UAJgBtF,EAAI,SAAS,EAAI,EAKnC,CACF,CACA,MAAO,CACL,OAAQoF,CACV,CACF,CACA,MAAM,MAAMA,CAAY,CAAEpF,CAAG,CAAE,CAC7B,IAAMuF,EAAsB,IAAI,CAAC,wBAAwB,CACvDH,EACApF,GAEIxE,EAAQ6F,GAAW,MAAOkE,GAC1B,CAAEN,SAAAA,CAAQ,CAAEtO,OAAAA,CAAM,CAAE,CAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAC3D2D,GAAa,MAAOJ,GAAeqL,IACnC/J,EACA,CAAE,UAAWwE,GAAK,SAAU,GAG9B,OADA,IAAI,CAAC,gBAAgB,CAACiF,GACftO,CACT,CACA,MAAM,aAAayO,CAAY,CAAEpF,CAAG,CAAE,CACpC,IAAMuF,EAAsB,IAAI,CAAC,wBAAwB,CACvDH,EACApF,GAEIxE,EAAQ6F,GAAW,aAAckE,GACjC,CAAEN,SAAAA,CAAQ,CAAEtO,OAAAA,CAAM,CAAE,CAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAC3D2D,GAAa,aAAcJ,GAAeqL,IAC1C/J,EACA,CAAE,UAAWwE,GAAK,SAAU,GAG9B,OADA,IAAI,CAAC,gBAAgB,CAACiF,GACftO,CACT,CACA,MAAM,QAAQyO,CAAY,CAAEpF,CAAG,CAAE,CAC/B,IAAMuF,EAAsB,IAAI,CAAC,wBAAwB,CACvDH,EACApF,GAEIxE,EAAQ6F,GAAW,QAASkE,GAC5B,CAAEN,SAAAA,CAAQ,CAAEtO,OAAAA,CAAM,CAAE,CAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAC3D2D,GAAa,QAASJ,GAAeqL,IACrC/J,EACA,CAAE,UAAWwE,GAAK,SAAU,GAG9B,OADA,IAAI,CAAC,gBAAgB,CAACiF,GACftO,CACT,CACA,MAAM,QAAQ3J,CAAK,CAAEoY,CAAY,CAAEpF,CAAG,CAAE,CACtC,AAAC,GAAGrG,EAAe,MAAM,AAAD,EACtB,AAAiB,UAAjB,OAAO3M,EACP,iFAEF,AAAC,GAAG2M,EAAe,MAAM,AAAD,EAAGyL,EAAc,mCACzC,IAAMG,EAAsB,IAAI,CAAC,wBAAwB,CACvDH,EACApF,GAEIxE,EAAQ6F,GAAW,QAASkE,EAAqB,CACrDvY,MAAAA,EACA,oBAAqBgT,GAAK,mBAC5B,GACM,CAAEiF,SAAAA,CAAQ,CAAEtO,OAAAA,CAAM,CAAE,CAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAC3D2D,GAAa,QAASJ,GAAeqL,IACrC/J,EACA,CACE,UAAWwE,GAAK,SAClB,GAGF,OADA,IAAI,CAAC,gBAAgB,CAACiF,GACftO,CACT,CACA,MAAM,gBAAgB6O,CAAO,CAAEJ,CAAY,CAAEpF,CAAG,CAAE,CAChD,AAAC,GAAGrG,EAAe,MAAM,AAAD,EAAG6L,EAAS,sCACpC,IAAMD,EAAsBH,EAAe,IAAI,CAAC,wBAAwB,CAACA,EAAcpF,GAAO,KAAK,EAC7FxE,EAAQ6F,GAAW,gBAAiBkE,EAAqB,CAC7D,MAAOC,CACT,GACM,CAAEP,SAAAA,CAAQ,CAAEtO,OAAAA,CAAM,CAAE,CAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAC3D2D,GAAa,gBAAiBJ,GAAeqL,IAC7C/J,EACA,CAAE,UAAWwE,GAAK,SAAU,GAG9B,OADA,IAAI,CAAC,gBAAgB,CAACiF,GACftO,CACT,CACA,MAAM,SAAS0D,CAAW,CAAE+K,CAAY,CAAEpF,CAAG,CAAE,CAC7C,IAAMuF,EAAsBH,EAAe,IAAI,CAAC,wBAAwB,CAACA,EAAcpF,GAAO,KAAK,EAC7FxE,EAAQ6F,GAAW,SAAUkE,EAAqBlL,GAClDoL,EAAeL,EAAe,CAAC,EAAElL,GAAeqL,GAAqB,GAAG,EAAEnL,GAAeC,GAAa,CAAC,CAAGD,GAAeC,GACzH,CAAE4K,SAAAA,CAAQ,CAAEtO,OAAAA,CAAM,CAAE,CAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAC3D2D,GAAa,SAAUmL,GACvBjK,EACA,CAAE,UAAWwE,GAAK,SAAU,GAG9B,OADA,IAAI,CAAC,gBAAgB,CAACiF,GACftO,CACT,CACA,MAAM,SAAS+O,CAAU,CAAE1F,CAAG,CAAE,CAC9B,IAAMsF,EAAYtF,GAAK,UACjB2F,EAAc,AAAoC,gBAApC,AAAC,GAAGlM,EAAY,YAAY,AAAD,IACzCmM,EAAeD,GAAeL,AAAc,KAAdA,EAAsB,KAAK,EAAI,IAAI,CAAC,SAAS,EAAE,eAAeI,GAClG,GAAIE,GAAgB,IAAI,CAAC,SAAS,EAAE,kBAAmB,CACrD,GAAM,CAAE,SAAUC,CAAS,CAAE,CAAG,MAAM,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAC5EH,EACAE,EAAa,YAAY,EAAE,aAE7B,OAAM,IAAI,CAAC,gBAAgB,CAACC,GAC5BvC,GAAO,uDACP,IAAMwC,EAAQF,EAAa,YAAY,EAAE,aACzC,OAAO,IAAI,CAAC,OAAO,CAACE,EACtB,CACA,GAAM,CAAEnP,OAAAA,CAAM,CAAEsO,SAAAA,CAAQ,CAAE,CAAG,MAAOU,CAAAA,EAAc,IAAI,CAAC,YAAY,CAAC,YAAY,CAACD,EAAY,CAAEJ,UAAAA,CAAU,GAAK,IAAI,CAAC,YAAY,CAAC,MAAM,CAACI,EAAY,IAAI,CAAC,IAAI,CAAC,eAAe,CAAE,CAC5KJ,UAAAA,CACF,EAAC,EACD,GAAI,IAAI,CAAC,SAAS,EAAI3O,GAAQ,UAAY2O,AAAc,KAAdA,EAAqB,CAC7D,IAAMS,EAAc,CAClB,MAAO,CACL,CACE,KAAML,EACN,KAAM/O,EAAO,QAAQ,AACvB,EACD,AACH,EACMqP,EAAcpQ,EAAgB,OAAO,CAAC,IAAI,CAACmQ,GACjD,IAAI,CAAC,SAAS,CAAC,yBAAyB,CACtC,CACE,KAAM,OACN,OAAQL,EACR,aAAcM,CAChB,EACAJ,EAEJ,CAEA,OADA,IAAI,CAAC,gBAAgB,CAACX,GACftO,CACT,CACA,MAAM,QAAQoJ,CAAM,CAAEC,EAAMkE,EAA2B,CAAE,CACvD,GAAM,CAAEvN,OAAAA,CAAM,CAAEsO,SAAAA,CAAQ,CAAE,CAAG,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAClF,EAAQC,GAEnE,OADA,IAAI,CAAC,gBAAgB,CAACiF,GACftO,CACT,CACA,MAAM,UAAUO,CAAM,CAAE8I,EAAMkE,EAA2B,CAAE,CACzD,GAAM,CAAEvN,OAAAA,CAAM,CAAEsO,SAAAA,CAAQ,CAAE,CAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC/N,EAAQ8I,GAErE,OADA,IAAI,CAAC,gBAAgB,CAACiF,GACftO,CACT,CACA,MAAM,SAASO,CAAM,CAAE8I,EAAMkE,EAA2B,CAAE,CACxD,GAAM,CAAEvN,OAAAA,CAAM,CAAEsO,SAAAA,CAAQ,CAAE,CAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC/N,EAAQ8I,GAEpE,OADA,IAAI,CAAC,gBAAgB,CAACiF,GACftO,CACT,CACA,MAAM,SAASO,CAAM,CAAE8I,EAAMkE,EAA2B,CAAE,CACxD,GAAM,CAAEvN,OAAAA,CAAM,CAAEsO,SAAAA,CAAQ,CAAE,CAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC/N,EAAQ8I,GAEpE,OADA,IAAI,CAAC,gBAAgB,CAACiF,GACftO,CACT,CACA,MAAM,uBAAuBsP,CAAM,CAAEjG,CAAG,CAAE,CACxC,IAKIkG,EALE,CAAEC,aAAAA,EAAe,EAAI,CAAEC,WAAAA,EAAa,CAAC,CAAE,CAAGpG,GAAO,CAAC,EACpDqG,EAAU,GACVC,EAAa,EACbC,EAAe,GACflB,EAAYrF,GAAK,WAAa,GAElC,KAAO,CAACqG,GAAWC,EAAaF,GAAY,CACtCE,GAAc,GAChBjB,CAAAA,EAAY,EAAG,EAEjB/B,GACE,aACA2C,EACA,eACAE,EACA,aACAG,EACA,YACAjB,GAEF,IAAM/W,EAAO,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC2X,EAAQ,CAAEZ,UAAAA,CAAU,GAC7D/B,GAAO,kBAAmBhV,GAC1B,AAAC,GAAGqL,EAAe,MAAM,AAAD,EAAGrL,EAAK,WAAW,CAAE,CAAC,+BAA+B,EAAE2X,EAAO,CAAC,CAAC,EACxFM,EAAejY,EAAK,WAAW,CAO3B4X,AANJA,CAAAA,EAAe,MAAM,IAAI,CAAC,aAAa,CACrCK,EACAlB,EAAY,CAAE,UAAW,EAAK,EAAI,KAAK,EACvCY,EACAjG,EACF,EACiB,IAAI,CACnBqG,EAAU,GAEVC,GAEJ,CACA,MAAO,CACL,OAAQC,EACRlB,UAAAA,EACAa,aAAAA,CACF,CACF,CACA,MAAM,cAAchP,CAAM,CAAEsP,CAAS,CAAEC,CAAY,CAAEC,CAAkB,CAAE,CACvEpD,GAAO,gBAAiBpM,EAAQsP,EAAWC,EAAcC,GACzD,GAAM,CAAE,OAAQC,CAAY,CAAE,KAAMC,CAAU,CAAE,CAAG,MAAM,IAAI,CAAC,QAAQ,CACpE1P,EACAsP,GAEI7T,EAAW4Q,GAAoBkD,EAAcE,GAC7CE,EAAW/C,GAAe2C,EAAcG,GAExCV,EAAe,CACnBY,KAFWnU,GAAa+T,CAAAA,GAAoB,yBAA2B,EAAC,GAAMG,EAG9E,KAAMD,EACN,OAAQD,EACR,eAAgBhU,CAClB,EAEA,OADA2Q,GAAO,0BAA2B4C,GAC3BA,CACT,CACA,MAAM,SAAShP,CAAM,CAAE8I,CAAG,CAAE,CAC1B,IAAMuF,EAAsB,IAAI,CAAC,wBAAwB,CAACrO,EAAQ8I,GAC5DxE,EAAQ6F,GAAW,SAAUkE,GAC7B,CAAEN,SAAAA,CAAQ,CAAEtO,OAAAA,CAAM,CAAE,CAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAC3D2D,GAAa,SAAUJ,GAAeqL,IACtC/J,EACA,CAAE,UAAWwE,GAAK,SAAU,GAE9B,IAAI,CAAC,gBAAgB,CAACiF,GACtB,GAAM,CAAEnS,QAAAA,CAAO,CAAE,CAAG6D,EACpB,MAAO,CACL,KAAM7D,GAAS,KACf,OAAQA,GAAS,MACnB,CACF,CACA,MAAM,SAAS+J,CAAS,CAAElQ,CAAG,CAAEqT,CAAG,CAAE,CAClC,GAAM,CAAErJ,OAAAA,CAAM,CAAEsO,SAAAA,CAAQ,CAAE,CAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAACpI,GAE5D,GADA,IAAI,CAAC,gBAAgB,CAACoI,EAAU,IAC5BtO,GAAUqJ,GAAK,gBACjB,OAAOrJ,EAET,GAAI,CAACA,GAAQ,KAAM,CACjB,IAAMoQ,EAASpa,GAAO,CAAC,kBAAkB,EAAEkQ,EAAU,CAAC,CAChDmK,EAAY,CAAC,QAAQ,EAAErQ,GAAQ,SAAWsO,EAAS,eAAe,IAAI,OAAS,cAAc,CAAC,AACpG,OAAM,AAAI/T,MAAM,CAAC,EAAE6V;AACzB,EAAEC,EAAU,CAAC,CACT,CACF,CACA,MAAM,UAAUnK,CAAS,CAAEmD,CAAG,CAAE,CAC9B,GAAM,CAAEiF,SAAAA,CAAQ,CAAE,CAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAACpI,EAAW,CAC9D,UAAWmD,GAAK,WAAa,KAC7B,gBAAiBA,GAAK,iBAAmB,IACzCnD,UAAAA,CACF,GAGA,GAFA,IAAI,CAAC,mBAAmB,CAACoI,EAAS,IAAI,IACtC,IAAI,CAAC,mBAAmB,GACpBA,EAAS,cAAc,GAAI,CAC7B,IAAME,EAAYF,EAAS,eAAe,EAC1C,OAAM,AAAI/T,MAAM,CAAC,EAAEiU,GAAW;AACpC,EAAEA,GAAW,WAAW,CAAC,CACrB,CACF,CACA,MAAM,GAAGO,CAAU,CAAEnL,EAAO,QAAQ,CAAE,CACpC,GAAIA,AAAS,WAATA,EACF,OAAO,IAAI,CAAC,QAAQ,CAACmL,GAEvB,GAAInL,AAAS,UAATA,EACF,OAAO,IAAI,CAAC,OAAO,CAACmL,GAEtB,GAAInL,AAAS,WAATA,EACF,OAAO,IAAI,CAAC,QAAQ,CAACmL,GAEvB,GAAInL,AAAS,QAATA,EACF,OAAO,IAAI,CAAC,KAAK,CAACmL,GAEpB,GAAInL,AAAS,eAATA,EACF,OAAO,IAAI,CAAC,YAAY,CAACmL,EAE3B,OAAM,AAAIxU,MACR,CAAC,cAAc,EAAEqJ,EAAK,+DAA+D,CAAC,CAE1F,CACA,MAAM,QAAQ0M,CAAiB,CAAE,CAE/B,IAAMC,EAAS,IAAInR,EADJoR,AA7zDnB,SAAyB3U,CAAO,CAAE4U,CAAQ,CAAEC,CAAoB,EAC9D,IAAIC,EAAmB9U,EACvB,GAAIA,AAA+B,KAA/BA,EAAQ,OAAO,CAAC,YAAqBA,EAAQ,KAAK,CAAC,qBAAsB,CAC3E,IAAI+U,EACJD,EAAmB9U,EAAQ,OAAO,CAChC,qBACA,CAACgV,EAAOC,KACNF,EAAkBE,EACX,CAAC,WAAW,EAAEA,EAAS,CAAC,CAAC,GAGpCjW,QAAQ,IAAI,CACV,CAAC,yEAAyE,EAAE+V,EAAgB,CAAC,CAAC,CAElG,CACA,IAAMG,EAvBClV,AAuBwC8U,EAvBhC,OAAO,CAAC,iBAAkB,CAACjd,EAAGsd,KAC3C,IAAM3a,EAAQkJ,EAAQ,GAAG,CAACyR,EAAO,IAAI,GAAG,CACxC,GAAI3a,AAAU,KAAK,IAAfA,EACF,MAAM,AAAIkE,MAAM,CAAC,sBAAsB,EAAEyW,EAAO,IAAI,GAAG,gBAAgB,CAAC,EAE1E,OAAO3a,CACT,GAkBMP,EAAM6M,EAAgB,OAAO,CAAC,IAAI,CAACoO,EAAqB,CAC5D,OAAQpO,EAAgB,OAAO,CAAC,WAAW,AAC7C,GACMsO,EAAUR,EAAW,CAAC,iBAAiB,EAAEA,EAAS,CAAC,CAAG,GACtDxO,EAAU,AAAuB,SAAhBnM,EAAI,OAAO,CAAmBb,OAAO,MAAM,CAAC,CAAC,EAAGa,EAAI,OAAO,EAAI,CAAC,GAAK,KAAK,EAC3Fob,EAAYpb,EAAI,GAAG,EAAIA,EAAI,MAAM,CACjCkM,EAAM,AAAqB,SAAdkP,EAA4Bjc,OAAO,MAAM,CAAC,CAAC,EAAGic,GAAa,CAAC,GAAK,KAAK,EAsBzF,MArBI,CAACR,IACH,AAAC,GAAGtU,EAAc,MAAM,AAAD,EACrB4F,GAAOC,EACP,CAAC,mFAAmF,EAAEgP,EAAQ,CAAC,EAEjG,AAAC,GAAG7U,EAAc,MAAM,AAAD,EACrB4F,GAAO,CAACC,GAAW,CAACD,GAAOC,EAC3B,CAAC,8EAA8E,EAAEgP,EAAQ,CAAC,EAExFjP,CAAAA,GAAOC,CAAM,GACf,AAAC,GAAG7F,EAAc,MAAM,AAAD,EACrB,AAAe,UAAf,OAAO4F,GAAoB,AAAmB,UAAnB,OAAOC,EAClC,CAAC,+CAA+C,EAAEgP,EAAQ,CAAC,GAIjE,AAAC,GAAG7U,EAAc,MAAM,AAAD,EAAGtG,EAAI,KAAK,CAAE,CAAC,4CAA4C,EAAEmb,EAAQ,CAAC,EAC7F,AAAC,GAAG7U,EAAc,MAAM,AAAD,EACrB/I,MAAM,OAAO,CAACyC,EAAI,KAAK,EACvB,CAAC,0DAA0D,EAAEA,EAAI,KAAK,CAAC,CAAC,EAEnEA,CACT,EAgxDmCwa,EAAmB,OAAQ,IAClB,MAAO7b,GACtC,EAAE,MAAO,IAAI,CAAE,OAAQ,EAAE,AAAC,IAGnC,GADA,MAAM8b,EAAO,GAAG,GACZA,AAAkB,UAAlBA,EAAO,MAAM,CAAc,CAC7B,IAAMY,EAASZ,EAAO,cAAc,CAAC,MAAM,CAAC,AAAC/Q,GAASA,AAAgB,UAAhBA,EAAK,MAAM,EAAc,GAAG,CAAC,AAACA,GAC3E,CAAC,OAAO,EAAEA,EAAK,IAAI,CAAC,EAAE,EAAEA,EAAK,KAAK,EAAE,QAAQ,CAAC,EACnD,IAAI,CAAC,KACR,OAAM,AAAIjF,MAAM,CAAC;AACvB,EAAE4W,EAAO,CAAC,CACN,CACA,MAAO,CACL,OAAQZ,EAAO,MAAM,AACvB,CACF,CACA,MAAM,mBAAmB5X,CAAM,CAAE,CAK/B,MAJA,AAAC,GAAGqK,EAAe,MAAM,AAAD,EACtB,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAC5B,wDAEK,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAACrK,EACtC,CACA,MAAM,SAAU,CACd,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EACzB,CACA,MAAM,cAAciQ,CAAK,CAAES,CAAG,CAAE,CAC9B,IAAMvN,EAAS,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,GACzCsO,EAAMlW,KAAK,GAAG,GAQdsL,EAAO,CACX,KAAM,MACN,QAAS,aACT,OAAQ,WACRkF,SAXe,CACf,CACE,KAAM,aACN,GAAI0F,EACJ,WAAYtO,CACd,EACD,CAMC,OAAQ,CACN,MAAOsO,EACP,IAAKA,EACL,KAAM,CACR,EACA,MAAO,CACL,QAASf,GAAK,SAAW,EAC3B,EACA,SAAU,UACV,CACF,EACM+H,EAAgB,CACpB,WAAY,GACZ,QAAShH,EACT,WAAY,GACZ,kBAAmB,GACnB,KAAM,CAAC,MAAM,EAAExB,GAAS,WAAW,CAAC,CACpC,YAAaS,GAAK,SAAW,GAC7B,MAAO,CAAC7J,EAAK,AACf,EACA,IAAI,CAAC,mBAAmB,CAAC4R,GACzB,IAAI,CAAC,mBAAmB,EAC1B,CACF,EAGI3T,GAAgC,cAAc+P,GAEhD,YAAYhP,CAAI,CAAEyF,CAAI,CAAE,CACtB,KAAK,CAACzF,EAAMyF,EACd,CACF,EAGIoN,GAAoB,EAAQ,OAC5BC,GAAiB,EAAQ,MAGzBna,GAAyB,EAAQ,OACjCoa,GAAiB,EAAQ,MAEzBla,GAAc,MAChB,YAAYC,CAAM,CAAE,CAClBlB,EAAa,IAAI,CAAExB,GACnBwB,EAAa,IAAI,CAAEtB,GACnBsB,EAAa,IAAI,CAAE1B,EAA8B,IAAI6C,KACrDnB,EAAa,IAAI,CAAEzB,EAAS,KAAK,GACjC,IAAI,CAAC,UAAU,CAAG,EAClB4B,EAAa,IAAI,CAAE5B,EAAS2C,EAC9B,CACA,aAAaA,CAAM,CAAE,CACnBf,EAAa,IAAI,CAAE5B,EAAS2C,EAC9B,CACA,MAAM,KAAK1B,CAAG,CAAE4B,EAAU,CACxB,KAAM,KAAK,EACX,SAAU,EAAE,AACd,CAAC,CAAE,CACD,IAAMC,EAAchB,EAAgB,IAAI,CAAE3B,EAA0BC,GAA4B,IAAI,CAAC,IAAI,CAAEa,GACrG8B,EAAaxB,EAAa,IAAI,CAAExB,GAAc,GAAG,CAAC+C,EAAY,IAAI,EACxEvB,EAAa,IAAI,CAAExB,GAAc,GAAG,CAAC+C,EAAY,IAAI,EACrD,IAAI,CAAC,UAAU,EAAIhB,EAAgB,IAAI,CAAE7B,EAAcC,GAAgB,IAAI,CAAC,IAAI,CAAE4C,EAAY,GAAG,EACjG,IAAME,EAAOH,AAAiB,KAAK,IAAtBA,EAAQ,IAAI,CAAcC,EAAY,IAAI,CAAGD,EAAQ,IAAI,AACtE,OAAMtB,EAAa,IAAI,CAAEvB,GAAS,IAAI,CAAC,yBAA0B,CAC/D,KAAMgD,EAAO,UAAY,aACzB,UAAW,IAAI,CAAC,UAAU,CAC1B,sBAAuBF,EAAY,OAAO,CAC1C,KAAMA,EAAY,IAAI,CACtB,IAAKA,EAAY,GAAG,CACpBE,KAAAA,EACA,eAAgBA,EAChBD,WAAAA,EACA,SAAUD,EAAY,QAAQ,CAC9B,SAAUA,AAAyB,IAAzBA,EAAY,QAAQ,CAC9B,SAAUD,EAAQ,QAAQ,AAC5B,EACF,CACA,MAAM,GAAG5B,CAAG,CAAE,CACZ,IAAM6B,EAAchB,EAAgB,IAAI,CAAE3B,EAA0BC,GAA4B,IAAI,CAAC,IAAI,CAAEa,EAC3G,KAAI,CAAC,UAAU,EAAI,CAACa,EAAgB,IAAI,CAAE7B,EAAcC,GAAgB,IAAI,CAAC,IAAI,CAAE4C,EAAY,GAAG,EAClGvB,EAAa,IAAI,CAAExB,GAAc,MAAM,CAAC+C,EAAY,IAAI,EACxD,MAAMvB,EAAa,IAAI,CAAEvB,GAAS,IAAI,CAAC,yBAA0B,CAC/D,KAAM,QACN,UAAW,IAAI,CAAC,UAAU,CAC1B,IAAK8C,EAAY,GAAG,CACpB,sBAAuBA,EAAY,OAAO,CAC1C,KAAMA,EAAY,IAAI,CACtB,SAAUA,EAAY,QAAQ,AAChC,EACF,CACA,MAAM,cAAcG,CAAI,CAAE,CACxB,MAAM1B,EAAa,IAAI,CAAEvB,GAAS,IAAI,CAAC,mBAAoB,CAAE,KAAMiD,CAAK,EAC1E,CACA,UAAUA,CAAI,CAAE,CACd,MAAO,CAAC,CAACT,GAAuB,eAAe,CAACS,EAAK,AACvD,CACA,MAAM,KAAKD,CAAI,CAAEH,EAAU,CAAC,CAAC,CAAE,CAC7B,IAAMK,EAAQL,EAAQ,KAAK,EAAI,KAAK,EACpC,IAAK,IAAMI,KAAQD,EACb,IAAI,CAAC,SAAS,CAACC,GACjB,MAAM,IAAI,CAAC,KAAK,CAACA,EAAM,CAAEC,MAAAA,CAAM,IAE3BA,GACF,MAAM,IAAIC,QAAQ,AAACnF,GACVoF,WAAWpF,EAAGkF,IAGzB,MAAM,IAAI,CAAC,aAAa,CAACD,GAG/B,CACA,MAAM,MAAMhC,CAAG,CAAE4B,EAAU,CAAC,CAAC,CAAE,CAC7B,GAAM,CAAEK,MAAAA,EAAQ,IAAI,CAAE,CAAGL,EACnBQ,EAAO3E,MAAM,OAAO,CAACuC,GAAOA,EAAM,CAACA,EAAI,CAC7C,IAAK,IAAMtB,KAAK0D,EACd,MAAM,IAAI,CAAC,IAAI,CAAC1D,EAAGkD,GAOrB,IAAK,IAAMlD,KALPuD,GACF,MAAM,IAAIC,QAAQ,AAACnF,GACVoF,WAAWpF,EAAG6E,EAAQ,KAAK,GAGtB,IAAIQ,EAAK,CAAC,OAAO,IAC/B,MAAM,IAAI,CAAC,EAAE,CAAC1D,EAElB,CACF,EACAI,EAAe,IAAIuD,QACnBtD,EAAU,IAAIsD,QACdrD,EAAe,IAAI0B,QACnBzB,EAAiB,SAASe,CAAG,QAC3B,AAAIA,AAAQ,QAARA,EACK,EAELA,AAAQ,YAARA,EACK,EAELA,AAAQ,SAARA,EACK,EAGA,EADLA,CAAAA,AAAQ,UAARA,CAAc,CAIpB,EACAd,EAA2B,IAAIwB,QAC/BvB,EAA6B,SAASmD,CAAS,EAC7C,IAAMC,EAAQ,AAAkB,EAAlB,IAAI,CAAC,UAAU,CACvBV,EAAc,CAClB,IAAK,GACL,QAAS,EACT,KAAM,GACN,KAAM,GACN,SAAU,CACZ,EACMW,EAAajB,GAAuB,eAAe,CAACe,EAAU,CAgCpE,MA/BA,AAAC,GAAGqZ,GAAe,MAAM,AAAD,EAAGnZ,EAAY,CAAC,cAAc,EAAEF,EAAU,CAAC,CAAC,EAChEE,EAAW,GAAG,EAChBX,CAAAA,EAAY,GAAG,CAAGW,EAAW,GAAG,AAAD,EAE7BD,GAASC,EAAW,QAAQ,EAC9BX,CAAAA,EAAY,GAAG,CAAGW,EAAW,QAAQ,AAAD,EAElCA,EAAW,OAAO,EACpBX,CAAAA,EAAY,OAAO,CAAGW,EAAW,OAAO,AAAD,EAErCD,GAASC,EAAW,YAAY,EAClCX,CAAAA,EAAY,OAAO,CAAGW,EAAW,YAAY,AAAD,EAE1CA,EAAW,IAAI,EACjBX,CAAAA,EAAY,IAAI,CAAGW,EAAW,IAAI,AAAD,EAE/BA,EAAW,QAAQ,EACrBX,CAAAA,EAAY,QAAQ,CAAGW,EAAW,QAAQ,AAAD,EAEZ,IAA3BX,EAAY,GAAG,CAAC,MAAM,EACxBA,CAAAA,EAAY,IAAI,CAAGA,EAAY,GAAG,AAAD,EAE/BW,EAAW,IAAI,EACjBX,CAAAA,EAAY,IAAI,CAAGW,EAAW,IAAI,AAAD,EAE/BD,GAASC,EAAW,SAAS,EAC/BX,CAAAA,EAAY,IAAI,CAAGW,EAAW,SAAS,AAAD,EAElB,GAAlB,IAAI,CAAC,UAAU,EACjBX,CAAAA,EAAY,IAAI,CAAG,EAAC,EAEfA,CACT,EAGA,IAAI+Z,GAAkBjU,EAAQ,EAAQ,QAClCkU,GAAiB,EAAQ,MACzBlZ,GAAyB,KACzBC,GAAuB,UACzB,IAAMC,EAAuBC,OAAO,OAAO,CAAC,MAAM,CAAC,0BACnD,GAAIH,GACF,OAAOA,GACT,GAAIkZ,GAAe,WAAW,CAAE,CAC9B,IAAM9Y,EAAS,MAAMC,MAAMH,GAE3B,OADAF,GAAyB,MAAMI,EAAO,IAAI,EAE5C,CACA,OAAO6Y,GAAgB,OAAO,CAAC,YAAY,CAAC/Y,EAAsB,OACpE,EACII,GAAkC,KAClCC,GAA2B,UAC7B,IAAMC,EAAgCL,OAAO,OAAO,CAAC,MAAM,CACzD,yBAEF,GAAIG,GACF,OAAOA,GACT,GAAI4Y,GAAe,WAAW,CAAE,CAC9B,IAAM9Y,EAAS,MAAMC,MAAMG,GAE3B,OADAF,GAAkC,MAAMF,EAAO,IAAI,EAErD,CACA,OAAO6Y,GAAgB,OAAO,CAAC,YAAY,CAACzY,EAA+B,OAC7E,EACIC,GAAsC,KACtCC,GAA+B,UACjC,IAAMC,EAAoCR,OAAO,OAAO,CAAC,MAAM,CAC7D,8BAEF,GAAIM,GACF,OAAOA,GACT,GAAIyY,GAAe,WAAW,CAAE,CAC9B,IAAM9Y,EAAS,MAAMC,MAAMM,GAE3B,OADAF,GAAsC,MAAML,EAAO,IAAI,EAEzD,CACA,OAAO6Y,GAAgB,OAAO,CAAC,YAAY,CAACtY,EAAmC,OACjF,EAGA,SAASwY,GAAOtY,CAAE,EAChB,OAAO,IAAItB,QAAQ,AAACyJ,GAAaxJ,WAAWwJ,EAAUnI,GACxD,CACA,IAAIE,GAA2B,MAC7B,YAAYC,CAAsB,CAAE,CAClC,IAAI,CAAC,QAAQ,CAAG,yBAChB,IAAI,CAAC,OAAO,CAAG,SACf,IAAI,CAAC,WAAW,CAAG,KACnB,IAAI,CAAC,uBAAuB,CAAG,KAC/B,IAAI,CAAC,iBAAiB,CAAG,KACzB,IAAI,CAAC,SAAS,CAAG,GACjB,IAAI,CAAC,iBAAiB,CAAG,KACzB,IAAI,CAAC,YAAY,CAAG,IACpB,IAAI,CAAC,YAAY,CAAG,IACpB,IAAI,CAAC,KAAK,CAAG,CACX,MAAO,MAAOC,EAAGxG,EAAGwE,KAClB,GAAM,CAAEiC,OAAAA,EAAS,MAAM,CAAEC,MAAAA,EAAQ,CAAC,CAAE,CAAGlC,GAAW,CAAC,EAEnD,GADA,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAACgC,EAAGxG,GACrB,AAA2B,OAA3B,IAAI,CAAC,iBAAiB,CAAW,CACnC,IAAM2G,EAAS,MAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CAClE,WAAY,CAAC;AACzB;AACA,cAAc,CAAC,CACH,cAAe,EACjB,EACA,KAAI,CAAC,iBAAiB,CAAGA,GAAQ,QAAQ,KAC3C,CACA,GAAI,IAAI,CAAC,iBAAiB,EAAIF,AAAW,SAAXA,EAAmB,CAC/C,IAAMG,EAAc,CAAC,CAAE,EAAG5F,KAAK,KAAK,CAACwF,GAAI,EAAGxF,KAAK,KAAK,CAAChB,EAAG,EAAE,AAC5D,OAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA4B,CAC3D,KAAM,aACN4G,YAAAA,EACA,UAAW,CACb,GACA,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA4B,CAC3D,KAAM,WACN,YAAa,EAAE,CACf,UAAW,CACb,EACF,MACE,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA4B,CAC3D,KAAM,eACNJ,EAAAA,EACAxG,EAAAA,EACAyG,OAAAA,EACA,WAAYC,CACd,GACA,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA4B,CAC3D,KAAM,gBACNF,EAAAA,EACAxG,EAAAA,EACAyG,OAAAA,EACA,WAAYC,CACd,EAEJ,EACA,MAAO,MAAOG,EAAQC,EAAQC,EAAQC,KACpC,IAAMC,EAASF,GAAU,IAAI,CAAC,YAAY,CACpCG,EAASF,GAAU,IAAI,CAAC,YAAY,AAC1C,OAAM,IAAI,CAAC,gBAAgB,CAACC,EAAQC,GACpC,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA4B,CAC3D,KAAM,aACN,EAAGD,EACH,EAAGC,EACHL,OAAAA,EACAC,OAAAA,CACF,GACA,IAAI,CAAC,YAAY,CAAGG,EACpB,IAAI,CAAC,YAAY,CAAGC,CACtB,EACA,KAAM,MAAOV,EAAGxG,KACd,MAAM,IAAI,CAAC,gBAAgB,CAACwG,EAAGxG,GAC/B,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA4B,CAC3D,KAAM,aACNwG,EAAAA,EACAxG,EAAAA,CACF,GACA,IAAI,CAAC,YAAY,CAAGwG,EACpB,IAAI,CAAC,YAAY,CAAGxG,CACtB,EACA,KAAM,MAAOyC,EAAMD,KACjB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAACC,EAAK,CAAC,CAAEA,EAAK,CAAC,EACpC,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA4B,CAC3D,KAAM,eACN,EAAGA,EAAK,CAAC,CACT,EAAGA,EAAK,CAAC,CACT,OAAQ,OACR,WAAY,CACd,GACA,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAACD,EAAG,CAAC,CAAEA,EAAG,CAAC,EAChC,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA4B,CAC3D,KAAM,gBACN,EAAGA,EAAG,CAAC,CACP,EAAGA,EAAG,CAAC,CACP,OAAQ,OACR,WAAY,CACd,EACF,CACF,EACA,IAAI,CAAC,QAAQ,CAAG,CACd,KAAM,MAAOmC,IACX,IAAMwC,EAAc,IAAI9C,GAAY,CAClC,KAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAC5C,EACA,OAAM8C,EAAY,IAAI,CAACxC,EAAM,CAAE,MAAO,CAAE,EAC1C,EACA,MAAO,MAAOyC,IACZ,IAAMD,EAAc,IAAI9C,GAAY,CAClC,KAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAC5C,GACMW,EAAO3E,MAAM,OAAO,CAAC+G,GAAUA,EAAS,CAACA,EAAO,CACtD,IAAK,IAAM9F,KAAK0D,EAAM,CACpB,IAAMqC,EAAW/F,EAAE,OAAO,CAAG,CAACA,EAAE,OAAO,CAAC,CAAG,EAAE,AAC7C,OAAM6F,EAAY,IAAI,CAAC7F,EAAE,GAAG,CAAE,CAAE+F,SAAAA,CAAS,EAC3C,CACA,IAAK,IAAM/F,IAAK,IAAI0D,EAAK,CAAC,OAAO,GAC/B,MAAMmC,EAAY,EAAE,CAAC7F,EAAE,GAAG,CAE9B,CACF,EACA,IAAI,CAAC,sBAAsB,CAAGiF,CAChC,CACA,MAAM,eAAee,CAAK,CAAE,CAC1B,GAAI,IAAI,CAAC,WAAW,CAClB,MAAM,AAAIC,MACR,CAAC,uCAAuC,EAAE,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAED,EAAM,CAAC,CAG3F,OAAM5B,OAAO,IAAI,CAAC,MAAM,CAAC4B,EAAO,CAAE,OAAQ,EAAK,GAC/C,IAAI,CAAC,WAAW,CAAGA,CACrB,CACA,MAAM,gBAAiB,CACrB,OAAO,IAAI,CAAC,WAAW,AACzB,CAKA,MAAM,mBAAoB,CAExB,MAAOE,AADM,OAAM9B,OAAO,IAAI,CAAC,KAAK,CAAC,CAAE,cAAe,EAAK,EAAC,EAChD,GAAG,CAAC,AAAC+B,GAAS,EACxB,GAAI,CAAC,EAAEA,EAAI,EAAE,CAAC,CAAC,CACf,MAAOA,EAAI,KAAK,CAChB,IAAKA,EAAI,GAAG,CACZ,iBAAkBA,EAAI,MAAM,AAC9B,IAAI,MAAM,CAAC,AAACA,GAAQA,EAAI,EAAE,EAAIA,EAAI,KAAK,EAAIA,EAAI,GAAG,CACpD,CACA,MAAM,+BAAgC,CACpC,GAAI,IAAI,CAAC,WAAW,CAClB,OAAO,IAAI,CAAC,WAAW,CAEzB,IAAMH,EAAQ,MAAM5B,OAAO,IAAI,CAAC,KAAK,CAAC,CAAE,OAAQ,GAAM,cAAe,EAAK,GAAG,IAAI,CAAC,AAAC8B,GAASA,CAAI,CAAC,EAAE,EAAE,IAErG,OADA,IAAI,CAAC,WAAW,CAAGF,GAAS,EACrB,IAAI,CAAC,WAAW,AACzB,CACA,MAAM,gBAAiB,CAErB,GADA,AAAC,GAAGgX,GAAe,MAAM,AAAD,EAAG,CAAC,IAAI,CAAC,SAAS,CAAE,qBACxC,IAAI,CAAC,iBAAiB,CAAE,CAC1B,MAAM,IAAI,CAAC,iBAAiB,CAC5B,MACF,CACA,IAAI,CAAC,iBAAiB,CAAG,AAAC,WACxB,IAAM5W,EAAM,MAAM,IAAI,CAAC,GAAG,GACtBC,EAAQ,KACZ,GAAID,EAAI,UAAU,CAAC,aACjB,MAAM,AAAIH,MACR,qHAGJ,GAAI,CACF,IAAMK,EAAe,MAAM,IAAI,CAAC,6BAA6B,GAC7D,GAAI,IAAI,CAAC,uBAAuB,GAAKA,EACnC,OAEF,GAAI,IAAI,CAAC,uBAAuB,EAAI,IAAI,CAAC,uBAAuB,GAAKA,EAAc,CACjFC,QAAQ,GAAG,CACT,0BACA,IAAI,CAAC,uBAAuB,CAC5B,KACAD,GAEF,GAAI,CACF,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,uBAAuB,CACxD,CAAE,MAAOE,EAAQ,CACfD,QAAQ,KAAK,CAAC,4BAA6BC,EAC7C,CACF,CACAD,QAAQ,GAAG,CAAC,qBAAsBD,GAClC,MAAMlC,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAE,MAAOkC,CAAa,EAAG,OACtD,MAAM8W,GAAO,KACb,IAAI,CAAC,uBAAuB,CAAG9W,EAC/B,MAAM,IAAI,CAAC,wBAAwB,EACrC,CAAE,MAAOxI,EAAG,CACVyI,QAAQ,KAAK,CAAC,4BAA6BzI,GAC3CuI,EAAQvI,CACV,QAAU,CACR,IAAI,CAAC,iBAAiB,CAAG,IAC3B,CACA,GAAIuI,EACF,MAAMA,CAEV,KACA,MAAM,IAAI,CAAC,iBAAiB,AAC9B,CACA,MAAM,iBAAiBnB,CAAC,CAAExG,CAAC,CAAE,CAC3B,IAAM+H,EAAgB,CAAC;AAC3B;AACA;AACA,2DAA2D,EAAEvB,EAAE,EAAE,EAAExG,EAAE;AACrE;AACA;AACA;AACA,QAAQ,CAAC,AACL,OAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CACnD,WAAY,CAAC,EAAE+H,EAAc,CAAC,AAChC,EACF,CACA,MAAM,kBAAmB,CACvB,MAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CACnD,WAAY,CAAC;AACnB;AACA;AACA;AACA,UAAU,CAAC,AACP,EACF,CACA,MAAM,eAAeT,CAAK,CAAE,CAC1B,IAAMU,EAAgBV,GAAS,IAAI,CAAC,uBAAuB,CAE3D,GADAO,QAAQ,GAAG,CAAC,qBAAsBG,GAC9B,CAACA,EAAe,CAClBH,QAAQ,IAAI,CAAC,uBACb,MACF,CACA,GAAI,CACF,MAAM,IAAI,CAAC,yBAAyB,CAACG,GACrC,MAAM0W,GAAO,IACf,CAAE,MAAO/W,EAAO,CACdE,QAAQ,IAAI,CAAC,yCAA0CF,EACzD,CACA,GAAI,CACF,MAAMjC,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAE,MAAOsC,CAAc,EACtD,CAAE,MAAOL,EAAO,CACdE,QAAQ,IAAI,CAAC,4BAA6BF,EAC5C,CACA,IAAI,CAAC,uBAAuB,CAAG,IACjC,CACA,MAAM,0BAA2B,CAC3B,IAAI,CAAC,sBAAsB,EAC7B,MAAMjC,OAAO,QAAQ,CAAC,WAAW,CAC/B,CAAE,MAAO,IAAI,CAAC,uBAAuB,AAAC,EACtC,mBACA,CACE,WAAY1B,EACd,GAGJ,IAAM2B,EAAS,MAAMG,IACrB,OAAMJ,OAAO,QAAQ,CAAC,WAAW,CAC/B,CAAE,MAAO,IAAI,CAAC,uBAAuB,AAAC,EACtC,mBACA,CACE,WAAYC,CACd,EAEJ,CACA,MAAM,0BAA0B2B,CAAK,CAAE,CACrC,IAAM3B,EAAS,MAAMM,IACrB,OAAMP,OAAO,QAAQ,CAAC,WAAW,CAAC,CAAE4B,MAAAA,CAAM,EAAG,mBAAoB,CAC/D,WAAY3B,CACd,EACF,CACA,MAAM,sBAAsBsC,CAAO,CAAEC,CAAM,CAAE,CAI3C,OAHA,MAAM,IAAI,CAAC,cAAc,GACzB,AAAC,GAAGoW,GAAe,MAAM,AAAD,EAAG,IAAI,CAAC,uBAAuB,CAAE,4BACzD,IAAI,CAAC,wBAAwB,GACtB,MAAM5Y,OAAO,QAAQ,CAAC,WAAW,CACtC,CAAE,MAAO,IAAI,CAAC,uBAAuB,AAAC,EACtCuC,EACAC,EAEJ,CACA,MAAM,qBAAsB,CAC1B,IAAMvC,EAAS,MAAMH,IACrB,OAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CACnD,WAAYG,CACd,GAYA,IAAMwC,EAAc,MAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CACvE,WAAY,CAAC,CAAC,EAAEC,AAZC,MACjBC,OAAO,0BAA0B,CAAC,4BAA4B,GACvD,CACL,KAAMA,OAAO,0BAA0B,CAAC,kBAAkB,GAC1D,KAAM,CACJ,MAAOC,SAAS,eAAe,CAAC,WAAW,CAC3C,OAAQA,SAAS,eAAe,CAAC,YAAY,CAC7C,IAAKD,OAAO,gBAAgB,AAC9B,CACF,EACF,EAE6B,QAAQ,GAAG,GAAG,CAAC,CAC1C,cAAe,EACjB,GACA,GAAI,CAACF,EAAY,MAAM,CAAC,KAAK,CAAE,CAC7B,IAAMI,EAAmBJ,EAAY,gBAAgB,EAAE,WAAW,aAAe,EAIjF,OAHKI,GACHV,QAAQ,KAAK,CAAC,uBAAwBM,GAElC,AAAIZ,MACR,CAAC,6CAA6C,EAAEgB,EAAiB,CAAC,CAEtE,CACA,OAAOJ,EAAY,MAAM,CAAC,KAAK,AACjC,CACA,MAAM,mBAAmBxC,CAAM,CAAE,CAC/B,OAAO,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CACpD,WAAYA,CACd,EACF,CAEA,MAAM,sBAAuB,CAE3B,IAAM6C,EAAYtH,KAAK,GAAG,GACtBuH,EAAiB,GACrB,KAAOvH,KAAK,GAAG,GAAKsH,EAHJ,KAGyB,CAKvC,GAAIC,AAAmB,aADvBA,CAAAA,EAAiB9B,AAHF,OAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CAClE,WAAY,qBACd,EAAC,EACuB,MAAM,CAAC,KAAK,AAAD,EACA,CACjC,MAAM,IAAI7B,QAAQ,AAACyJ,GAAaxJ,WAAWwJ,EAAU,MACrD,MACF,CACA,MAAM,IAAIzJ,QAAQ,AAACyJ,GAAaxJ,WAAWwJ,EAAU,KACvD,CACA,MAAM,AAAIhH,MACR,CAAC,oDAAoD,EAAEkB,EAAe,CAAC,CAE3E,CACA,MAAM,iBAAkB,CACtB,IAAMC,EAAO,MAAM,IAAI,CAAC,mBAAmB,GAC3C,MAAO,AAAC,GAAG2V,GAAkB,UAAU,AAAD,EAAG3V,EAC3C,CACA,MAAM,cAAcC,CAAE,CAAE,CACtB,IAAMhD,EAAS,MAAMH,KAQrB,OAPA,MAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CACnD,WAAYG,CACd,GAKOgB,AAJQ,OAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CAClE,WAAY,CAAC,iDAAiD,EAAEgC,EAAG,EAAE,CAAC,CACtE,cAAe,EACjB,EAAC,EACa,MAAM,CAAC,KAAK,AAC5B,CACA,MAAM,sBAAsBC,CAAK,CAAE,CACjC,IAAMjD,EAAS,MAAMH,KAQrB,OAPA,MAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CACnD,WAAYG,CACd,GAKOgB,AAJQ,OAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAoB,CAClE,WAAY,CAAC,yDAAyD,EAAEiC,EAAM,EAAE,CAAC,CACjF,cAAe,EACjB,EAAC,EACa,MAAM,CAAC,KAAK,AAC5B,CACA,MAAM,qBAAsB,CAC1B,MAAM,IAAI,CAAC,gBAAgB,GAC3B,IAAMC,EAAU,MAAM,IAAI,CAAC,mBAAmB,GAI9C,OAHIA,GAAS,MACX,KAAI,CAAC,YAAY,CAAGA,EAAQ,IAAI,AAAD,EAE1BA,GAAS,MAAQ,CAAE,KAAM,KAAM,SAAU,EAAE,AAAC,CACrD,CACA,MAAM,MAAO,QACX,AAAI,IAAI,CAAC,YAAY,CACZ,IAAI,CAAC,YAAY,CAEnBA,AADS,OAAM,IAAI,CAAC,mBAAmB,EAAC,EAChC,IAAI,AACrB,CACA,MAAM,kBAAmB,CACvB,MAAM,IAAI,CAAC,gBAAgB,GAC3B,IAAMC,EAAS,MAAM,IAAI,CAAC,qBAAqB,CAAC,yBAA0B,CACxE,OAAQ,OACR,QAAS,EACX,GACA,MAAO,CAAC,uBAAuB,EAAEA,EAAO,IAAI,CAAC,CAAC,AAChD,CACA,MAAM,KAAM,CACV,IAAMxB,EAAQ,MAAM,IAAI,CAAC,6BAA6B,GAEtD,OAAOI,AADK,MAAMhC,OAAO,IAAI,CAAC,GAAG,CAAC4B,GAAO,IAAI,CAAC,AAACG,GAAQA,EAAI,GAAG,GAChD,EAChB,CACA,MAAM,eAAesB,CAAa,CAAE,CAIlC,OAHIA,GACF,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAACA,EAAc,IAAI,CAAEA,EAAc,GAAG,EAEtD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAG,SAC7B,CACA,MAAM,kBAAkBA,CAAa,CAAE,CAIrC,OAHIA,GACF,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAACA,EAAc,IAAI,CAAEA,EAAc,GAAG,EAEtD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAG,QAC7B,CACA,MAAM,gBAAgBA,CAAa,CAAE,CAInC,OAHIA,GACF,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAACA,EAAc,IAAI,CAAEA,EAAc,GAAG,EAEtD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAU,EACpC,CACA,MAAM,iBAAiBA,CAAa,CAAE,CAIpC,OAHIA,GACF,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAACA,EAAc,IAAI,CAAEA,EAAc,GAAG,EAEtD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAS,EACnC,CACA,MAAM,SAASC,CAAQ,CAAED,CAAa,CAAE,CACtC,GAAM,CAAEE,OAAAA,CAAM,CAAE,CAAG,MAAM,IAAI,CAAC,IAAI,GAElC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CACrB,EACA,CAHqBD,CAAAA,GAAYC,AAAS,GAATA,CAAW,EAI5CF,GAAe,KACfA,GAAe,IAEnB,CACA,MAAM,WAAWC,CAAQ,CAAED,CAAa,CAAE,CACxC,GAAM,CAAEE,OAAAA,CAAM,CAAE,CAAG,MAAM,IAAI,CAAC,IAAI,GAElC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CACrB,EAFqBD,GAAYC,AAAS,GAATA,EAIjCF,GAAe,KACfA,GAAe,IAEnB,CACA,MAAM,WAAWC,CAAQ,CAAED,CAAa,CAAE,CACxC,GAAM,CAAEG,MAAAA,CAAK,CAAE,CAAG,MAAM,IAAI,CAAC,IAAI,GAEjC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CACrB,CAFqBF,CAAAA,GAAYE,AAAQ,GAARA,CAAU,EAG3C,EACAH,GAAe,KACfA,GAAe,IAEnB,CACA,MAAM,YAAYC,CAAQ,CAAED,CAAa,CAAE,CACzC,GAAM,CAAEG,MAAAA,CAAK,CAAE,CAAG,MAAM,IAAI,CAAC,IAAI,GAEjC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CADAF,GAAYE,AAAQ,GAARA,EAGjC,EACAH,GAAe,KACfA,GAAe,IAEnB,CACA,MAAM,WAAWI,CAAO,CAAE,CACxB,GAAI,CAACA,EAAS,CACZtB,QAAQ,IAAI,CAAC,6BACb,MACF,CACA,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAACsB,EAAQ,MAAM,CAAC,EAAE,CAAEA,EAAQ,MAAM,CAAC,EAAE,EAC3D,MAAM,IAAI,CAAC,qBAAqB,CAAC,yBAA0B,CACzD,KAAM,UACN,SAAU,CAAC,YAAY,AACzB,GACA,MAAM,IAAI,CAAC,qBAAqB,CAAC,yBAA0B,CACzD,KAAM,QACN,SAAU,CAAC,YAAY,AACzB,GACA,MAAMuV,GAAO,KACb,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CACxB,IAAK,WACP,EACF,CACA,MAAM,SAAU,CACd,IAAI,CAAC,WAAW,CAAG,KACnB,MAAM,IAAI,CAAC,cAAc,GACzB,IAAI,CAAC,SAAS,CAAG,EACnB,CACF,EAGI/T,GAAc,EAAQ,2KCj9FnB,SAASgU,EAAwBC,CAAO,EAC3C,GAHOC,KAGgBD,EACnB,OAAOD,EAAwBC,CAAO,CAACC,EAAsB,EAEjE,IAAIxN,EAAO,2BAA6B,CAACuN,GAKzC,OAJKvN,IACDA,EAAO,CAAC,EACR,2BAA6B,CAACuN,EAASvN,IAEpCA,CACX,QAjFO,SAASyN,EAAuCC,CAAkB,CAAEC,CAAS,CAAEC,CAAc,EAChG,IAEI5b,EAFA6b,EAAgBF,EAAU,aAAa,CACrCG,EAAkBH,EAAU,MAAM,CAKxC,GAH6B,UAAzB,OAAOE,GACPA,CAAAA,EAAgB,CAACA,EAAc,AAAD,EAE9B7e,MAAM,OAAO,CAAC6e,GACd,IAAIA,EAAc,MAAM,CAAG,EACvB,GAAIC,EAAgB,UAAU,CAC1B9b,EAAQ8b,EAAgB,YAAY,KAEnC,CACD,IAAIC,EAAuBC,EAA6BN,EAAoBG,EACxE,EAACE,EAAqB,aAAa,EAAIH,GACvCG,CAAAA,EAAuBC,EAA6BJ,EAAgBC,EAAa,EAErF,IAAII,EAAkB,EACjBF,CAAAA,EAAqB,aAAa,EACnCE,CAAAA,EACIH,EAAgB,QAAQ,EACnBD,AAAqB,YAArBA,CAAa,CAAC,EAAE,EAAkBA,AAAyB,IAAzBA,EAAc,MAAM,AAAM,EAEzE7b,EAAQic,EAAkBH,EAAgB,YAAY,CAAGC,EAAqB,aAAa,AAC/F,CACJ,MAMA,IAAK,IAAMG,KAHPJ,EAAgB,QAAQ,EACxB9b,CAAAA,EAAQ,CAAC,GAEc6b,EAAe,CACtC,IAAMM,EAAiBL,EAAgB,IAAI,CAAC,eAAe,CAACI,EAAa,CAEnEE,EAAgBX,EAAuCC,EAAoB,CAC7E,cAFiBG,CAAa,CAACK,EAAa,CAG5C,OAAQC,CACZ,EAAGP,EACmBS,MAAAA,IAAlBD,IACKpc,GACDA,CAAAA,EAAQ,CAAC,GAEbA,CAAK,CAACkc,EAAa,CAAGE,EAE9B,CAEJ,OAAOpc,CACX,mBACA,SAASgc,EAA6BM,CAAM,CAAET,CAAa,EACvD,IAAMvY,EAAS,CAAE,cAAe,EAAM,EAClCrH,EAAI,EACR,KAAOA,EAAI4f,EAAc,MAAM,CAAE,EAAE5f,EAAG,CAClC,IAAMsgB,EAAoBV,CAAa,CAAC5f,EAAE,CAE1C,GAAIqgB,GAAUC,KAAqBD,EAC/BA,EAASA,CAAM,CAACC,EAAkB,MAGlC,KAER,CAKA,OAJItgB,IAAM4f,EAAc,MAAM,GAC1BvY,EAAO,aAAa,CAAGgZ,EACvBhZ,EAAO,aAAa,CAAG,IAEpBA,CACX,CACA,IAAMkY,EAAwBgB,OAAO,GAAG,CAAC,6ZC3ElC,IAAMC,EAAmB,CAC5B,KAAMD,OAAO,GAAG,CAAC,4BACjB,UAAWA,OAAO,GAAG,CAAC,gCAC1B,EAQO,SAASE,EAAqBvb,EAAU,CAAC,CAAC,EAC7C,IAAIiN,EAAU,IAAIuO,EAAmBxb,EAAQ,aAAa,EAO1D,OANIA,EAAQ,IAAI,EACZiN,CAAAA,EAAUA,EAAQ,QAAQ,CAACqO,EAAiB,IAAI,CAAEtb,EAAQ,IAAI,GAE9DA,EAAQ,SAAS,EACjBiN,CAAAA,EAAUA,EAAQ,QAAQ,CAACqO,EAAiB,SAAS,CAAEtb,EAAQ,SAAS,GAErEiN,CACX,CAEO,MAAMuO,EACT,YAAYC,CAAc,CAAE,CACxB,IAAI,CAAC,WAAW,CACZA,aAA0BD,EACpB,IAAIE,IAAID,EAAe,WAAW,EAClC,IAAIC,GAClB,CACA,SAAStd,CAAG,CAAES,CAAK,CAAE,CACjB,IAAM8c,EAAa,IAAIH,EAAmB,IAAI,EAE9C,OADAG,EAAW,WAAW,CAAC,GAAG,CAACvd,EAAKS,GACzB8c,CACX,CACA,SAASvd,CAAG,CAAE,CACV,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAACA,EAChC,CACA,YAAYA,CAAG,CAAE,CACb,IAAMud,EAAa,IAAIH,EAAmB,IAAI,EAE9C,OADAG,EAAW,WAAW,CAAC,MAAM,CAACvd,GACvBud,CACX,CACJ"}