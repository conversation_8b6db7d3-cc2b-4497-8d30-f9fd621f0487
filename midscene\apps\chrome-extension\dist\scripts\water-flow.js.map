{"version": 3, "file": "scripts/water-flow.js", "sources": ["webpack://chrome-extension/./src/scripts/water-flow.ts"], "sourcesContent": ["const midsceneWaterFlowAnimation = {\r\n  styleElement: null as null | HTMLStyleElement,\r\n\r\n  mousePointerAttribute: 'data-water-flow-pointer',\r\n\r\n  lastCallTime: 0,\r\n\r\n  cleanupTimeout: null as null | number,\r\n\r\n  // call to reset the self cleaning timer\r\n  registerSelfCleaning() {\r\n    // clean up all the indicators if there is no call for 30 seconds\r\n    this.lastCallTime = Date.now();\r\n    const cleaningTimeout = 30000;\r\n\r\n    if (this.cleanupTimeout) {\r\n      clearTimeout(this.cleanupTimeout);\r\n    }\r\n\r\n    this.cleanupTimeout = window.setTimeout(() => {\r\n      const now = Date.now();\r\n      if (now - this.lastCallTime >= cleaningTimeout) {\r\n        this.disable();\r\n      }\r\n    }, cleaningTimeout);\r\n  },\r\n\r\n  showMousePointer(x: number, y: number) {\r\n    this.enable(); // show water flow animation\r\n    this.registerSelfCleaning();\r\n    const existingPointer = document.querySelector(\r\n      `div[${this.mousePointerAttribute}]`,\r\n    ) as HTMLDivElement | null;\r\n\r\n    // Clear any existing timeouts to prevent race conditions\r\n    if (existingPointer) {\r\n      const timeoutId = Number(existingPointer.getAttribute('data-timeout-id'));\r\n      if (timeoutId) clearTimeout(timeoutId);\r\n      const removeTimeoutId = Number(\r\n        existingPointer.getAttribute('data-remove-timeout-id'),\r\n      );\r\n      if (removeTimeoutId) clearTimeout(removeTimeoutId);\r\n    }\r\n\r\n    const size = 30;\r\n    const pointer =\r\n      existingPointer ||\r\n      (() => {\r\n        const p = document.createElement('div');\r\n        p.setAttribute(this.mousePointerAttribute, 'true');\r\n        p.style.position = 'fixed';\r\n        p.style.width = `${size}px`;\r\n        p.style.height = `${size}px`;\r\n        p.style.borderRadius = '50%';\r\n        p.style.backgroundColor = 'rgba(0, 0, 255, 0.3)';\r\n        p.style.border = '1px solid rgba(0, 0, 255, 0.3)';\r\n        p.style.zIndex = '99999';\r\n        p.style.transition = 'all 1s ease-in';\r\n        p.style.pointerEvents = 'none'; // Make pointer not clickable\r\n        // Start from offset position if new pointer\r\n        p.style.left = `${x - size / 2}px`;\r\n        p.style.top = `${y - size / 2}px`;\r\n        document.body.appendChild(p);\r\n        return p;\r\n      })();\r\n\r\n    requestAnimationFrame(() => {\r\n      pointer.style.left = `${x - size / 2}px`;\r\n      pointer.style.top = `${y - size / 2}px`;\r\n      pointer.style.opacity = '1';\r\n    });\r\n\r\n    // Set new timeouts\r\n    const fadeTimeoutId = setTimeout(() => {\r\n      pointer.style.opacity = '0';\r\n      const removeTimeoutId = setTimeout(() => {\r\n        if (pointer.parentNode) {\r\n          document.body.removeChild(pointer);\r\n        }\r\n      }, 500);\r\n      pointer.setAttribute('data-remove-timeout-id', String(removeTimeoutId));\r\n    }, 3000);\r\n    pointer.setAttribute('data-timeout-id', String(fadeTimeoutId));\r\n  },\r\n\r\n  hideMousePointer() {\r\n    this.registerSelfCleaning();\r\n    const pointer = document.querySelector(\r\n      `div[${this.mousePointerAttribute}]`,\r\n    ) as HTMLDivElement | null;\r\n    if (pointer) {\r\n      document.body.removeChild(pointer);\r\n    }\r\n  },\r\n\r\n  enable() {\r\n    this.registerSelfCleaning();\r\n    if (this.styleElement) {\r\n      // double check if styleElement is still in the dom tree\r\n      if (document.head.contains(this.styleElement)) {\r\n        return;\r\n      }\r\n      this.styleElement = null;\r\n    }\r\n\r\n    this.styleElement = document.createElement('style');\r\n    this.styleElement.id = 'water-flow-animation';\r\n    this.styleElement.textContent = `\r\n    html::before {\r\n      content: \"\";\r\n      position: fixed;\r\n      top: 0; right: 0; bottom: 0; left: 0;\r\n      pointer-events: none;\r\n      z-index: 9999;\r\n      background:\r\n        linear-gradient(to right, rgba(30, 144, 255, 0.4), transparent 50%) left,\r\n        linear-gradient(to left, rgba(30, 144, 255, 0.4), transparent 50%) right,\r\n        linear-gradient(to bottom, rgba(30, 144, 255, 0.4), transparent 50%) top,\r\n        linear-gradient(to top, rgba(30, 144, 255, 0.4), transparent 50%) bottom;\r\n      background-repeat: no-repeat;\r\n      background-size: 10% 100%, 10% 100%, 100% 10%, 100% 10%;\r\n      animation: waterflow 5s cubic-bezier(0.4, 0, 0.6, 1) infinite;\r\n      filter: blur(8px);\r\n    }\r\n\r\n    @keyframes waterflow {\r\n      0%, 100% {\r\n        background-image:\r\n          linear-gradient(to right, rgba(30, 144, 255, 0.4), transparent 50%),\r\n          linear-gradient(to left, rgba(30, 144, 255, 0.4), transparent 50%),\r\n          linear-gradient(to bottom, rgba(30, 144, 255, 0.4), transparent 50%),\r\n          linear-gradient(to top, rgba(30, 144, 255, 0.4), transparent 50%);\r\n        transform: scale(1);\r\n      }\r\n      25% {\r\n        background-image:\r\n          linear-gradient(to right, rgba(30, 144, 255, 0.39), transparent 52%),\r\n          linear-gradient(to left, rgba(30, 144, 255, 0.39), transparent 52%),\r\n          linear-gradient(to bottom, rgba(30, 144, 255, 0.39), transparent 52%),\r\n          linear-gradient(to top, rgba(30, 144, 255, 0.39), transparent 52%);\r\n        transform: scale(1.03);\r\n      }\r\n      50% {\r\n        background-image:\r\n          linear-gradient(to right, rgba(30, 144, 255, 0.38), transparent 55%),\r\n          linear-gradient(to left, rgba(30, 144, 255, 0.38), transparent 55%),\r\n          linear-gradient(to bottom, rgba(30, 144, 255, 0.38), transparent 55%),\r\n          linear-gradient(to top, rgba(30, 144, 255, 0.38), transparent 55%);\r\n        transform: scale(1.05);\r\n      }\r\n      75% {\r\n        background-image:\r\n          linear-gradient(to right, rgba(30, 144, 255, 0.39), transparent 52%),\r\n          linear-gradient(to left, rgba(30, 144, 255, 0.39), transparent 52%),\r\n          linear-gradient(to bottom, rgba(30, 144, 255, 0.39), transparent 52%),\r\n          linear-gradient(to top, rgba(30, 144, 255, 0.39), transparent 52%);\r\n        transform: scale(1.03);\r\n      }\r\n    }\r\n    `;\r\n    document.head.appendChild(this.styleElement);\r\n  },\r\n\r\n  disable() {\r\n    if (this.cleanupTimeout) {\r\n      clearTimeout(this.cleanupTimeout);\r\n      this.cleanupTimeout = null;\r\n    }\r\n\r\n    const styleElements = document.querySelectorAll('#water-flow-animation');\r\n    styleElements.forEach((element) => {\r\n      document.head.removeChild(element);\r\n    });\r\n    this.styleElement = null;\r\n\r\n    // remove all mouse pointers\r\n    const mousePointers = document.querySelectorAll(\r\n      `div[${this.mousePointerAttribute}]`,\r\n    );\r\n    mousePointers.forEach((element) => {\r\n      document.body.removeChild(element);\r\n    });\r\n  },\r\n};\r\n\r\nexport {};\r\ndeclare global {\r\n  interface Window {\r\n    midsceneWaterFlowAnimation: typeof midsceneWaterFlowAnimation;\r\n  }\r\n}\r\n(window as any).midsceneWaterFlowAnimation =\r\n  (window as any).midsceneWaterFlowAnimation || midsceneWaterFlowAnimation;\r\n(window as any).midsceneWaterFlowAnimation.enable();\r\n"], "names": ["window", "Date", "clearTimeout", "x", "y", "existingPointer", "document", "timeoutId", "Number", "removeTimeoutId", "pointer", "p", "size", "requestAnimationFrame", "fadeTimeoutId", "setTimeout", "String", "styleElements", "element", "mousePointers"], "mappings": "mNA+LCA,OAAe,0BAA0B,CACvCA,OAAe,0BAA0B,EAhMT,CACjC,aAAc,KAEd,sBAAuB,0BAEvB,aAAc,EAEd,eAAgB,KAGhB,uBAEE,IAAI,CAAC,YAAY,CAAGC,KAAK,GAAG,GAGxB,IAAI,CAAC,cAAc,EACrBC,aAAa,IAAI,CAAC,cAAc,EAGlC,IAAI,CAAC,cAAc,CAAGF,OAAO,UAAU,CAAC,KAC1BC,KAAK,GAAG,GACV,IAAI,CAAC,YAAY,EARL,KASpB,IAAI,CAAC,OAAO,EAEhB,EAXwB,IAY1B,EAEA,iBAAiBE,CAAS,CAAEC,CAAS,EACnC,IAAI,CAAC,MAAM,GACX,IAAI,CAAC,oBAAoB,GACzB,IAAMC,EAAkBC,SAAS,aAAa,CAC5C,CAAC,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAItC,GAAID,EAAiB,CACnB,IAAME,EAAYC,OAAOH,EAAgB,YAAY,CAAC,oBAClDE,GAAWL,aAAaK,GAC5B,IAAME,EAAkBD,OACtBH,EAAgB,YAAY,CAAC,2BAE3BI,GAAiBP,aAAaO,EACpC,CAGA,IAAMC,EACJL,GACC,AAAC,MACA,IAAMM,EAAIL,SAAS,aAAa,CAAC,OAejC,OAdAK,EAAE,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAE,QAC3CA,EAAE,KAAK,CAAC,QAAQ,CAAG,QACnBA,EAAE,KAAK,CAAC,KAAK,CAAG,OAChBA,EAAE,KAAK,CAAC,MAAM,CAAG,OACjBA,EAAE,KAAK,CAAC,YAAY,CAAG,MACvBA,EAAE,KAAK,CAAC,eAAe,CAAG,uBAC1BA,EAAE,KAAK,CAAC,MAAM,CAAG,iCACjBA,EAAE,KAAK,CAAC,MAAM,CAAG,QACjBA,EAAE,KAAK,CAAC,UAAU,CAAG,iBACrBA,EAAE,KAAK,CAAC,aAAa,CAAG,OAExBA,EAAE,KAAK,CAAC,IAAI,CAAG,CAAC,EAAER,EAAIS,GAAS,EAAE,CAAC,CAClCD,EAAE,KAAK,CAAC,GAAG,CAAG,CAAC,EAAEP,EAAIQ,GAAS,EAAE,CAAC,CACjCN,SAAS,IAAI,CAAC,WAAW,CAACK,GACnBA,CACT,KAEFE,sBAAsB,KACpBH,EAAQ,KAAK,CAAC,IAAI,CAAG,CAAC,EAAEP,EAAIS,GAAS,EAAE,CAAC,CACxCF,EAAQ,KAAK,CAAC,GAAG,CAAG,CAAC,EAAEN,EAAIQ,GAAS,EAAE,CAAC,CACvCF,EAAQ,KAAK,CAAC,OAAO,CAAG,GAC1B,GAGA,IAAMI,EAAgBC,WAAW,KAC/BL,EAAQ,KAAK,CAAC,OAAO,CAAG,IACxB,IAAMD,EAAkBM,WAAW,KAC7BL,EAAQ,UAAU,EACpBJ,SAAS,IAAI,CAAC,WAAW,CAACI,EAE9B,EAAG,KACHA,EAAQ,YAAY,CAAC,yBAA0BM,OAAOP,GACxD,EAAG,KACHC,EAAQ,YAAY,CAAC,kBAAmBM,OAAOF,GACjD,EAEA,mBACE,IAAI,CAAC,oBAAoB,GACzB,IAAMJ,EAAUJ,SAAS,aAAa,CACpC,CAAC,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAElCI,GACFJ,SAAS,IAAI,CAAC,WAAW,CAACI,EAE9B,EAEA,SAEE,GADA,IAAI,CAAC,oBAAoB,GACrB,IAAI,CAAC,YAAY,CAAE,CAErB,GAAIJ,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAC1C,MAEF,KAAI,CAAC,YAAY,CAAG,IACtB,CAEA,IAAI,CAAC,YAAY,CAAGA,SAAS,aAAa,CAAC,SAC3C,IAAI,CAAC,YAAY,CAAC,EAAE,CAAG,uBACvB,IAAI,CAAC,YAAY,CAAC,WAAW,CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoDhC,CAAC,CACDA,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAC7C,EAEA,UACM,IAAI,CAAC,cAAc,GACrBJ,aAAa,IAAI,CAAC,cAAc,EAChC,IAAI,CAAC,cAAc,CAAG,MAIxBe,AADsBX,SAAS,gBAAgB,CAAC,yBAClC,OAAO,CAAC,AAACY,IACrBZ,SAAS,IAAI,CAAC,WAAW,CAACY,EAC5B,GACA,IAAI,CAAC,YAAY,CAAG,KAMpBC,AAHsBb,SAAS,gBAAgB,CAC7C,CAAC,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAExB,OAAO,CAAC,AAACY,IACrBZ,SAAS,IAAI,CAAC,WAAW,CAACY,EAC5B,EACF,CACF,EAUClB,OAAe,0BAA0B,CAAC,MAAM,G"}