"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// modern.inspect.config.ts
var modern_inspect_config_exports = {};
__export(modern_inspect_config_exports, {
  default: () => modern_inspect_config_default
});
module.exports = __toCommonJS(modern_inspect_config_exports);
var import_module_tools = require("@modern-js/module-tools");
var modern_inspect_config_default = (0, import_module_tools.defineConfig)({
  plugins: [(0, import_module_tools.moduleTools)()],
  buildConfig: {
    platform: "browser",
    buildType: "bundle",
    format: "iife",
    dts: false,
    input: {
      htmlElement: "src/extractor/index.ts",
      htmlElementDebug: "src/extractor/debug.ts"
    },
    autoExternal: false,
    outDir: "dist/script",
    esbuildOptions: (options) => {
      options.globalName = "midscene_element_inspector";
      return options;
    },
    target: "es6"
  }
});
