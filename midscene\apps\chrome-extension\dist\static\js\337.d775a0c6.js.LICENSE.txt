/*!
   * mustache.js - Logic-less {{mustache}} templates with JavaScript
   * http://github.com/janl/mustache.js
   */

/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> <<EMAIL>> <http://feross.org>
 * @license  MIT
 */

/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> <https://feross.org>
 * @license  MIT
 */

/*!
 * https://github.com/Starcounter-Jack/JSON-Patch
 * (c) 2013-2021 <PERSON>
 * MIT license
 */

/*!
 * https://github.com/Starcounter-Jack/JSON-Patch
 * (c) 2017-2022 <PERSON>
 * <PERSON> licensed
 */

/*! @azure/msal-browser v3.28.0 2024-12-12 */

/*! @azure/msal-common v14.16.0 2024-11-05 */

/*! http://mths.be/fromcodepoint v0.2.1 by @mathias */

/*! https://mths.be/punycode v1.4.1 by @mathias */

/*! https://mths.be/utf8js v3.0.0 by @mathias */

/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */

/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */

/**
 * [js-sha256]{@link https://github.com/emn178/js-sha256}
 *
 * @version 0.11.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2024
 * @license MIT
 */