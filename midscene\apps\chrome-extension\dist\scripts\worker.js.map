{"version": 3, "file": "scripts/worker.js", "sources": ["webpack://chrome-extension/./src/utils.ts", "webpack://chrome-extension/./src/scripts/worker.ts"], "sourcesContent": ["/// <reference types=\"chrome\" />\r\nimport type { WebUIContext } from '@midscene/web/utils';\r\n\r\nexport const workerMessageTypes = {\r\n  SAVE_CONTEXT: 'save-context',\r\n  GET_CONTEXT: 'get-context',\r\n};\r\n\r\n// save screenshot\r\nexport interface WorkerRequestSaveContext {\r\n  context: WebUIContext;\r\n}\r\n\r\nexport interface WorkerResponseSaveContext {\r\n  id: string;\r\n}\r\n\r\n// get screenshot\r\nexport interface WorkerRequestGetContext {\r\n  id: string;\r\n}\r\n\r\nexport interface WorkerResponseGetContext {\r\n  context: WebUIContext;\r\n}\r\n\r\nexport async function sendToWorker<Payload, Result = any>(\r\n  type: string,\r\n  payload: Payload,\r\n): Promise<Result> {\r\n  return new Promise((resolve, reject) => {\r\n    chrome.runtime.sendMessage({ type, payload }, (response) => {\r\n      if (response.error) {\r\n        reject(response.error);\r\n      } else {\r\n        resolve(response);\r\n      }\r\n    });\r\n  });\r\n}\r\n\r\nexport function getPlaygroundUrl(cacheContextId: string) {\r\n  return chrome.runtime.getURL(\r\n    `./pages/playground.html?cache_context_id=${cacheContextId}`,\r\n  );\r\n}\r\n\r\nexport async function activeTab(): Promise<chrome.tabs.Tab> {\r\n  return new Promise((resolve, reject) => {\r\n    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {\r\n      if (tabs?.[0]) {\r\n        resolve(tabs[0]);\r\n      } else {\r\n        reject(new Error('No active tab found'));\r\n      }\r\n    });\r\n  });\r\n}\r\n\r\nexport async function currentWindowId(): Promise<number> {\r\n  return new Promise((resolve, reject) => {\r\n    chrome.windows.getCurrent((window) => {\r\n      if (window?.id) {\r\n        resolve(window.id);\r\n      } else {\r\n        reject(new Error('No active window found'));\r\n      }\r\n    });\r\n  });\r\n}\r\n\r\nexport function getExtensionVersion() {\r\n  return chrome.runtime?.getManifest()?.version || 'unknown';\r\n}\r\n", "/// <reference types=\"chrome\" />\r\n\r\nimport type { WebUIContext } from '@midscene/web/utils';\r\nimport {\r\n  type WorkerRequestGetContext,\r\n  type WorkerRequestSaveContext,\r\n  workerMessageTypes,\r\n} from '../utils';\r\n\r\n// console-browserify won't work in worker, so we need to use globalThis.console\r\nconst console = globalThis.console;\r\n\r\nchrome.sidePanel\r\n  .setPanelBehavior({ openPanelOnActionClick: true })\r\n  .catch((error) => console.error(error));\r\n\r\n// cache data between sidepanel and fullscreen playground\r\nconst randomUUID = () => {\r\n  return Math.random().toString(36).substring(2, 15);\r\n};\r\nconst cacheMap = new Map<string, WebUIContext>();\r\nchrome.runtime.onMessage.addListener((request, sender, sendResponse) => {\r\n  console.log('Message received in service worker:', request);\r\n\r\n  switch (request.type) {\r\n    case workerMessageTypes.SAVE_CONTEXT: {\r\n      const payload: WorkerRequestSaveContext = request.payload;\r\n      const { context } = payload;\r\n      const id = randomUUID();\r\n      cacheMap.set(id, context);\r\n      sendResponse({ id });\r\n      break;\r\n    }\r\n    case workerMessageTypes.GET_CONTEXT: {\r\n      const payload: WorkerRequestGetContext = request.payload;\r\n      const { id } = payload;\r\n      const context = cacheMap.get(id) as WebUIContext;\r\n      if (!context) {\r\n        sendResponse({ error: 'Screenshot not found' });\r\n      } else {\r\n        sendResponse({ context });\r\n      }\r\n\r\n      break;\r\n    }\r\n    default:\r\n      console.log('sending response');\r\n      sendResponse({ error: 'Unknown message type' });\r\n      break;\r\n  }\r\n});\r\n"], "names": ["workerMessageTypes", "console", "globalThis", "chrome", "error", "randomUUID", "Math", "cacheMap", "Map", "request", "sender", "sendResponse", "context", "id"], "mappings": "mBAGO,IAAMA,EAAqB,CAChC,aAAc,eACd,YAAa,aACf,ECIMC,EAAUC,WAAW,OAAO,CAElCC,OAAO,SAAS,CACb,gBAAgB,CAAC,CAAE,uBAAwB,EAAK,GAChD,KAAK,CAAC,AAACC,GAAUH,EAAQ,KAAK,CAACG,IAGlC,IAAMC,EAAa,IACVC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,EAAG,IAE3CC,EAAW,IAAIC,IACrBL,OAAO,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAACM,EAASC,EAAQC,KAGrD,OAFAV,EAAQ,GAAG,CAAC,sCAAuCQ,GAE3CA,EAAQ,IAAI,EAClB,KAAKT,EAAmB,YAAY,CAAE,CAEpC,GAAM,CAAEY,QAAAA,CAAO,CAAE,CADyBH,EAAQ,OAAO,CAEnDI,EAAKR,IACXE,EAAS,GAAG,CAACM,EAAID,GACjBD,EAAa,CAAEE,GAAAA,CAAG,GAClB,KACF,CACA,KAAKb,EAAmB,WAAW,CAAE,CAEnC,GAAM,CAAEa,GAAAA,CAAE,CAAE,CAD6BJ,EAAQ,OAAO,CAElDG,EAAUL,EAAS,GAAG,CAACM,GAI3BF,EAAa,AAHVC,EAGU,CAAEA,QAAAA,CAAQ,EAFV,CAAE,MAAO,sBAAuB,GAK/C,KACF,CACA,QACEX,EAAQ,GAAG,CAAC,oBACZU,EAAa,CAAE,MAAO,sBAAuB,EAEjD,CACF,G"}