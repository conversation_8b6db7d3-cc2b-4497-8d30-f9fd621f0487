declare const ifInBrowser: boolean;
declare function uuid(): string;
declare function generateHashId(rect: any, content?: string): string;
/**
 * A utility function that asserts a condition and throws an error with a message if the condition is false.
 *
 * @param condition - The condition to assert
 * @param message - The error message to throw if the condition is false
 * @throws Error with the provided message if the condition is false
 */
declare function assert(condition: any, message?: string): asserts condition;
type GlobalScope = typeof window | typeof globalThis | typeof self | undefined;
declare function getGlobalScope(): GlobalScope;
declare function setIsMcp(value: boolean): void;
declare function logMsg(...message: Parameters<typeof console.log>): void;
declare function repeat(times: number, fn: (index: number) => Promise<void>): Promise<void>;
declare const escapeHtml: (html: string) => string;
declare const antiEscapeHtml: (html: string) => string;

export { antiEscapeHtml, assert, escapeHtml, generateHashId, getGlobalScope, ifInBrowser, logMsg, repeat, setIsMcp, uuid };
