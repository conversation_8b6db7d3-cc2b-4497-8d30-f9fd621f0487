{"version": 3, "file": "static/js/async/450.41f8b458.js", "sources": ["webpack://chrome-extension/../../node_modules/.pnpm/@langchain+core@0.3.26_openai@4.81.0_ws@8.18.1_zod@3.24.3_/node_modules/@langchain/core/dist/messages/modifier.js", "webpack://chrome-extension/../../node_modules/.pnpm/@langchain+core@0.3.26_openai@4.81.0_ws@8.18.1_zod@3.24.3_/node_modules/@langchain/core/dist/prompts/image.js", "webpack://chrome-extension/../../node_modules/.pnpm/@langchain+core@0.3.26_openai@4.81.0_ws@8.18.1_zod@3.24.3_/node_modules/@langchain/core/dist/prompts/chat.js", "webpack://chrome-extension/../../node_modules/.pnpm/@langchain+core@0.3.26_openai@4.81.0_ws@8.18.1_zod@3.24.3_/node_modules/@langchain/core/dist/prompts/few_shot.js"], "sourcesContent": ["import { BaseMessage } from \"./base.js\";\n/**\n * Message responsible for deleting other messages.\n */\nexport class RemoveMessage extends BaseMessage {\n    constructor(fields) {\n        super({\n            ...fields,\n            content: \"\",\n        });\n        /**\n         * The ID of the message to remove.\n         */\n        Object.defineProperty(this, \"id\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.id = fields.id;\n    }\n    _getType() {\n        return \"remove\";\n    }\n    get _printableFields() {\n        return {\n            ...super._printableFields,\n            id: this.id,\n        };\n    }\n}\n", "import { ImagePromptValue } from \"../prompt_values.js\";\nimport { BasePromptTemplate, } from \"./base.js\";\nimport { checkValidTemplate, renderTemplate, } from \"./template.js\";\n/**\n * An image prompt template for a multimodal model.\n */\nexport class ImagePromptTemplate extends BasePromptTemplate {\n    static lc_name() {\n        return \"ImagePromptTemplate\";\n    }\n    constructor(input) {\n        super(input);\n        Object.defineProperty(this, \"lc_namespace\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: [\"langchain_core\", \"prompts\", \"image\"]\n        });\n        Object.defineProperty(this, \"template\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"templateFormat\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"f-string\"\n        });\n        Object.defineProperty(this, \"validateTemplate\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        /**\n         * Additional fields which should be included inside\n         * the message content array if using a complex message\n         * content.\n         */\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        Object.defineProperty(this, \"additionalContentFields\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.template = input.template;\n        this.templateFormat = input.templateFormat ?? this.templateFormat;\n        this.validateTemplate = input.validateTemplate ?? this.validateTemplate;\n        this.additionalContentFields = input.additionalContentFields;\n        if (this.validateTemplate) {\n            let totalInputVariables = this.inputVariables;\n            if (this.partialVariables) {\n                totalInputVariables = totalInputVariables.concat(Object.keys(this.partialVariables));\n            }\n            checkValidTemplate([\n                { type: \"image_url\", image_url: this.template },\n            ], this.templateFormat, totalInputVariables);\n        }\n    }\n    _getPromptType() {\n        return \"prompt\";\n    }\n    /**\n     * Partially applies values to the prompt template.\n     * @param values The values to be partially applied to the prompt template.\n     * @returns A new instance of ImagePromptTemplate with the partially applied values.\n     */\n    async partial(values) {\n        const newInputVariables = this.inputVariables.filter((iv) => !(iv in values));\n        const newPartialVariables = {\n            ...(this.partialVariables ?? {}),\n            ...values,\n        };\n        const promptDict = {\n            ...this,\n            inputVariables: newInputVariables,\n            partialVariables: newPartialVariables,\n        };\n        return new ImagePromptTemplate(promptDict);\n    }\n    /**\n     * Formats the prompt template with the provided values.\n     * @param values The values to be used to format the prompt template.\n     * @returns A promise that resolves to a string which is the formatted prompt.\n     */\n    async format(values) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const formatted = {};\n        for (const [key, value] of Object.entries(this.template)) {\n            if (typeof value === \"string\") {\n                formatted[key] = renderTemplate(value, this.templateFormat, values);\n            }\n            else {\n                formatted[key] = value;\n            }\n        }\n        const url = values.url || formatted.url;\n        const detail = values.detail || formatted.detail;\n        if (!url) {\n            throw new Error(\"Must provide either an image URL.\");\n        }\n        if (typeof url !== \"string\") {\n            throw new Error(\"url must be a string.\");\n        }\n        const output = { url };\n        if (detail) {\n            output.detail = detail;\n        }\n        return output;\n    }\n    /**\n     * Formats the prompt given the input values and returns a formatted\n     * prompt value.\n     * @param values The input values to format the prompt.\n     * @returns A Promise that resolves to a formatted prompt value.\n     */\n    async formatPromptValue(values) {\n        const formattedPrompt = await this.format(values);\n        return new ImagePromptValue(formattedPrompt);\n    }\n}\n", "// Default generic \"any\" values are for backwards compatibility.\n// Replace with \"string\" when we are comfortable with a breaking change.\nimport { AIMessage, HumanMessage, SystemMessage, BaseMessage, ChatMessage, coerceMessageLikeToMessage, isBaseMessage, } from \"../messages/index.js\";\nimport { ChatPromptValue, } from \"../prompt_values.js\";\nimport { Runnable } from \"../runnables/base.js\";\nimport { BaseStringPromptTemplate } from \"./string.js\";\nimport { BasePromptTemplate, } from \"./base.js\";\nimport { PromptTemplate, } from \"./prompt.js\";\nimport { ImagePromptTemplate } from \"./image.js\";\nimport { parseFString, parseMustache, } from \"./template.js\";\nimport { addLangChainErrorFields } from \"../errors/index.js\";\n/**\n * Abstract class that serves as a base for creating message prompt\n * templates. It defines how to format messages for different roles in a\n * conversation.\n */\nexport class BaseMessagePromptTemplate extends Runnable {\n    constructor() {\n        super(...arguments);\n        Object.defineProperty(this, \"lc_namespace\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: [\"langchain_core\", \"prompts\", \"chat\"]\n        });\n        Object.defineProperty(this, \"lc_serializable\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n    }\n    /**\n     * Calls the formatMessages method with the provided input and options.\n     * @param input Input for the formatMessages method\n     * @param options Optional BaseCallbackConfig\n     * @returns Formatted output messages\n     */\n    async invoke(input, options) {\n        return this._callWithConfig((input) => this.formatMessages(input), input, { ...options, runType: \"prompt\" });\n    }\n}\n/**\n * Class that represents a placeholder for messages in a chat prompt. It\n * extends the BaseMessagePromptTemplate.\n */\nexport class MessagesPlaceholder extends BaseMessagePromptTemplate {\n    static lc_name() {\n        return \"MessagesPlaceholder\";\n    }\n    constructor(fields) {\n        if (typeof fields === \"string\") {\n            // eslint-disable-next-line no-param-reassign\n            fields = { variableName: fields };\n        }\n        super(fields);\n        Object.defineProperty(this, \"variableName\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"optional\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.variableName = fields.variableName;\n        this.optional = fields.optional ?? false;\n    }\n    get inputVariables() {\n        return [this.variableName];\n    }\n    async formatMessages(values) {\n        const input = values[this.variableName];\n        if (this.optional && !input) {\n            return [];\n        }\n        else if (!input) {\n            const error = new Error(`Field \"${this.variableName}\" in prompt uses a MessagesPlaceholder, which expects an array of BaseMessages as an input value. Received: undefined`);\n            error.name = \"InputFormatError\";\n            throw error;\n        }\n        let formattedMessages;\n        try {\n            if (Array.isArray(input)) {\n                formattedMessages = input.map(coerceMessageLikeToMessage);\n            }\n            else {\n                formattedMessages = [coerceMessageLikeToMessage(input)];\n            }\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        }\n        catch (e) {\n            const readableInput = typeof input === \"string\" ? input : JSON.stringify(input, null, 2);\n            const error = new Error([\n                `Field \"${this.variableName}\" in prompt uses a MessagesPlaceholder, which expects an array of BaseMessages or coerceable values as input.`,\n                `Received value: ${readableInput}`,\n                `Additional message: ${e.message}`,\n            ].join(\"\\n\\n\"));\n            error.name = \"InputFormatError\";\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            error.lc_error_code = e.lc_error_code;\n            throw error;\n        }\n        return formattedMessages;\n    }\n}\n/**\n * Abstract class that serves as a base for creating message string prompt\n * templates. It extends the BaseMessagePromptTemplate.\n */\nexport class BaseMessageStringPromptTemplate extends BaseMessagePromptTemplate {\n    constructor(fields) {\n        if (!(\"prompt\" in fields)) {\n            // eslint-disable-next-line no-param-reassign\n            fields = { prompt: fields };\n        }\n        super(fields);\n        Object.defineProperty(this, \"prompt\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.prompt = fields.prompt;\n    }\n    get inputVariables() {\n        return this.prompt.inputVariables;\n    }\n    async formatMessages(values) {\n        return [await this.format(values)];\n    }\n}\n/**\n * Abstract class that serves as a base for creating chat prompt\n * templates. It extends the BasePromptTemplate.\n */\nexport class BaseChatPromptTemplate extends BasePromptTemplate {\n    constructor(input) {\n        super(input);\n    }\n    async format(values) {\n        return (await this.formatPromptValue(values)).toString();\n    }\n    async formatPromptValue(values) {\n        const resultMessages = await this.formatMessages(values);\n        return new ChatPromptValue(resultMessages);\n    }\n}\n/**\n * Class that represents a chat message prompt template. It extends the\n * BaseMessageStringPromptTemplate.\n */\nexport class ChatMessagePromptTemplate extends BaseMessageStringPromptTemplate {\n    static lc_name() {\n        return \"ChatMessagePromptTemplate\";\n    }\n    constructor(fields, role) {\n        if (!(\"prompt\" in fields)) {\n            // eslint-disable-next-line no-param-reassign, @typescript-eslint/no-non-null-assertion\n            fields = { prompt: fields, role: role };\n        }\n        super(fields);\n        Object.defineProperty(this, \"role\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.role = fields.role;\n    }\n    async format(values) {\n        return new ChatMessage(await this.prompt.format(values), this.role);\n    }\n    static fromTemplate(template, role, options) {\n        return new this(PromptTemplate.fromTemplate(template, {\n            templateFormat: options?.templateFormat,\n        }), role);\n    }\n}\nclass _StringImageMessagePromptTemplate extends BaseMessagePromptTemplate {\n    static _messageClass() {\n        throw new Error(\"Can not invoke _messageClass from inside _StringImageMessagePromptTemplate\");\n    }\n    constructor(\n    /** @TODO When we come up with a better way to type prompt templates, fix this */\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    fields, additionalOptions) {\n        if (!(\"prompt\" in fields)) {\n            // eslint-disable-next-line no-param-reassign\n            fields = { prompt: fields };\n        }\n        super(fields);\n        Object.defineProperty(this, \"lc_namespace\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: [\"langchain_core\", \"prompts\", \"chat\"]\n        });\n        Object.defineProperty(this, \"lc_serializable\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"inputVariables\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: []\n        });\n        Object.defineProperty(this, \"additionalOptions\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: {}\n        });\n        Object.defineProperty(this, \"prompt\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"messageClass\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        // ChatMessage contains role field, others don't.\n        // Because of this, we have a separate class property for ChatMessage.\n        Object.defineProperty(this, \"chatMessageClass\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.prompt = fields.prompt;\n        if (Array.isArray(this.prompt)) {\n            let inputVariables = [];\n            this.prompt.forEach((prompt) => {\n                if (\"inputVariables\" in prompt) {\n                    inputVariables = inputVariables.concat(prompt.inputVariables);\n                }\n            });\n            this.inputVariables = inputVariables;\n        }\n        else {\n            this.inputVariables = this.prompt.inputVariables;\n        }\n        this.additionalOptions = additionalOptions ?? this.additionalOptions;\n    }\n    createMessage(content) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const constructor = this.constructor;\n        if (constructor._messageClass()) {\n            const MsgClass = constructor._messageClass();\n            return new MsgClass({ content });\n        }\n        else if (constructor.chatMessageClass) {\n            const MsgClass = constructor.chatMessageClass();\n            // Assuming ChatMessage constructor also takes a content argument\n            return new MsgClass({\n                content,\n                role: this.getRoleFromMessageClass(MsgClass.lc_name()),\n            });\n        }\n        else {\n            throw new Error(\"No message class defined\");\n        }\n    }\n    getRoleFromMessageClass(name) {\n        switch (name) {\n            case \"HumanMessage\":\n                return \"human\";\n            case \"AIMessage\":\n                return \"ai\";\n            case \"SystemMessage\":\n                return \"system\";\n            case \"ChatMessage\":\n                return \"chat\";\n            default:\n                throw new Error(\"Invalid message class name\");\n        }\n    }\n    static fromTemplate(template, additionalOptions) {\n        if (typeof template === \"string\") {\n            return new this(PromptTemplate.fromTemplate(template, additionalOptions));\n        }\n        const prompt = [];\n        for (const item of template) {\n            if (typeof item === \"string\" ||\n                (typeof item === \"object\" && \"text\" in item)) {\n                let text = \"\";\n                if (typeof item === \"string\") {\n                    text = item;\n                }\n                else if (typeof item.text === \"string\") {\n                    text = item.text ?? \"\";\n                }\n                const options = {\n                    ...additionalOptions,\n                    ...(typeof item !== \"string\"\n                        ? { additionalContentFields: item }\n                        : {}),\n                };\n                prompt.push(PromptTemplate.fromTemplate(text, options));\n            }\n            else if (typeof item === \"object\" && \"image_url\" in item) {\n                let imgTemplate = item.image_url ?? \"\";\n                let imgTemplateObject;\n                let inputVariables = [];\n                if (typeof imgTemplate === \"string\") {\n                    let parsedTemplate;\n                    if (additionalOptions?.templateFormat === \"mustache\") {\n                        parsedTemplate = parseMustache(imgTemplate);\n                    }\n                    else {\n                        parsedTemplate = parseFString(imgTemplate);\n                    }\n                    const variables = parsedTemplate.flatMap((item) => item.type === \"variable\" ? [item.name] : []);\n                    if ((variables?.length ?? 0) > 0) {\n                        if (variables.length > 1) {\n                            throw new Error(`Only one format variable allowed per image template.\\nGot: ${variables}\\nFrom: ${imgTemplate}`);\n                        }\n                        inputVariables = [variables[0]];\n                    }\n                    else {\n                        inputVariables = [];\n                    }\n                    imgTemplate = { url: imgTemplate };\n                    imgTemplateObject = new ImagePromptTemplate({\n                        template: imgTemplate,\n                        inputVariables,\n                        templateFormat: additionalOptions?.templateFormat,\n                        additionalContentFields: item,\n                    });\n                }\n                else if (typeof imgTemplate === \"object\") {\n                    if (\"url\" in imgTemplate) {\n                        let parsedTemplate;\n                        if (additionalOptions?.templateFormat === \"mustache\") {\n                            parsedTemplate = parseMustache(imgTemplate.url);\n                        }\n                        else {\n                            parsedTemplate = parseFString(imgTemplate.url);\n                        }\n                        inputVariables = parsedTemplate.flatMap((item) => item.type === \"variable\" ? [item.name] : []);\n                    }\n                    else {\n                        inputVariables = [];\n                    }\n                    imgTemplateObject = new ImagePromptTemplate({\n                        template: imgTemplate,\n                        inputVariables,\n                        templateFormat: additionalOptions?.templateFormat,\n                        additionalContentFields: item,\n                    });\n                }\n                else {\n                    throw new Error(\"Invalid image template\");\n                }\n                prompt.push(imgTemplateObject);\n            }\n        }\n        return new this({ prompt, additionalOptions });\n    }\n    async format(input) {\n        // eslint-disable-next-line no-instanceof/no-instanceof\n        if (this.prompt instanceof BaseStringPromptTemplate) {\n            const text = await this.prompt.format(input);\n            return this.createMessage(text);\n        }\n        else {\n            const content = [];\n            for (const prompt of this.prompt) {\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                let inputs = {};\n                if (!(\"inputVariables\" in prompt)) {\n                    throw new Error(`Prompt ${prompt} does not have inputVariables defined.`);\n                }\n                for (const item of prompt.inputVariables) {\n                    if (!inputs) {\n                        inputs = { [item]: input[item] };\n                    }\n                    inputs = { ...inputs, [item]: input[item] };\n                }\n                // eslint-disable-next-line no-instanceof/no-instanceof\n                if (prompt instanceof BaseStringPromptTemplate) {\n                    const formatted = await prompt.format(inputs);\n                    let additionalContentFields;\n                    if (\"additionalContentFields\" in prompt) {\n                        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                        additionalContentFields = prompt.additionalContentFields;\n                    }\n                    content.push({\n                        ...additionalContentFields,\n                        type: \"text\",\n                        text: formatted,\n                    });\n                    /** @TODO replace this */\n                    // eslint-disable-next-line no-instanceof/no-instanceof\n                }\n                else if (prompt instanceof ImagePromptTemplate) {\n                    const formatted = await prompt.format(inputs);\n                    let additionalContentFields;\n                    if (\"additionalContentFields\" in prompt) {\n                        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                        additionalContentFields = prompt.additionalContentFields;\n                    }\n                    content.push({\n                        ...additionalContentFields,\n                        type: \"image_url\",\n                        image_url: formatted,\n                    });\n                }\n            }\n            return this.createMessage(content);\n        }\n    }\n    async formatMessages(values) {\n        return [await this.format(values)];\n    }\n}\n/**\n * Class that represents a human message prompt template. It extends the\n * BaseMessageStringPromptTemplate.\n * @example\n * ```typescript\n * const message = HumanMessagePromptTemplate.fromTemplate(\"{text}\");\n * const formatted = await message.format({ text: \"Hello world!\" });\n *\n * const chatPrompt = ChatPromptTemplate.fromMessages([message]);\n * const formattedChatPrompt = await chatPrompt.invoke({\n *   text: \"Hello world!\",\n * });\n * ```\n */\nexport class HumanMessagePromptTemplate extends _StringImageMessagePromptTemplate {\n    static _messageClass() {\n        return HumanMessage;\n    }\n    static lc_name() {\n        return \"HumanMessagePromptTemplate\";\n    }\n}\n/**\n * Class that represents an AI message prompt template. It extends the\n * BaseMessageStringPromptTemplate.\n */\nexport class AIMessagePromptTemplate extends _StringImageMessagePromptTemplate {\n    static _messageClass() {\n        return AIMessage;\n    }\n    static lc_name() {\n        return \"AIMessagePromptTemplate\";\n    }\n}\n/**\n * Class that represents a system message prompt template. It extends the\n * BaseMessageStringPromptTemplate.\n * @example\n * ```typescript\n * const message = SystemMessagePromptTemplate.fromTemplate(\"{text}\");\n * const formatted = await message.format({ text: \"Hello world!\" });\n *\n * const chatPrompt = ChatPromptTemplate.fromMessages([message]);\n * const formattedChatPrompt = await chatPrompt.invoke({\n *   text: \"Hello world!\",\n * });\n * ```\n */\nexport class SystemMessagePromptTemplate extends _StringImageMessagePromptTemplate {\n    static _messageClass() {\n        return SystemMessage;\n    }\n    static lc_name() {\n        return \"SystemMessagePromptTemplate\";\n    }\n}\nfunction _isBaseMessagePromptTemplate(baseMessagePromptTemplateLike) {\n    return (typeof baseMessagePromptTemplateLike\n        .formatMessages === \"function\");\n}\nfunction _coerceMessagePromptTemplateLike(messagePromptTemplateLike, extra) {\n    if (_isBaseMessagePromptTemplate(messagePromptTemplateLike) ||\n        isBaseMessage(messagePromptTemplateLike)) {\n        return messagePromptTemplateLike;\n    }\n    if (Array.isArray(messagePromptTemplateLike) &&\n        messagePromptTemplateLike[0] === \"placeholder\") {\n        const messageContent = messagePromptTemplateLike[1];\n        if (typeof messageContent !== \"string\" ||\n            messageContent[0] !== \"{\" ||\n            messageContent[messageContent.length - 1] !== \"}\") {\n            throw new Error(`Invalid placeholder template: \"${messagePromptTemplateLike[1]}\". Expected a variable name surrounded by curly braces.`);\n        }\n        const variableName = messageContent.slice(1, -1);\n        return new MessagesPlaceholder({ variableName, optional: true });\n    }\n    const message = coerceMessageLikeToMessage(messagePromptTemplateLike);\n    let templateData;\n    if (typeof message.content === \"string\") {\n        templateData = message.content;\n    }\n    else {\n        // Assuming message.content is an array of complex objects, transform it.\n        templateData = message.content.map((item) => {\n            if (\"text\" in item) {\n                return { ...item, text: item.text };\n            }\n            else if (\"image_url\" in item) {\n                return { ...item, image_url: item.image_url };\n            }\n            else {\n                return item;\n            }\n        });\n    }\n    if (message._getType() === \"human\") {\n        return HumanMessagePromptTemplate.fromTemplate(templateData, extra);\n    }\n    else if (message._getType() === \"ai\") {\n        return AIMessagePromptTemplate.fromTemplate(templateData, extra);\n    }\n    else if (message._getType() === \"system\") {\n        return SystemMessagePromptTemplate.fromTemplate(templateData, extra);\n    }\n    else if (ChatMessage.isInstance(message)) {\n        return ChatMessagePromptTemplate.fromTemplate(message.content, message.role, extra);\n    }\n    else {\n        throw new Error(`Could not coerce message prompt template from input. Received message type: \"${message._getType()}\".`);\n    }\n}\nfunction isMessagesPlaceholder(x) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return x.constructor.lc_name() === \"MessagesPlaceholder\";\n}\n/**\n * Class that represents a chat prompt. It extends the\n * BaseChatPromptTemplate and uses an array of BaseMessagePromptTemplate\n * instances to format a series of messages for a conversation.\n * @example\n * ```typescript\n * const message = SystemMessagePromptTemplate.fromTemplate(\"{text}\");\n * const chatPrompt = ChatPromptTemplate.fromMessages([\n *   [\"ai\", \"You are a helpful assistant.\"],\n *   message,\n * ]);\n * const formattedChatPrompt = await chatPrompt.invoke({\n *   text: \"Hello world!\",\n * });\n * ```\n */\nexport class ChatPromptTemplate extends BaseChatPromptTemplate {\n    static lc_name() {\n        return \"ChatPromptTemplate\";\n    }\n    get lc_aliases() {\n        return {\n            promptMessages: \"messages\",\n        };\n    }\n    constructor(input) {\n        super(input);\n        Object.defineProperty(this, \"promptMessages\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"validateTemplate\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"templateFormat\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"f-string\"\n        });\n        // If input is mustache and validateTemplate is not defined, set it to false\n        if (input.templateFormat === \"mustache\" &&\n            input.validateTemplate === undefined) {\n            this.validateTemplate = false;\n        }\n        Object.assign(this, input);\n        if (this.validateTemplate) {\n            const inputVariablesMessages = new Set();\n            for (const promptMessage of this.promptMessages) {\n                // eslint-disable-next-line no-instanceof/no-instanceof\n                if (promptMessage instanceof BaseMessage)\n                    continue;\n                for (const inputVariable of promptMessage.inputVariables) {\n                    inputVariablesMessages.add(inputVariable);\n                }\n            }\n            const totalInputVariables = this.inputVariables;\n            const inputVariablesInstance = new Set(this.partialVariables\n                ? totalInputVariables.concat(Object.keys(this.partialVariables))\n                : totalInputVariables);\n            const difference = new Set([...inputVariablesInstance].filter((x) => !inputVariablesMessages.has(x)));\n            if (difference.size > 0) {\n                throw new Error(`Input variables \\`${[\n                    ...difference,\n                ]}\\` are not used in any of the prompt messages.`);\n            }\n            const otherDifference = new Set([...inputVariablesMessages].filter((x) => !inputVariablesInstance.has(x)));\n            if (otherDifference.size > 0) {\n                throw new Error(`Input variables \\`${[\n                    ...otherDifference,\n                ]}\\` are used in prompt messages but not in the prompt template.`);\n            }\n        }\n    }\n    _getPromptType() {\n        return \"chat\";\n    }\n    async _parseImagePrompts(message, inputValues) {\n        if (typeof message.content === \"string\") {\n            return message;\n        }\n        const formattedMessageContent = await Promise.all(message.content.map(async (item) => {\n            if (item.type !== \"image_url\") {\n                return item;\n            }\n            let imageUrl = \"\";\n            if (typeof item.image_url === \"string\") {\n                imageUrl = item.image_url;\n            }\n            else {\n                imageUrl = item.image_url.url;\n            }\n            const promptTemplatePlaceholder = PromptTemplate.fromTemplate(imageUrl, {\n                templateFormat: this.templateFormat,\n            });\n            const formattedUrl = await promptTemplatePlaceholder.format(inputValues);\n            if (typeof item.image_url !== \"string\" && \"url\" in item.image_url) {\n                // eslint-disable-next-line no-param-reassign\n                item.image_url.url = formattedUrl;\n            }\n            else {\n                // eslint-disable-next-line no-param-reassign\n                item.image_url = formattedUrl;\n            }\n            return item;\n        }));\n        // eslint-disable-next-line no-param-reassign\n        message.content = formattedMessageContent;\n        return message;\n    }\n    async formatMessages(values) {\n        const allValues = await this.mergePartialAndUserVariables(values);\n        let resultMessages = [];\n        for (const promptMessage of this.promptMessages) {\n            // eslint-disable-next-line no-instanceof/no-instanceof\n            if (promptMessage instanceof BaseMessage) {\n                resultMessages.push(await this._parseImagePrompts(promptMessage, allValues));\n            }\n            else {\n                const inputValues = promptMessage.inputVariables.reduce((acc, inputVariable) => {\n                    if (!(inputVariable in allValues) &&\n                        !(isMessagesPlaceholder(promptMessage) && promptMessage.optional)) {\n                        const error = addLangChainErrorFields(new Error(`Missing value for input variable \\`${inputVariable.toString()}\\``), \"INVALID_PROMPT_INPUT\");\n                        throw error;\n                    }\n                    acc[inputVariable] = allValues[inputVariable];\n                    return acc;\n                }, {});\n                const message = await promptMessage.formatMessages(inputValues);\n                resultMessages = resultMessages.concat(message);\n            }\n        }\n        return resultMessages;\n    }\n    async partial(values) {\n        // This is implemented in a way it doesn't require making\n        // BaseMessagePromptTemplate aware of .partial()\n        const newInputVariables = this.inputVariables.filter((iv) => !(iv in values));\n        const newPartialVariables = {\n            ...(this.partialVariables ?? {}),\n            ...values,\n        };\n        const promptDict = {\n            ...this,\n            inputVariables: newInputVariables,\n            partialVariables: newPartialVariables,\n        };\n        return new ChatPromptTemplate(promptDict);\n    }\n    static fromTemplate(template, options) {\n        const prompt = PromptTemplate.fromTemplate(template, options);\n        const humanTemplate = new HumanMessagePromptTemplate({ prompt });\n        return this.fromMessages([humanTemplate]);\n    }\n    /**\n     * Create a chat model-specific prompt from individual chat messages\n     * or message-like tuples.\n     * @param promptMessages Messages to be passed to the chat model\n     * @returns A new ChatPromptTemplate\n     */\n    static fromMessages(promptMessages, extra) {\n        const flattenedMessages = promptMessages.reduce((acc, promptMessage) => acc.concat(\n        // eslint-disable-next-line no-instanceof/no-instanceof\n        promptMessage instanceof ChatPromptTemplate\n            ? promptMessage.promptMessages\n            : [\n                _coerceMessagePromptTemplateLike(promptMessage, extra),\n            ]), []);\n        const flattenedPartialVariables = promptMessages.reduce((acc, promptMessage) => \n        // eslint-disable-next-line no-instanceof/no-instanceof\n        promptMessage instanceof ChatPromptTemplate\n            ? Object.assign(acc, promptMessage.partialVariables)\n            : acc, Object.create(null));\n        const inputVariables = new Set();\n        for (const promptMessage of flattenedMessages) {\n            // eslint-disable-next-line no-instanceof/no-instanceof\n            if (promptMessage instanceof BaseMessage)\n                continue;\n            for (const inputVariable of promptMessage.inputVariables) {\n                if (inputVariable in flattenedPartialVariables) {\n                    continue;\n                }\n                inputVariables.add(inputVariable);\n            }\n        }\n        return new this({\n            ...extra,\n            inputVariables: [...inputVariables],\n            promptMessages: flattenedMessages,\n            partialVariables: flattenedPartialVariables,\n            templateFormat: extra?.templateFormat,\n        });\n    }\n    /** @deprecated Renamed to .fromMessages */\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    static fromPromptMessages(promptMessages) {\n        return this.fromMessages(promptMessages);\n    }\n}\n", "import { BaseStringPromptTemplate } from \"./string.js\";\nimport { checkValidTemplate, renderTemplate, } from \"./template.js\";\nimport { PromptTemplate } from \"./prompt.js\";\nimport { BaseChatPromptTemplate, } from \"./chat.js\";\n/**\n * Prompt template that contains few-shot examples.\n * @augments BasePromptTemplate\n * @augments FewShotPromptTemplateInput\n * @example\n * ```typescript\n * const examplePrompt = PromptTemplate.fromTemplate(\n *   \"Input: {input}\\nOutput: {output}\",\n * );\n *\n * const exampleSelector = await SemanticSimilarityExampleSelector.fromExamples(\n *   [\n *     { input: \"happy\", output: \"sad\" },\n *     { input: \"tall\", output: \"short\" },\n *     { input: \"energetic\", output: \"lethargic\" },\n *     { input: \"sunny\", output: \"gloomy\" },\n *     { input: \"windy\", output: \"calm\" },\n *   ],\n *   new OpenAIEmbeddings(),\n *   HNSWLib,\n *   { k: 1 },\n * );\n *\n * const dynamicPrompt = new FewShotPromptTemplate({\n *   exampleSelector,\n *   examplePrompt,\n *   prefix: \"Give the antonym of every input\",\n *   suffix: \"Input: {adjective}\\nOutput:\",\n *   inputVariables: [\"adjective\"],\n * });\n *\n * // Format the dynamic prompt with the input 'rainy'\n * console.log(await dynamicPrompt.format({ adjective: \"rainy\" }));\n *\n * ```\n */\nexport class FewShotPromptTemplate extends BaseStringPromptTemplate {\n    constructor(input) {\n        super(input);\n        Object.defineProperty(this, \"lc_serializable\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: false\n        });\n        Object.defineProperty(this, \"examples\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"exampleSelector\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"examplePrompt\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"suffix\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"\"\n        });\n        Object.defineProperty(this, \"exampleSeparator\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"\\n\\n\"\n        });\n        Object.defineProperty(this, \"prefix\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"\"\n        });\n        Object.defineProperty(this, \"templateFormat\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"f-string\"\n        });\n        Object.defineProperty(this, \"validateTemplate\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.assign(this, input);\n        if (this.examples !== undefined && this.exampleSelector !== undefined) {\n            throw new Error(\"Only one of 'examples' and 'example_selector' should be provided\");\n        }\n        if (this.examples === undefined && this.exampleSelector === undefined) {\n            throw new Error(\"One of 'examples' and 'example_selector' should be provided\");\n        }\n        if (this.validateTemplate) {\n            let totalInputVariables = this.inputVariables;\n            if (this.partialVariables) {\n                totalInputVariables = totalInputVariables.concat(Object.keys(this.partialVariables));\n            }\n            checkValidTemplate(this.prefix + this.suffix, this.templateFormat, totalInputVariables);\n        }\n    }\n    _getPromptType() {\n        return \"few_shot\";\n    }\n    static lc_name() {\n        return \"FewShotPromptTemplate\";\n    }\n    async getExamples(inputVariables) {\n        if (this.examples !== undefined) {\n            return this.examples;\n        }\n        if (this.exampleSelector !== undefined) {\n            return this.exampleSelector.selectExamples(inputVariables);\n        }\n        throw new Error(\"One of 'examples' and 'example_selector' should be provided\");\n    }\n    async partial(values) {\n        const newInputVariables = this.inputVariables.filter((iv) => !(iv in values));\n        const newPartialVariables = {\n            ...(this.partialVariables ?? {}),\n            ...values,\n        };\n        const promptDict = {\n            ...this,\n            inputVariables: newInputVariables,\n            partialVariables: newPartialVariables,\n        };\n        return new FewShotPromptTemplate(promptDict);\n    }\n    /**\n     * Formats the prompt with the given values.\n     * @param values The values to format the prompt with.\n     * @returns A promise that resolves to a string representing the formatted prompt.\n     */\n    async format(values) {\n        const allValues = await this.mergePartialAndUserVariables(values);\n        const examples = await this.getExamples(allValues);\n        const exampleStrings = await Promise.all(examples.map((example) => this.examplePrompt.format(example)));\n        const template = [this.prefix, ...exampleStrings, this.suffix].join(this.exampleSeparator);\n        return renderTemplate(template, this.templateFormat, allValues);\n    }\n    serialize() {\n        if (this.exampleSelector || !this.examples) {\n            throw new Error(\"Serializing an example selector is not currently supported\");\n        }\n        if (this.outputParser !== undefined) {\n            throw new Error(\"Serializing an output parser is not currently supported\");\n        }\n        return {\n            _type: this._getPromptType(),\n            input_variables: this.inputVariables,\n            example_prompt: this.examplePrompt.serialize(),\n            example_separator: this.exampleSeparator,\n            suffix: this.suffix,\n            prefix: this.prefix,\n            template_format: this.templateFormat,\n            examples: this.examples,\n        };\n    }\n    static async deserialize(data) {\n        const { example_prompt } = data;\n        if (!example_prompt) {\n            throw new Error(\"Missing example prompt\");\n        }\n        const examplePrompt = await PromptTemplate.deserialize(example_prompt);\n        let examples;\n        if (Array.isArray(data.examples)) {\n            examples = data.examples;\n        }\n        else {\n            throw new Error(\"Invalid examples format. Only list or string are supported.\");\n        }\n        return new FewShotPromptTemplate({\n            inputVariables: data.input_variables,\n            examplePrompt,\n            examples,\n            exampleSeparator: data.example_separator,\n            prefix: data.prefix,\n            suffix: data.suffix,\n            templateFormat: data.template_format,\n        });\n    }\n}\n/**\n * Chat prompt template that contains few-shot examples.\n * @augments BasePromptTemplateInput\n * @augments FewShotChatMessagePromptTemplateInput\n */\nexport class FewShotChatMessagePromptTemplate extends BaseChatPromptTemplate {\n    _getPromptType() {\n        return \"few_shot_chat\";\n    }\n    static lc_name() {\n        return \"FewShotChatMessagePromptTemplate\";\n    }\n    constructor(fields) {\n        super(fields);\n        Object.defineProperty(this, \"lc_serializable\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"examples\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"exampleSelector\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"examplePrompt\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"suffix\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"\"\n        });\n        Object.defineProperty(this, \"exampleSeparator\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"\\n\\n\"\n        });\n        Object.defineProperty(this, \"prefix\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"\"\n        });\n        Object.defineProperty(this, \"templateFormat\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"f-string\"\n        });\n        Object.defineProperty(this, \"validateTemplate\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        this.examples = fields.examples;\n        this.examplePrompt = fields.examplePrompt;\n        this.exampleSeparator = fields.exampleSeparator ?? \"\\n\\n\";\n        this.exampleSelector = fields.exampleSelector;\n        this.prefix = fields.prefix ?? \"\";\n        this.suffix = fields.suffix ?? \"\";\n        this.templateFormat = fields.templateFormat ?? \"f-string\";\n        this.validateTemplate = fields.validateTemplate ?? true;\n        if (this.examples !== undefined && this.exampleSelector !== undefined) {\n            throw new Error(\"Only one of 'examples' and 'example_selector' should be provided\");\n        }\n        if (this.examples === undefined && this.exampleSelector === undefined) {\n            throw new Error(\"One of 'examples' and 'example_selector' should be provided\");\n        }\n        if (this.validateTemplate) {\n            let totalInputVariables = this.inputVariables;\n            if (this.partialVariables) {\n                totalInputVariables = totalInputVariables.concat(Object.keys(this.partialVariables));\n            }\n            checkValidTemplate(this.prefix + this.suffix, this.templateFormat, totalInputVariables);\n        }\n    }\n    async getExamples(inputVariables) {\n        if (this.examples !== undefined) {\n            return this.examples;\n        }\n        if (this.exampleSelector !== undefined) {\n            return this.exampleSelector.selectExamples(inputVariables);\n        }\n        throw new Error(\"One of 'examples' and 'example_selector' should be provided\");\n    }\n    /**\n     * Formats the list of values and returns a list of formatted messages.\n     * @param values The values to format the prompt with.\n     * @returns A promise that resolves to a string representing the formatted prompt.\n     */\n    async formatMessages(values) {\n        const allValues = await this.mergePartialAndUserVariables(values);\n        let examples = await this.getExamples(allValues);\n        examples = examples.map((example) => {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const result = {};\n            this.examplePrompt.inputVariables.forEach((inputVariable) => {\n                result[inputVariable] = example[inputVariable];\n            });\n            return result;\n        });\n        const messages = [];\n        for (const example of examples) {\n            const exampleMessages = await this.examplePrompt.formatMessages(example);\n            messages.push(...exampleMessages);\n        }\n        return messages;\n    }\n    /**\n     * Formats the prompt with the given values.\n     * @param values The values to format the prompt with.\n     * @returns A promise that resolves to a string representing the formatted prompt.\n     */\n    async format(values) {\n        const allValues = await this.mergePartialAndUserVariables(values);\n        const examples = await this.getExamples(allValues);\n        const exampleMessages = await Promise.all(examples.map((example) => this.examplePrompt.formatMessages(example)));\n        const exampleStrings = exampleMessages\n            .flat()\n            .map((message) => message.content);\n        const template = [this.prefix, ...exampleStrings, this.suffix].join(this.exampleSeparator);\n        return renderTemplate(template, this.templateFormat, allValues);\n    }\n    /**\n     * Partially formats the prompt with the given values.\n     * @param values The values to partially format the prompt with.\n     * @returns A promise that resolves to an instance of `FewShotChatMessagePromptTemplate` with the given values partially formatted.\n     */\n    async partial(values) {\n        const newInputVariables = this.inputVariables.filter((variable) => !(variable in values));\n        const newPartialVariables = {\n            ...(this.partialVariables ?? {}),\n            ...values,\n        };\n        const promptDict = {\n            ...this,\n            inputVariables: newInputVariables,\n            partialVariables: newPartialVariables,\n        };\n        return new FewShotChatMessagePromptTemplate(promptDict);\n    }\n}\n"], "names": ["ImagePromptTemplate", "input", "Object", "totalInputVariables", "values", "newInputVariables", "iv", "newPartialVariables", "formatted", "key", "value", "url", "detail", "Error", "output", "formattedPrompt", "FewShotPromptTemplate", "undefined", "inputVariables", "allValues", "examples", "exampleStrings", "Promise", "example", "template", "data", "example_prompt", "examplePrompt", "Array"], "mappings": "qRAImC,KAAW,C,kCCEvC,OAAMA,UAA4B,GAAkB,CACvD,OAAO,SAAU,CACb,MAAO,qBACX,CACA,YAAYC,CAAK,CAAE,CA0Cf,GAzCA,KAAK,CAACA,GACNC,OAAO,cAAc,CAAC,IAAI,CAAE,eAAgB,CACxC,WAAY,GACZ,aAAc,GACd,SAAU,GACV,MAAO,CAAC,iBAAkB,UAAW,QAAQ,AACjD,GACAA,OAAO,cAAc,CAAC,IAAI,CAAE,WAAY,CACpC,WAAY,GACZ,aAAc,GACd,SAAU,GACV,MAAO,KAAK,CAChB,GACAA,OAAO,cAAc,CAAC,IAAI,CAAE,iBAAkB,CAC1C,WAAY,GACZ,aAAc,GACd,SAAU,GACV,MAAO,UACX,GACAA,OAAO,cAAc,CAAC,IAAI,CAAE,mBAAoB,CAC5C,WAAY,GACZ,aAAc,GACd,SAAU,GACV,MAAO,EACX,GAOAA,OAAO,cAAc,CAAC,IAAI,CAAE,0BAA2B,CACnD,WAAY,GACZ,aAAc,GACd,SAAU,GACV,MAAO,KAAK,CAChB,GACA,IAAI,CAAC,QAAQ,CAAGD,EAAM,QAAQ,CAC9B,IAAI,CAAC,cAAc,CAAGA,EAAM,cAAc,EAAI,IAAI,CAAC,cAAc,CACjE,IAAI,CAAC,gBAAgB,CAAGA,EAAM,gBAAgB,EAAI,IAAI,CAAC,gBAAgB,CACvE,IAAI,CAAC,uBAAuB,CAAGA,EAAM,uBAAuB,CACxD,IAAI,CAAC,gBAAgB,CAAE,CACvB,IAAIE,EAAsB,IAAI,CAAC,cAAc,AACzC,KAAI,CAAC,gBAAgB,EACrBA,CAAAA,EAAsBA,EAAoB,MAAM,CAACD,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAC,EAEvF,SAAmB,CACf,CAAE,KAAM,YAAa,UAAW,IAAI,CAAC,QAAQ,AAAC,EACjD,CAAE,IAAI,CAAC,cAAc,CAAEC,EAC5B,CACJ,CACA,gBAAiB,CACb,MAAO,QACX,CAMA,MAAM,QAAQC,CAAM,CAAE,CAClB,IAAMC,EAAoB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,AAACC,GAAO,CAAEA,CAAAA,KAAMF,CAAK,GACpEG,EAAsB,CACxB,GAAI,IAAI,CAAC,gBAAgB,EAAI,CAAC,CAAC,CAC/B,GAAGH,CAAM,AACb,EAMA,OAAO,IAAIJ,EALQ,CACf,GAAG,IAAI,CACP,eAAgBK,EAChB,iBAAkBE,CACtB,EAEJ,CAMA,MAAM,OAAOH,CAAM,CAAE,CAEjB,IAAMI,EAAY,CAAC,EACnB,IAAK,GAAM,CAACC,EAAKC,EAAM,GAAIR,OAAO,OAAO,CAAC,IAAI,CAAC,QAAQ,EAC/C,AAAiB,UAAjB,OAAOQ,EACPF,CAAS,CAACC,EAAI,CAAG,SAAeC,EAAO,IAAI,CAAC,cAAc,CAAEN,GAG5DI,CAAS,CAACC,EAAI,CAAGC,EAGzB,IAAMC,EAAMP,EAAO,GAAG,EAAII,EAAU,GAAG,CACjCI,EAASR,EAAO,MAAM,EAAII,EAAU,MAAM,CAChD,GAAI,CAACG,EACD,MAAM,AAAIE,MAAM,qCAEpB,GAAI,AAAe,UAAf,OAAOF,EACP,MAAM,AAAIE,MAAM,yBAEpB,IAAMC,EAAS,CAAEH,IAAAA,CAAI,EAIrB,OAHIC,GACAE,CAAAA,EAAO,MAAM,CAAGF,CAAK,EAElBE,CACX,CAOA,MAAM,kBAAkBV,CAAM,CAAE,CAC5B,IAAMW,EAAkB,MAAM,IAAI,CAAC,MAAM,CAACX,GAC1C,OAAO,IAAI,IAAgB,CAACW,EAChC,CACJ,C,SC3G+C,IAAQ,CA2HX,GAAkB,ACnGvD,OAAMC,UAA8B,GAAwB,CAC/D,YAAYf,CAAK,CAAE,CAyDf,GAxDA,KAAK,CAACA,GACNC,OAAO,cAAc,CAAC,IAAI,CAAE,kBAAmB,CAC3C,WAAY,GACZ,aAAc,GACd,SAAU,GACV,MAAO,EACX,GACAA,OAAO,cAAc,CAAC,IAAI,CAAE,WAAY,CACpC,WAAY,GACZ,aAAc,GACd,SAAU,GACV,MAAO,KAAK,CAChB,GACAA,OAAO,cAAc,CAAC,IAAI,CAAE,kBAAmB,CAC3C,WAAY,GACZ,aAAc,GACd,SAAU,GACV,MAAO,KAAK,CAChB,GACAA,OAAO,cAAc,CAAC,IAAI,CAAE,gBAAiB,CACzC,WAAY,GACZ,aAAc,GACd,SAAU,GACV,MAAO,KAAK,CAChB,GACAA,OAAO,cAAc,CAAC,IAAI,CAAE,SAAU,CAClC,WAAY,GACZ,aAAc,GACd,SAAU,GACV,MAAO,EACX,GACAA,OAAO,cAAc,CAAC,IAAI,CAAE,mBAAoB,CAC5C,WAAY,GACZ,aAAc,GACd,SAAU,GACV,MAAO,MACX,GACAA,OAAO,cAAc,CAAC,IAAI,CAAE,SAAU,CAClC,WAAY,GACZ,aAAc,GACd,SAAU,GACV,MAAO,EACX,GACAA,OAAO,cAAc,CAAC,IAAI,CAAE,iBAAkB,CAC1C,WAAY,GACZ,aAAc,GACd,SAAU,GACV,MAAO,UACX,GACAA,OAAO,cAAc,CAAC,IAAI,CAAE,mBAAoB,CAC5C,WAAY,GACZ,aAAc,GACd,SAAU,GACV,MAAO,EACX,GACAA,OAAO,MAAM,CAAC,IAAI,CAAED,GAChB,AAAkBgB,KAAAA,IAAlB,IAAI,CAAC,QAAQ,EAAkB,AAAyBA,KAAAA,IAAzB,IAAI,CAAC,eAAe,CACnD,MAAM,AAAIJ,MAAM,oEAEpB,GAAI,AAAkBI,KAAAA,IAAlB,IAAI,CAAC,QAAQ,EAAkB,AAAyBA,KAAAA,IAAzB,IAAI,CAAC,eAAe,CACnD,MAAM,AAAIJ,MAAM,+DAEpB,GAAI,IAAI,CAAC,gBAAgB,CAAE,CACvB,IAAIV,EAAsB,IAAI,CAAC,cAAc,AACzC,KAAI,CAAC,gBAAgB,EACrBA,CAAAA,EAAsBA,EAAoB,MAAM,CAACD,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAC,EAEvF,SAAmB,IAAI,CAAC,MAAM,CAAG,IAAI,CAAC,MAAM,CAAE,IAAI,CAAC,cAAc,CAAEC,EACvE,CACJ,CACA,gBAAiB,CACb,MAAO,UACX,CACA,OAAO,SAAU,CACb,MAAO,uBACX,CACA,MAAM,YAAYe,CAAc,CAAE,CAC9B,GAAI,AAAkBD,KAAAA,IAAlB,IAAI,CAAC,QAAQ,CACb,OAAO,IAAI,CAAC,QAAQ,CAExB,GAAI,AAAyBA,KAAAA,IAAzB,IAAI,CAAC,eAAe,CACpB,OAAO,IAAI,CAAC,eAAe,CAAC,cAAc,CAACC,EAE/C,OAAM,AAAIL,MAAM,8DACpB,CACA,MAAM,QAAQT,CAAM,CAAE,CAClB,IAAMC,EAAoB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,AAACC,GAAO,CAAEA,CAAAA,KAAMF,CAAK,GACpEG,EAAsB,CACxB,GAAI,IAAI,CAAC,gBAAgB,EAAI,CAAC,CAAC,CAC/B,GAAGH,CAAM,AACb,EAMA,OAAO,IAAIY,EALQ,CACf,GAAG,IAAI,CACP,eAAgBX,EAChB,iBAAkBE,CACtB,EAEJ,CAMA,MAAM,OAAOH,CAAM,CAAE,CACjB,IAAMe,EAAY,MAAM,IAAI,CAAC,4BAA4B,CAACf,GACpDgB,EAAW,MAAM,IAAI,CAAC,WAAW,CAACD,GAClCE,EAAiB,MAAMC,QAAQ,GAAG,CAACF,EAAS,GAAG,CAAC,AAACG,GAAY,IAAI,CAAC,aAAa,CAAC,MAAM,CAACA,KACvFC,EAAW,CAAC,IAAI,CAAC,MAAM,IAAKH,EAAgB,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EACzF,MAAO,SAAeG,EAAU,IAAI,CAAC,cAAc,CAAEL,EACzD,CACA,WAAY,CACR,GAAI,IAAI,CAAC,eAAe,EAAI,CAAC,IAAI,CAAC,QAAQ,CACtC,MAAM,AAAIN,MAAM,8DAEpB,GAAI,AAAsBI,KAAAA,IAAtB,IAAI,CAAC,YAAY,CACjB,MAAM,AAAIJ,MAAM,2DAEpB,MAAO,CACH,MAAO,IAAI,CAAC,cAAc,GAC1B,gBAAiB,IAAI,CAAC,cAAc,CACpC,eAAgB,IAAI,CAAC,aAAa,CAAC,SAAS,GAC5C,kBAAmB,IAAI,CAAC,gBAAgB,CACxC,OAAQ,IAAI,CAAC,MAAM,CACnB,OAAQ,IAAI,CAAC,MAAM,CACnB,gBAAiB,IAAI,CAAC,cAAc,CACpC,SAAU,IAAI,CAAC,QAAQ,AAC3B,CACJ,CACA,aAAa,YAAYY,CAAI,CAAE,CAC3B,IAKIL,EALE,CAAEM,eAAAA,CAAc,CAAE,CAAGD,EAC3B,GAAI,CAACC,EACD,MAAM,AAAIb,MAAM,0BAEpB,IAAMc,EAAgB,MAAM,4BAA0B,CAACD,GAEvD,GAAIE,MAAM,OAAO,CAACH,EAAK,QAAQ,EAC3BL,EAAWK,EAAK,QAAQ,MAGxB,MAAM,AAAIZ,MAAM,+DAEpB,OAAO,IAAIG,EAAsB,CAC7B,eAAgBS,EAAK,eAAe,CACpCE,cAAAA,EACAP,SAAAA,EACA,iBAAkBK,EAAK,iBAAiB,CACxC,OAAQA,EAAK,MAAM,CACnB,OAAQA,EAAK,MAAM,CACnB,eAAgBA,EAAK,eAAe,AACxC,EACJ,CACJ,C"}