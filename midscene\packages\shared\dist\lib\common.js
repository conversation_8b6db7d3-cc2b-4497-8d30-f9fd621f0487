"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/common.ts
var common_exports = {};
__export(common_exports, {
  defaultRunDirName: () => defaultRunDirName,
  getMidsceneRunBaseDir: () => getMidsceneRunBaseDir,
  getMidsceneRunDir: () => getMidsceneRunDir,
  getMidsceneRunSubDir: () => getMidsceneRunSubDir,
  isNodeEnv: () => isNodeEnv
});
module.exports = __toCommonJS(common_exports);
var import_node_fs = require("fs");
var import_node_os = require("os");
var import_node_path = __toESM(require("path"));

// src/env.ts
var MIDSCENE_OPENAI_INIT_CONFIG_JSON = "MIDSCENE_OPENAI_INIT_CONFIG_JSON";
var MIDSCENE_MODEL_NAME = "MIDSCENE_MODEL_NAME";
var MIDSCENE_LANGSMITH_DEBUG = "MIDSCENE_LANGSMITH_DEBUG";
var MIDSCENE_DEBUG_AI_PROFILE = "MIDSCENE_DEBUG_AI_PROFILE";
var MIDSCENE_DEBUG_AI_RESPONSE = "MIDSCENE_DEBUG_AI_RESPONSE";
var MIDSCENE_DANGEROUSLY_PRINT_ALL_CONFIG = "MIDSCENE_DANGEROUSLY_PRINT_ALL_CONFIG";
var MIDSCENE_DEBUG_MODE = "MIDSCENE_DEBUG_MODE";
var MIDSCENE_MCP_USE_PUPPETEER_MODE = "MIDSCENE_MCP_USE_PUPPETEER_MODE";
var MIDSCENE_FORCE_DEEP_THINK = "MIDSCENE_FORCE_DEEP_THINK";
var MIDSCENE_OPENAI_SOCKS_PROXY = "MIDSCENE_OPENAI_SOCKS_PROXY";
var MIDSCENE_OPENAI_HTTP_PROXY = "MIDSCENE_OPENAI_HTTP_PROXY";
var OPENAI_API_KEY = "OPENAI_API_KEY";
var OPENAI_BASE_URL = "OPENAI_BASE_URL";
var OPENAI_MAX_TOKENS = "OPENAI_MAX_TOKENS";
var MIDSCENE_ADB_PATH = "MIDSCENE_ADB_PATH";
var MIDSCENE_ADB_REMOTE_HOST = "MIDSCENE_ADB_REMOTE_HOST";
var MIDSCENE_ADB_REMOTE_PORT = "MIDSCENE_ADB_REMOTE_PORT";
var MIDSCENE_CACHE = "MIDSCENE_CACHE";
var MIDSCENE_USE_VLM_UI_TARS = "MIDSCENE_USE_VLM_UI_TARS";
var MIDSCENE_USE_QWEN_VL = "MIDSCENE_USE_QWEN_VL";
var MIDSCENE_USE_DOUBAO_VISION = "MIDSCENE_USE_DOUBAO_VISION";
var MIDSCENE_USE_GEMINI = "MIDSCENE_USE_GEMINI";
var MIDSCENE_USE_VL_MODEL = "MIDSCENE_USE_VL_MODEL";
var MATCH_BY_POSITION = "MATCH_BY_POSITION";
var MIDSCENE_REPORT_TAG_NAME = "MIDSCENE_REPORT_TAG_NAME";
var MIDSCENE_PREFERRED_LANGUAGE = "MIDSCENE_PREFERRED_LANGUAGE";
var MIDSCENE_USE_AZURE_OPENAI = "MIDSCENE_USE_AZURE_OPENAI";
var MIDSCENE_AZURE_OPENAI_SCOPE = "MIDSCENE_AZURE_OPENAI_SCOPE";
var MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON = "MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON";
var AZURE_OPENAI_ENDPOINT = "AZURE_OPENAI_ENDPOINT";
var AZURE_OPENAI_KEY = "AZURE_OPENAI_KEY";
var AZURE_OPENAI_API_VERSION = "AZURE_OPENAI_API_VERSION";
var AZURE_OPENAI_DEPLOYMENT = "AZURE_OPENAI_DEPLOYMENT";
var MIDSCENE_USE_ANTHROPIC_SDK = "MIDSCENE_USE_ANTHROPIC_SDK";
var ANTHROPIC_API_KEY = "ANTHROPIC_API_KEY";
var MIDSCENE_RUN_DIR = "MIDSCENE_RUN_DIR";
var OPENAI_USE_AZURE = "OPENAI_USE_AZURE";
var allConfigFromEnv = () => {
  return {
    [MIDSCENE_OPENAI_INIT_CONFIG_JSON]: process.env[MIDSCENE_OPENAI_INIT_CONFIG_JSON] || void 0,
    [MIDSCENE_MODEL_NAME]: process.env[MIDSCENE_MODEL_NAME] || void 0,
    [MIDSCENE_DEBUG_MODE]: process.env[MIDSCENE_DEBUG_MODE] || void 0,
    [MIDSCENE_FORCE_DEEP_THINK]: process.env[MIDSCENE_FORCE_DEEP_THINK] || void 0,
    [MIDSCENE_LANGSMITH_DEBUG]: process.env[MIDSCENE_LANGSMITH_DEBUG] || void 0,
    [MIDSCENE_DEBUG_AI_PROFILE]: process.env[MIDSCENE_DEBUG_AI_PROFILE] || void 0,
    [MIDSCENE_DEBUG_AI_RESPONSE]: process.env[MIDSCENE_DEBUG_AI_RESPONSE] || void 0,
    [MIDSCENE_DANGEROUSLY_PRINT_ALL_CONFIG]: process.env[MIDSCENE_DANGEROUSLY_PRINT_ALL_CONFIG] || void 0,
    [OPENAI_API_KEY]: process.env[OPENAI_API_KEY] || void 0,
    [OPENAI_BASE_URL]: process.env[OPENAI_BASE_URL] || void 0,
    [OPENAI_MAX_TOKENS]: process.env[OPENAI_MAX_TOKENS] || void 0,
    [OPENAI_USE_AZURE]: process.env[OPENAI_USE_AZURE] || void 0,
    [MIDSCENE_ADB_PATH]: process.env[MIDSCENE_ADB_PATH] || void 0,
    [MIDSCENE_ADB_REMOTE_HOST]: process.env[MIDSCENE_ADB_REMOTE_HOST] || void 0,
    [MIDSCENE_ADB_REMOTE_PORT]: process.env[MIDSCENE_ADB_REMOTE_PORT] || void 0,
    [MIDSCENE_CACHE]: process.env[MIDSCENE_CACHE] || void 0,
    [MATCH_BY_POSITION]: process.env[MATCH_BY_POSITION] || void 0,
    [MIDSCENE_REPORT_TAG_NAME]: process.env[MIDSCENE_REPORT_TAG_NAME] || void 0,
    [MIDSCENE_OPENAI_SOCKS_PROXY]: process.env[MIDSCENE_OPENAI_SOCKS_PROXY] || void 0,
    [MIDSCENE_OPENAI_HTTP_PROXY]: process.env[MIDSCENE_OPENAI_HTTP_PROXY] || void 0,
    [MIDSCENE_USE_AZURE_OPENAI]: process.env[MIDSCENE_USE_AZURE_OPENAI] || void 0,
    [MIDSCENE_AZURE_OPENAI_SCOPE]: process.env[MIDSCENE_AZURE_OPENAI_SCOPE] || void 0,
    [MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON]: process.env[MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON] || void 0,
    [MIDSCENE_USE_ANTHROPIC_SDK]: process.env[MIDSCENE_USE_ANTHROPIC_SDK] || void 0,
    [MIDSCENE_USE_VLM_UI_TARS]: process.env[MIDSCENE_USE_VLM_UI_TARS] || void 0,
    [MIDSCENE_USE_QWEN_VL]: process.env[MIDSCENE_USE_QWEN_VL] || void 0,
    [MIDSCENE_USE_DOUBAO_VISION]: process.env[MIDSCENE_USE_DOUBAO_VISION] || void 0,
    [MIDSCENE_USE_GEMINI]: process.env[MIDSCENE_USE_GEMINI] || void 0,
    [MIDSCENE_USE_VL_MODEL]: process.env[MIDSCENE_USE_VL_MODEL] || void 0,
    [ANTHROPIC_API_KEY]: process.env[ANTHROPIC_API_KEY] || void 0,
    [AZURE_OPENAI_ENDPOINT]: process.env[AZURE_OPENAI_ENDPOINT] || void 0,
    [AZURE_OPENAI_KEY]: process.env[AZURE_OPENAI_KEY] || void 0,
    [AZURE_OPENAI_API_VERSION]: process.env[AZURE_OPENAI_API_VERSION] || void 0,
    [AZURE_OPENAI_DEPLOYMENT]: process.env[AZURE_OPENAI_DEPLOYMENT] || void 0,
    [MIDSCENE_MCP_USE_PUPPETEER_MODE]: process.env[MIDSCENE_MCP_USE_PUPPETEER_MODE] || void 0,
    [MIDSCENE_RUN_DIR]: process.env[MIDSCENE_RUN_DIR] || void 0,
    [MIDSCENE_PREFERRED_LANGUAGE]: process.env[MIDSCENE_PREFERRED_LANGUAGE] || void 0
  };
};
var globalConfig = null;
var getGlobalConfig = () => {
  if (globalConfig === null) {
    globalConfig = allConfigFromEnv();
  }
  return globalConfig;
};
var getAIConfig = (configKey) => {
  if (configKey === MATCH_BY_POSITION) {
    throw new Error(
      "MATCH_BY_POSITION is deprecated, use MIDSCENE_USE_VL_MODEL instead"
    );
  }
  return getGlobalConfig()[configKey]?.trim?.();
};

// src/common.ts
var defaultRunDirName = "midscene_run";
var isNodeEnv = typeof process !== "undefined" && process.versions != null && process.versions.node != null;
var getMidsceneRunDir = () => {
  if (!isNodeEnv) {
    return "";
  }
  return getAIConfig(MIDSCENE_RUN_DIR) || defaultRunDirName;
};
var getMidsceneRunBaseDir = () => {
  if (!isNodeEnv) {
    return "";
  }
  let basePath = import_node_path.default.resolve(process.cwd(), getMidsceneRunDir());
  if (!(0, import_node_fs.existsSync)(basePath)) {
    try {
      (0, import_node_fs.mkdirSync)(basePath, { recursive: true });
    } catch (error) {
      basePath = import_node_path.default.join((0, import_node_os.tmpdir)(), defaultRunDirName);
      (0, import_node_fs.mkdirSync)(basePath, { recursive: true });
    }
  }
  return basePath;
};
var getMidsceneRunSubDir = (subdir) => {
  if (!isNodeEnv) {
    return "";
  }
  const basePath = getMidsceneRunBaseDir();
  const logPath = import_node_path.default.join(basePath, subdir);
  if (!(0, import_node_fs.existsSync)(logPath)) {
    (0, import_node_fs.mkdirSync)(logPath, { recursive: true });
  }
  return logPath;
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  defaultRunDirName,
  getMidsceneRunBaseDir,
  getMidsceneRunDir,
  getMidsceneRunSubDir,
  isNodeEnv
});
