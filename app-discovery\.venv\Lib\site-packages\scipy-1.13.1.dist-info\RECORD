scipy-1.13.1-cp39-cp39-win_amd64.whl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy-1.13.1.dist-info/DELVEWHEEL,sha256=wTnGjs8PsitKRJhU0MxzzN16ZUUNWGn7qCCnmeRbZZE,439
scipy-1.13.1.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
scipy-1.13.1.dist-info/LICENSE.txt,sha256=UlkCiee0Uvv9lJ2s9Q4oE-NhKrt8EW5cegwnpJa5xe8,47742
scipy-1.13.1.dist-info/METADATA,sha256=Ln2AOLdpn9Xh3EsFGsXv8roOC02Oc7pt1eO837mxaAI,60579
scipy-1.13.1.dist-info/RECORD,,
scipy-1.13.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy-1.13.1.dist-info/WHEEL,sha256=8AdrFzOtKQ6LLJ-VyqCU3y1iN8N--fMXYqrdkeTKDn0,83
scipy.libs/.load-order-scipy-1.13.1,sha256=lACmSUZAOkpbCC-AwipGw6SpX4BBJGolXR-YCQvrw0I,58
scipy.libs/libopenblas_v0.3.27--3aa239bc726cfb0bd8e5330d8d4c15c6.dll,sha256=LuAmK0KRc_b6NWnXskLmlnNpliELIVLHP7zgtdYdz8E,38129340
scipy/__config__.py,sha256=YFdl4RUMUJbY2Rbo4DSK2SsidWqp72YAa7RiAI-VARQ,5322
scipy/__init__.py,sha256=6cj800n7FX5Cs3gqmgAP4A0W5evlAjPtjns6UGJdcJQ,5645
scipy/_distributor_init.py,sha256=JOqSUAVCNcvuXYwzfs41kJFHvhLdrqRfp3ZYk5U71Pc,1247
scipy/_lib/__init__.py,sha256=lcHbxe9IkuoUZjXA8VX6-SYYCREBLq1cqoPo2mR6V1w,367
scipy/_lib/_array_api.py,sha256=HRW7dHD01NaTQnlCapucUNExIdyB-_PGoNrAIC_FK-k,13096
scipy/_lib/_bunch.py,sha256=wQK9j5N61P1eXt73dEZnUcEc8bS5Y59qOgp_av6eGPc,8345
scipy/_lib/_ccallback.py,sha256=RDz5WUY_jgPtUlEgtm-VGxcub9nvF4laqIsyExK8Nzk,7338
scipy/_lib/_ccallback_c.cp39-win_amd64.dll.a,sha256=zuoeXSoEx2PhG7D90e_MSpO6mIW_HYenNYlp0p-Sbf4,1596
scipy/_lib/_ccallback_c.cp39-win_amd64.pyd,sha256=IBX_4Mw92U42u9QlP2NY97bDaGeBWB42wpd5CF_8L5E,85504
scipy/_lib/_disjoint_set.py,sha256=3EIkZl2z9UajnPRVjSD5H8JszidKURpxHCO580OJGC8,6414
scipy/_lib/_docscrape.py,sha256=5dCdfJJwgOz_Utb7OrSkiEmeCDxCm-Xs6kYLbQu_fEQ,22177
scipy/_lib/_elementwise_iterative_method.py,sha256=JRIoSiVxBSVlOSuFZheud619U3_961FXKaNRsN31xF8,13829
scipy/_lib/_finite_differences.py,sha256=Uu28sJ1PNvgW0Y7ESs0voon5AVYtj0QRrbqsVPjoL70,4317
scipy/_lib/_fpumode.cp39-win_amd64.dll.a,sha256=qYgSmyKADBN9ogaImC3oMY9EqtGsqPAXIaRR_x-QlUk,1548
scipy/_lib/_fpumode.cp39-win_amd64.pyd,sha256=qwXd9NYPRi8MPEVT62PPZmFswx0darMyLHGRV5uflsk,15872
scipy/_lib/_gcutils.py,sha256=ULLIREutjgyo8dLsvU2hhI0lDGeu5LqX6557X60xPi0,2774
scipy/_lib/_pep440.py,sha256=xSupJHSD_X9EOOUla1iKcNZ9lwxW6qraE3cbHHLunBY,14492
scipy/_lib/_test_ccallback.cp39-win_amd64.dll.a,sha256=QwGE_beTqDdTNlREmSof_qjK_QQzwcrkhjYTk0p1HmQ,1636
scipy/_lib/_test_ccallback.cp39-win_amd64.pyd,sha256=mzViHvAfn8YrAiIm763QDoCGcxN9DYjwdCfS_9vQBd0,52224
scipy/_lib/_test_deprecation_call.cp39-win_amd64.dll.a,sha256=YqU4zQ6oj0fbXVms2I_n02DT6ZQsLXBSGAKjEqKZOv0,1716
scipy/_lib/_test_deprecation_call.cp39-win_amd64.pyd,sha256=G5ZxcBjEC1Ku87sVaxnsKezKWVCzIen-BWu7_caB21w,34816
scipy/_lib/_test_deprecation_def.cp39-win_amd64.dll.a,sha256=3kv7n8sTXPeOGXWAUP4MCfPgZxC2Ub64e0V8T6L8CGU,1708
scipy/_lib/_test_deprecation_def.cp39-win_amd64.pyd,sha256=9lmRY9XyhtInNdTGSwZUyZtxa0muFqDmLsYQo-8Kd44,27136
scipy/_lib/_testutils.py,sha256=iFDk4V5mvCfSQiYzt2MwmMGfoK9fk_NcGM_QwrrCX3o,8387
scipy/_lib/_threadsafety.py,sha256=2dkby9bQV_BrdlThHUfiqZJsQq-Mte5R28_ueFpNikA,1513
scipy/_lib/_tmpdirs.py,sha256=r8sz8dcfBYiPAYondvpGHVVFB2_rB5sajkTeV1gt1fw,2460
scipy/_lib/_uarray/LICENSE,sha256=5MSswx6ChwZtfJldJuPvkCyhl37hh7JgsRwng2ZQOBE,1543
scipy/_lib/_uarray/__init__.py,sha256=pex73GgY7YUWARVgHzYw_Ky2fdAH7M9BcuDMif_hm38,4609
scipy/_lib/_uarray/_backend.py,sha256=bKJqAjPtPrnHUpph4Po4rSvN7V9cRKRZVVOsExkV9x0,21136
scipy/_lib/_uarray/_uarray.cp39-win_amd64.dll.a,sha256=nH2sPVfWIRHTy-auCRFjsoPcUBcrfR_tQ9kIDr61rRc,1540
scipy/_lib/_uarray/_uarray.cp39-win_amd64.pyd,sha256=NOuGNUlY1_Bl-J2S2Fl15QZYl2NHolJGMNo4NAHqfrw,233472
scipy/_lib/_util.py,sha256=M-VXbWgendvGEIfpukt09h4ZkUPn2Dzn6YEOosh0OQU,33165
scipy/_lib/array_api_compat/__init__.py,sha256=gtgisvkP32MILKzV55pjVROEW25q1e_6hWwNa3MWV4c,968
scipy/_lib/array_api_compat/_internal.py,sha256=BC0giq7U-zElzbzEtrSmtrJKo61duyrC6RHi4QL64yc,1030
scipy/_lib/array_api_compat/common/__init__.py,sha256=IOoccfR-BZ_F-CEN37M9ss_hT4nWMKqQLNbkUq6CHkM,25
scipy/_lib/array_api_compat/common/_aliases.py,sha256=tIue9bd-9GMLJVX-GLqbldaDO3TrKbHM5dXe45Y2rXk,16799
scipy/_lib/array_api_compat/common/_helpers.py,sha256=QEv9NRzNIGJLkRhzvturkEbpeAq4kIwA-lIJK0pGHdk,8438
scipy/_lib/array_api_compat/common/_linalg.py,sha256=pAbUtdlUgPOmzcsXhMziToeCrgeEQ7ih4-pJ74LDOc0,6459
scipy/_lib/array_api_compat/common/_typing.py,sha256=_ZLNGpqmAzvUAscbROUlJt03MHvG_xdMX13r4GAqTPI,408
scipy/_lib/array_api_compat/cupy/__init__.py,sha256=i1de5OVYzS7-17NyRs9Ede0x_U7giHk7-iGNoOvKeAI,413
scipy/_lib/array_api_compat/cupy/_aliases.py,sha256=zCz_C9yHFJR6hU0srn5c0JqH4p6-13ZgUyjgp43c1gQ,2681
scipy/_lib/array_api_compat/cupy/_typing.py,sha256=VhPA4g6G5Y8acUHqd5XG_vABH_M5mHUajSqFFfesBgM,663
scipy/_lib/array_api_compat/cupy/linalg.py,sha256=CdkGbkYdqtmjrvpqUeNQrjcMLSeWq8-kpQHZqstxIWc,1405
scipy/_lib/array_api_compat/numpy/__init__.py,sha256=avpnV0rdoayyE6lAmOiD1S-VHfOT47Q8M1pm26_v9XA,618
scipy/_lib/array_api_compat/numpy/_aliases.py,sha256=Ikych35AsBYMkX3_eBYMKKa0d5jUmwMMO6Bup6UOuGk,2685
scipy/_lib/array_api_compat/numpy/_typing.py,sha256=m3A_ClFAyC17Hbsm77fe-eSfXbOFpx5h9_WggniIN5A,664
scipy/_lib/array_api_compat/numpy/linalg.py,sha256=VDTqfuw80DsZeHkxS02do6Rs-P_4GTnDG9KLQmuW6ps,1229
scipy/_lib/array_api_compat/torch/__init__.py,sha256=4-LLJdmr5h5HIeCBNXPmxfViYReQD5QamtJ-FnzN3Fc,540
scipy/_lib/array_api_compat/torch/_aliases.py,sha256=fDLJVNJ9LtVOG7psLqWBknYtXu2e4ewT5snc0QVvSgE,27499
scipy/_lib/array_api_compat/torch/linalg.py,sha256=xwdm_I_hS8kitY3HhuUlRyPv-M4ei9q_tV6hHoOU_QA,2547
scipy/_lib/decorator.py,sha256=JoJOywg9I_xfdfasNhrwiasJEnNTvV3SYGv6GtC1Mak,15439
scipy/_lib/deprecation.py,sha256=L6_eIt28ZIJf3vlVMRXJl4cmbUhdMVZhwz00Ty_Tqq0,8313
scipy/_lib/doccer.py,sha256=9Ins8j58b2YQkD-vWCH1nMJ7pklhhRWqjELTeiJ-a_w,8637
scipy/_lib/messagestream.cp39-win_amd64.dll.a,sha256=IzJE-IPO7cyGABWhrVQ0Fneict-T9-FSGamrHj49rfo,1612
scipy/_lib/messagestream.cp39-win_amd64.pyd,sha256=pxQxEWja4EWfUXfpjzhvQsVatMOW5_ED653S776_yFo,66560
scipy/_lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/tests/test__gcutils.py,sha256=EeeSFQ1MTmcgxcDD5yI16UUAfESjSx40n3prcogOX30,3517
scipy/_lib/tests/test__pep440.py,sha256=4AS6UhkhxfVqvZfZmxM0pgIOvQVbAjPdWN3YtgfXWkY,2344
scipy/_lib/tests/test__testutils.py,sha256=cI6oLinJsZDXIi_pmPMTVAYTvepddvhYVGEMd2MlW2Q,832
scipy/_lib/tests/test__threadsafety.py,sha256=nnAypNwYK4YNe2s3xcYfGslLKXqdcrOeza73EeybiAQ,1373
scipy/_lib/tests/test__util.py,sha256=XlNa1i1e3DpusGZKY5TTtOUcUVXNnq2CS20MynhCsts,14887
scipy/_lib/tests/test_array_api.py,sha256=_AdwAO_D6pqZhwlltMCinGxItpFgKCuR2AgQikPAhNk,4148
scipy/_lib/tests/test_bunch.py,sha256=yZmGHnJ-qBIMP-8TmnyvK7OGiXRzHzbRHCpMwIsmsk0,6330
scipy/_lib/tests/test_ccallback.py,sha256=klbEMLd28QAUyzGp6Z89Fr6FzV6jKnmuQumzpe4bXco,6379
scipy/_lib/tests/test_deprecation.py,sha256=NGUuuv24fSTGyTUZWd2saZkapR2NOpIpx0tijRjBQ7Y,374
scipy/_lib/tests/test_import_cycles.py,sha256=pKC7H1Tsu1Cuun_moQFZrRPYrdeuAT0s8dFAeZMeyZI,514
scipy/_lib/tests/test_public_api.py,sha256=9XjNijRP0AbcE5Eqz2MLh_1IZmT6mGbdUJHooGF4ePE,19725
scipy/_lib/tests/test_scipy_version.py,sha256=j-i3VewqD2gfdZZiJQCdlB3x_4EVekZkND1IFWszqhc,624
scipy/_lib/tests/test_tmpdirs.py,sha256=jY1yJyn2NN6l_BX_7u5HOuohD9K_SlU08W_yaoR15ek,1282
scipy/_lib/tests/test_warnings.py,sha256=tB6GTT1535YBWeX7eOISBTQ5s1htSqFMCjqEnYzqzWk,4683
scipy/_lib/uarray.py,sha256=qXvvUluJiQJwLybyo5ZZtGGWa_o2T0mSvojeu8t_IkQ,846
scipy/cluster/__init__.py,sha256=ck3TgyUyHOG1-MiymZd04JoIvkWrBaFL56fM-LS-tK8,907
scipy/cluster/_hierarchy.cp39-win_amd64.dll.a,sha256=Lur4PMZ8rrU2sIz4tJYzhsPOZhQncSp9Dn0sf6KhNFc,1572
scipy/cluster/_hierarchy.cp39-win_amd64.pyd,sha256=czQbHhKRH7eaiiUWbXJPpP4u0-HixSzrqBiod7f1iHY,382976
scipy/cluster/_optimal_leaf_ordering.cp39-win_amd64.dll.a,sha256=JL6VGG-52Q2wYNAAhymHU3IRcK3TZtUTzaZyZ8w39ds,1716
scipy/cluster/_optimal_leaf_ordering.cp39-win_amd64.pyd,sha256=R2DtclxkVLrLsI1u9WXFJHDDQM1-t7U-0jy4UDS7a6Y,329728
scipy/cluster/_vq.cp39-win_amd64.dll.a,sha256=iwRC8DL5xMbEt1j5W65yaUuLU0yJUhjHvmDxw0VhogA,1492
scipy/cluster/_vq.cp39-win_amd64.pyd,sha256=ffgd9hgiQJKQ01pX_aaBW86Vo3lYc0E8KpVzAi6S2g0,108032
scipy/cluster/hierarchy.py,sha256=w3NbtKv1i_HbJ2m30zDRycnDjR6ExxHSxowc80-LCXg,152761
scipy/cluster/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/cluster/tests/hierarchy_test_data.py,sha256=lvVMk2L5iEKlhX32aFRBJ3clecx6AXLYw75Epl07OBo,6995
scipy/cluster/tests/test_disjoint_set.py,sha256=fczIL8hK_ICE23j54dwEdn9KBOipPnpxAhWK7xj8X4o,5727
scipy/cluster/tests/test_hierarchy.py,sha256=5x6oiSgY0PRVIE4ImbTUvlwrcpFH3MlkgzKFH0szPBU,49951
scipy/cluster/tests/test_vq.py,sha256=pyVUfPbmaMxBjsqty89BxMT_e4g_W4YyesVGFbIkjdE,18030
scipy/cluster/vq.py,sha256=L6Q47NKwdW7grMT_JSAa1zqU7IMVVQUfDCyD9zRUlSM,31573
scipy/conftest.py,sha256=iwcRIC5wDZjr21Wn2a4rP0OlPFtuZixlp3t2JAkXniQ,9272
scipy/constants/__init__.py,sha256=21Fu1jN-LvN-EbuCnzxGTQgAiv33VwvKM2PcDM7WeHc,12784
scipy/constants/_codata.py,sha256=R3T8P1TLrAnQV9WCYF8c9OriLHtGmtp0vQ15PmT45cI,157383
scipy/constants/_constants.py,sha256=DYar8r1j0djolADiEz1ab8St1oBLcYfMVYsf4mUjqWI,10738
scipy/constants/codata.py,sha256=HSemkal4KFVqUurlalWfb7kzHuUBtWZ8gOgrYi5cNS0,818
scipy/constants/constants.py,sha256=vdC_jDTQZWQlrkYZY0Uj7NKO2AtBwH5mc28Jhy7Ur9s,2303
scipy/constants/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/constants/tests/test_codata.py,sha256=0p4cAPH0UMK3JWHGI2Ha05dQpxBOzNSme3_pWcIVTDw,2016
scipy/constants/tests/test_constants.py,sha256=oZy6J36DLygy5IVdcNY_NFJlbvuTTFoAt3EM4JGCIkM,1667
scipy/datasets/__init__.py,sha256=9FDldqGVaM-sobI1bN43qJeNppyBd-W48hpEEjnCHqg,2892
scipy/datasets/_download_all.py,sha256=i-fMQWgAfvOgCEoEabsd68iUYDki-4OUfPjjcGLHRDI,1758
scipy/datasets/_fetchers.py,sha256=Vt5-ngD7ZXuLRUy_iYKS04eBk48ffXa2KAlCbwJLzJQ,6980
scipy/datasets/_registry.py,sha256=3_ZNYpe3lCXvUGalpNsrhRwKcnFA2P6yWV7MFdKb2Hw,1098
scipy/datasets/_utils.py,sha256=7bK_NVCoURt-HDGe7I0iBGLEdcrLIU5eJuSjJqAkgJg,3048
scipy/datasets/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/datasets/tests/test_data.py,sha256=loTZrDHWZEQjIihEcSYi6S6HlzltygW3HeRQp61SdVA,4187
scipy/fft/__init__.py,sha256=tTo8pSk5GeJaL2pHakUVxMByC78MMcDPtGtcgtBElp4,3624
scipy/fft/_backend.py,sha256=ySd0v6IodaTh-_kjfEOogdlfCsJ3rdcRLqv-ZU8NkTM,6740
scipy/fft/_basic.py,sha256=pSqks7ZcODqlyWVorO1pg3C_GyfcQxVdgUSHvNTTHmg,64627
scipy/fft/_basic_backend.py,sha256=ZIIZd7dfAD8G5h0uhslcqiCB69sWt-pDdLotnfjcAS8,6722
scipy/fft/_debug_backends.py,sha256=R0zCc_CgDubYnaGbQGjk0GTmd5HlYFpBLw7WSlqljpQ,620
scipy/fft/_fftlog.py,sha256=YKigZKxZIgBlFDzvJyMgnpY8cgA72ax7tFr8OhnoZkI,8089
scipy/fft/_fftlog_backend.py,sha256=NUrIgnZUV1EX4OmrUIRvpZiaMcAzgFyVHohuOCWwXEY,5434
scipy/fft/_helper.py,sha256=dhNVdCqnZqkbJwVFY876HNKVB0Y1I2MFQNd3NQTcNKY,10361
scipy/fft/_pocketfft/LICENSE.md,sha256=wEZhaFz_nYA1_hxMOrNX5_djPK_AanPp2Qw8X-3oqGo,1523
scipy/fft/_pocketfft/__init__.py,sha256=WKXb59wx9DK9zXq9DwKchp-zg1SuJIynTQ_POQrFXxo,216
scipy/fft/_pocketfft/basic.py,sha256=dST8PBFhoiOR1Kj1j3CcjMC0p7KFxTA9qDr1oN2YrFw,8389
scipy/fft/_pocketfft/helper.py,sha256=za_Q6JMsWy8U2YOzyJXxidyQ1E4tircVaVdtMEVZ0DI,6028
scipy/fft/_pocketfft/pypocketfft.cp39-win_amd64.dll.a,sha256=vqdcUWkgpytLoP95G7r-StL9u6OHe_SGHY_lQQhFeyE,1588
scipy/fft/_pocketfft/pypocketfft.cp39-win_amd64.pyd,sha256=FQagmJvl1UIFxQtZCs9jER2rbRYsw5ocK-H8DqIAgog,1073664
scipy/fft/_pocketfft/realtransforms.py,sha256=nk1e31laxa9920AeBgZgb1vZSXIpoILwWqSI028HCyQ,3453
scipy/fft/_pocketfft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/_pocketfft/tests/test_basic.py,sha256=Jy59AaqVo2qdunyKB7bfXeCOTXnkqBx4oo7TPRpBpc0,36378
scipy/fft/_pocketfft/tests/test_real_transforms.py,sha256=EBq5FEvMEVSyczizYsXnPBNIlPALwmVSAPzaUAmZss4,17150
scipy/fft/_realtransforms.py,sha256=ba4SZIlpA405Ujt5lEo2RngO3zsoJJS0259t7nC23_s,26079
scipy/fft/_realtransforms_backend.py,sha256=hJ3LkFOvggibVF_E36fM9R3ZqAJHHicYrGUceH8B81g,2452
scipy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/tests/mock_backend.py,sha256=jlJZ-GOwaAmSjp6g_riFY9X_86a2ngqmhOt-mUF0SGg,2646
scipy/fft/tests/test_backend.py,sha256=MW6yc0GGrT0tGuiXX-AxZXUDwUHJZ9qIoYS40w_tlJs,4373
scipy/fft/tests/test_basic.py,sha256=1EmL70mPfchmj0hMnvlDBvbvhsMGwg8-Xf5hr3VnwvA,21720
scipy/fft/tests/test_fftlog.py,sha256=jtD3gzNBEGi8S-5ArzwWBOp4OXGbiBfrDZXpLdLn2ls,6342
scipy/fft/tests/test_helper.py,sha256=a-BoFaTHkkag3kGpLj4Eqc29yN6zT2OlSTG5gvIyh1g,15881
scipy/fft/tests/test_multithreading.py,sha256=AD3naMjpD9aQ0sJwLs8n7ln3IzeCzPm5Y6QV8su0YgI,2215
scipy/fft/tests/test_real_transforms.py,sha256=ZvMXVXO1lzCXFW9wlg5l6JctZ9UvjKBDaWbKxxE6K9M,8855
scipy/fftpack/__init__.py,sha256=NKSnRJ6EMDP3nDxGSJMyd2PJBB2ytMAO01Kq884NXXo,3258
scipy/fftpack/_basic.py,sha256=6AFPL2JeBvZsIZV-C3kAmvG3ybQ8XtiRLSJQ9Go9L2E,13526
scipy/fftpack/_helper.py,sha256=KTS-_cTl2lNu8PXdopJRG80EbwBI3HnhvvziiWRT2tE,3465
scipy/fftpack/_pseudo_diffs.py,sha256=5IZuPf96AqFK17zyOv3PJUICEd5qrzQPqW7Aq-B-Pa8,14751
scipy/fftpack/_realtransforms.py,sha256=gG8Q39JAiO1Y3k8neJR1rfXLNhb9IvfBn7w8URM-Ndw,19812
scipy/fftpack/basic.py,sha256=FFn2KxrsmC6IsOQdjcoVr8Nvrlng2FRiv7gNeT1ZrY4,597
scipy/fftpack/convolve.cp39-win_amd64.dll.a,sha256=1KVPNypEWfuFcHCEoywB0VnL7wmptbF3DHwsBuCQfiw,1548
scipy/fftpack/convolve.cp39-win_amd64.pyd,sha256=tKa0OYhew6CqcZ9Fx2eP98iEJFsL-Uu2B6CkGQi2Q_U,252416
scipy/fftpack/helper.py,sha256=1b1b278FWyTc2MeAjeLFB8eyV76pRxOigGtBUvCp_lo,599
scipy/fftpack/pseudo_diffs.py,sha256=OccZOhf76stov4474cq8Juy4rcQQ37pQnuxUsG7X_Nc,696
scipy/fftpack/realtransforms.py,sha256=oUJXNb5KAyS4k8xubnE7hGE9BpLCcdkk_iiReyB8OOE,614
scipy/fftpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fftpack/tests/fftw_double_ref.npz,sha256=pgxklBW2RSI5JNg0LMxcCXgByGkBKHo2nlP8kln17E4,162120
scipy/fftpack/tests/fftw_longdouble_ref.npz,sha256=pAbL1NrQTQxZ3Tj1RBb7SUJMgiKcGgdLakTsDN4gAOM,296072
scipy/fftpack/tests/fftw_single_ref.npz,sha256=J2qRQTGOb8NuSrb_VKYbZAVO-ISbZg8XNZ5fVBtDxSY,95144
scipy/fftpack/tests/test.npz,sha256=Nt6ASiLY_eoFRZDOSd3zyFmDi32JGTxWs7y2YMv0N5c,11968
scipy/fftpack/tests/test_basic.py,sha256=j3VoREWfwuTJF3vbCGcqUoL4Dt-L9ehKKE6KCQDAfWs,31180
scipy/fftpack/tests/test_helper.py,sha256=HuCAP5esSm4lMnw9f9i-ByJj1DURntgC64s2fX5RVIg,1729
scipy/fftpack/tests/test_import.py,sha256=96z6hsyxt6iMH0Xn1xHd2T-kTyYM3obvlf0xniF73Cw,1151
scipy/fftpack/tests/test_pseudo_diffs.py,sha256=5wmGeVj5QXbFkxrRG7Tl162v4pFSxiMSONtm4Uoy-AQ,13769
scipy/fftpack/tests/test_real_transforms.py,sha256=iaJJV0JFnFKLSXmfrA2Y4qTltkG0HW0sjMPo_c4ON3M,24786
scipy/integrate/__init__.py,sha256=QAHaKXYrkhDhF44xiF9szNiRqLoHmCzC0ePjuEHlJ_U,4346
scipy/integrate/_bvp.py,sha256=mi8S-exWGnFTlopJti0UDTDnUPjrH6ewrDZYRtJyuzE,42082
scipy/integrate/_dop.cp39-win_amd64.dll.a,sha256=G3XoYunZ3z9nKFN-LiE6fyQchoX9ndVCOQD9UP91qXM,1500
scipy/integrate/_dop.cp39-win_amd64.pyd,sha256=39EYMug9NgTU2VIAqVVsBTvsKgVkrO-LX53gFJwJjY4,433664
scipy/integrate/_ivp/__init__.py,sha256=XVoxnj-1q1Xm4StWijukIoIrqc6Ny1Dxi625GurInWs,264
scipy/integrate/_ivp/base.py,sha256=efUVvU8k5dFgglCta7f5ZiCMyTU4XN38JMfiKLQcYFQ,10585
scipy/integrate/_ivp/bdf.py,sha256=TYyHuFvF-eKxe-5PMtzkPs192A9HzkEw7adDazq0QDY,18001
scipy/integrate/_ivp/common.py,sha256=YXx_X0y6OjycqQb3FVZK20pj8npSkLVcbFhhMZkTilc,15714
scipy/integrate/_ivp/dop853_coefficients.py,sha256=4oMhmg8eXyzDeSSrlh2dJJMvaabWn-aO44S3wx8Ig5o,7430
scipy/integrate/_ivp/ivp.py,sha256=mPMpFEVNg0vwPKwIQacx_xTuP24n_fw0BU2JwMqZx-k,32220
scipy/integrate/_ivp/lsoda.py,sha256=95NTm0zyHR5zFH5IeKo9kEWtDkdR3Mdfrbubnsf4Awo,10151
scipy/integrate/_ivp/radau.py,sha256=I33zwaSN9IJzaGiaRy3cU0SS56TyaCpBEiDPCo7IWXs,20317
scipy/integrate/_ivp/rk.py,sha256=bEgsvVyu4xwfhfLWGInrwbKsX-zZy3VOQibnn3wfCvE,23382
scipy/integrate/_ivp/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/_ivp/tests/test_ivp.py,sha256=-geJV2ZrrJEnJVfRdjHofvzfebHrUKCNinusAbX08Fo,38223
scipy/integrate/_ivp/tests/test_rk.py,sha256=_8nQKtotVvR218br-0mNSQSEZ-ST4L2Tgaz5xKXHgJw,1363
scipy/integrate/_lsoda.cp39-win_amd64.dll.a,sha256=url58FXTtlwkp9Qu6srO19OYYvmlJt_x_vw95-ywdCI,1524
scipy/integrate/_lsoda.cp39-win_amd64.pyd,sha256=LoLmB6q_ngWRZmAn6igGYR44XyfqKK_OW6ENS-GHq1U,429056
scipy/integrate/_ode.py,sha256=Ot4fEYgvhX3eUNIW3d_iwSXyIbZ0EQiPrLeyo4uXsFM,49450
scipy/integrate/_odepack.cp39-win_amd64.dll.a,sha256=zNPMDBW1X1hQ_rVAWX_fWn71gE1xC3LNMQ6FmVEQBXo,1548
scipy/integrate/_odepack.cp39-win_amd64.pyd,sha256=glDrG-WN14KxmjSYBKFfPDc7C5Ng6IlaY_Jy9scv2Ao,410624
scipy/integrate/_odepack_py.py,sha256=VjytB7LLxOuwql5CSSdIEqjR8lSHTqq1k8DUR08smso,11174
scipy/integrate/_quad_vec.py,sha256=YNSgy0aVBB5TQ5_vtWVqprwBB_XHOUkz33RdDUYEuaA,21890
scipy/integrate/_quadpack.cp39-win_amd64.dll.a,sha256=Qpm59C6zsGzUutY8o__Ihw5HXsKmVPbO5GUKW2gFtI4,1564
scipy/integrate/_quadpack.cp39-win_amd64.pyd,sha256=zDRGZBE3ANQ3q39RThsFyav0n6mGnAsknunVqysHUxE,446976
scipy/integrate/_quadpack_py.py,sha256=iPHtbxdH2v4R-fANmpfyGa8twaEcIY8scjm_qIq9WPc,54913
scipy/integrate/_quadrature.py,sha256=v80xmPElS63rk7JXMTRaxwVsNKTfDT6sgdaiWMMbdh4,66891
scipy/integrate/_tanhsinh.py,sha256=B9-BDSmigLlH5C0wCidoHr1gnFUDLa2UpxaZ1bGDHFI,54136
scipy/integrate/_test_multivariate.cp39-win_amd64.dll.a,sha256=jBGhpq2B6TnH0ukY2GK-ra-WuIjx9q42tVENLSBNsJU,1668
scipy/integrate/_test_multivariate.cp39-win_amd64.pyd,sha256=b3_rFjzFhRGSsM7Cxt0u9WP-AycCq-sHO13T76k-oaI,17920
scipy/integrate/_test_odeint_banded.cp39-win_amd64.dll.a,sha256=7fSnic5xe_Z4uBM3aaQ3Fto-OJ4ACfdPT7FrLCzmHdE,1684
scipy/integrate/_test_odeint_banded.cp39-win_amd64.pyd,sha256=jwaK6_IHsO-hvEPVBds_5JTSLn9I-jcasnT0PQaRqD0,430592
scipy/integrate/_vode.cp39-win_amd64.dll.a,sha256=wwKJPLEinVXON7zTVt47eRvt1upEALretVe7fQmMbV0,1516
scipy/integrate/_vode.cp39-win_amd64.pyd,sha256=HMc7RzGo1-DQ4tzMMDo7KuJn3befLf59sNFalLKaL4k,491520
scipy/integrate/dop.py,sha256=ZgmvSd-IxtG5kvMIQajBvUyi-T6JjeE5LDtHZaK2K1w,471
scipy/integrate/lsoda.py,sha256=KtndEiRbVPejH0aNBaj3mSHWayXP-wqXSoMZJRPWhAg,451
scipy/integrate/odepack.py,sha256=TxsXidD25atNMK5kKXD-FyNCLXnzrwCdVB1hP4aLSQ8,562
scipy/integrate/quadpack.py,sha256=cPEsDzMW-ghFwSDKI_ow1d4E08coR1vmrw9GBftE_UM,641
scipy/integrate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/tests/test__quad_vec.py,sha256=JHj_lZSbWKWc_oFLBShigs8Xgft9AkVFFXQ2j_h07UA,6493
scipy/integrate/tests/test_banded_ode_solvers.py,sha256=erjGeCJNAVCaiulMn698i6ZjMqUFFr0SCZcmo2jJZsM,6905
scipy/integrate/tests/test_bvp.py,sha256=V4jOddeW6ukG3U4jgNinE5hR08ihZ0bZEWOlxe5B6rA,20892
scipy/integrate/tests/test_integrate.py,sha256=AQeUxBCo_LJsPIB7ba0e5GMwc9GLHz7xszGXRJvKW0A,25234
scipy/integrate/tests/test_odeint_jac.py,sha256=XedvNm_tVVuiQhUiL_5_OfPe5nDOEc821vsqiLZtm8c,1890
scipy/integrate/tests/test_quadpack.py,sha256=VZQGbIMf6eiQ6cSlNrtdqf6T8MZiEHfOJm3Inh-g7wM,28660
scipy/integrate/tests/test_quadrature.py,sha256=1wWUmnVu2Etd2twHyDch8QE86EPM8ubUiqjWSyk_oJQ,30765
scipy/integrate/tests/test_tanhsinh.py,sha256=BjUX3blflsMVsxYGOqUJBSiM7KcYIf3bd3UVBTCQtko,35138
scipy/integrate/vode.py,sha256=txo8KXcwm0NGuFF8Oah4CszUPq5jxLfu5phYqH82e8U,471
scipy/interpolate/__init__.py,sha256=Qo38iQ5KFhyz2V1gj1BjpUFA92Vr_WrZyX3FeiUZsxY,3731
scipy/interpolate/_bspl.cp39-win_amd64.dll.a,sha256=EPRMPqTZxMzsNnlB8Uq88lGYmI2t7m-0i_xiVs3XaeY,1516
scipy/interpolate/_bspl.cp39-win_amd64.pyd,sha256=idUIeqQIST1XX0nE-od3eI_e5Wj558jsEgfodmLZvlg,572416
scipy/interpolate/_bsplines.py,sha256=ORICb3zyfTxEiqvAtPRxJROkJbPVkJerGw_WOs74mJk,77655
scipy/interpolate/_cubic.py,sha256=S6J5V6ip73hxmlzTs0MmIbMhNGZ81icWmQzsXQHVeRY,39132
scipy/interpolate/_fitpack.cp39-win_amd64.dll.a,sha256=3ZegUIw5mAif3QQR-qfOhYQpFjyse6GJopt32VkgLN0,1548
scipy/interpolate/_fitpack.cp39-win_amd64.pyd,sha256=hLW0BJx6RFLGkoPXFiOoyTYFkdqK6MYh1owVu5D6nPE,420864
scipy/interpolate/_fitpack2.py,sha256=UwFgtMivM8bzG1wY6JNj5NT2pFq7WPQwQa_SYR66puY,91534
scipy/interpolate/_fitpack_impl.py,sha256=rdW2bjiiMs4luM86-w_n2lRULhozh0G4x_DwD8v04NQ,29474
scipy/interpolate/_fitpack_py.py,sha256=PadIEQbDVfPjUwAe9K8aWibaWHCSa8j4J7CVnrdzLyw,28860
scipy/interpolate/_interpolate.py,sha256=1uBErKZ7X3ze2HC7K80ZMevZTIhVvROTKRaB4V_4VXo,90732
scipy/interpolate/_ndbspline.py,sha256=41ELM-4ZXtqMaYb_mfWPvunEFeJrtNp33SsuPZZ3lGs,13100
scipy/interpolate/_ndgriddata.py,sha256=gG8EHsX2cqpEBLoAV-grw7J3gKmysKmYqNcx2BpY9_A,12424
scipy/interpolate/_pade.py,sha256=Phydd4f5oVM_-B3_iwe7H3t66FoCjbfsK_TagWLz9Jo,1894
scipy/interpolate/_polyint.py,sha256=tzpv5wZB16Vb8dwVwXnAlzJfWeU4GYWoNgNQm1__rLY,35921
scipy/interpolate/_ppoly.cp39-win_amd64.dll.a,sha256=B_ZhLwIirhvVuF1DA8hs3nQyuGtHFuuBtSPZ7AMMlGI,1524
scipy/interpolate/_ppoly.cp39-win_amd64.pyd,sha256=RaK3DoSFFfiCWjwJe5S-Ix0uXf8M2ru9kAfYuYtb-Z8,444416
scipy/interpolate/_rbf.py,sha256=U7QwA94uCzHjxnFFYOApzqHJ6J6LoncTLs4cJEi_Mng,11964
scipy/interpolate/_rbfinterp.py,sha256=tdgC9U5X78-tCOVFB5ZmCKRs3ouCBqzVaNAUtP9lPbE,20273
scipy/interpolate/_rbfinterp_pythran.cp39-win_amd64.dll.a,sha256=cZDm7qJg1yWV0trVXPLXzPZ4bu_e42T8bNc945moY5g,1668
scipy/interpolate/_rbfinterp_pythran.cp39-win_amd64.pyd,sha256=xaNpNnfz8DL_GlmN-2m5X8M7Ioiak8knBJ_Js8K--JA,1165312
scipy/interpolate/_rgi.py,sha256=bp-PJAC2617HFcDPnm4LRBxkN64paW3-jiTUz51nE08,32257
scipy/interpolate/_rgi_cython.cp39-win_amd64.dll.a,sha256=3nBKDWxLsbJ1wlo-B-MXFB3GC5rzLzF-BAVamCiGIqw,1588
scipy/interpolate/_rgi_cython.cp39-win_amd64.pyd,sha256=uSq2O7vXSXwmBg2J3BPEGm18PXEAnG_q_x2hJeLPFX0,274944
scipy/interpolate/dfitpack.cp39-win_amd64.dll.a,sha256=fPGynetn2hGPfg0n0K_TF_NOi8GYNMXey088WO6jAbU,1548
scipy/interpolate/dfitpack.cp39-win_amd64.pyd,sha256=lUiquH7aYB_g0P7UJdvr9YzofTOeol2qPZWO--bSq8g,662528
scipy/interpolate/fitpack.py,sha256=FjYeE_uNfnCAdJjGdoIL93tDEh_4cSWS75wU7q6t2t0,748
scipy/interpolate/fitpack2.py,sha256=OADrx9ZTP7Tdc_mXi9n0wXpRQA8qGWVg4od37gf9018,1002
scipy/interpolate/interpnd.cp39-win_amd64.dll.a,sha256=HbxZ8xSx9YguHt5ZeMAjTJBawjI5ay3C-AR905lAkQY,1548
scipy/interpolate/interpnd.cp39-win_amd64.pyd,sha256=Jwpgo8seyAHfZ6ayYQn3p6gghtWu3AfJfK_Zw9TfwCU,440832
scipy/interpolate/interpolate.py,sha256=1-IYFTuGl8ueDaf93h2hHRltX9mS5dXgfRvw5F8Yayg,1007
scipy/interpolate/ndgriddata.py,sha256=7uNTwKpcjIF5pV-XLf3bdhZ-xmE1TErNqRB4JbXNUSI,702
scipy/interpolate/polyint.py,sha256=T5CNYqyuCvgEqVi6tGLCPAI3jgNqTxS3x__15aGWd5g,738
scipy/interpolate/rbf.py,sha256=JxNFnTsH_NI0QOCBt7qovGDEWBubxkN97lXUEkF2d98,622
scipy/interpolate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/interpolate/tests/data/bug-1310.npz,sha256=jWgDwLOY8nBMI28dG56OXt4GvRZaCrsPIoKBq71FWuk,2648
scipy/interpolate/tests/data/estimate_gradients_hang.npy,sha256=QGwQhXQX_16pjYzSiUXJ0OT1wk-SpIrQ6Pq5Vb8kd_E,35680
scipy/interpolate/tests/data/gcvspl.npz,sha256=A86BVabLoMG_CiRBoQwigZH5Ft7DbLggcjQpgRKWu6g,3138
scipy/interpolate/tests/test_bsplines.py,sha256=d7e-L3RSrYg5GN9wxhvc761QwL86TV2y41fJyf5av_M,96469
scipy/interpolate/tests/test_fitpack.py,sha256=PpI1NCUtFOFhuH8PKlARH7Qmt2HzGvCT991MhHWsiUk,16560
scipy/interpolate/tests/test_fitpack2.py,sha256=BrtNhvSn2XNE1JZlPniBXSWWyFn48tRfbT7aHyogj2U,60081
scipy/interpolate/tests/test_gil.py,sha256=U7P1Jo7ztDrrkjDRv_2AZb7I2f6hv-c6WyP8UeeNVVE,1939
scipy/interpolate/tests/test_interpnd.py,sha256=dUrvp9JawMiT640P8goeiBEfLRVueubldAGDnIJOvEw,14062
scipy/interpolate/tests/test_interpolate.py,sha256=QoGaSxZXUtafqz9ogKXLlXUjstRcKkysta7DC3t-VuI,100117
scipy/interpolate/tests/test_ndgriddata.py,sha256=G2DrnpFWvHdqy3Hzsd0kwFz35OwstHhmceMboGBkdZ8,11264
scipy/interpolate/tests/test_pade.py,sha256=hyxMyYpWDds6pUN9LaweoZAov32b8dIVrXacdoDOibE,3912
scipy/interpolate/tests/test_polyint.py,sha256=GpS8uDxh7EtJoJF4O5hWN5tcfBLgGbobSxWf4gCl0lQ,37267
scipy/interpolate/tests/test_rbf.py,sha256=p4TlyxPEMCcKaVKbFpl8foncvs8LCyyBrMkYAj2lYfg,6781
scipy/interpolate/tests/test_rbfinterp.py,sha256=YyAtQOl6qPrsgOrQ_3raS20ra4B9UA4M4LVpVDj1fuk,19034
scipy/interpolate/tests/test_rgi.py,sha256=IKPjAdCCOCXvumzlaSCEdA5P56G-PesiGxNYEjSrlzY,45885
scipy/io/__init__.py,sha256=3ETOeDKr2QQL-Ty2qrrzxAzqX09sfJzsKaPm5r8tZyA,2851
scipy/io/_fast_matrix_market/__init__.py,sha256=JNjYiv7_ojzemjmyFSvyblDQOEMC5Kit6fIUKv5nfBY,17470
scipy/io/_fast_matrix_market/_fmm_core.cp39-win_amd64.dll.a,sha256=rFR_MYoELFCsgV2lNTWK9uefYaUK7jPyi3urRRN60aM,1564
scipy/io/_fast_matrix_market/_fmm_core.cp39-win_amd64.pyd,sha256=RsQC7Mlq1udFu3X7utTpRxd9IVxja2lepEKFZjbbpyI,2743808
scipy/io/_fortran.py,sha256=vaaYYQPTMA8TiFob56TKborkigS6wnuZl2xGnpJO9ws,11249
scipy/io/_harwell_boeing/__init__.py,sha256=YnsHlxg7HQ-WxR5OOELtfv1JrwYrSkojji4_8hpppBs,591
scipy/io/_harwell_boeing/_fortran_format_parser.py,sha256=1bJDeg-occK-jF_eneTCRc6nj9yWQU1QGPweRNMkflw,9226
scipy/io/_harwell_boeing/hb.py,sha256=4Lvcgr_BdskwmAVliLhk3F6yomsmR9o-RL0xVYgSLgk,19748
scipy/io/_harwell_boeing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/_harwell_boeing/tests/test_fortran_format.py,sha256=oBT5mfOa0sthUlb4qo_lJnP5BYFj3h8a6HqUZPMTXrk,2434
scipy/io/_harwell_boeing/tests/test_hb.py,sha256=BCfVyKAE3O0ABcfeGfxtGquwhehBGMPKGJl95oC3EIo,2349
scipy/io/_idl.py,sha256=Ym0tUIyqMhZkx9Sr7_A9HTWXXIHVD1D61FVAFbsnMPY,28020
scipy/io/_mmio.py,sha256=_V9VYFI084fqhAo0bbBN9FtrVfyLVeGR2dqwOeh_lkc,32833
scipy/io/_netcdf.py,sha256=kM4993iMVHNPtqJ8X1W1HOPERpsPzzu9xPX2zqW30ow,40358
scipy/io/_test_fortran.cp39-win_amd64.dll.a,sha256=RMjwv_Xyc4-tlgieGxcjgtbjUaj92_6vSvTrU45hh7M,1612
scipy/io/_test_fortran.cp39-win_amd64.pyd,sha256=hEyVmUuxN3Mr4gUsXoynl9F_vBSWUq6IRoWg6yeVwgQ,381440
scipy/io/arff/__init__.py,sha256=3F0yvqizJT7Hq5gygpmZw2WMuy0r9aCmb6G2yNNn8-I,833
scipy/io/arff/_arffread.py,sha256=KAVr3DpekgNZXXM7zJfDyUT02kxGK5xBeLIKRxpgnjI,27467
scipy/io/arff/arffread.py,sha256=7ddVqvI1JkdbeYccQQS7TyNU8kKBtxHlbjEyk823ANg,1173
scipy/io/arff/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/arff/tests/data/iris.arff,sha256=gzevjIJmmJLocLjOEnQMkZVZuIpfPJLocJdIG4RYHIk,7711
scipy/io/arff/tests/data/missing.arff,sha256=m53l1Himyx0TtVgNLucoz1go8-9BmSE70Ta9bm6dt_k,128
scipy/io/arff/tests/data/nodata.arff,sha256=6oAHQ7CUQEG8h5glXKpCaXH0UABOCEoxs5j50WYPAKU,240
scipy/io/arff/tests/data/quoted_nominal.arff,sha256=rc8IS9thME1NM9tAR-C4IUixLrSpw64YNeHkIRMS6Ng,299
scipy/io/arff/tests/data/quoted_nominal_spaces.arff,sha256=NbE6wwE0HkJDomErLPHgPGO6iF2HA1bLpmcamzx3F58,305
scipy/io/arff/tests/data/test1.arff,sha256=NV86lzIKWCwq3UNZmMGt1XK0yurcYmcp0KS9c5rKLkE,201
scipy/io/arff/tests/data/test10.arff,sha256=GbDWnG6LzjsbjYxiarYG4Ft3WlK1rvuM6w3proNFD58,199016
scipy/io/arff/tests/data/test11.arff,sha256=RFWNoWrx2LHJ0izc1bJdmGA2M2L8NT58MwKQN2B-3AI,252
scipy/io/arff/tests/data/test2.arff,sha256=zk5hNFTEcphFXkon2At_FD9ia_lAyzOcSFj4xnJTVPs,315
scipy/io/arff/tests/data/test3.arff,sha256=R2zwCzapweGqylmLD-LXx7GAS957NNJ8emM48hLTwEY,78
scipy/io/arff/tests/data/test4.arff,sha256=KtyokEnupbYZVi-fR7GJtr-hHefP54xgV2l26Nz3YZU,249
scipy/io/arff/tests/data/test5.arff,sha256=_1-QhpmflJKp1polaKwjkeJZsHeho7RzM3_ic2JJykA,391
scipy/io/arff/tests/data/test6.arff,sha256=WOCPN8QRdxCUSfzs-Y-OlJKiUmgLKuDrfy6wsWIFCJU,207
scipy/io/arff/tests/data/test7.arff,sha256=AQdPXYEKEhb10Odd_SDrPcv_gHNR0ymFKnD_91zjFc8,573
scipy/io/arff/tests/data/test8.arff,sha256=YlCbztVn-7x4w41HLIP852BqkKvWBrO9N8Nb0LSR0ZM,440
scipy/io/arff/tests/data/test9.arff,sha256=X0fH-_mz5ScbEvPJV7LJH-7Kbr2RTOlamf2_x18ydY0,324
scipy/io/arff/tests/test_arffread.py,sha256=YFtoh4I83UWxVdMBhLhGIkRRG1_U01e3EzKFcDZyzFw,13525
scipy/io/harwell_boeing.py,sha256=l_sl2xco4bRk70iaN5-oeCFlsXb6vHPV7PDms2Nmk2E,703
scipy/io/idl.py,sha256=RvSPSnoqWHFi4ECsQYdN8XgUBK6qyzX8Su60Olv8218,619
scipy/io/matlab/__init__.py,sha256=7N61Ssi3RyZbcXWAX5SHdfCsDkj3TIy1cHQkdC6csQY,2091
scipy/io/matlab/_byteordercodes.py,sha256=tVJ4_1yhVsey9Yaabn9mjl7srPkkdEWJ5YjRP3LLapw,2060
scipy/io/matlab/_mio.py,sha256=xqSXoT67Nh-ulbm8e0nW1NipfC1v9dxPsFc1Hg7k7ZA,13192
scipy/io/matlab/_mio4.py,sha256=hGegW-Q3w6HvXMsGa7LksZvzlnCXmiqQk06yT6zo2N0,21337
scipy/io/matlab/_mio5.py,sha256=eAijMJ-PErmMjyVfwiJwl3mHJlrPx5uzo1w3U3VRa_Q,34472
scipy/io/matlab/_mio5_params.py,sha256=f5HyjzbAwkN4zNRwn0N_n01LadTbsmUFGWjflumbxeU,8482
scipy/io/matlab/_mio5_utils.cp39-win_amd64.dll.a,sha256=qlIQkC08qw7EhF8zWdXLztX7jjLgigXMG0663pTEW6M,1588
scipy/io/matlab/_mio5_utils.cp39-win_amd64.pyd,sha256=YI6j0bym2PoclM7BGK2KDtmzMlCpbFMI-rrZGiwMSJc,212480
scipy/io/matlab/_mio_utils.cp39-win_amd64.dll.a,sha256=Lu2bbl1a24_hQ_8H3B7rSgCeN7EtNEOwBhtpzo0mTYA,1572
scipy/io/matlab/_mio_utils.cp39-win_amd64.pyd,sha256=XXgur-g5fXG9i0HKVDizxsBG6DKUfnhQrjDZFZVtwi4,57344
scipy/io/matlab/_miobase.py,sha256=f1fu5PDL99ULHRQspDSm9wry9gze_3cumKJ9ch07NXo,13391
scipy/io/matlab/_streams.cp39-win_amd64.dll.a,sha256=rHSZHdzUq92-NNaRi_1YZNx4NlyZpv0jK59WfMDI8qg,1548
scipy/io/matlab/_streams.cp39-win_amd64.pyd,sha256=AZgv4PPmzJM0ro_HPSrSN6vMGeDBC5Bo9kSFIAh9R0U,116736
scipy/io/matlab/byteordercodes.py,sha256=IihL2pGCaxGfzTo4YEEUtCq2DCMIfSwVK7sA948DoQo,631
scipy/io/matlab/mio.py,sha256=1-lmQbo4alZNFgqQvEMHyMRWljZVfO1w1zKGrJKCsho,698
scipy/io/matlab/mio4.py,sha256=S3Ma9Vh8tOuikSzzYQlByem3Dni5dohucB5ngTF7mQM,1007
scipy/io/matlab/mio5.py,sha256=an0HVQlCzJ_spApJ0qei_bty8a2eZ3KUC9l2X0jNWCU,1245
scipy/io/matlab/mio5_params.py,sha256=ZuW_moknJgk7Q43CnzqN-XzFIJQueNmhjn5Kwtb3CGs,1322
scipy/io/matlab/mio5_utils.py,sha256=8_MdWfraDwWiOg-F39QbgEZ3SDHnEga-mg_irE8mZA8,680
scipy/io/matlab/mio_utils.py,sha256=wo_7kYRsLRZca_ROx9GOqFAhesSBdVp8WnJUY26_bYI,575
scipy/io/matlab/miobase.py,sha256=cf1KQ7eDsVhySKdCYj19_NYGzJh010BV_j4YIfqsui8,786
scipy/io/matlab/streams.py,sha256=dNiZsOftjoMdRSLzMRFL4-qpVwulW_ft9CUnYbLvS9I,603
scipy/io/matlab/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/matlab/tests/data/bad_miuint32.mat,sha256=CVkYHp_U4jxYKRRHSuZ5fREop4tJjnZcQ02DKfObkRA,272
scipy/io/matlab/tests/data/bad_miutf8_array_name.mat,sha256=V-jfVMkYyy8qRGcOIsNGcoO0GCgTxchrsQUBGBnfWHE,208
scipy/io/matlab/tests/data/big_endian.mat,sha256=2ttpiaH2B6nmHnq-gsFeMvZ2ZSLOlpzt0IJiqBTcc8M,273
scipy/io/matlab/tests/data/broken_utf8.mat,sha256=nm8aotRl6NIxlM3IgPegKR3EeevYZoJCrYpV4Sa1T5I,216
scipy/io/matlab/tests/data/corrupted_zlib_checksum.mat,sha256=X4dvE7K9DmGEF3D6I-48hC86W41jB54H7bD8KTXjtYA,276
scipy/io/matlab/tests/data/corrupted_zlib_data.mat,sha256=DfE1YBH-pYw-dAaEeKA6wZcyKeo9GlEfrzZtql-fO_w,3451
scipy/io/matlab/tests/data/japanese_utf8.txt,sha256=rgxiBH7xmEKF91ZkB3oMLrqABBXINEMHPXDKdZXNBEY,270
scipy/io/matlab/tests/data/little_endian.mat,sha256=FQP_2MNod-FFF-JefN7ZxovQ6QLCdHQ0DPL_qBCP44Y,265
scipy/io/matlab/tests/data/logical_sparse.mat,sha256=qujUUpYewaNsFKAwGpYS05z7kdUv9TQZTHV5_lWhRrs,208
scipy/io/matlab/tests/data/malformed1.mat,sha256=DTuTr1-IzpLMBf8u5DPb3HXmw9xJo1aWfayA5S_3zUI,2208
scipy/io/matlab/tests/data/miuint32_for_miint32.mat,sha256=romrBP_BS46Sl2-pKWsUnxYDad2wehyjq4wwLaVqums,272
scipy/io/matlab/tests/data/miutf8_array_name.mat,sha256=Vo8JptFr-Kg2f2cEoDg8LtELSjVNyccdJY74WP_kqtc,208
scipy/io/matlab/tests/data/nasty_duplicate_fieldnames.mat,sha256=bvdmj6zDDUIpOfIP8J4Klo107RYCDd5VK5gtOYx3GsU,8168
scipy/io/matlab/tests/data/one_by_zero_char.mat,sha256=Z3QdZjTlOojjUpS0cfBP4XfNQI3GTjqU0n_pnAzgQhU,184
scipy/io/matlab/tests/data/parabola.mat,sha256=ENWuWX_uwo4Av16dIGOwnbMReAMrShDhalkq8QUI8Rg,729
scipy/io/matlab/tests/data/single_empty_string.mat,sha256=4uTmX0oydTjmtnhxqi9SyPWCG2I24gj_5LarS80bPik,171
scipy/io/matlab/tests/data/some_functions.mat,sha256=JA736oG3s8PPdKhdsYK-BndLUsGrJCJAIRBseSIEZtM,1397
scipy/io/matlab/tests/data/sqr.mat,sha256=3DtGl_V4wABKCDQ0P3He5qfOzpUTC-mINdK73MKS7AM,679
scipy/io/matlab/tests/data/test3dmatrix_6.1_SOL2.mat,sha256=-odiBIQAbOLERg0Vg682QHGfs7C8MaA_gY77OWR8x78,232
scipy/io/matlab/tests/data/test3dmatrix_6.5.1_GLNX86.mat,sha256=G5siwvZ-7Uv5KJ6h7AA3OHL6eiFsd8Lnjx4IcoByzCU,232
scipy/io/matlab/tests/data/test3dmatrix_7.1_GLNX86.mat,sha256=EVj1wPnoyWGIdTpkSj3YAwqzTAm27eqZNxCaJAs3pwU,213
scipy/io/matlab/tests/data/test3dmatrix_7.4_GLNX86.mat,sha256=S_Sd3sxorDd8tZ5CxD5_J8vXbfcksLWzhUQY5b82L9g,213
scipy/io/matlab/tests/data/test_empty_struct.mat,sha256=WoC7g7TyXqNr2T0d5xE3IUq5PRzatE0mxXjqoHX5Xec,173
scipy/io/matlab/tests/data/test_mat4_le_floats.mat,sha256=2xvn3Cg4039shJl62T-bH-VeVP_bKtwdqvGfIxv8FJ4,38
scipy/io/matlab/tests/data/test_skip_variable.mat,sha256=pJLVpdrdEb-9SMZxaDu-uryShlIi90l5LfXhvpVipJ0,20225
scipy/io/matlab/tests/data/testbool_8_WIN64.mat,sha256=_xBw_2oZA7u9Xs6GJItUpSIEV4jVdfdcwzmLNFWM6ow,185
scipy/io/matlab/tests/data/testcell_6.1_SOL2.mat,sha256=OWOBzNpWTyAHIcZABRytVMcABiRYgEoMyF9gDaIkFe4,536
scipy/io/matlab/tests/data/testcell_6.5.1_GLNX86.mat,sha256=7111TN_sh1uMHmYx-bjd_v9uaAnWhJMhrQFAtAw6Nvk,536
scipy/io/matlab/tests/data/testcell_7.1_GLNX86.mat,sha256=62p6LRW6PbM-Y16aUeGVhclTVqS5IxPUtsohe7MjrYo,283
scipy/io/matlab/tests/data/testcell_7.4_GLNX86.mat,sha256=NkTA8UW98hIQ0t5hGx_leG-MzNroDelYwqx8MPnO63Q,283
scipy/io/matlab/tests/data/testcellnest_6.1_SOL2.mat,sha256=AeNaog8HUDCVrIuGICAXYu9SGDsvV6qeGjgvWHrVQho,568
scipy/io/matlab/tests/data/testcellnest_6.5.1_GLNX86.mat,sha256=Gl4QA0yYwGxjiajjgWS939WVAM-W2ahNIm9wwMaT5oc,568
scipy/io/matlab/tests/data/testcellnest_7.1_GLNX86.mat,sha256=CUGtkwIU9CBa0Slx13mbaM67_ec0p-unZdu8Z4YYM3c,228
scipy/io/matlab/tests/data/testcellnest_7.4_GLNX86.mat,sha256=TeTk5yjl5j_bcnmIkpzuYHxGGQXNu-rK6xOsN4t6lX8,228
scipy/io/matlab/tests/data/testcomplex_4.2c_SOL2.mat,sha256=WOwauWInSVUFBuOJ1Bo3spmUQ3UWUIlsIe4tYGlrU7o,176
scipy/io/matlab/tests/data/testcomplex_6.1_SOL2.mat,sha256=GpAEccizI8WvlrBPdvlKUv6uKbZOo_cjUK3WVVb2lo4,352
scipy/io/matlab/tests/data/testcomplex_6.5.1_GLNX86.mat,sha256=3MEbf0zJdQGAO7x-pzFCup2QptfYJHQG59z0vVOdxl4,352
scipy/io/matlab/tests/data/testcomplex_7.1_GLNX86.mat,sha256=VNHV2AIEkvPuhae1kKIqt5t8AMgUyr0L_CAp-ykLxt4,247
scipy/io/matlab/tests/data/testcomplex_7.4_GLNX86.mat,sha256=8rWGf5bqY7_2mcd5w5gTYgMkXVePlLL8qT7lh8kApn0,247
scipy/io/matlab/tests/data/testdouble_4.2c_SOL2.mat,sha256=MzT7OYPEUXHYNPBrVkyKEaG5Cas2aOA0xvrO7l4YTrQ,103
scipy/io/matlab/tests/data/testdouble_6.1_SOL2.mat,sha256=DpB-mVKx1gsjl-3IbxfxHNuzU5dnuku-MDQCA8kALVI,272
scipy/io/matlab/tests/data/testdouble_6.5.1_GLNX86.mat,sha256=4hY5VEubavNEv5KvcqQnd7MWWvFUzHXXpYIqUuUt-50,272
scipy/io/matlab/tests/data/testdouble_7.1_GLNX86.mat,sha256=N2QOOIXPyy0zPZZ_qY7xIDaodMGrTq3oXNBEHZEscw0,232
scipy/io/matlab/tests/data/testdouble_7.4_GLNX86.mat,sha256=TrkJ4Xx_dC9YrPdewlsOvYs_xag7gT3cN4HkDsJmT8I,232
scipy/io/matlab/tests/data/testemptycell_5.3_SOL2.mat,sha256=g96Vh9FpNhkiWKsRm4U6KqeKd1hNAEyYSD7IVzdzwsU,472
scipy/io/matlab/tests/data/testemptycell_6.5.1_GLNX86.mat,sha256=2Zw-cMv-Mjbs2HkSl0ubmh_htFUEpkn7XVHG8iM32o0,472
scipy/io/matlab/tests/data/testemptycell_7.1_GLNX86.mat,sha256=t5Ar8EgjZ7fkTUHIVpdXg-yYWo_MBaigMDJUGWEIrmU,218
scipy/io/matlab/tests/data/testemptycell_7.4_GLNX86.mat,sha256=5PPvfOoL-_Q5ou_2nIzIrHgeaOZGFXGxAFdYzCQuwEQ,218
scipy/io/matlab/tests/data/testfunc_7.4_GLNX86.mat,sha256=ScTKftENe78imbMc0I5ouBlIMcEEmZgu8HVKWAMNr58,381
scipy/io/matlab/tests/data/testhdf5_7.4_GLNX86.mat,sha256=ZoVbGk38_MCppZ0LRr6OE07HL8ZB4rHXgMj9LwUBgGg,4168
scipy/io/matlab/tests/data/testmatrix_4.2c_SOL2.mat,sha256=14YMiKAN9JCPTqSDXxa58BK6Un7EM4hEoSGAUuwKWGQ,151
scipy/io/matlab/tests/data/testmatrix_6.1_SOL2.mat,sha256=ZdjNbcIE75V5Aht5EVBvJX26aabvNqbUH0Q9VBnxBS4,216
scipy/io/matlab/tests/data/testmatrix_6.5.1_GLNX86.mat,sha256=OB82QgB6SwtsxT4t453OVSj-B777XrHGEGOMgMD1XGc,216
scipy/io/matlab/tests/data/testmatrix_7.1_GLNX86.mat,sha256=-TYB0kREY7i7gt5x15fOYjXi410pXuDWUFxPYuMwywI,193
scipy/io/matlab/tests/data/testmatrix_7.4_GLNX86.mat,sha256=l9psDc5K1bpxNeuFlyYIYauswLnOB6dTX6-jvelW0kU,193
scipy/io/matlab/tests/data/testminus_4.2c_SOL2.mat,sha256=2914WYQajPc9-Guy3jDOLU3YkuE4OXC_63FUSDzJzX0,38
scipy/io/matlab/tests/data/testminus_6.1_SOL2.mat,sha256=2X2fZKomz0ktBvibj7jvHbEvt2HRA8D6hN9qA1IDicw,200
scipy/io/matlab/tests/data/testminus_6.5.1_GLNX86.mat,sha256=i364SgUCLSYRjQsyygvY1ArjEaO5uLip3HyU-R7zaLo,200
scipy/io/matlab/tests/data/testminus_7.1_GLNX86.mat,sha256=gtYNC9_TciYdq8X9IwyGEjiw2f1uCVTGgiOPFOiQbJc,184
scipy/io/matlab/tests/data/testminus_7.4_GLNX86.mat,sha256=eXcoTM8vKuh4tQnl92lwdDaqssGB6G9boSHh3FOCkng,184
scipy/io/matlab/tests/data/testmulti_4.2c_SOL2.mat,sha256=Zhyu2KCsseSJ5NARdS00uwddCs4wmjcWNP2LJFns2-Q,240
scipy/io/matlab/tests/data/testmulti_7.1_GLNX86.mat,sha256=KI3H58BVj6k6MFsj8icSbjy_0Z-jOesWN5cafStLPG8,276
scipy/io/matlab/tests/data/testmulti_7.4_GLNX86.mat,sha256=Yr4YKCP27yMWlK5UOK3BAEOAyMr-m0yYGcj8v1tCx-I,276
scipy/io/matlab/tests/data/testobject_6.1_SOL2.mat,sha256=kzLxy_1o1HclPXWyA-SX5gl6LsG1ioHuN4eS6x5iZio,800
scipy/io/matlab/tests/data/testobject_6.5.1_GLNX86.mat,sha256=dq_6_n0v7cUz9YziXn-gZFNc9xYtNxZ8exTsziWIM7s,672
scipy/io/matlab/tests/data/testobject_7.1_GLNX86.mat,sha256=3z-boFw0SC5142YPOLo2JqdusPItVzjCFMhXAQNaQUQ,306
scipy/io/matlab/tests/data/testobject_7.4_GLNX86.mat,sha256=5OwLTMgCBlxsDfiEUzlVjqcSbVQG-X5mIw5JfW3wQXA,306
scipy/io/matlab/tests/data/testonechar_4.2c_SOL2.mat,sha256=BCvppGhO19-j-vxAvbdsORIiyuJqzCuQog9Ao8V1lvA,40
scipy/io/matlab/tests/data/testonechar_6.1_SOL2.mat,sha256=ThppTHGJFrUfal5tewS70DL00dSwk1otazuVdJrTioE,200
scipy/io/matlab/tests/data/testonechar_6.5.1_GLNX86.mat,sha256=SBfN6e7Vz1rAdi8HLguYXcHUHk1viaXTYccdEyhhob4,200
scipy/io/matlab/tests/data/testonechar_7.1_GLNX86.mat,sha256=m8W9GqvflfAsizkhgAfT0lLcxuegZIWCLNuHVX69Jac,184
scipy/io/matlab/tests/data/testonechar_7.4_GLNX86.mat,sha256=t9ObKZOLy3vufnER8TlvQcUkd_wmXbJSdQoG4f3rVKY,184
scipy/io/matlab/tests/data/testscalarcell_7.4_GLNX86.mat,sha256=5LX9sLH7Y6h_N_a1XRN2GuMgp_P7ECpPsXGDOypAJg0,194
scipy/io/matlab/tests/data/testsimplecell.mat,sha256=Aoeh0PX2yiLDTwkxMEyZ_CNX2mJHZvyfuFJl817pA1c,220
scipy/io/matlab/tests/data/testsparse_4.2c_SOL2.mat,sha256=dFUcB1gunfWqexgR4YDZ_Ec0w0HffM1DUE1C5PVfDDc,223
scipy/io/matlab/tests/data/testsparse_6.1_SOL2.mat,sha256=9Sgd_SPkGNim7ZL0xgD71qml3DK0yDHYC7VSNLNQEXA,280
scipy/io/matlab/tests/data/testsparse_6.5.1_GLNX86.mat,sha256=jp1ILNxLyV6XmCCGxAz529XoZ9dhCqGEO-ExPH70_Pg,328
scipy/io/matlab/tests/data/testsparse_7.1_GLNX86.mat,sha256=k8QuQ_4Zu7FWTzHjRnHCVZ9Yu5vwNP0WyNzu6TuiY-4,229
scipy/io/matlab/tests/data/testsparse_7.4_GLNX86.mat,sha256=QbZOCqIvnaK0XOH3kaSXBe-m_1_Rb33psq8E-WMSBTU,229
scipy/io/matlab/tests/data/testsparsecomplex_4.2c_SOL2.mat,sha256=QMVoBXVyl9RBGvAjLoiW85kAXYJ-hHprUMegEG69A5w,294
scipy/io/matlab/tests/data/testsparsecomplex_6.1_SOL2.mat,sha256=WfEroAT5YF4HGAKq3jTJxlFrKaTCh3rwlSlKu__VjwA,304
scipy/io/matlab/tests/data/testsparsecomplex_6.5.1_GLNX86.mat,sha256=e0s6cyoKJeYMArdceHpnKDvtCVcw7XuB44OBDHpoa6U,400
scipy/io/matlab/tests/data/testsparsecomplex_7.1_GLNX86.mat,sha256=kgHcuq-deI2y8hfkGwlMOkW7lntexdPHfuz0ar6b3jo,241
scipy/io/matlab/tests/data/testsparsecomplex_7.4_GLNX86.mat,sha256=rYCaWNLXK7f_jjMc6_UvZz6ZDuMCuVRmJV5RyeXiDm8,241
scipy/io/matlab/tests/data/testsparsefloat_7.4_GLNX86.mat,sha256=hnNV6GZazEeqTXuA9vcOUo4xam_UnKRYGYH9PUGTLv8,219
scipy/io/matlab/tests/data/teststring_4.2c_SOL2.mat,sha256=cAhec51DlqIYfDXXGaumOE3Hqb3cFWM1UsUK3K_lDP8,375
scipy/io/matlab/tests/data/teststring_6.1_SOL2.mat,sha256=ciFzNGMO7gjYecony-E8vtOwBY4vXIUhyug6Euaz3Kg,288
scipy/io/matlab/tests/data/teststring_6.5.1_GLNX86.mat,sha256=yrJrpLiwLvU_LI1D6rw1Pk1qJK1YlC7Cmw7lwyJVLtw,288
scipy/io/matlab/tests/data/teststring_7.1_GLNX86.mat,sha256=zo7sh-8dMpGqhoNxLEnfz3Oc7RonxiY5j0B3lxk0e8o,224
scipy/io/matlab/tests/data/teststring_7.4_GLNX86.mat,sha256=igL_CvtAcNEa1nxunDjQZY5wS0rJOlzsUkBiDreJssk,224
scipy/io/matlab/tests/data/teststringarray_4.2c_SOL2.mat,sha256=pRldk-R0ig1k3ouvaR9oVtBwZsQcDW_b4RBEDYu1-Vk,156
scipy/io/matlab/tests/data/teststringarray_6.1_SOL2.mat,sha256=B9IdaSsyb0wxjyYyHOj_GDO0laAeWDEJhoEhC9xdm1E,232
scipy/io/matlab/tests/data/teststringarray_6.5.1_GLNX86.mat,sha256=t4tKGJg2NEg_Ar5MkOjCoQb2hVL8Q_Jdh9FF4TPL_4g,232
scipy/io/matlab/tests/data/teststringarray_7.1_GLNX86.mat,sha256=lpYkBZX8K-c4FO5z0P9DMfYc7Y-yzyg11J6m-19uYTU,203
scipy/io/matlab/tests/data/teststringarray_7.4_GLNX86.mat,sha256=lG-c7U-5Bo8j8xZLpd0JAsMYwewT6cAw4eJCZH5xf6E,203
scipy/io/matlab/tests/data/teststruct_6.1_SOL2.mat,sha256=3GJbA4O7LP57J6IYzmJqTPeSJrEaiNSk-rg7h0ANR1w,608
scipy/io/matlab/tests/data/teststruct_6.5.1_GLNX86.mat,sha256=fRbqAnzTeOU3dTQx7O24MfMVFr6pM5u594FRrPPkYJE,552
scipy/io/matlab/tests/data/teststruct_7.1_GLNX86.mat,sha256=mCtI_Yot08NazvWHvehOZbTV4bW_I4-D5jBgJ6T9EbI,314
scipy/io/matlab/tests/data/teststruct_7.4_GLNX86.mat,sha256=52qaF4HRCtPl1jE6ljbkEl2mofZVAPpmBxrm-J5OTTI,314
scipy/io/matlab/tests/data/teststructarr_6.1_SOL2.mat,sha256=vneCpWBwApBGfeKzdZcybyajxjR-ZYf64j0l08_hU84,528
scipy/io/matlab/tests/data/teststructarr_6.5.1_GLNX86.mat,sha256=gqhRpSfNNB5SR9sCp-wWrvokr5VV_heGnvco6dmfOvY,472
scipy/io/matlab/tests/data/teststructarr_7.1_GLNX86.mat,sha256=6VDU0mtTBEG0bBHqKP1p8xq846eMhSZ_WvBZv8MzE7M,246
scipy/io/matlab/tests/data/teststructarr_7.4_GLNX86.mat,sha256=ejtyxeeX_W1a2rNrEUUiG9txPW8_UtSgt8IaDOxE2pg,246
scipy/io/matlab/tests/data/teststructnest_6.1_SOL2.mat,sha256=sbi0wUwOrbU-gBq3lyDwhAbvchdtOJkflOR_MU7uGKA,496
scipy/io/matlab/tests/data/teststructnest_6.5.1_GLNX86.mat,sha256=uTkKtrYBTuz4kICVisEaG7V5C2nJDKjy92mPDswTLPE,416
scipy/io/matlab/tests/data/teststructnest_7.1_GLNX86.mat,sha256=o4F2jOhYyNpJCo-BMg6v_ITZQvjenXfXHLq94e7iwRo,252
scipy/io/matlab/tests/data/teststructnest_7.4_GLNX86.mat,sha256=CNXO12O6tedEuMG0jNma4qfbTgCswAbHwh49a3uE3Yk,252
scipy/io/matlab/tests/data/testunicode_7.1_GLNX86.mat,sha256=KV97FCW-1XZiXrwXJoZPbgyAht79oIFHa917W1KFLwE,357
scipy/io/matlab/tests/data/testunicode_7.4_GLNX86.mat,sha256=9-8xzACZleBkMjZnbr8t4Ncs9B6mbzrONDblPnteBPU,357
scipy/io/matlab/tests/data/testvec_4_GLNX86.mat,sha256=GQzR3mBVS266_NBfrRC9X0dLgmeu8Jl4r4ZYMOrn1V0,93
scipy/io/matlab/tests/test_byteordercodes.py,sha256=YKPAsE36MRPDi3Jsh2QrinvLOEvDPmsBylkwUzqoDTg,967
scipy/io/matlab/tests/test_mio.py,sha256=t6CenLDZSnvBsvwT3y0-4hanNpBJOoPOlY2Wh446Ia0,46149
scipy/io/matlab/tests/test_mio5_utils.py,sha256=Z1Byr0AJMkdlidOETqIsAJdCZCx0TGqR4OyFI4SDBaY,5568
scipy/io/matlab/tests/test_mio_funcs.py,sha256=JoLN42aZLd-f-kju-kTgRFQjQmEy4VtJlKgJ5EgnVFI,1443
scipy/io/matlab/tests/test_mio_utils.py,sha256=I4_z8My_JDy6jverX4bm-GeDNnnC6jX71hprsGSnCF8,1639
scipy/io/matlab/tests/test_miobase.py,sha256=G3Xm9Q14vWR37Wt7uxxgIBnLvmSkJ86GVABDhlJMflU,1496
scipy/io/matlab/tests/test_pathological.py,sha256=8lcveXzzknQH_009kNTvrf4aAr-qgatrXPEuRZtxQ4w,1088
scipy/io/matlab/tests/test_streams.py,sha256=3T0aC_eSvutWlfNsKOL4JCUUqg9jYEhGbPgJ4ZdynpM,7638
scipy/io/mmio.py,sha256=JUd3o5FazDpeJ7RFY1EMIQcPvtaeWqR6rEwf2L6IeyM,589
scipy/io/netcdf.py,sha256=Z2OaZi2xfYuSSEaiXNZ4cRTZw3vVZRM67_lmzAW64TM,905
scipy/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/tests/data/Transparent Busy.ani,sha256=vwoK3ysYo87-TwzvjerHjFjSPIGpw83jjiMDXcHPWjA,4362
scipy/io/tests/data/array_float32_1d.sav,sha256=A_xXWkfS1sQCxP4ONezeEZvlKEXwZ1TPG2rCCFdmBNM,2628
scipy/io/tests/data/array_float32_2d.sav,sha256=qJmN94pywXznXMHzt-L6DJgaIq_FfruVKJl_LMaI8UU,3192
scipy/io/tests/data/array_float32_3d.sav,sha256=U7P6As7Nw6LdBY1pTOaW9C-O_NlXLXZwSgbT3H8Z8uk,13752
scipy/io/tests/data/array_float32_4d.sav,sha256=Tl6erEw_Zq3dwVbVyPXRWqB83u_o4wkIVFOe3wQrSro,6616
scipy/io/tests/data/array_float32_5d.sav,sha256=VmaBgCD854swYyLouDMHJf4LL6iUNgajEOQf0pUjHjg,7896
scipy/io/tests/data/array_float32_6d.sav,sha256=lb7modI0OQDweJWbDxEV2OddffKgMgq1tvCy5EK6sOU,19416
scipy/io/tests/data/array_float32_7d.sav,sha256=pqLWIoxev9sLCs9LLwxFlM4RCFwxHC4Q0dEEz578mpI,3288
scipy/io/tests/data/array_float32_8d.sav,sha256=R8A004f9XLWvF6eKMNEqIrC6PGP1vLZr9sFqawqM8ZA,13656
scipy/io/tests/data/array_float32_pointer_1d.sav,sha256=sV7qFNwHK-prG5vODa7m5HYK7HlH_lqdfsI5Y1RWDyg,2692
scipy/io/tests/data/array_float32_pointer_2d.sav,sha256=b0brvK6xQeezoRuujmEcJNw2v6bfASLM3FSY9u5dMSg,3256
scipy/io/tests/data/array_float32_pointer_3d.sav,sha256=a_Iyg1YjPBRh6B-N_n_BGIVjFje4K-EPibKV-bPbF7E,13816
scipy/io/tests/data/array_float32_pointer_4d.sav,sha256=cXrkHHlPyoYstDL_OJ15-55sZOOeDNW2OJ3KWhBv-Kk,6680
scipy/io/tests/data/array_float32_pointer_5d.sav,sha256=gRVAZ6jeqFZyIQI9JVBHed9Y0sjS-W4bLseb01rIcGs,7960
scipy/io/tests/data/array_float32_pointer_6d.sav,sha256=9yic-CQiS0YR_ow2yUA2Nix0Nb_YCKMUsIgPhgcJT1c,19480
scipy/io/tests/data/array_float32_pointer_7d.sav,sha256=Rp1s8RbW8eoEIRTqxba4opAyY0uhTuyy3YkwRlNspQU,3352
scipy/io/tests/data/array_float32_pointer_8d.sav,sha256=Wk3Dd2ClAwWprXLKZon3blY7aMvMrJqz_NXzK0J5MFY,13720
scipy/io/tests/data/example_1.nc,sha256=EkfC57dWXeljgXy5sidrJHJG12D1gmQUyPDK18WzlT4,1736
scipy/io/tests/data/example_2.nc,sha256=wywMDspJ2QT431_sJUr_5DHqG3pt9VTvDJzfR9jeWCk,272
scipy/io/tests/data/example_3_maskedvals.nc,sha256=P9N92jCJgKJo9VmNd7FeeJSvl4yUUFwBy6JpR4MeuME,1424
scipy/io/tests/data/fortran-3x3d-2i.dat,sha256=oYCXgtY6qqIqLAhoh_46ob_RVQRcV4uu333pOiLKgRM,451
scipy/io/tests/data/fortran-mixed.dat,sha256=zTi7RLEnyAat_DdC3iSEcSbyDtAu0aTKwUT-tExjasw,40
scipy/io/tests/data/fortran-sf8-11x1x10.dat,sha256=KwaOrZOAe-wRhuxvmHIK-Wr59us40MmiA9QyWtIAUaA,888
scipy/io/tests/data/fortran-sf8-15x10x22.dat,sha256=5ohvjjOUcIsGimSqDhpUUKwflyhVsfwKL5ElQe_SU0I,26408
scipy/io/tests/data/fortran-sf8-1x1x1.dat,sha256=Djmoip8zn-UcxWGUPKV5wzKOYOf7pbU5L7HaR3BYlec,16
scipy/io/tests/data/fortran-sf8-1x1x5.dat,sha256=Btgavm3w3c9md_5yFfq6Veo_5IK9KtlLF1JEPeHhZoU,48
scipy/io/tests/data/fortran-sf8-1x1x7.dat,sha256=L0r9yAEMbfMwYQytzYsS45COqaVk-o_hi6zRY3yIiO4,64
scipy/io/tests/data/fortran-sf8-1x3x5.dat,sha256=c2LTocHclwTIeaR1Pm3mVMyf5Pl_imfjIFwi4Lpv0Xs,128
scipy/io/tests/data/fortran-si4-11x1x10.dat,sha256=OesvSIGsZjpKZlZsV74PNwy0Co0KH8-3gxL9-DWoa08,448
scipy/io/tests/data/fortran-si4-15x10x22.dat,sha256=OJcKyw-GZmhHb8REXMsHDn7W5VP5bhmxgVPIAYG-Fj4,13208
scipy/io/tests/data/fortran-si4-1x1x1.dat,sha256=1Lbx01wZPCOJHwg99MBDuc6QZKdMnccxNgICt4omfFM,12
scipy/io/tests/data/fortran-si4-1x1x5.dat,sha256=L1St4yiHTA3v91JjnndYfUrdKfT1bWxckwnnrscEZXc,28
scipy/io/tests/data/fortran-si4-1x1x7.dat,sha256=Dmqt-tD1v2DiPZkghGGZ9Ss-nJGfei-3yFXPO5Acpk4,36
scipy/io/tests/data/fortran-si4-1x3x5.dat,sha256=3vl6q93m25jEcZVKD0CuKNHmhZwZKp-rv0tfHoPVP88,68
scipy/io/tests/data/invalid_pointer.sav,sha256=JmgoISXC4r5fSmI5FqyapvmzQ4qpYLf-9N7_Et1p1HQ,1280
scipy/io/tests/data/null_pointer.sav,sha256=P_3a_sU614F3InwM82jSMtWycSZkvqRn1apwd8XxbtE,2180
scipy/io/tests/data/scalar_byte.sav,sha256=dNJbcE5OVDY_wHwN_UBUtfIRd13Oqu-RBEO74g5SsBA,2076
scipy/io/tests/data/scalar_byte_descr.sav,sha256=DNTmDgDWOuzlQnrceER6YJ0NutUUwZ9tozVMBWQmuuY,2124
scipy/io/tests/data/scalar_complex32.sav,sha256=NGd-EvmFZgt8Ko5MP3T_TLwyby6yS0BXM_OW8197hpU,2076
scipy/io/tests/data/scalar_complex64.sav,sha256=gFBWtxuAajazupGFSbvlWUPDYK-JdWgZcEWih2-7IYU,2084
scipy/io/tests/data/scalar_float32.sav,sha256=EwWQw2JTwq99CHVpDAh4R20R0jWaynXABaE2aTRmXrs,2072
scipy/io/tests/data/scalar_float64.sav,sha256=iPcDlgF1t0HoabvNLWCbSiTPIa9rvVEbOGGmE_3Ilsk,2076
scipy/io/tests/data/scalar_heap_pointer.sav,sha256=JXZbPmntXILsNOuLIKL8qdu8gDJekYrlN9DQxAWve0E,2204
scipy/io/tests/data/scalar_int16.sav,sha256=kDBLbPYGo2pzmZDhyl8rlDv0l6TMEWLIoLtmgJXDMkk,2072
scipy/io/tests/data/scalar_int32.sav,sha256=IzJwLvEoqWLO5JRaHp8qChfptlauU-ll3rb0TfDDM8Y,2072
scipy/io/tests/data/scalar_int64.sav,sha256=-aSHQRiaE3wjAxINwuLX33_8qmWl4GUkTH45elTkA-8,2076
scipy/io/tests/data/scalar_string.sav,sha256=AQ7iZ8dKk9QfnLdP9idKv1ojz0M_SwpL7XAUmbHodDQ,2124
scipy/io/tests/data/scalar_uint16.sav,sha256=928fmxLsQM83ue4eUS3IEnsLSEzmHBklDA59JAUvGK8,2072
scipy/io/tests/data/scalar_uint32.sav,sha256=X3RbPhS6_e-u-1S1gMyF7s9ys7oV6ZNwPrJqJ6zIJsk,2072
scipy/io/tests/data/scalar_uint64.sav,sha256=ffVyS2oKn9PDtWjJdOjSRT2KZzy6Mscgd4u540MPHC4,2076
scipy/io/tests/data/struct_arrays.sav,sha256=TzH-Gf0JgbP_OgeKYbV8ZbJXvWt1VetdUr6C_ziUlzg,2580
scipy/io/tests/data/struct_arrays_byte_idl80.sav,sha256=oOmhTnmKlE60-JMJRRMv_zfFs4zqioMN8QA0ldlgQZo,1388
scipy/io/tests/data/struct_arrays_replicated.sav,sha256=kXU8j9QI2Q8D22DVboH9fwwDQSLVvuWMJl3iIOhUAH8,2936
scipy/io/tests/data/struct_arrays_replicated_3d.sav,sha256=s3ZUwhT6TfiVfk4AGBSyxYR4FRzo4sZQkTxFCJbIQMI,4608
scipy/io/tests/data/struct_inherit.sav,sha256=4YajBZcIjqMQ4CI0lRUjXpYDY3rI5vzJJzOYpjWqOJk,2404
scipy/io/tests/data/struct_pointer_arrays.sav,sha256=fkldO6-RO2uAN_AI9hM6SEaBPrBf8TfiodFGJpViaqg,2408
scipy/io/tests/data/struct_pointer_arrays_replicated.sav,sha256=eKVerR0LoD9CuNlpwoBcn7BIdj3-8x56VNg--Qn7Hgc,2492
scipy/io/tests/data/struct_pointer_arrays_replicated_3d.sav,sha256=vsqhGpn3YkZEYjQuI-GoX8Jg5Dv8A2uRtP0kzQkq4lg,2872
scipy/io/tests/data/struct_pointers.sav,sha256=Zq6d5V9ZijpocxJpimrdFTQG827GADBkMB_-6AweDYI,2268
scipy/io/tests/data/struct_pointers_replicated.sav,sha256=aIXPBIXTfPmd4IaLpYD5W_HUoIOdL5Y3Hj7WOeRM2sA,2304
scipy/io/tests/data/struct_pointers_replicated_3d.sav,sha256=t1jhVXmhW6VotQMNZ0fv0sDO2pkN4EutGsx5No4VJQs,2456
scipy/io/tests/data/struct_scalars.sav,sha256=LYICjERzGJ_VvYgtwJ_Up2svQTv8wBzNcVD3nsd_OPg,2316
scipy/io/tests/data/struct_scalars_replicated.sav,sha256=lw3fC4kppi6BUWAd4n81h8_KgoUdiJl5UIt3CvJIuBs,2480
scipy/io/tests/data/struct_scalars_replicated_3d.sav,sha256=xVAup6f1dSV_IsSwBQC3KVs0eLEZ6-o5EaZT9yUoDZI,3240
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-be.wav,sha256=gjv__ng9xH_sm34hyxCbCgO4AP--PZAfDOArH5omkjM,3586
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-le.wav,sha256=H0LLyv2lc2guzYGnx4DWXU6vB57JrRX-G9Dd4qGh0hM,3586
scipy/io/tests/data/test-44100Hz-be-1ch-4bytes.wav,sha256=KKz9SXv_R3gX_AVeED2vyhYnj4BvD1uyDiKpCT3ulZ0,17720
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof-no-data.wav,sha256=YX1g8qdCOAG16vX9G6q4SsfCj2ZVk199jzDQ8S0zWYI,72
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof.wav,sha256=bFrsRqw0QXmsaDtjD6TFP8hZ5jEYMyaCmt-ka_C6GNk,1024
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-incomplete-chunk.wav,sha256=zMnhvZvrP4kyOWKVKfbBneyv03xvzgqXYhHNxsAxDJ4,13
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes.wav,sha256=9qTCvpgdz3raecVN1ViggHPnQjBf47xmXod9iCDsEik,17720
scipy/io/tests/data/test-48000Hz-2ch-64bit-float-le-wavex.wav,sha256=EqYBnEgTxTKvaTAtdA5HIl47CCFIje93y4hawR6Pyu0,7792
scipy/io/tests/data/test-8000Hz-be-3ch-5S-24bit.wav,sha256=hGYchxQFjrtvZCBo0ULi-xdZ8krqXcKdTl3NSUfqe8k,90
scipy/io/tests/data/test-8000Hz-le-1ch-10S-20bit-extra.wav,sha256=h8CXsW5_ShKR197t_d-TUTlgDqOZ-7wK_EcVGucR-aY,74
scipy/io/tests/data/test-8000Hz-le-1ch-1byte-ulaw.wav,sha256=BoUCDct3GiY_JJV_HoghF3mzAebT18j02c-MOn19KxU,70
scipy/io/tests/data/test-8000Hz-le-2ch-1byteu.wav,sha256=R6EJshvQp5YVR4GB9u4Khn5HM1VMfJUj082i8tkBIJ8,1644
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-inconsistent.wav,sha256=t2Mgri3h6JLQDekrwIhDBOaG46OUzHynUz0pKbvOpNU,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit.wav,sha256=yCv0uh-ux_skJsxeOjzog0YBk3ZQO_kw5HJHMqtVyI0,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-36bit.wav,sha256=oiMVsQV9-qGBz_ZwsfAkgA9BZXNjXbH4zxCGvvdT0RY,120
scipy/io/tests/data/test-8000Hz-le-3ch-5S-45bit.wav,sha256=e97XoPrPGJDIh8nO6mii__ViY5yVlmt4OnPQoDN1djs,134
scipy/io/tests/data/test-8000Hz-le-3ch-5S-53bit.wav,sha256=wbonKlzvzQ_bQYyBsj-GwnihZOhn0uxfKhL_nENCGNc,150
scipy/io/tests/data/test-8000Hz-le-3ch-5S-64bit.wav,sha256=Uu5QPQcbtnFlnxOd4zFGxpiTC4wgdp6JOoYJ2VMZIU0,164
scipy/io/tests/data/test-8000Hz-le-4ch-9S-12bit.wav,sha256=1F67h8tr2xz0C5K21T9y9gspcGA0qnSOzsl2vjArAMs,116
scipy/io/tests/data/test-8000Hz-le-5ch-9S-5bit.wav,sha256=TJvGU7GpgXdCrdrjzMlDtpieDMnDK-lWMMqlWjT23BY,89
scipy/io/tests/data/various_compressed.sav,sha256=H-7pc-RCQx5y6_IbHk1hB6OfnhvuPyW6EJq4EwI9iMc,1015
scipy/io/tests/test_fortran.py,sha256=Oc1omSrxrhyELm5hHA_m3TL-hXcivkBw3EMYLx7KCAY,7767
scipy/io/tests/test_idl.py,sha256=hy3ER4-qVpni6T9rvhkra80FqoGRUaQGDLgU_2US9TQ,20992
scipy/io/tests/test_mmio.py,sha256=2PWDz3tc03WLh9uMyjdojm2YJ_BKBeW893O3Mx4lrBU,28676
scipy/io/tests/test_netcdf.py,sha256=izzpjJbXiIWPAy_xL5Mo2x5-b65LJtjxJR6P8cgnGiQ,19950
scipy/io/tests/test_paths.py,sha256=yfqQtS6YQkg2SFa4_YbGBqZlIhd-brGut6jSRDtv4yc,3271
scipy/io/tests/test_wavfile.py,sha256=1N9YujKYraHjT5x6-RjQLDJfh6pzqRoFD_a6pI6vvL8,16073
scipy/io/wavfile.py,sha256=T83mGMzTOXlaoKt-a4KLrqAiE2bBz4S-NH7830qVy6E,27465
scipy/linalg.pxd,sha256=SUm9fRHyF3s5-nhG0PWlenOlEEXtMRBEW_5X2q0ufGs,54
scipy/linalg/__init__.py,sha256=byYkkwVKNJuoJzNiksMlhQvd3RxaZU35EnJImC3bhlw,7753
scipy/linalg/_basic.py,sha256=RRjKlTY0LY5ecaS2Uz5t3g-djV1f9SKOfVHfAEWUhD8,70833
scipy/linalg/_blas_subroutines.h,sha256=ioYgIFieoNChvc1H3M0C2sKFtRw1dUYHwiF_IOK_NZA,18298
scipy/linalg/_cythonized_array_utils.cp39-win_amd64.dll.a,sha256=wxuT0kxAJ8JopAPRSuszrPHHSJ3J8NzG6AmyW6gvbOU,1732
scipy/linalg/_cythonized_array_utils.cp39-win_amd64.pyd,sha256=KSeIgBKWrIGb_lswAs8r2gAFsnEL4uqBMpWzgUI2Wss,580608
scipy/linalg/_cythonized_array_utils.pxd,sha256=fyp2eQgNSXX88dHrIV5qUuZ-fTEU-8JdXKOTLagAeK8,930
scipy/linalg/_cythonized_array_utils.pyi,sha256=scRj6ew53DtnyIry4gVV3IsZlbwiNZJeptv_0icKE2g,356
scipy/linalg/_decomp.py,sha256=COCiA-2W8pZT4DdGWtgPoyTVqossj1JxFbnbkSRR6hQ,63798
scipy/linalg/_decomp_cholesky.py,sha256=FwQTaP-ryuEzWSebXj7w-qBS-FKVsQr_-8iT09vIEeM,12171
scipy/linalg/_decomp_cossin.py,sha256=mwXU7s2k92ser1wQBZUTWJL6vbyFyKO2KOuwKwRK7sA,9194
scipy/linalg/_decomp_ldl.py,sha256=fx4qmHmF_JuJ3R_r9aqcn7UZG4jTui6Vy5SqmIiD2Ns,12888
scipy/linalg/_decomp_lu.py,sha256=JOTZ7cGblpT-KU7N9NXm50WOuN-xqKIP7S6WkAbGnqI,12962
scipy/linalg/_decomp_lu_cython.cp39-win_amd64.dll.a,sha256=DIybcVmf1pMUbuwn4-S70Rroyr5PKMi_nVG1o8mEaVQ,1660
scipy/linalg/_decomp_lu_cython.cp39-win_amd64.pyd,sha256=e1d34GhOHX4zY3P884qX1pTgH1AnUs6OmA7nX1vLy3g,248320
scipy/linalg/_decomp_lu_cython.pyi,sha256=bwyBF7nl-jGGrmj_D16QSXx7MBXAVtU3k4eswqKohCA,297
scipy/linalg/_decomp_polar.py,sha256=4KAJQAgKoN6W_onEM-m3oGqfwCK3__ASC7UFMFdKcac,3689
scipy/linalg/_decomp_qr.py,sha256=JGDqUCS1BEfq3JLqJMCdDZw-SSolulSF6wPmcaF8XGc,14206
scipy/linalg/_decomp_qz.py,sha256=RMxX7pLyNuFhvTKwRnuaR0NraFwuZhvC8DBlIPc8I4Q,16779
scipy/linalg/_decomp_schur.py,sha256=ak_6WACsIjJDQnOatwZnio7Toe4hjEHQ64oVK9ZF3IE,10719
scipy/linalg/_decomp_svd.py,sha256=wrIYkTatSlNgGqXged3xidZ-289EDmnrxArlJR966Ag,16133
scipy/linalg/_decomp_update.cp39-win_amd64.dll.a,sha256=i2IFDH2Po6UIqc7jKlegmVhYO0sWStEUBX1d5KwIBoU,1620
scipy/linalg/_decomp_update.cp39-win_amd64.pyd,sha256=NPylclwBMutKJpTVUURf9LxJ3G30kwP5DERbWGBA1WM,337920
scipy/linalg/_expm_frechet.py,sha256=m4h3Ibq3Jlh2LMuHNoRsB_LsGHDmTo5XyCdPcudx3Zs,12741
scipy/linalg/_fblas.cp39-win_amd64.dll.a,sha256=K6m_GCBNqdzXQdSm7a3Sk0RPtU286uddmsAiRcNcMNo,1524
scipy/linalg/_fblas.cp39-win_amd64.pyd,sha256=fraOcTTL_hKufKAoxXCjTLgTrxW5dkBw8yObzhG6XAQ,593408
scipy/linalg/_flapack.cp39-win_amd64.dll.a,sha256=14X-YDX06xXmflQsZ9uW0na_4YzX6TQqz1D3_42wkx4,1548
scipy/linalg/_flapack.cp39-win_amd64.pyd,sha256=zhlujEXTDeZKV6haQSStzj1MlPCRcPVoU34n0LDkGl0,1890816
scipy/linalg/_interpolative.cp39-win_amd64.dll.a,sha256=uARt6XAUV7KEEomIaJ5D8tYhAS3lOB4LtujTuVRQw7o,1620
scipy/linalg/_interpolative.cp39-win_amd64.pyd,sha256=Q_K5NLdw4pjz-rLgLp063olwdyUTGApHHYNcQOAWxZY,757248
scipy/linalg/_interpolative_backend.py,sha256=mwdx_kKIlNkWDBeLqx7WnD2ce4UEo2nXqCSyKHny-9s,46873
scipy/linalg/_lapack_subroutines.h,sha256=Qs2tpIEsllXFG8Fbdd_HjPYvPXq4WJOG71xIhcfV5uY,240824
scipy/linalg/_matfuncs.py,sha256=czqh-6fKAexTbgjeqoRUndyB0vxzMug7B8_qmbE4_P4,25192
scipy/linalg/_matfuncs_expm.cp39-win_amd64.dll.a,sha256=FFVRSW8wsexKgXq0tsKgQN0_CfXRHTjG0n4Qy9AyErI,1620
scipy/linalg/_matfuncs_expm.cp39-win_amd64.pyd,sha256=gA5mO2bSOptbAeoFL_AlVnYhDj3uXL0id17TUeKtS1k,479232
scipy/linalg/_matfuncs_expm.pyi,sha256=J4OGtavgQPeZXtpBbWDQMH08jKqrdM028ZpWzyEoDfI,193
scipy/linalg/_matfuncs_inv_ssq.py,sha256=9O4ImZRTV9pbiBzbYMwir_C3_3kFLu2RIa9fPHkuOAE,28945
scipy/linalg/_matfuncs_sqrtm.py,sha256=4Acsd3bWFAkDN4OmHQ1bOO6lSAAXorxKREEts5H_KyM,7034
scipy/linalg/_matfuncs_sqrtm_triu.cp39-win_amd64.dll.a,sha256=cdaiRbMaZ6i2Xfr_IheH4Sw_-4mCR70L_gAWRD3Vkr4,1692
scipy/linalg/_matfuncs_sqrtm_triu.cp39-win_amd64.pyd,sha256=apvBfiEQbBIPf2B-YkuYp5onSuCuamTko6cZwXOREV4,255488
scipy/linalg/_misc.py,sha256=GuAl0DgvKf35a2oyvYgcud84O0e3DCQLSUdR2AzXE3k,6474
scipy/linalg/_procrustes.py,sha256=5s-CF7-cmMR7NRJTqHn1FgozjMKLpmEOMImID7wc7o4,2853
scipy/linalg/_sketches.py,sha256=nVwWE2o7wZw69rFbEDoosoZoNm31jW1iM9fxhdz5BnU,6324
scipy/linalg/_solve_toeplitz.cp39-win_amd64.dll.a,sha256=m-dZxG-xUL6RSgSL7ZxYZ48XJYNGvqjVl9xFGQ6btq8,1636
scipy/linalg/_solve_toeplitz.cp39-win_amd64.pyd,sha256=OTb7z0AtBxgmMDrTO7drfr1ThMQ6kamggh9eczugvp4,274944
scipy/linalg/_solvers.py,sha256=0OBuAxJ8S41wGjO09o6jfJfxKd_GasCO8aY5y9hURL8,29188
scipy/linalg/_special_matrices.py,sha256=bRPhIZf-e93FZ3B0EZl71kQLwgK2Sk4YsMqrR5oq5n8,38122
scipy/linalg/_testutils.py,sha256=s8zF_A9X4ToKUuwd5QUToC1YoQg7QcFr5UgruaCat2U,1872
scipy/linalg/basic.py,sha256=pvsMSg2glM_0HSLCr7770MzFYX3B4yoNGl7BCvtZMbA,821
scipy/linalg/blas.py,sha256=9VNxn8qpwCwDI3yHdqp9rdxUfJQmei9sFH8xkVpS1kg,12181
scipy/linalg/cython_blas.cp39-win_amd64.dll.a,sha256=BSSE6IQ0nR7VjzPD7pLVbdSDmgey7TBJJ1hU2-zGS_8,1588
scipy/linalg/cython_blas.cp39-win_amd64.pyd,sha256=QJc_py2jr3uIHpC_XGGxnR0BkH_xW8YnLhr9bN8ySpM,280064
scipy/linalg/cython_blas.pxd,sha256=voPCvko12ZS_Bve1IvptSdsq6fu5KXa8ztkLU-zmVF4,15761
scipy/linalg/cython_blas.pyx,sha256=UTGiyVw-ZFdkgfVL6DqoA0VxXK6B6ij7f5Of-uZhXdw,66340
scipy/linalg/cython_lapack.cp39-win_amd64.dll.a,sha256=zGxVUNlT9BdzmrUqMX3PGXaRY7Z4WdJOdwrYRyApKB4,1612
scipy/linalg/cython_lapack.cp39-win_amd64.pyd,sha256=9c8_zOrHSJLNlNDy-IDCbQJIs76LN6jK9uL5v337AnI,520704
scipy/linalg/cython_lapack.pxd,sha256=Up9I8aC8Q6olO0WmMfipi7g6micBTiKAztfpZ0zXOeE,206084
scipy/linalg/cython_lapack.pyx,sha256=IPh0oR3HK92VmBuZqSwVVUhFqE2uxSFUqZVke7WNVB4,719027
scipy/linalg/decomp.py,sha256=K3PKvW3LDX3IfONdWAt763BOU-40ic479y4yVnoWtjQ,863
scipy/linalg/decomp_cholesky.py,sha256=rx2bt5Xk3TslWMHIQQTTDlflCBgKeVMhZxG9yFg5DBg,710
scipy/linalg/decomp_lu.py,sha256=CLYKKnfAGnsZ9XO2s5_kK-H6hD0VbFZc8RcIk3Bwjwk,635
scipy/linalg/decomp_qr.py,sha256=uBMdbPI3zbwJg6V9wSJc4jGUWRZBz8JrSYajVWHg0cY,599
scipy/linalg/decomp_schur.py,sha256=X_z3WQXWzAYCSfXM0tm0bpFzTry6ruOtSmfSOR4pF04,682
scipy/linalg/decomp_svd.py,sha256=6vTAujJiNDCu1-UTkPcPmWkT9s2BXSi9H9d_o-RZCow,652
scipy/linalg/interpolative.py,sha256=a33573E5xJCAsYQltbzEC2WJi2QyF3Lowk0khyp3wRk,33266
scipy/linalg/lapack.py,sha256=qdxxtL5Gjc8qLjlnzgUbUE5xYn7eINDw_hdRBgHahDM,16706
scipy/linalg/matfuncs.py,sha256=yha-BcBUNQ5P9r_Ieb6jOsPOLS3INFr9NwFVZj4oj1o,908
scipy/linalg/misc.py,sha256=SY3ronlv05KREUi0vhxT3ruSPUg6MZmRoZWgXJh-7ZQ,613
scipy/linalg/special_matrices.py,sha256=dzaFE9IduLFCRrMwYRCiTalMCJttJ4cwT555KeZUA_o,793
scipy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/linalg/tests/data/carex_15_data.npz,sha256=E_PhSRqHa79Z1-oQrSnB-bWZaiq5khbzHVv81lkBLB4,34462
scipy/linalg/tests/data/carex_18_data.npz,sha256=Wfg5Rn8nUrffb7bUCUOW7dMqWSm3ZPf_oeZmZDHmysY,161487
scipy/linalg/tests/data/carex_19_data.npz,sha256=OOj8ewQd8LI9flyhXq0aBl5kZ2Ee-ahIzH25P4Ct_Yc,34050
scipy/linalg/tests/data/carex_20_data.npz,sha256=FOIi00pxGMcoShZ1xv7O7ne4TflRpca6Kl7p_zBU-h0,31231
scipy/linalg/tests/data/carex_6_data.npz,sha256=GyoHNrVB6_XEubTADW2rKB5zyfuZE8biWBp4Gze2Avk,15878
scipy/linalg/tests/data/gendare_20170120_data.npz,sha256=o9-rRR2dXCAkPg7YXNi2yWV2afuaD4O1vhZVhXg9VbU,2164
scipy/linalg/tests/test_basic.py,sha256=msR1CLmUFTacycBbzhn0XN17t1Y9wahAM8TA_lkEM04,71768
scipy/linalg/tests/test_blas.py,sha256=_4-mo4JV8tz4XrRdyI3QJX4-1wgK3Fhfav09axkNcDc,41956
scipy/linalg/tests/test_cython_blas.py,sha256=w-Xg6YtJKzlhfD5EKninnVnvmwJuDYnffM-kzotXoCE,4205
scipy/linalg/tests/test_cython_lapack.py,sha256=FuwDgBZSL9JtQSLjW5-0Bueom1sBYLd4iCwZT2EGX3k,818
scipy/linalg/tests/test_cythonized_array_utils.py,sha256=uGVyBm288GVIieUXpc0Iu81z9u8Vc6Q6neIN2aCnIvE,3961
scipy/linalg/tests/test_decomp.py,sha256=wy-sjYezwiCBsJxV4UOs1B4AR2NgBAr9QIOjPP2A32Y,109693
scipy/linalg/tests/test_decomp_cholesky.py,sha256=p6Avy3JTt-YH47Vj4i3MF6UIseB-sBFH9R1OlVhejp0,8076
scipy/linalg/tests/test_decomp_cossin.py,sha256=YnnP5FMr9D7S3wB0tEf3dJN733AlPPQoBlYmu9bx0fE,6112
scipy/linalg/tests/test_decomp_ldl.py,sha256=Qt5tWnNSs6k3dCTWAKRuWDt5U2A8ibo1J-_BF_nJISI,5080
scipy/linalg/tests/test_decomp_lu.py,sha256=5SDUfPF-VUpUYEs604H4JxIQKkC5MtYOhXeIGXjpVRA,11453
scipy/linalg/tests/test_decomp_polar.py,sha256=dx9ubrGOCBZW2IVPBveUt6v6wWldGUOrh0MzFlB6h7w,2736
scipy/linalg/tests/test_decomp_update.py,sha256=xIhcW4BRZWWRX4hPZsGiEaISxpU7MGMjTKdbiltgabs,70190
scipy/linalg/tests/test_fblas.py,sha256=FoEZpLFpdle1scpFXl5CeVTEH31c5GBxxW5FNPx_Usg,19294
scipy/linalg/tests/test_interpolative.py,sha256=feCEd-BNhbvJV0HJpCmfDQt0uaQhk1LK6t3waSpbyYI,9210
scipy/linalg/tests/test_lapack.py,sha256=GpChthYxQZfBRkuLmb8BtSqkFIhSdtqfDAwRPXs5FFo,132748
scipy/linalg/tests/test_matfuncs.py,sha256=El-5zgPZ7dN2tslhV09N4z9yUBb_YSmeKsvpalybaWE,40939
scipy/linalg/tests/test_matmul_toeplitz.py,sha256=jrzob-NZ16k_1cYe-7xqI5L8ic7VMYxVlqG7IyiMTy4,3995
scipy/linalg/tests/test_misc.py,sha256=k0264gAdjSMDmE4KRZcMztRKkkwWuzblNOrAD4K17Ec,81
scipy/linalg/tests/test_procrustes.py,sha256=JalCsDisOMEY2lD9RSHfSU6X_W4wVmev_9BZpn8VAko,6949
scipy/linalg/tests/test_sketches.py,sha256=RMUKNRQTj1o08CGHGf68bsES-jzLuN0AswGG5aNXsk0,4078
scipy/linalg/tests/test_solve_toeplitz.py,sha256=1y0Vec8ZOK1tmjclVMXVxCJJ8ZgO_s4TguYRFawrKcU,4131
scipy/linalg/tests/test_solvers.py,sha256=R8_jDVe2HreuLVe7jajgjRHjOyeHd6Ll79jLJFWp-O0,32419
scipy/linalg/tests/test_special_matrices.py,sha256=pkFRNNUB7P3WMm7ipXv0zkrosJhKE-GWruVW8BIXHNc,24193
scipy/misc/__init__.py,sha256=yGB5XvVr2KLtK1e40nBG1TTne-7oxFrwSYufByVeUDE,1793
scipy/misc/_common.py,sha256=6UkM80cLptkn_NdOuhU09nGYQB9Ns7wYRPSWWb3_GPY,11497
scipy/misc/ascent.dat,sha256=6KhJOUhEY6uAUa7cW0CqJiqzOpHWRYps0TxqHK1aAj0,527630
scipy/misc/common.py,sha256=zhZRCBTlj4XhdPSo0wr8RQ77vAzRfM1fPLbNoBkGemw,639
scipy/misc/doccer.py,sha256=NQvWWhsiS5fZXEUjbdWlj1OMjqfJyhZdzUe4DhJJNe8,1503
scipy/misc/ecg.dat,sha256=8grTNl-5t_hF0OXEi2_mcIE3fuRmw6Igt_afNciVi68,119035
scipy/misc/face.dat,sha256=nYsLTQgTE-K0hXSMdwRy5ale0XOBRog9hMcDBJPoKIY,1581821
scipy/misc/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/misc/tests/test_common.py,sha256=rfcxQnBUc22G95MDYoYRmIciDjL8Pgmrf0fbMp-pqGs,859
scipy/misc/tests/test_config.py,sha256=Ma6jMr-KuIuDdFFz88ZHmFcawmOugzfJnspZv9Aqda4,1288
scipy/misc/tests/test_doccer.py,sha256=CWV_zZfJCz88jyPJ-pcIk8hMXxmXh_f58LgJjjBbJzg,3872
scipy/ndimage/__init__.py,sha256=Oo-CeM7DPYAGMsDW39xOMPXINXdq0tSRaASScCIJj1g,5158
scipy/ndimage/_ctest.cp39-win_amd64.dll.a,sha256=dZiMe6iAgYZZKIJE3pr-xcN6iJfGFqvhe98OGGupwuA,1524
scipy/ndimage/_ctest.cp39-win_amd64.pyd,sha256=LmN5HOIUrBBYc9wfMxGXIUmQ3VCs-YW3ACNtL7m8oZA,16896
scipy/ndimage/_cytest.cp39-win_amd64.dll.a,sha256=I_B_nLdQhm3Sogy7jzoyJm0wXHoTzcYYkS4allupmQU,1540
scipy/ndimage/_cytest.cp39-win_amd64.pyd,sha256=jJl6Eyb1BTeld_YZoNLerm_T7ETczeeekOpF_HxrkDc,75264
scipy/ndimage/_filters.py,sha256=paKODLam9vhfX2L8JS2mroL6Nk4bzkQDHlVB9C_rTBI,67775
scipy/ndimage/_fourier.py,sha256=qp5zfKy385ESHgKsnGRdLqo_06cPXykO5ubU0cKnxp8,11692
scipy/ndimage/_interpolation.py,sha256=KPIA3rYBpX2pdFAnUoThxEQoS_BqGCEEmusFaTAHyXw,38033
scipy/ndimage/_measurements.py,sha256=NBr_UCp0fwmXFTOudUU_8FJ25piAtn481eRz2e5c-9I,57778
scipy/ndimage/_morphology.py,sha256=WEw0GY88mopiU4qFAN0J8F33qEvPM1_PxvQxvOM5nxI,97433
scipy/ndimage/_nd_image.cp39-win_amd64.dll.a,sha256=qVHaUrAYFGm-KZd1wlZ1JJa6LguPjIOWsWBqXl7rpw4,1564
scipy/ndimage/_nd_image.cp39-win_amd64.pyd,sha256=g74Y2ho37vUWG7L5n6giA0jcuBs0Um0HYYFbWeUDTSc,176128
scipy/ndimage/_ni_docstrings.py,sha256=gUp13eFtd2wqZwlpI7O6SqDIlY7bGikdujvOAntqwf8,8713
scipy/ndimage/_ni_label.cp39-win_amd64.dll.a,sha256=a363g5UOxak5w9XVwOMYi4O92M48qOVNhxs_qPGta9U,1564
scipy/ndimage/_ni_label.cp39-win_amd64.pyd,sha256=GJd_tGG_8LrXdIucCIJzALDSvjJPdMZeTLbOkkn-X5I,395776
scipy/ndimage/_ni_support.py,sha256=gUZhDnd6N2Ipzs6oaKXIA2ozwuYJ57qoJbGU1BYxn18,4765
scipy/ndimage/filters.py,sha256=pBSTBZeUY3XAJCIFdL1UZmB3OM6KJld2jhzPVWHpOYs,1003
scipy/ndimage/fourier.py,sha256=Mg8ym6fd2BXBFSrUiwrW3GUoeDTYXbdOqQe75EJiKYw,620
scipy/ndimage/interpolation.py,sha256=7VM2PUpSqDH6hFvaF4QIIsArLrF5Eml_Vq3ADrmv6Do,703
scipy/ndimage/measurements.py,sha256=NNTrZLZSbU6hO42Tj49me-S1A-V-pnODbpxkm4ehLOI,812
scipy/ndimage/morphology.py,sha256=6axnW-p_Fp3Bm-21zYoaaz-1SdNxEPYCl8SoeRXwDQQ,992
scipy/ndimage/tests/__init__.py,sha256=FNT5j4JCFxTapyNfZrM6cxnn7WlOGq0ZvCwhUZerX54,408
scipy/ndimage/tests/data/label_inputs.txt,sha256=oBKOjlyOqkDERFefuyjlsPeVfGRyfmDc-uiawuMey4A,315
scipy/ndimage/tests/data/label_results.txt,sha256=hNPE8YOPm5MZ169I4c5JnG2cWxleOlKddG1VdA-Fn3Y,4603
scipy/ndimage/tests/data/label_strels.txt,sha256=fLjLCCb72QdX55kKssZdwg262SaV5NgdIG_Bn_gPHSg,294
scipy/ndimage/tests/dots.png,sha256=sgtW-tx0ccBpTT6BSNniioPXlnusFr-IUglK_qOVBBQ,2114
scipy/ndimage/tests/test_c_api.py,sha256=ii8Y7KA9O4TJEosO_bQ8yN3jBS9QvqnmM4r2KlEU0fs,3832
scipy/ndimage/tests/test_datatypes.py,sha256=4qhH8s06BlCG0Jbw5gps_7RDFNuw_D7r4fFtdV8tYqQ,2893
scipy/ndimage/tests/test_filters.py,sha256=c_PgfA7lNG2kOYLoab3VgVRmOWiQKhUVkughMntyg4M,96717
scipy/ndimage/tests/test_fourier.py,sha256=2LfhIaRNyrWY8KilrcngCTuvpIZChj8Homl-j7d2KGs,6815
scipy/ndimage/tests/test_interpolation.py,sha256=kgFq2LAiZINPmVqSgL69MFB9rqt2gSJqueqg7ZPR3U0,56098
scipy/ndimage/tests/test_measurements.py,sha256=Jy7VSAJeByEvbpLX5TQEUj2eWf6ABh5fgfcGxJc8ooU,49907
scipy/ndimage/tests/test_morphology.py,sha256=lLyaza5WhYKwX-UxAOKH6-ZKjRusfQREOdvnx-S0w5k,109082
scipy/ndimage/tests/test_ni_support.py,sha256=8DTgqgKOyw6ydsgPfSTeiYyfhcnAS9QjRdMk8slo67A,2549
scipy/ndimage/tests/test_splines.py,sha256=r1rRYS7GD0ZsQWvuobXIBBC1Mh1MdHusr68vZLdhXBU,2264
scipy/odr/__init__.py,sha256=gHT9GMAwFz2ewCC0B1kTenNLG6g8CHNm__5z6vtF_zc,4456
scipy/odr/__odrpack.cp39-win_amd64.dll.a,sha256=nIwxUqHtjerBseeH4shzYc21_wq2jOTUQ79OQkrF5GQ,1564
scipy/odr/__odrpack.cp39-win_amd64.pyd,sha256=1pDtSUL2FHIE-_UUOZ5_FZBe0-uvGsIBpRw_2MMO-II,566272
scipy/odr/_add_newdocs.py,sha256=nquKKPO9q-4oOImnO766H3wnLIN8dBZJfqPh8BgKJ_8,1162
scipy/odr/_models.py,sha256=Mi8SuWoWXIFqGi3BfgMSc_eu1GQfGRtXSvkeI9fblWQ,8115
scipy/odr/_odrpack.py,sha256=vjMaJwARP-JUkLnVowpi3eLUSdE_jlpUwKER6AY_jDA,43551
scipy/odr/models.py,sha256=_7pQbo0FThkV9yo2NvXC21_SMbNqUiBjeUsxnd9PerM,610
scipy/odr/odrpack.py,sha256=NqRd2QtNcw1-gq4KpkbkddvMF3G78DxKGSFlJ8Day4Q,653
scipy/odr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/odr/tests/test_odr.py,sha256=gplFVJp6YtaKvcbpUSF7bzYMwpWW95hB2rwkhHGm9BQ,21576
scipy/optimize.pxd,sha256=Go47l_Tk8YvXQxnuJNN6UmDEApEKgBUrplitjO4tsUM,40
scipy/optimize/README,sha256=_CUBWHgG5xW7HG5La9gsHQ2Cr7cRidzPOexd4rg3oIA,3297
scipy/optimize/__init__.py,sha256=p8skARtfSpeZyrNCOOfU2dfI9v5QR0DI9qwtIRycCtg,13559
scipy/optimize/_basinhopping.py,sha256=ZhU0A8eDSNOLuOmxDnumhzQtM4E0XolgRTcHogtCgWw,31444
scipy/optimize/_bglu_dense.cp39-win_amd64.dll.a,sha256=sEtcReNJ-kAw6RrYL8m-89vZ45kFKFdulKp0RxiQN9g,1588
scipy/optimize/_bglu_dense.cp39-win_amd64.pyd,sha256=QQiEm6YXGC9x9nufHavunKOS5wl0Mpj32_JXQByS_TQ,318976
scipy/optimize/_bracket.py,sha256=5MOw_CykQpd2HS5UhAmMk7WeTSlg6jnODvNqO9n1I4Q,29277
scipy/optimize/_chandrupatla.py,sha256=Xdcv8ztK7_xyzRkbpqdCN8_yDr9kO_5cqvT3CO2aKXY,23746
scipy/optimize/_cobyla.cp39-win_amd64.dll.a,sha256=_ElE0G_MeBvYc0x1S4jDGzSHXy-8jK4640HaAK-zip4,1540
scipy/optimize/_cobyla.cp39-win_amd64.pyd,sha256=3HT6lLsSr4yFYJlPChq0YqIb7Ztwrs2Q8v_0xzC6Vs8,418816
scipy/optimize/_cobyla_py.py,sha256=Ycqnv7D9YqcDeM1pl-TNS0tMnAAJK9wEsY7KnCbxwUc,11185
scipy/optimize/_constraints.py,sha256=ejG1EP0e6MTOR9sPaeXhAvqeGQtGhORT0dbbap3xq_U,23444
scipy/optimize/_dcsrch.py,sha256=HLVdwRymefDXa8QZQyZjca1y-yUWOfYn0Jh8SDs0RM4,25963
scipy/optimize/_differentiable_functions.py,sha256=eXjmu5Vzm84Cm7BhuEbs1SizBGfUfbTstQ0MXaHmaDE,24311
scipy/optimize/_differentialevolution.py,sha256=FJ9DEnk4sd9axlvjVX_esWpgEGXClXUesx-iYQSVtBY,85103
scipy/optimize/_differentiate.py,sha256=0N9z_Yhod2TDXpPNk7a6eJcJd_GwLtM8hdw-OoEMinM,31539
scipy/optimize/_direct.cp39-win_amd64.dll.a,sha256=PfdZ1adSS1aMj2AQGarUX__y-CRWhpeKnAhAvY6xjUs,1540
scipy/optimize/_direct.cp39-win_amd64.pyd,sha256=HA4IVWSJsNX1J6JnNlIOOLmbvpbDQc557HV3aL8bC38,69632
scipy/optimize/_direct_py.py,sha256=kRtd45Akip2Z-lzRzoLdOkJoxAkr9wTLVJArnpfq8bY,12076
scipy/optimize/_dual_annealing.py,sha256=IYKMG5gYR0zOUN8uTVErpIuF_YiLk1zRa2394go_ffw,31061
scipy/optimize/_group_columns.cp39-win_amd64.dll.a,sha256=O7dEuqzy44D2w_IsxPkKVr507Y0kqa4HSaRWlKqLEe8,1620
scipy/optimize/_group_columns.cp39-win_amd64.pyd,sha256=X-Ac8yAeJQX4ZLZlfsetP4YK5lQsSkUOz60gW6099aA,1029120
scipy/optimize/_hessian_update_strategy.py,sha256=ELbQBvWV3bcHOynxIO4Febymi6m2zj-15Q9hROBWJsg,16292
scipy/optimize/_highs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_highs/_highs_constants.cp39-win_amd64.dll.a,sha256=Tn7maqgzkMX30iEgGLZ_i351vZPISM073mRAW9IKm4o,1644
scipy/optimize/_highs/_highs_constants.cp39-win_amd64.pyd,sha256=G5WLt0kwtO7K49NvxWwf6IhWKo9j2Xn6J_ukuuI8LL8,948224
scipy/optimize/_highs/_highs_wrapper.cp39-win_amd64.dll.a,sha256=OydUyBhJ4eMgIPu-urzoP431B6OWhOYx52-Oty-6KjI,1620
scipy/optimize/_highs/_highs_wrapper.cp39-win_amd64.pyd,sha256=Md9wL--9HF5aTL2NfbwBLmrTygdTLKIzUCtnCamf-R0,4546048
scipy/optimize/_highs/src/cython/HConst.pxd,sha256=L15CusfL00lWvNnDBadRg4cfyFQ9OBZjx7DnuWTiQAM,5617
scipy/optimize/_highs/src/cython/Highs.pxd,sha256=DEAT3kbjSV8yG4ODe3pf13WxXXTrtxdXd3iPZB1C-lc,2203
scipy/optimize/_highs/src/cython/HighsIO.pxd,sha256=cyJqwFONXeG4JCUM732mCNtjBqpwlu-NdZTEGNZeWDc,725
scipy/optimize/_highs/src/cython/HighsInfo.pxd,sha256=UVxVCOiJv01N9sdMx7ZJYEWr4Sb3v2JsTyf1oqwli4I,757
scipy/optimize/_highs/src/cython/HighsLp.pxd,sha256=vlYC8YlR4B_nve_g7zc8V7XxDhojrdIl21cAQIwIdfI,1152
scipy/optimize/_highs/src/cython/HighsLpUtils.pxd,sha256=X7RWGM_jqH9iomrdE82vJ3FiQTlwCxOK0dFWwyhBcvk,298
scipy/optimize/_highs/src/cython/HighsModelUtils.pxd,sha256=_wdfARiJz-1uVj4-DCDQFL4qlxtwfSGCEcPENTxMa98,345
scipy/optimize/_highs/src/cython/HighsOptions.pxd,sha256=Q9qcpapCKmpUPQzvnSi8ikl5AwQ1evrw3UilgXO6Xxk,3270
scipy/optimize/_highs/src/cython/HighsRuntimeOptions.pxd,sha256=lGf-04sraouYfLC-nMhelSxckt5KzVBIh6E2fC7IktU,270
scipy/optimize/_highs/src/cython/HighsStatus.pxd,sha256=zbFQHPW_dv9veyurF4ifqzQd4KMvaI-3c812neTMK_Q,351
scipy/optimize/_highs/src/cython/SimplexConst.pxd,sha256=8f0-itFFhq9qWHZCxeGFW1-wu61X6rTt1HhNqcFI_PM,5113
scipy/optimize/_highs/src/cython/highs_c_api.pxd,sha256=Acdk0ptEr5H66e3sFkfcRRRCGp2x-Wl98Z3tp9D7XFw,339
scipy/optimize/_isotonic.py,sha256=RxEmV_dGFcb7dKP7FBypBE3nbaXPak9RMl63l_1FM5E,6212
scipy/optimize/_lbfgsb.cp39-win_amd64.dll.a,sha256=pvVNYYyIUuZ7VMsqirQA9fobEPV8jaDtjc1AmJLE7C4,1540
scipy/optimize/_lbfgsb.cp39-win_amd64.pyd,sha256=FunN7NLHhXVUZhmGWU7vsAk-VPleEmB2ltCrnfTWpmE,436736
scipy/optimize/_lbfgsb_py.py,sha256=fVkQ5qw6fdSuc_LxOHHU5Hz_bKZtyeYFkaKHAfKVsiI,21261
scipy/optimize/_linesearch.py,sha256=qDgwdijDDCU80gPX1fFsUkH8iT5-poSi2zHjTpj5bgI,28179
scipy/optimize/_linprog.py,sha256=Y2b70ex0Nc5s7yqTgnZBlVDodnBN2vHPQnu3-6IGGB0,30433
scipy/optimize/_linprog_doc.py,sha256=no_OyJgCCqufunfiL-7B1booijS_1OTY8luxvJBQ3b0,63379
scipy/optimize/_linprog_highs.py,sha256=vzNHd5QFirk-fb3j7dBTLF6KC3fHL8fbX9lTcfybCCQ,18013
scipy/optimize/_linprog_ip.py,sha256=LvPFzxHXKOkcTD02x4M4I-eS5i1MVQ6iG3Xjbb5HZOc,46911
scipy/optimize/_linprog_rs.py,sha256=DsKjiN4S4U510ahDcSWyFPIhakRrAXEFBl6i_dl7IiY,23718
scipy/optimize/_linprog_simplex.py,sha256=bWEwU99qgy0fC1G6F6TJHSr765eiKcQXA1_bVgxMJlI,25377
scipy/optimize/_linprog_util.py,sha256=98yfDJILV7iLuBNRTdLmDgbzHBersO54SZfVGJB10Gw,64271
scipy/optimize/_lsap.cp39-win_amd64.dll.a,sha256=bUjNqnRBy0ovcQFgX8EMPPaZisc4yclWpmMzhFGfE2s,1516
scipy/optimize/_lsap.cp39-win_amd64.pyd,sha256=RcWlfMoPNaquGfxBjbLzM2isIkdydvI4U4qpOkoqQK8,177664
scipy/optimize/_lsq/__init__.py,sha256=c4V-tnMyi2dXNNXkjFQ8SdvGANtgbrqXEM-OpdBdZpQ,177
scipy/optimize/_lsq/bvls.py,sha256=h9bYh3astIRvMtMGghtEGpG5fDsh74SMRbmgF3HN8YM,5378
scipy/optimize/_lsq/common.py,sha256=zQN7x4qxtX5PCaY2gdYMv4Io0YM4Cit9Eqb_L_k8xrM,21256
scipy/optimize/_lsq/dogbox.py,sha256=KuyTxgJPRumBkrk8KlgZZVhm8tpdmsGNBtPAsj2U1Xs,12013
scipy/optimize/_lsq/givens_elimination.cp39-win_amd64.dll.a,sha256=0nozqlOJ4BQLdofN3U_vFMgl4IZ8zqpZnVe82SCOl-E,1668
scipy/optimize/_lsq/givens_elimination.cp39-win_amd64.pyd,sha256=PoEt1CjbSjgYoGzEJaD5mrhWS4-N_jX-HvkngygCk68,213504
scipy/optimize/_lsq/least_squares.py,sha256=nHh4Fzgy28drPE3io6zAe_SfxVj6cOOgSW8RPA-YbU0,40639
scipy/optimize/_lsq/lsq_linear.py,sha256=9Xlb1mb02xqkOja-8Mb6cJDpW4ovNHvyoNRgZSt4QzM,15542
scipy/optimize/_lsq/trf.py,sha256=BBsxUjWZjzdVILU-Zr98t3WiySH_MUIp-unCQlRAo6U,20037
scipy/optimize/_lsq/trf_linear.py,sha256=GEMGyMqWwaVLUa7sCo9iwDmUkuT746oci1iK6KjnJQA,7891
scipy/optimize/_milp.py,sha256=vVoU-xFaKFD81wmmmp7TXrwy8_1p6GfqNXEqfu-aFBM,15579
scipy/optimize/_minimize.py,sha256=DBZTTzUJ5uMpvfhBQpwyUTIKr8wDACUfmu3m1ByJaqc,49315
scipy/optimize/_minpack.cp39-win_amd64.dll.a,sha256=tZPfw5L-VrlPidgNIjSvj67Czu0D-goCLwXZRABIy4Q,1548
scipy/optimize/_minpack.cp39-win_amd64.pyd,sha256=mqT_5jdkttbPrW4oICP29iFKeXF32-igbFikwzHPjzg,107520
scipy/optimize/_minpack2.cp39-win_amd64.dll.a,sha256=c6i1GiMt2Kjm6MCtFg55Wfzm_O5tb5GfuQ4AMI5eg_Q,1564
scipy/optimize/_minpack2.cp39-win_amd64.pyd,sha256=UrGEbJcX2D41gPXvr5YMj27R7-jgx_nHSjgqKo9igfs,74752
scipy/optimize/_minpack_py.py,sha256=QadZ1n3za72TrzKs2_p-MX1ioGNjW1RKsXNarsMFRhA,45828
scipy/optimize/_moduleTNC.cp39-win_amd64.dll.a,sha256=_RlZ9XbTUbCMBH4PVVmOA9ce9I6Dvkah5ViY9mO5QrA,1572
scipy/optimize/_moduleTNC.cp39-win_amd64.pyd,sha256=dk6VIya4VTM7MA92l5op_clOOnLejgWGMzjHS1Q6ubE,156160
scipy/optimize/_nnls.py,sha256=uyU0tvulUuhOAGP5JHVbj_fQrZKpypTGmm9OyUEZwcU,5648
scipy/optimize/_nonlin.py,sha256=YCWgcPSX-0XCNiRj_zfTgvZIQbKfqUJGshT9h8kXy3A,51471
scipy/optimize/_numdiff.py,sha256=sCRqhF7LdmRkxMBAcr0Wqz4oLq-fnBTCFPHK6z-OSM4,29495
scipy/optimize/_optimize.py,sha256=FaK7wO3nffss3sXfA-SKWK5A9H172PxwGT9WtqNj9Lg,150667
scipy/optimize/_pava_pybind.cp39-win_amd64.dll.a,sha256=5BJOSg_Nk98T6eKw7i3xumGvHWKcpUdAFuX-bdCTiTI,1596
scipy/optimize/_pava_pybind.cp39-win_amd64.pyd,sha256=B73bJBEQaT3c8UUxApWpPNzQ5B-aQPCKUfW0XIyBkXs,285184
scipy/optimize/_qap.py,sha256=WD5UQxYxTcb7Xhatba5J45DcqIVJvVUCtKee0qwol9Y,28562
scipy/optimize/_remove_redundancy.py,sha256=wm3BTNUsnRTd-emKeiFTss68K09fIH4IYyFR7MD3cJg,19291
scipy/optimize/_root.py,sha256=61OOP0PFQH893GgEhf2oFLgrrU0F_2UcEgH7emxRiug,28517
scipy/optimize/_root_scalar.py,sha256=zehSxyAbZI7bZNWLSzeR776P4c85v1La2R2yP7C3a5c,20120
scipy/optimize/_shgo.py,sha256=x39wcoLRMghqFJY3aGGtZFG35RCO79lpq-mNiifPOnk,63852
scipy/optimize/_shgo_lib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_shgo_lib/_complex.py,sha256=8r1bC4GHNpMYp2D5VT-Q98rBTK-l3x6r-PfXCG7LDs4,51484
scipy/optimize/_shgo_lib/_vertex.py,sha256=z9a-UXpMVD8NXa_y1ePp-Va3b7B1tcAQPmRcky1W2IA,14456
scipy/optimize/_slsqp.cp39-win_amd64.dll.a,sha256=i3M2UQ--VXyROKyTQFOMU-e62m9YiGxp6EoQjBHqYNg,1524
scipy/optimize/_slsqp.cp39-win_amd64.pyd,sha256=GjWZtB3Fmqfx0IkhZVDtROuu8rNT2P75ih--vYksEco,107520
scipy/optimize/_slsqp_py.py,sha256=O0SxKS5SxEfn19ui6K87xpvg7Qpx4EAUgQBiKvKg2MY,19601
scipy/optimize/_spectral.py,sha256=j33TVc8_7styCVxKZP76_2C5CsdyFi5lD5TpeBbo54U,8392
scipy/optimize/_tnc.py,sha256=uL9ald4ZzsvDK4W-kPTiIx5Ra8Eqrc21GrvYOaqvGvI,17338
scipy/optimize/_trlib/__init__.py,sha256=5TbV8UJSSEwW_qONgzozoHDgvhhugzqykJzybDCrNj0,536
scipy/optimize/_trlib/_trlib.cp39-win_amd64.dll.a,sha256=zRQh-wFm2nPVN3W7rLscekOmQWIdsFa4Md_QcqAI_-M,1524
scipy/optimize/_trlib/_trlib.cp39-win_amd64.pyd,sha256=Kx1oNUpFZoBxZfY4RMlCHR2cgMjHsltjYCvwh4K_7Bw,335872
scipy/optimize/_trustregion.py,sha256=1TlWgkWc2EGAkhKfJAwGmwz5GWdxvIHDtVwuvtux6Jk,11105
scipy/optimize/_trustregion_constr/__init__.py,sha256=Y2OLn2HBQ5rxpAfI-UjWZc4j_sDqjOBGmgdS2zAC_24,186
scipy/optimize/_trustregion_constr/canonical_constraint.py,sha256=pYMm2qLrCrvpD7pQZAZpz1H7Zfag3uiwezGBCDx6syk,12928
scipy/optimize/_trustregion_constr/equality_constrained_sqp.py,sha256=uuZ-Jtgl6G6sew7J2aeA4pERgAQ6tPtvvebGLBdNFUQ,8809
scipy/optimize/_trustregion_constr/minimize_trustregion_constr.py,sha256=GkloTNfoZpPSmyOC7E91BEUU-ffCAaiI53UnwwrzWec,26308
scipy/optimize/_trustregion_constr/projections.py,sha256=-1w4LbwrFnQc8xMhoyiBazu-YNydDqvDcBUMNsu7yx8,13576
scipy/optimize/_trustregion_constr/qp_subproblem.py,sha256=kzhhoadtUgeFmSoBKerKMnSgMSSpzHc_m43hkgqjTlg,23229
scipy/optimize/_trustregion_constr/report.py,sha256=Fvc4n77YuGiRQuGxiDIgkvkgScq5dqko07g7zJOwj-w,1869
scipy/optimize/_trustregion_constr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_trustregion_constr/tests/test_canonical_constraint.py,sha256=z8MKN6NbaxsH7pj2rbCGdOQ4jXE-eM-SPu_IcNMtIaI,10165
scipy/optimize/_trustregion_constr/tests/test_projections.py,sha256=YQ5iAUYXSTKZusnJxG8yD3Ctyb2qlCPm9IAk8y8pkAY,9048
scipy/optimize/_trustregion_constr/tests/test_qp_subproblem.py,sha256=YJmS5Zewk0mh8qsVCgWQKoDoIoMqOTE_32klYQXK8QY,28368
scipy/optimize/_trustregion_constr/tests/test_report.py,sha256=DWLQhuc4K6-UxV810cLlbTFQg7i7Ueja1XHZMWt4DsU,1102
scipy/optimize/_trustregion_constr/tr_interior_point.py,sha256=FU2a3KT4uJ189s4eAxrBmwVh2GlIiSzMN9LFvZqkIBo,14144
scipy/optimize/_trustregion_dogleg.py,sha256=XTvNQtebZTPzl6xGtzHBLDcFD2qe5sSOFOTbmtDWswo,4511
scipy/optimize/_trustregion_exact.py,sha256=H9E0sxJmLMebKgfd5L64h-BxP-WFIdCf6CyvMRUUNjI,15993
scipy/optimize/_trustregion_krylov.py,sha256=OE6ABN1_oeRUUBXWGBA2QFlzxO4GJVBseoNOMB2b4Sw,3095
scipy/optimize/_trustregion_ncg.py,sha256=O2_8p92l_faUHLI26GHK2wV0RTdAR1scli0XhXTTsSo,4706
scipy/optimize/_tstutils.py,sha256=g6-cA3Yl9OQNmbrYK3ojJFIrRTS9c7EJHhF_hoU5f2Q,34878
scipy/optimize/_zeros.cp39-win_amd64.dll.a,sha256=D2lZJ0tlpWH9XcBDf53WaGSuymCcD0WnMZwc2bMoc_w,1524
scipy/optimize/_zeros.cp39-win_amd64.pyd,sha256=lPKQs0VCfyZmvQoMyn216kyLh69AjW0khy8CfwYmCio,23040
scipy/optimize/_zeros_py.py,sha256=u5E-SHpvmg4y-0on6W5ovbHmsefYoc8u2MNKcnLQPMY,53593
scipy/optimize/cobyla.py,sha256=0zBa2apVL3bjSIZebIGcNc8mph8NxyxgpK9dhqbaxbc,642
scipy/optimize/cython_optimize.pxd,sha256=FhEeI0aq_Y2P00BpghHKOa7UjhW_uv4e7aEzXdS-QjU,453
scipy/optimize/cython_optimize/__init__.py,sha256=LEyUcZ_tUfRNMYgyFBYUsHfztui15TviJlLCsbZWrDw,5020
scipy/optimize/cython_optimize/_zeros.cp39-win_amd64.dll.a,sha256=MqC-kA-SOoTlgLLACsoNnHkMqt0udSbZ12yZrAj8CdA,1524
scipy/optimize/cython_optimize/_zeros.cp39-win_amd64.pyd,sha256=HDqYfHa_KtFYNth3j2S56jqtuwT0LsoSKf--wOZdYAo,94720
scipy/optimize/cython_optimize/_zeros.pxd,sha256=5o3CUJecdwYcTh6bX9c4O8F04MtmvvW9KluxtFsYDh4,1272
scipy/optimize/cython_optimize/c_zeros.pxd,sha256=kbSds0gdPDP4-oM6U2AaaFhsuEhCDXkzlfZ4BS82sHE,1144
scipy/optimize/lbfgsb.py,sha256=2VfUgbmMydioFy4UXnOZIuIhlb7vT9Vt-Y8G-OVsHw8,737
scipy/optimize/linesearch.py,sha256=BNAKRI9InccO4mPP7kZP9mPtl7xlEcxfI4d_Cskfq8Q,811
scipy/optimize/minpack.py,sha256=xxwF1UHbneXoJJ_F8tw25RA2zGIwRt0PJ_ctEhYcZDE,1106
scipy/optimize/minpack2.py,sha256=mIKiFAvS0cJ5as_BiqErPP_ey_htS3yGYtJfM3aFi0s,568
scipy/optimize/moduleTNC.py,sha256=0BZtj41NEa1nM2XYN9Df-kmG7W1QJG4PWcY40Zour-s,526
scipy/optimize/nonlin.py,sha256=PlyDnxrzMMX3fJrVFSYarqYO-Yv_jdwsQvGv0fRNltc,1257
scipy/optimize/optimize.py,sha256=b5JLY_axDmHbqDUjrYjm_krKlDv2azfOwEE5RQUarXk,1300
scipy/optimize/slsqp.py,sha256=F7lLTdRfavwHD53hcS39psok1Rly8yoYcgmgYisWsKQ,846
scipy/optimize/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/tests/test__basinhopping.py,sha256=OnRO7GaHTr1QffrCZc0CbXnrX2zzQMUoMpzsnkbt5hU,19422
scipy/optimize/tests/test__differential_evolution.py,sha256=hZYTzMwlrMh29tdY3U1l8h1PhKlCtzgGtX9cKN2Sznw,70521
scipy/optimize/tests/test__dual_annealing.py,sha256=IQq0e2n8dr3YidXZe4eMKazGLYP5C-FQgkoYmZo91hU,15552
scipy/optimize/tests/test__linprog_clean_inputs.py,sha256=8l7kHLqk5mqiRVkv8OLlMQpPXjNJOBWi-jkaWD5n2D0,11988
scipy/optimize/tests/test__numdiff.py,sha256=KBPAsPpz4DwxfbzfvVAgpYNqKWz9MGjlif--0k5RPYs,32166
scipy/optimize/tests/test__remove_redundancy.py,sha256=VC2Tz00TMHan0FJ8QXgKdvazARH58aWCLb97BQZyd08,7027
scipy/optimize/tests/test__root.py,sha256=qpL7_trYXSVfzBwsxIEfrSZNksLT_Ck-2QP1ouDYtzU,4334
scipy/optimize/tests/test__shgo.py,sha256=GGCGsrKD2AIidDMhG2KN5CJJ62HBlXGmc-Cj41anoGo,41450
scipy/optimize/tests/test__spectral.py,sha256=B3d_qUmY55_NiIrAljlKrk4kHDxP8iyb8PVAXFsx_U8,6890
scipy/optimize/tests/test_bracket.py,sha256=harzd2cGwRyZmhDWynM6t63Ol8oshYVOqNHIgOp4VBE,31359
scipy/optimize/tests/test_chandrupatla.py,sha256=jfXil-XrB1K1j03t6z23N_YRTkMMVUEPzmlFMkMzewk,30935
scipy/optimize/tests/test_cobyla.py,sha256=aDeGZw7EH9DCnnEp40FY-MFM4zk3R5NPprnyW3LTZ_E,5437
scipy/optimize/tests/test_constraint_conversion.py,sha256=ib0hzSBpHty30rMRHEug5ESN2PPXXHyujdwiCUDjnH4,12161
scipy/optimize/tests/test_constraints.py,sha256=cn-3BR5et1YJ4FSgvu8bVFsSmLeSqzoVDbcBlR6AvL8,9663
scipy/optimize/tests/test_cython_optimize.py,sha256=XmUrnJrxWKWfH5337FiCAfCV6djffjpQWVg1olG1vg0,2730
scipy/optimize/tests/test_differentiable_functions.py,sha256=h3F9dLx-18mV3iSOuGZPHHC0JV0nszMYlak6q7LLHmA,27777
scipy/optimize/tests/test_differentiate.py,sha256=z452w6AsOSxqtr2ykBrKXSomxOCVhNYXGCquSMQIy04,15926
scipy/optimize/tests/test_direct.py,sha256=_JopiGDv5bJeZuALeYyE5a9umkHw21Z-r7yfWbFtdtQ,13470
scipy/optimize/tests/test_hessian_update_strategy.py,sha256=Wff9cHabaF98onJS3pTR-nOre9uIKWcsjwQZ0k9eCPY,10323
scipy/optimize/tests/test_isotonic_regression.py,sha256=h81VacabdXRg0CXrxmm2Q6zBI100sxC89bKdScM9ouM,7171
scipy/optimize/tests/test_lbfgsb_hessinv.py,sha256=8_Bvj0kpB1mvK3fmh3v_vsYCrH8iFTnmaVjqe1EnSGA,1180
scipy/optimize/tests/test_lbfgsb_setulb.py,sha256=MBgku6a6vtpw8BWoGQNQygMhxYMhMV8vnbpJxXTL8RQ,3727
scipy/optimize/tests/test_least_squares.py,sha256=EsIjLhRNICefAvsODXlVI8x8xor0GT9APGfkDFJVmzY,34822
scipy/optimize/tests/test_linear_assignment.py,sha256=51pamAv4Kf0zrqIUkxk-8iFVF4-iZS6F8CxVH1h0J1A,4201
scipy/optimize/tests/test_linesearch.py,sha256=0y2A8OGW749xhQdw530qfH75GiJ5N-JmY4zKPbtW5-8,11230
scipy/optimize/tests/test_linprog.py,sha256=Ddt12X_dLsAowsJxnL5nASgLHtvLuQruGueu59Tye2M,99462
scipy/optimize/tests/test_lsq_common.py,sha256=AXdfzIlCO5gQa3gy7Ou9mp1SAHD8-qMP1yCyfK1kaI4,9797
scipy/optimize/tests/test_lsq_linear.py,sha256=mxw9ot2qoE8uHzCuJZ4FZ_eKYjlur7sG8smqXx-952o,11145
scipy/optimize/tests/test_milp.py,sha256=0vwenAWdg--VjEJbZrG_agTQARI5v6wXGdEoZvhfaoM,14938
scipy/optimize/tests/test_minimize_constrained.py,sha256=eX3VCEGQUOM8gPzkdQqYtkRSKWTgYXjpwVLrpB1YnVs,27328
scipy/optimize/tests/test_minpack.py,sha256=-HKM7kSmYuC54cXgF5bvPz1yho9mKWZSA3AMVpy_BBg,42440
scipy/optimize/tests/test_nnls.py,sha256=hbqSgthyvAPxhLHTIYYQOrLHNdn0XA5G0eRxcWDyzcQ,19512
scipy/optimize/tests/test_nonlin.py,sha256=YFtOa0TlziAW-xOqe5lxjw370FcNE_dVZ9b2OET-qb0,19027
scipy/optimize/tests/test_optimize.py,sha256=pMsJ161mIpJibFS6wlrfvKzT4gGaSRQLkTAq-oSG19Q,126535
scipy/optimize/tests/test_quadratic_assignment.py,sha256=C_jLzYepRtwE5iQVbsXJvuzQE6vFzLwSOGWfofP1LSc,16738
scipy/optimize/tests/test_regression.py,sha256=jc-uV89QavAhf34j8J2riNxYY9gIIgOddrRMV5ltC0Y,1117
scipy/optimize/tests/test_slsqp.py,sha256=VwGhdCImOwE3Xq2aTxB5AT4f1tNN4U_Nc0J94q78cdQ,23866
scipy/optimize/tests/test_tnc.py,sha256=XWL1j_u_9ZrkUBOfyFVytIAdD_dw5rRo-0w5IHx_7PM,13045
scipy/optimize/tests/test_trustregion.py,sha256=S57y-AFuek-24XWgUozVMLaoTGySNufU84-nZ8bo-6o,4813
scipy/optimize/tests/test_trustregion_exact.py,sha256=0rw6xflyPotyI0iiuJIucXjWunzS7WJdKDbat7zjvFc,13303
scipy/optimize/tests/test_trustregion_krylov.py,sha256=S9GEHBHe_59OEEbR16q0ukOWOGpk6FFqUyBKZDGPgHc,6805
scipy/optimize/tests/test_zeros.py,sha256=44NYWCf258SpbG4X17hbyvqx-hqCcOoUl0PnwZijptQ,36630
scipy/optimize/tnc.py,sha256=KF2pfcTx3eNBqx0xCABBjmomGmNdal9KrBKtmjaLgWY,964
scipy/optimize/zeros.py,sha256=ngRMAKNJ60Acb_zYjD4XPn9FKMizByjQeufD5wmlJRg,825
scipy/signal/__init__.py,sha256=fH2AZexuTrr3INWXnqhNzwRe4fpxr_qUUzzdXFxTSIo,14329
scipy/signal/_arraytools.py,sha256=W7k_0e3AW8Lz3RLPAmi4SXnJMge9N3y1-VZcvetm2jM,8558
scipy/signal/_bsplines.py,sha256=qhxiilrE-SS_ztGnI94eczUB9thXD3w9ZWWr6KZ7aDs,16250
scipy/signal/_czt.py,sha256=dhVmn1ScMOn8rzcvaou8n5s4ik8mlPH7BPsViaSA_so,20020
scipy/signal/_filter_design.py,sha256=4K34jq5hX0SC660lMoaEfZ9gk-SwZ_Th6T2jU2yHXNo,192226
scipy/signal/_fir_filter_design.py,sha256=YlhSw1siaJCLV0aRbVr1BC1C6dc51oL4afY7OjaI3jY,50682
scipy/signal/_lti_conversion.py,sha256=x_Ppo57VMk-Pvk3lKOuz2mGP934aH-H8Z8adZib8-lE,16675
scipy/signal/_ltisys.py,sha256=Z2oCb--KJTv10lgp-wBkQwS9tPvGi6UciDXXamS_nmg,119850
scipy/signal/_max_len_seq.py,sha256=oVUkL3qSfM1_FDWY_AcUI5fFi2vBQH0f1iXiDVOrIbE,5199
scipy/signal/_max_len_seq_inner.cp39-win_amd64.dll.a,sha256=tpq6ZmzcgPDKqHnZuV6ilHUpHOxF8ebrwBoe24vNFc8,1668
scipy/signal/_max_len_seq_inner.cp39-win_amd64.pyd,sha256=7zl6kEOpHzmsGyi1wvTfa5hTbtjMHt47qDEMOKf_LSE,1005568
scipy/signal/_peak_finding.py,sha256=g7-zEKNdR1CxtAPtP5snOpgSPQTQZag0Q1s-WcCsZZs,50204
scipy/signal/_peak_finding_utils.cp39-win_amd64.dll.a,sha256=TGSemBkJJfkQWLrcVABr13F1GgY08ii_zBBa6_4GPh8,1684
scipy/signal/_peak_finding_utils.cp39-win_amd64.pyd,sha256=GogHKXsLVyhOeERUv-nULhT2tKFwn8GGeunBdmzJl1g,274432
scipy/signal/_savitzky_golay.py,sha256=TA5zbmZeExTMJS2ORPe4BJ23PfS7NHKkoOTjRj552z8,13774
scipy/signal/_short_time_fft.py,sha256=3gUdJN9n0llTXqjU_ve-QA_0VApY9HX4azr6peDUDvE,75139
scipy/signal/_signaltools.py,sha256=fzJzR3ok19I7M95ZznQyaEaw_83mmbt5PxAfZWVXaNQ,162218
scipy/signal/_sigtools.cp39-win_amd64.dll.a,sha256=XPHImt2O3nVVCJLPPq0fW1-SN2DuRW35PtT-lX5IhZA,1564
scipy/signal/_sigtools.cp39-win_amd64.pyd,sha256=XcT5UyXY_QP4xI1itpxqkeSC28A_xLJ2aIuppChxl9o,123904
scipy/signal/_sosfilt.cp39-win_amd64.dll.a,sha256=2w951xE1WpYgPbx4HyjFZ08R0ynOx-vQOb_-MXRuYdk,1548
scipy/signal/_sosfilt.cp39-win_amd64.pyd,sha256=6tU2XnR7YNHhVxWOJzUHZDjxRGAA8hu5tQI8EWcXDBo,284672
scipy/signal/_spectral.cp39-win_amd64.dll.a,sha256=XUmAQhZ1Ki_uL3U_3yhJb4YMe0G-hbmo1aOWgZ-nIpg,1564
scipy/signal/_spectral.cp39-win_amd64.pyd,sha256=NMEukRlDKkLRC7L96VCUhI44EPxbR8EsCbvap7gh7jI,1009152
scipy/signal/_spectral_py.py,sha256=uZmMN52Fvw7tXuvzYgJDCt_ExNbWp7y0TImEkUhWS7g,80507
scipy/signal/_spline.cp39-win_amd64.dll.a,sha256=5ydUsn984ahEJ1VU8bOSYvrHqy6h_rwPaIjulQf-j2Y,1540
scipy/signal/_spline.cp39-win_amd64.pyd,sha256=COvX1Qf5xpKD3bGpd3a-ezzwDrihfGTuzHxTTwdEdug,84480
scipy/signal/_upfirdn.py,sha256=SZeLNVfPAOjmX6Bqjh0xvoUehjYcPFpJ3wwLwqf7RJg,8098
scipy/signal/_upfirdn_apply.cp39-win_amd64.dll.a,sha256=vqvRedabekaMPMTa71FCFKRuoL7NaNw2E7qxlyPTVgI,1620
scipy/signal/_upfirdn_apply.cp39-win_amd64.pyd,sha256=D2CT_We4FulZkLiIfu4DoJbmukEiGnoNW5mPMTB1Jww,367104
scipy/signal/_waveforms.py,sha256=SjUjL0zu-cOy1V-xsWxzz3tkwT7ycG4LXlJdQbiuKiY,21195
scipy/signal/_wavelets.py,sha256=Ed42rmbMTF1gDrkvvZj5foxEV73q6XhiijCHJHZ75js,16651
scipy/signal/bsplines.py,sha256=ZYfKCFSDRBTkPb6YO1VakXlYmt-MukMeAKj9mD6KCHE,761
scipy/signal/filter_design.py,sha256=_QriamQ5xlcALbbu99bhiOOH8YT5wE6jyV5VD_A4q3M,1505
scipy/signal/fir_filter_design.py,sha256=uPZDlO9HNEvqzqYJVedyqD93-DfTcPOjsVRQX9zwzwc,788
scipy/signal/lti_conversion.py,sha256=4QYYriDXwapouFyJ1bldIguKbxMlTX1rA4aZC_jt4v8,728
scipy/signal/ltisys.py,sha256=0OFS8lKTtd2IB126RnkFdMYZEdbHanc02SqXcWFNq4I,1176
scipy/signal/signaltools.py,sha256=aZUkwLC_U5lDup43svcw7fr3xMgHfJk1fd5sZFLzr2c,1208
scipy/signal/spectral.py,sha256=RzHRkz_0kOZaS9gHuyLgtoY-lDN6-50M42Vz3bO1HEU,745
scipy/signal/spline.py,sha256=4xylmC4JyNUioC_v_jsn4fdi2aFMVOy23Id14X8Q9wM,836
scipy/signal/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/signal/tests/_scipy_spectral_test_shim.py,sha256=mDThCaIAywm55MH8pqan8xUBhNlpYNSwXZpwunLp6K4,20483
scipy/signal/tests/mpsig.py,sha256=3cd_WPgz9dkjalruhXxxAPYCV8Kzs0L8CH4h-0W8jX4,3430
scipy/signal/tests/test_array_tools.py,sha256=GHjEyc4Q-O3PN9C9R1CCQ2Izm-LKGZhg5mX5gZ_FxUk,3743
scipy/signal/tests/test_bsplines.py,sha256=hJ5NvO8Zc1XPiBjrJKfPQ9O2l1W1Px3RFQizEh9uvA8,9041
scipy/signal/tests/test_cont2discrete.py,sha256=1vzkouv0wINTChU3pmQksPPbN9WQ3sbL7l0hahGnOnY,15021
scipy/signal/tests/test_czt.py,sha256=gSrHRP4imVuSnNrYkNrxvb4sdYnAFp6rRUMFr-4C6Qs,7212
scipy/signal/tests/test_dltisys.py,sha256=2ETM-Wwlgc-p6-Vbpu7-Mz1Vp6UK51M8qR2yvb70gOg,22156
scipy/signal/tests/test_filter_design.py,sha256=emqmQA_6KH9-nY-_thP1mxlATJKHn2FQJHmvEPaQQTg,198180
scipy/signal/tests/test_fir_filter_design.py,sha256=7-bPodaKeTsP3gEMXhIw96a-YrE2MgFV3rlpP4Yd6Sc,30638
scipy/signal/tests/test_ltisys.py,sha256=mEtvF61ENtxS6sfoe2ivNei-mXd-5dnT5iQULHRtQ-A,46429
scipy/signal/tests/test_max_len_seq.py,sha256=8caIaIvvSgV9zsQ8t2MnNBeOSBLqJajPISerank05Qo,3171
scipy/signal/tests/test_peak_finding.py,sha256=wGIxBw9M10mWvrYNtUTGal7HVocplloRf5qRIOuOV-c,34754
scipy/signal/tests/test_result_type.py,sha256=zWsEnxBFDAKmi9IhebffJbvjY9R2aO0kFtUm7skwAm8,1679
scipy/signal/tests/test_savitzky_golay.py,sha256=e7NiioSNnsP2SXYW6xc_ygBPLske5eDIjVf6GjUCIQo,12782
scipy/signal/tests/test_short_time_fft.py,sha256=1dGTYdXRAn3h_CJY7MucN_DovcDoKuPvSZcIRF3Syx4,35314
scipy/signal/tests/test_signaltools.py,sha256=ugLYkVC7EBNXFzadx17AmdhLiekSltwTEGqVNOUc1ZY,145138
scipy/signal/tests/test_spectral.py,sha256=UOfgZ8wRFrxrqNBLP13a8yUTXreYGtHK1zu3MS2EYlg,65488
scipy/signal/tests/test_upfirdn.py,sha256=qpJJpoo_Hl0yk7pkuDIAQd6Zgs7CIX5_jE6qhHNICJk,11527
scipy/signal/tests/test_waveforms.py,sha256=j80USvnR7ddMZZeqQ5PeiHbJ5m4E7qHG7QbVD5TxKA4,12326
scipy/signal/tests/test_wavelets.py,sha256=_RGwdTrq5bOJhbBJNTlrcP0HGF5H3utiNv_zTfr4edg,6882
scipy/signal/tests/test_windows.py,sha256=pYFLpJij_6QMdREBieWqCw9KcUORoquAtBgIRPYgfXo,41836
scipy/signal/waveforms.py,sha256=YRq5urkoch1Ds5UapLlAqRA3u3TUdUj1oLd0knhRXe4,693
scipy/signal/wavelets.py,sha256=k-Ic_dp3OXXwcay9lq91nIn1s3xLCBK-rGdAlfijrfE,632
scipy/signal/windows/__init__.py,sha256=G97xwCUcxrUmZO1pb3K_VfzlyUrzcJYpGjMN9UWWC2k,2171
scipy/signal/windows/_windows.py,sha256=LGrWA14Bumyj7RZUDRIy_tL1Rqw3nOBk0k4g7oueB7g,85981
scipy/signal/windows/windows.py,sha256=a5ffA3zEmDdcNUDYiQ8Lcen36nEXCixMQn2U4FBQ5kA,903
scipy/sparse/__init__.py,sha256=HLeZGtUqc2AlDSnRAgU0Od9xZ7gl8DSz4E6Be3e6VPc,9596
scipy/sparse/_base.py,sha256=r1H97l3S1zMk7iET3QCqRdwBcRstSpxpbxf6fYwc8o8,54125
scipy/sparse/_bsr.py,sha256=UgkAR3LWVh5i1frWWbXNd7MEpHHidGw7ZsqzqaCjM7E,31129
scipy/sparse/_compressed.py,sha256=Qtq7P1ruDsRjvTMw_IdaVucDM2Ut2NNJNUVHpX-NAcw,54724
scipy/sparse/_construct.py,sha256=gSeVjfNRBk9Q9Dwbqb8nYZpjYwW1hTWa56Rjz1lowrw,48580
scipy/sparse/_coo.py,sha256=uYB1KBXaJCFPqmZTNX7_oo-T0yyjNWYmA4dTrIPxu0Y,32619
scipy/sparse/_csc.py,sha256=FVIItYuIYfSp3MhmVfLSBx48BX2L6xvq7-ZccbNzoto,11421
scipy/sparse/_csparsetools.cp39-win_amd64.dll.a,sha256=qLfvbah0UdMEml_74cyBj4U2dxigFqx8ZY3uq98CNb4,1612
scipy/sparse/_csparsetools.cp39-win_amd64.pyd,sha256=PA-JVkLR1p0kgN9ju19rrxv6HrSVK0HNSaknq70eBXU,760832
scipy/sparse/_csr.py,sha256=sRu0sJxb_Z_xsExh7aTmx2_V74FR_TNYKnux2m2Wy-U,16166
scipy/sparse/_data.py,sha256=Nh2nIUJVsUIWXm3VcqsIbm7HFIhR9lraO0OQBLr47Zk,17725
scipy/sparse/_dia.py,sha256=FGYHUOBIBQLxNtvFIpPe7HWUt19QwoMLJMafTWHTA48,19470
scipy/sparse/_dok.py,sha256=JIIvy0PgXelOOgVR3rmKbw5pHNdforPxJCIlAEqjqsQ,22873
scipy/sparse/_extract.py,sha256=2Dsbnq2DpthHCZgPqeckpVoAi2z4eULH8s-sz_9tYPQ,5165
scipy/sparse/_index.py,sha256=Il5CqTi56jpP8XUJwV4Lod87c9jf0VsvPpb0momqCao,13671
scipy/sparse/_lil.py,sha256=hY1C8Cb75QtRTo3riYWUC1oRCk21QutoiHqMjCA9UUg,21246
scipy/sparse/_matrix.py,sha256=AENnr8pqeJM_-i4yCKP_ovO91m-LESoicbDkJQYmfPM,3194
scipy/sparse/_matrix_io.py,sha256=__pQRY6H9O6KHX7W0t76OK3Fih6kJ7vSYc5y57ZpnXk,6172
scipy/sparse/_sparsetools.cp39-win_amd64.dll.a,sha256=mOqyfI3P1Zmra5AL_-Xu9MKTCxoWgCh0u15VJjk9KZw,1596
scipy/sparse/_sparsetools.cp39-win_amd64.pyd,sha256=LjDcW7frvZ-9txz99FxQ-HjlzfoppIn6NPCz-7lmfgU,4189184
scipy/sparse/_spfuncs.py,sha256=vwBEj6xNGh1V17dONJX76wCAj9iFfmteGqh48DZ_vng,2063
scipy/sparse/_sputils.py,sha256=FrGmY1S_V68HH8C8KJGLf8BquMuRBUFvri66HBNjkpg,14996
scipy/sparse/base.py,sha256=JL80rDo0TwYloUa1n4tyLRxb_e6lp2ermCU4f0YkG4k,824
scipy/sparse/bsr.py,sha256=QSzbgv-z06WX8-uPsUnkow_tI2SKMjzbaIp_EWXLmvU,847
scipy/sparse/compressed.py,sha256=wx4UDp1GEYdVvdxZg7HdnAVhCYhfWb1Sg4zZICB9rbo,1052
scipy/sparse/construct.py,sha256=expKWVHfVSTs_809lRGCyJM1zm9urS2cECQdPiqmgAo,969
scipy/sparse/coo.py,sha256=irEGMbdj__vcKOQOcqARXS-exo17SFl7cplcmOOQ1qc,881
scipy/sparse/csc.py,sha256=2zsFtNCw5agBC2g8U-AUtB_PbQfVrPgPhzUSQ2bpA_A,634
scipy/sparse/csgraph/__init__.py,sha256=qDn3IX1VAWjclSfhoY0hKIZGJ_SNheENl-LUO2YIlqw,7961
scipy/sparse/csgraph/_flow.cp39-win_amd64.dll.a,sha256=-cx1r9gLbFsovPQjyzhPEswjUOkqRnXiqN2L6uotFZY,1516
scipy/sparse/csgraph/_flow.cp39-win_amd64.pyd,sha256=h3ALeC21x0uMYasZlDpY87u-C6Ve0gPp6mxxDghTVn8,308224
scipy/sparse/csgraph/_laplacian.py,sha256=5imF-yW1747hr0lB_xiC7B2b23HkP-VXJxJ44jLDfko,18771
scipy/sparse/csgraph/_matching.cp39-win_amd64.dll.a,sha256=iavHzk1z5WGDW_3OPXb3_XgY3m1s_xkBHkfprZ03fVo,1564
scipy/sparse/csgraph/_matching.cp39-win_amd64.pyd,sha256=Xu5w5BYNslaSotuqAeYco8E51qEWb7YaztFtpKCSCSI,317440
scipy/sparse/csgraph/_min_spanning_tree.cp39-win_amd64.dll.a,sha256=VnQJIImmavOO9XszAWhaO4G9WvGRClzLKqUtELhzIe4,1668
scipy/sparse/csgraph/_min_spanning_tree.cp39-win_amd64.pyd,sha256=AY_wNojeSeENhz834HGBdpCo3AKyNQCRvktfFD_AsaU,238080
scipy/sparse/csgraph/_reordering.cp39-win_amd64.dll.a,sha256=pv2nhOEezuE9airR-qPs3x9eEWNADrYAzZs6N3wxfJo,1588
scipy/sparse/csgraph/_reordering.cp39-win_amd64.pyd,sha256=E4ExZGoC-kYZIvjBUKlY5DcSPnZ5kQs9g97neNdS_AY,304640
scipy/sparse/csgraph/_shortest_path.cp39-win_amd64.dll.a,sha256=0PFi4Zs4BwJSwDFfktpqElUjKoZPChyUMDDIFLfLf4s,1620
scipy/sparse/csgraph/_shortest_path.cp39-win_amd64.pyd,sha256=NvOJav7ZYSKzE8A8cPHNXPB5OX1cW6lWpMgwG4-dapA,452608
scipy/sparse/csgraph/_tools.cp39-win_amd64.dll.a,sha256=5vjyRbh_T-xkFB7GOYY8eGAI4z42qOpI6_U0-l3sZKU,1524
scipy/sparse/csgraph/_tools.cp39-win_amd64.pyd,sha256=SPvrlD2PdmFY41TVN7nds8CfkX4h2aEI-r5CHavyNH4,177152
scipy/sparse/csgraph/_traversal.cp39-win_amd64.dll.a,sha256=5-pznpYlFJWmQG2hJxzOhcNa0BwwD7G65cdHnA-CxbE,1572
scipy/sparse/csgraph/_traversal.cp39-win_amd64.pyd,sha256=KLNIxljxmvG2TZHcipHwof2TVB7VlDpFCTnmsBUwVIM,624640
scipy/sparse/csgraph/_validation.py,sha256=fmwYnUz1dqo9ronNI3g8RtjxBALgW-6z77cONv8c-hQ,2537
scipy/sparse/csgraph/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/csgraph/tests/test_connected_components.py,sha256=HiW9gS_ttLPBl9XvdVCRZGRKfWdoiW4PsmVTqp1Q-kQ,4067
scipy/sparse/csgraph/tests/test_conversions.py,sha256=s0xD-47azJTCrYl3u1Gb7A6k2frjRnYYKXyomPAH2ug,1917
scipy/sparse/csgraph/tests/test_flow.py,sha256=FDRd335RogmRkkRtH_wUwylXXYr-3WUFwqvktzHLWlM,7621
scipy/sparse/csgraph/tests/test_graph_laplacian.py,sha256=voWlNpjqyE7ksNiPV4abXv-4ZCkn8bdCc2PRAwO52ew,11359
scipy/sparse/csgraph/tests/test_matching.py,sha256=GEoiMXISJlDsyVfWR0LS2fH1YkOpSJzyiyuZWcEbwMk,12238
scipy/sparse/csgraph/tests/test_pydata_sparse.py,sha256=klNb8q050ogW-Sw_ROJt3Ujd8pajnPTFIz34SNOYYJ0,3750
scipy/sparse/csgraph/tests/test_reordering.py,sha256=4YgPFLLffko_c_k1QpqUd2NvXAwIsVbsECnIFNuVHmA,2683
scipy/sparse/csgraph/tests/test_shortest_path.py,sha256=0QopCD2rVQU1olqq_I1YNvBlfRC717LZta_pGOUg16M,14836
scipy/sparse/csgraph/tests/test_spanning_tree.py,sha256=HfH-MvJm86KJoeIKAyuhgkzqU5VxvJEamusUAjPeiOg,2234
scipy/sparse/csgraph/tests/test_traversal.py,sha256=6kHqs9jJSzi5qrBGkP5A7LenAI646d-eg1ZoN5eobgU,2921
scipy/sparse/csr.py,sha256=pVwLe3hgplNxZCjDixenMe2_UhHwHnNm_uNTb8X1Ytw,685
scipy/sparse/data.py,sha256=jBxVtluwVoejuNTf-QYgAYw3DdQpJiXM4WfgMUd_SV0,596
scipy/sparse/dia.py,sha256=FI2MAFv_1H0zFa6rrjdE-UZC-TBilpKVdGAspwpL0yY,718
scipy/sparse/dok.py,sha256=GdVpJOh-A6vtT3hR-73ziDU1-V0PFdPVUAfqeTTvUTo,765
scipy/sparse/extract.py,sha256=tcwXkf3fvxKg4PotXMrvrjeMzCgqvUQBbDBzrKdLEJo,590
scipy/sparse/lil.py,sha256=-8OevezbOf1Car1z9mjlfaBOQactS4wWQtEmzIPDBtE,765
scipy/sparse/linalg/__init__.py,sha256=Tkrzqd0JaFrVJLC1CtQOmIQC37_ceXPTrTQDKCa4xOg,4145
scipy/sparse/linalg/_dsolve/__init__.py,sha256=kacCyi9C3cT-m5LKPk8RPUhbjPToaL1dZ9V-UEbJD0c,2062
scipy/sparse/linalg/_dsolve/_add_newdocs.py,sha256=4NQF7CmyoMBGK9Z_QwULJjgdwiugSyIJXP7Dv-U0XQ0,4065
scipy/sparse/linalg/_dsolve/_superlu.cp39-win_amd64.dll.a,sha256=vchGMObTNbGl9ZAUBADz2UxUHmLXt-cdqQwPGb00Bis,1548
scipy/sparse/linalg/_dsolve/_superlu.cp39-win_amd64.pyd,sha256=gAz7LPo7JjiUggGI1RMeN-6XzJ0dgq33b1_WacDH1Ng,412160
scipy/sparse/linalg/_dsolve/linsolve.py,sha256=xAo02lX8un8dEQAmebAjtyP0zKKF1uGp_VuXuQuOIJw,27232
scipy/sparse/linalg/_dsolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_dsolve/tests/test_linsolve.py,sha256=aRc3Hvcq0oKLY38n2KWd0kjpM1bdaZ_vHh29Al6TGaU,28589
scipy/sparse/linalg/_eigen/__init__.py,sha256=013F3u6pMe0J0wKjMFq_a1jSuO-pwS_h_XzmJI3xfMM,482
scipy/sparse/linalg/_eigen/_svds.py,sha256=IpdPMgpB0-WLqOxPad4Q6060-m_W2IRQ5TzknRNwKCA,20784
scipy/sparse/linalg/_eigen/_svds_doc.py,sha256=EoiLm60_nxfHhizy2cG6N93fs5V5g8GV_j-ivhf5Cdk,16005
scipy/sparse/linalg/_eigen/arpack/COPYING,sha256=_LPGx94UYM99CGPDxZluUY64AVouztNpEfPaF4RAs98,1937
scipy/sparse/linalg/_eigen/arpack/__init__.py,sha256=EU0vXuTlZzMcXxCITUTVxUZ_N0EgcDF06Q2rMzy0Q3o,582
scipy/sparse/linalg/_eigen/arpack/_arpack.cp39-win_amd64.dll.a,sha256=leuIQjGESPIGNd4pWLhFqfKO6E8Oweq2iMRVgAc5azc,1540
scipy/sparse/linalg/_eigen/arpack/_arpack.cp39-win_amd64.pyd,sha256=oW6z5fUOw7ZWIg7pySunPM0KAs1Y2BMHMGh_WU1xtAY,799744
scipy/sparse/linalg/_eigen/arpack/arpack.py,sha256=_8BUYPXO5lpi_H7XxDmO87SZYjfV4MQqiDV7DIhacWI,69103
scipy/sparse/linalg/_eigen/arpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/arpack/tests/test_arpack.py,sha256=Rlm-Fm25WDegCn422wcGCXwx909okcR2cQSVOJYBvHI,24468
scipy/sparse/linalg/_eigen/lobpcg/__init__.py,sha256=8aw6542gPHNriFRBENTZ5rb1M3cqJKToG--paaqpaxM,436
scipy/sparse/linalg/_eigen/lobpcg/lobpcg.py,sha256=qSqRfT2i7aqbVePMJL83Gmovql5s5sfzyDGYzctYVww,43017
scipy/sparse/linalg/_eigen/lobpcg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/lobpcg/tests/test_lobpcg.py,sha256=WABPdYlKlUhRHWVIo7Q2RGA-3wwD_9FUywwuyLdrZA8,24458
scipy/sparse/linalg/_eigen/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/tests/test_svds.py,sha256=g10o3WNw2ycEglgfCdtatiMHngxGj-ODiz0MRSKDEVM,37030
scipy/sparse/linalg/_expm_multiply.py,sha256=eDxFOfSuievrru-l7ktsp5aJqtOatvpqUMac1rowIsg,27122
scipy/sparse/linalg/_interface.py,sha256=ZL3f_IP4pUi7FGRhf6rbkNQeH1xLH3c1Iyc9oZ1ZEZQ,28875
scipy/sparse/linalg/_isolve/__init__.py,sha256=rFcoMYM70Tbtjlc3xhWhgjdNHUOwc4TSV1sOFZwL6Gw,499
scipy/sparse/linalg/_isolve/_gcrotmk.py,sha256=UcnjruHTMrTeSYRF5h_RBj_1OoMtJkCBE_t4e2qxN6g,16726
scipy/sparse/linalg/_isolve/iterative.py,sha256=Ty0g_m7dUPQxJQ1OrENv2qYUUekOasCbAwlV-cXEeWc,36847
scipy/sparse/linalg/_isolve/lgmres.py,sha256=JoXIngYHL-zjxpRsGUn14zMEQUtnrKicV4WObazOOQk,9401
scipy/sparse/linalg/_isolve/lsmr.py,sha256=-TOkfv2dcjZYhk0C0zI7OK3mkV0ytSFil75ngO3t4aI,16139
scipy/sparse/linalg/_isolve/lsqr.py,sha256=6pld_K09HE1YPVYCSMEWfWynJoUR2cqnkHHB1-6hngQ,21801
scipy/sparse/linalg/_isolve/minres.py,sha256=f7iMkQ1kj95OC5CkO6DQO5w_Zk2G61rzrGbZsFJuufg,11998
scipy/sparse/linalg/_isolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_isolve/tests/test_gcrotmk.py,sha256=utTSPmJ1OZPN_qEeCezs9FWSAYsh_P1ae53JYAn-3x4,5578
scipy/sparse/linalg/_isolve/tests/test_iterative.py,sha256=zx65IlDAb2g-VVqJh_5l2qSS8Y4EV5msKHKAemBlTK0,26422
scipy/sparse/linalg/_isolve/tests/test_lgmres.py,sha256=JpvwkFXdDyTF5bmQamomfllVJVdEqgMW4RHRfyC4EHs,7275
scipy/sparse/linalg/_isolve/tests/test_lsmr.py,sha256=H6BTOYSAtri-W0OzpJNGDYqIdliXB4egC1qrnDsGaT8,6550
scipy/sparse/linalg/_isolve/tests/test_lsqr.py,sha256=KiB1ndwvfJ1oRE1NezjiQAifBVP8uZIvyhY109P2m0c,3874
scipy/sparse/linalg/_isolve/tests/test_minres.py,sha256=QgErAFy1NTZDB_IjcKx0TZcpH7zmqTCRpt67h3DSg64,2532
scipy/sparse/linalg/_isolve/tests/test_utils.py,sha256=W_ERpPV4ZfxThvgBHxuyhiTBmmfSbQKFFZaSK25mGBg,274
scipy/sparse/linalg/_isolve/tfqmr.py,sha256=TZnfW_I-Kn6jO_uVjB4hcf399eSLwW5p0eTWv9JaMxI,6880
scipy/sparse/linalg/_isolve/utils.py,sha256=LjktcTFezfc_dtnhxbzOfGB-TZhsjTxx_CDVLDA4MZE,3725
scipy/sparse/linalg/_matfuncs.py,sha256=ocotsbEuxi-0o6ZagJq_7iC5UFJexqDVA-dS9mAYRRA,30325
scipy/sparse/linalg/_norm.py,sha256=ORHvQ7Pz-idLk-htCVSvAvlkdJG-ykKKXa-voelRr9A,6260
scipy/sparse/linalg/_onenormest.py,sha256=Ep8GIsh0rx7TlL2azhWfCm_vm6DK5zVtSljwMt2WISE,15953
scipy/sparse/linalg/_propack/_cpropack.cp39-win_amd64.dll.a,sha256=MLv3Kfo_vj5KnybfFaG8t8JVD5xAljr4vl8RIrXB0-8,1564
scipy/sparse/linalg/_propack/_cpropack.cp39-win_amd64.pyd,sha256=bwUSc_Xuy36e7ZyhgGvk4_XCoqtDTCO-oLpI32T3yfg,479232
scipy/sparse/linalg/_propack/_dpropack.cp39-win_amd64.dll.a,sha256=IDd-HECUjQDEuE4i2jPnKUjOJeYqLIWHeoXgJY9XZMI,1564
scipy/sparse/linalg/_propack/_dpropack.cp39-win_amd64.pyd,sha256=JT3_gkdVwn_gVPQgBSGwkUJfqWtxEEKomQUEUNXx1SY,448512
scipy/sparse/linalg/_propack/_spropack.cp39-win_amd64.dll.a,sha256=UqVbk4GnZJVKPhHYml7l8HBRfMe01EheDYTjTNpseVI,1564
scipy/sparse/linalg/_propack/_spropack.cp39-win_amd64.pyd,sha256=xdN1HaFL9REZH04hR2o9fI5cMTsrFbRSFO64Pq5va6U,450048
scipy/sparse/linalg/_propack/_zpropack.cp39-win_amd64.dll.a,sha256=3Bc80oRFLDNhEo_r_cS2X6p8Ltw7du8Sc2Ve6m5XZzk,1564
scipy/sparse/linalg/_propack/_zpropack.cp39-win_amd64.pyd,sha256=Euartug2JmGj0Be60YrnCHRuTgMWo2DMGwdSDl3BKXU,469504
scipy/sparse/linalg/_special_sparse_arrays.py,sha256=Sg9dmMxm-2_U2C9pF-KeDQSiKjcLXleOn24UzfiqXXs,35246
scipy/sparse/linalg/_svdp.py,sha256=RGMIJrf3u9tiO0HiybGkXovLxVooVa0U25QkImuM6Zk,11730
scipy/sparse/linalg/dsolve.py,sha256=sfGJEXQ4lzZNi1XrpxrUnv9H2HyYbd_MC8TrCSqhyho,721
scipy/sparse/linalg/eigen.py,sha256=eVsVKh6PJv5yToPAZrBGkbXafLuVsZPTeqe60YAj0LQ,687
scipy/sparse/linalg/interface.py,sha256=19D-A1Fpnj_6RFMfIlzXOc9FFRVIZ0bja_QRuC9CLas,704
scipy/sparse/linalg/isolve.py,sha256=_UyMkdBJ0PI_bT1A9s_wUekhlFp8u1teot66-TpMQhc,693
scipy/sparse/linalg/matfuncs.py,sha256=lhuLmWkMNKBHA2xuNVanK43R2aMsLZJcos4BQhhr9Qg,719
scipy/sparse/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/tests/propack_test_data.npz,sha256=v-NNmpI1Pgj0APODcTblU6jpHUQRhpE9ObWb-KYnu6M,600350
scipy/sparse/linalg/tests/test_expm_multiply.py,sha256=looGI5_o6CTlX5WnohlnsW_qPIofdrmpVl75yVUqLvI,14301
scipy/sparse/linalg/tests/test_interface.py,sha256=AoiAmDNBot5HDtyTWN9Ymjx70MuJtUVZ0av4rvWglw8,18434
scipy/sparse/linalg/tests/test_matfuncs.py,sha256=lPvG9v9QpSB9hQedbNpXYxIM_gIHs2IH3LYRwF1e0-8,22483
scipy/sparse/linalg/tests/test_norm.py,sha256=ZuZ0yjU4U5XmkGkWJLPFTdAwF0T-sNUfaHovTuuXcUI,6304
scipy/sparse/linalg/tests/test_onenormest.py,sha256=_1CbXJgWFL-72PYjtlheU27jd5xGX90_Vki834yZ6qk,9479
scipy/sparse/linalg/tests/test_propack.py,sha256=hmtmvvH34DpFxGvV2LO9mhLVMb1ea8k5NLJUOBhl8p0,5704
scipy/sparse/linalg/tests/test_pydata_sparse.py,sha256=DMlHQ-uhYiTaz5uV7sYlRjhRLThM9qHs9ItNCGtJOaE,6488
scipy/sparse/linalg/tests/test_special_sparse_arrays.py,sha256=a3CKlj_vI_A45sZgx-36usRCswvQNeutrX0hRK9bkzk,13191
scipy/sparse/sparsetools.py,sha256=gPjiZqKCFLDgY68WW59SOMVYPjE1CCzARK3QSywCNwE,2266
scipy/sparse/spfuncs.py,sha256=wWCn33GmSfnR6aHu3L2eTFhbIcOXLaI59PU6gtQpMeM,604
scipy/sparse/sputils.py,sha256=hMvU6alEf5kux3-vEVVw4e8ssiEhhzU9QgvoGPvFROs,1017
scipy/sparse/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/tests/data/csc_py2.npz,sha256=usJ_Gj6x_dEC2uObfdYc6D6C8JY4jjROFChQcZhNAfo,846
scipy/sparse/tests/data/csc_py3.npz,sha256=axuEMVxwd0F-cgUS0IalpiF8KHW4GNJ3BK6bcjfGnf4,851
scipy/sparse/tests/test_array_api.py,sha256=3Drk4W7T0EI4dUFTyNmnhinMLAEX_1j0yA5LTPbBw-Y,15026
scipy/sparse/tests/test_base.py,sha256=QeZw-jV47AwA5my0fG_ZCbuDbLjbSF8CASZknjeAx9Q,196250
scipy/sparse/tests/test_common1d.py,sha256=NdTGzkYCNEPNj31v_8ANFu5NCBW_6jiDINdAzRrPVL0,16598
scipy/sparse/tests/test_construct.py,sha256=OXH40umC6HLXRhki9s1MKSKCSbIo2Tg_VasI49vJAqM,34178
scipy/sparse/tests/test_coo.py,sha256=BSWEDrVnpRjAHfPliskM-6Q9gsFBm3J8DZ0e3P3lSIY,8751
scipy/sparse/tests/test_csc.py,sha256=ak1_Ka7itovqPa9NltnRgnMx_yvjNNw6sFsPaISZZyU,3056
scipy/sparse/tests/test_csr.py,sha256=lO-8VvYFr5Q9RxaoxsCpwn_5QXJXo4HrNScDMlQOV44,6671
scipy/sparse/tests/test_deprecations.py,sha256=ZYhEgae-c5I6rXdeXVGOvOdJtK4SwdHEmIacluFCxlk,676
scipy/sparse/tests/test_dok.py,sha256=qZSf_xppksWXr37XimNxqzeR8d_xs2lMiblPpy-gloA,6258
scipy/sparse/tests/test_extract.py,sha256=NJIEcflkpb-k0llvH_6uJMAoaysWliWyi4bRjl0NhN8,1736
scipy/sparse/tests/test_matrix_io.py,sha256=7SOFHH8giia5Xdm5QmNuu1Sr5rLYtGF916iQYPqIsJU,3414
scipy/sparse/tests/test_minmax1d.py,sha256=1fhijO10dBMvR2fcwuUuCYkdXLoUhC-3AfEGC_mvjF4,2457
scipy/sparse/tests/test_sparsetools.py,sha256=5qjHLoIkDjWyiSTJQ0aXK9srBn9ysK2rJkGny5o1FZs,10882
scipy/sparse/tests/test_spfuncs.py,sha256=jUPhJjGat0YjcEeZWrD6Iz2G3el0ubAyts4ezvbrCM8,3355
scipy/sparse/tests/test_sputils.py,sha256=ddNCfkxAd75AnzYQNFCqfNY3yvNWEWLJD4II2T8YOqo,7482
scipy/spatial/__init__.py,sha256=R-KvSAgfE2qCcRt2wtRTFMlkdaoniXqXloDLaayMyLs,3826
scipy/spatial/_ckdtree.cp39-win_amd64.dll.a,sha256=c1MwqUGoSCfowhW6je0TXKwBP5mcgTg_E63rervJAZA,1548
scipy/spatial/_ckdtree.cp39-win_amd64.pyd,sha256=IhXGKl51M1EHBoH9ohlErMD2UMZRLdwGaYjnO7fv6mA,1701888
scipy/spatial/_ckdtree.pyi,sha256=GGsYjAasPsHNWYhkkRfe08fP9mtp1e5kFO9ZM2SS-Ls,6106
scipy/spatial/_distance_pybind.cp39-win_amd64.dll.a,sha256=333pPDiNMKKp_NrBmK8rUqpyBEMcHaTUELkRFOE8Tic,1644
scipy/spatial/_distance_pybind.cp39-win_amd64.pyd,sha256=e9xlfHNXEQ10l3mZvOKLBr3Mfe32dTOdVGLsAwxtoKw,1372160
scipy/spatial/_distance_wrap.cp39-win_amd64.dll.a,sha256=YNmBxLhz5vLm6-ZbquTIu00iCf2TeyC2RFHjyXLIqg8,1620
scipy/spatial/_distance_wrap.cp39-win_amd64.pyd,sha256=j3U8Q8coshkeyoaNvR5daanZflqKmsmCU6KYHIZP118,111104
scipy/spatial/_geometric_slerp.py,sha256=BYxiz6U5lSov5Y238WxGbf51yEycmM3HjGgGvOhARVI,8221
scipy/spatial/_hausdorff.cp39-win_amd64.dll.a,sha256=FKvWqOIQxFv3WUd5qf7hZI6SAowHm8euA_HE46x24dI,1572
scipy/spatial/_hausdorff.cp39-win_amd64.pyd,sha256=q9bPtRh7B6Z9tbq5UyX_kEuwhsOrkPCm7CTPI8QJ69Q,228864
scipy/spatial/_kdtree.py,sha256=zVE9nZP-rDaMFfSMiATExFwGHz7TMYmhuI_xQwX933w,34363
scipy/spatial/_plotutils.py,sha256=DPj2t9Jrs-ujsAeT9IxpxEAPbny1QNfr-GG9rvPNGlg,7529
scipy/spatial/_procrustes.py,sha256=5k0L3ausfrPyOjQFckcGa9_2BNxZp7E-6R7EY6jNCLE,4561
scipy/spatial/_qhull.cp39-win_amd64.dll.a,sha256=Rqfl1Otjw5p_q73SXjDGyI9x0G2t8TjZkuzY19VUfMs,1524
scipy/spatial/_qhull.cp39-win_amd64.pyd,sha256=-ZPKySgjYUIAA9D4yJwsQ7ULQ9nReOiYUvHkznkPdPI,1076736
scipy/spatial/_qhull.pyi,sha256=L06jd8pQcSB-DcRMJe8wq0XvlIEzuOrZQAffq4zD7xU,6182
scipy/spatial/_spherical_voronoi.py,sha256=4qbm4MAsoy-DcllT4erfjLgFLGVQL0X8iBKpZJKUaNE,13880
scipy/spatial/_voronoi.cp39-win_amd64.dll.a,sha256=NTkuQa1sK67gLtxkgrZ9GpFTuW2Uwat2K_YyxM5joRc,1548
scipy/spatial/_voronoi.cp39-win_amd64.pyd,sha256=zTFay_62SWOEk-ubAtYN8lsNW1VCZUvTrrED3Gr5CPg,220160
scipy/spatial/_voronoi.pyi,sha256=gaEmdjWgHeIA9-D6tYXwimJCppJzgc6yc-sfLju9Vyc,130
scipy/spatial/ckdtree.py,sha256=6GA2m3VQ1dSnG-51Q6LuQLS7yw8JZhMpB4uKALy_jAU,672
scipy/spatial/distance.py,sha256=FqDt0LwIRoVBna1hClg2R2-BM1Sg7pXIsWCyklPKroE,94476
scipy/spatial/distance.pyi,sha256=AvjPPnJxnoDRusxs6Mh-UgAdqLW3yJtdje7lZelWSk0,5484
scipy/spatial/kdtree.py,sha256=O9wDDCyL7bOMbhWZxzGPKZe33h7UEdStW779J089t5g,681
scipy/spatial/qhull.py,sha256=10n1eDcF9qrUmGpqrEb8sLjw_cXfSSgT_WU0-z4ucU4,647
scipy/spatial/qhull_src/COPYING.txt,sha256=liRS5zfffHQ6PcJ0QjIHECi4wEUOdnSlUFfDMOoZd-s,1673
scipy/spatial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/tests/data/cdist-X1.txt,sha256=LTxfJD5Hzxi0mmMzhzCgp0dUJflrg6O5duj8LVxa9lA,5760
scipy/spatial/tests/data/cdist-X2.txt,sha256=Tb2q8iKbPvUlxO_n1pBfYkWvu1z_LuBcwFXCIRZgBOg,11520
scipy/spatial/tests/data/degenerate_pointset.npz,sha256=BIq8Hd2SS_LU0fIWAVVS7ZQx-emVRvvzgnaO2lh4gXU,22548
scipy/spatial/tests/data/iris.txt,sha256=sWic54AuDE3IyLk6XOw0BcyCSzr5BkXrpDJc7B00OTQ,15150
scipy/spatial/tests/data/pdist-boolean-inp.txt,sha256=D6kMC8OK5H2Z6G07l592VtEENnehwy8wuM1rjVq7wXU,50020
scipy/spatial/tests/data/pdist-chebyshev-ml-iris.txt,sha256=Ysw196_xQ_ApwKiaB7OXZ-gVvhvhFH8wUZAw7EfacTs,178802
scipy/spatial/tests/data/pdist-chebyshev-ml.txt,sha256=jtPmZwg5AfpIom-wMjty8_6tI69ePUnJ7eYdFyFq9G4,3042
scipy/spatial/tests/data/pdist-cityblock-ml-iris.txt,sha256=Wf3dVmhan8EEYiKAsrgN5WNMmAmH9v9NjJpO4xRdmPE,178802
scipy/spatial/tests/data/pdist-cityblock-ml.txt,sha256=aMGSV28cjKIV2CRfdR2zZsisln6oxi1tYQbxZ9tuMMo,3042
scipy/spatial/tests/data/pdist-correlation-ml-iris.txt,sha256=PdVB_30O5CHL9ms8KRB1kFiswp9Sed8CV540WQvNaJc,178802
scipy/spatial/tests/data/pdist-correlation-ml.txt,sha256=VDgXqx3vUn26r1EZFcSk-rq1-wef2WVFcoBWLG8zvro,3042
scipy/spatial/tests/data/pdist-cosine-ml-iris.txt,sha256=q5nrvDyyqdltrjBRyT5eFVQIW-y2Igb2JdC8tBAXl7E,178802
scipy/spatial/tests/data/pdist-cosine-ml.txt,sha256=nLK5yDtcmU-svoYeLPuZZoolZpxmpOaeLswLLR5QoGM,3042
scipy/spatial/tests/data/pdist-double-inp.txt,sha256=2UzMIvz_r7ZT2Twt-jH-pst0Avue4ueGZezk7T2_6q0,50020
scipy/spatial/tests/data/pdist-euclidean-ml-iris.txt,sha256=VWMmzL2jP_vhDzTBhYtZH7-VBKJFpafRROrefDeokWI,178802
scipy/spatial/tests/data/pdist-euclidean-ml.txt,sha256=Kn8vHq17IbWtj1G9XzCFY1EuXFTPnD7gX9FwW2KSm44,3042
scipy/spatial/tests/data/pdist-hamming-ml.txt,sha256=OqjHwrqGBwmwsfTeN2fI1EFiWSpOA6ZlZYwN121Ys1o,3042
scipy/spatial/tests/data/pdist-jaccard-ml.txt,sha256=nj_JYQ6bfvhlFDKu5OC_zBeOzPKgewkmpxScJEPAzhs,3042
scipy/spatial/tests/data/pdist-jensenshannon-ml-iris.txt,sha256=wz1O-EHZT2cDwFtow4ZoLWQgS8WY6DPzb_Ik9SXA8Fs,172739
scipy/spatial/tests/data/pdist-jensenshannon-ml.txt,sha256=rps5AwUK4Up9qCR3wAE1wfwuKE5ftKZTHChPYH0hsfA,2819
scipy/spatial/tests/data/pdist-minkowski-3.2-ml-iris.txt,sha256=rfWuQ4RgSfY0ko_jqlSTjQIJYGCHeEbEwjHYpAC9Ivo,178802
scipy/spatial/tests/data/pdist-minkowski-3.2-ml.txt,sha256=jKlA87ldGof8ndZLkV9l97-Tsh2uXTIlyZRsMoWr0gA,3042
scipy/spatial/tests/data/pdist-minkowski-5.8-ml-iris.txt,sha256=bWYNT7XqpLTGkQBMfnCFJ7bnoxooZlzfI0_O54UOquI,178802
scipy/spatial/tests/data/pdist-seuclidean-ml-iris.txt,sha256=bGIB7ygJC8CGHAoVKtwOdPgAqDM5bzffpgxYMTCKgmQ,178802
scipy/spatial/tests/data/pdist-seuclidean-ml.txt,sha256=g9-qvSA4qe4S0xPxnTa17zqa4Z-P5S0TV1gAsbr-RAs,3042
scipy/spatial/tests/data/pdist-spearman-ml.txt,sha256=vpgBfMlrUUH-nYlnLqm_Ms-MIjrgOlNCW-TW8C4Yan0,3042
scipy/spatial/tests/data/random-bool-data.txt,sha256=sydRG9aL6CH9i7-Nt5X2mNyQDsu9OC1fbUD-MRdt5bc,6100
scipy/spatial/tests/data/random-double-data.txt,sha256=OcmsKJSbi_eY65ld50iAatodSzw2NxFfDJIDHx0lOpQ,75100
scipy/spatial/tests/data/random-int-data.txt,sha256=saHYkK0CMRzV78RV18tUGIF4uKQlOqkN4cEWqD4gnFw,10366
scipy/spatial/tests/data/random-uint-data.txt,sha256=aBbEt1tldXIvAvkDMbSr9WddkxXtMTa05Qq8j4s55CE,8811
scipy/spatial/tests/data/selfdual-4d-polytope.txt,sha256=x-c_sDM8alTwdaj7vYvGAQEsjrg6n5LycGrrPR4dlY0,507
scipy/spatial/tests/test__plotutils.py,sha256=n9OCDDgipsHJy2le-P-8fcS6PD8IQ33zmiz2ZAzbslY,3905
scipy/spatial/tests/test__procrustes.py,sha256=iZypw3jevzOPiHv15UZHwc3sXE3pNlUlCeKTZfoL5vE,5090
scipy/spatial/tests/test_distance.py,sha256=EmXa5NaV7a7r0oI8kLhBrJEPgriSq_H3BCi7abcn__0,86420
scipy/spatial/tests/test_hausdorff.py,sha256=7ZZJjse3SoM8wYx_roN7d2nYIJwHAh_QbXD2ZqZxEjE,7286
scipy/spatial/tests/test_kdtree.py,sha256=U66JmJchwzH3vRrmEFnwVne9yNKpkAviAK6SCjConIA,50803
scipy/spatial/tests/test_qhull.py,sha256=WKLjGBhbVavLIG3qfLarTImGST2nnKkhAx4BWKrvecM,45448
scipy/spatial/tests/test_slerp.py,sha256=DDZie6nQFvdtM8PAY26gnUluKQALFUPfudI-WQbP1cA,16812
scipy/spatial/tests/test_spherical_voronoi.py,sha256=nNxAPYBP0fzY5WAsGLBO3PHMIJ7UeIBqjY1_nAqrFdI,14850
scipy/spatial/transform/__init__.py,sha256=AR19SJ8oEv3Pt2eXGRiHkglp7wU_rGvRJ8JEIBqs4AI,729
scipy/spatial/transform/_rotation.cp39-win_amd64.dll.a,sha256=RlIPibafsUAfun105tAnzX3y3KdWlWBHnHvVrABhItU,1564
scipy/spatial/transform/_rotation.cp39-win_amd64.pyd,sha256=7Ylpuw97p1_6Pt7wttmGlHtc7UTfWnLb4HvOX9oe6FA,917504
scipy/spatial/transform/_rotation.pyi,sha256=X5RTw3o4MIgco7GWagRt4_qg1HOKCjGIy__OJ3eN1aI,3145
scipy/spatial/transform/_rotation_groups.py,sha256=XBEazTsMg71VeDaexivk9VYmVDgHPkNji9FrlRypjyc,4562
scipy/spatial/transform/_rotation_spline.py,sha256=bQlcYfmYrF1-__9YFv_Utkr-5yLRwlsXJcS-y6eENDs,14543
scipy/spatial/transform/rotation.py,sha256=iLAmViCchCLGqwzAui2jx_YP2l4qBo4fhebbQJh1d5I,636
scipy/spatial/transform/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/transform/tests/test_rotation.py,sha256=1I0iV6gVUYWvhV9KSjFkSsmUat8y9AZVxtZAn0K5ycI,63983
scipy/spatial/transform/tests/test_rotation_groups.py,sha256=mATjBt62JCkoqpEMHU2zXB9HF-h7KFpWMBrSLyxnkTA,5729
scipy/spatial/transform/tests/test_rotation_spline.py,sha256=yMBr91x2Tt2GqMvJVTOMcO2n3xgOwGC80MN7HNXQkdM,5267
scipy/special.pxd,sha256=vZV_tS467FzwvnjYCtD4T8r5NAuPmITjU8ccJioLZ3I,43
scipy/special/__init__.py,sha256=3Unqj-9K2tCJXIKkl5NjgjcBu2EO-mZUJDgYxxAXQbc,32838
scipy/special/_add_newdocs.py,sha256=A6yj3kPhB2ItJvyK7mD9PCSzRaPuy5jfULG3p6S52_8,413063
scipy/special/_basic.py,sha256=3M-NAEfSBx8lVDyvd0LqnRtpQb4f6Z4A4ilPvnJQhEI,107249
scipy/special/_cdflib.cp39-win_amd64.dll.a,sha256=eyoqKlfRoFCpZ86xgVqhW8w8F2leSzwuwd27N88nPgk,1540
scipy/special/_cdflib.cp39-win_amd64.pyd,sha256=Hwuw7CSeWfZc8-DZEVNOgZINr1CoiF3dDXum3KPrXpg,171008
scipy/special/_comb.cp39-win_amd64.dll.a,sha256=7rqAAS8Ohq0OnbA9gdczlGuSjKPxfULkvrd5tWaeTCo,1516
scipy/special/_comb.cp39-win_amd64.pyd,sha256=j3cg-jaPZvmHbES4T21yYHtBF0JdDFi6uR16Ywoir2A,49664
scipy/special/_ellip_harm.py,sha256=Km_A9XgXnYTleTtRuUzOYLxR8OEUtmiYJLYsRSJaSNI,5596
scipy/special/_ellip_harm_2.cp39-win_amd64.dll.a,sha256=fBIyxiEV7JuUC0pn8rCgkuQy-ZCtgHPaVltCQWI2K1g,1612
scipy/special/_ellip_harm_2.cp39-win_amd64.pyd,sha256=N1fgnzE0IOgUgDZM-PYEiDoYaCw8gsAPMnib1R8JpU8,108544
scipy/special/_lambertw.py,sha256=IYmy0Ymjk-l7T84uHr8_OADgpsUPy1K4F17QGRwWXuE,4111
scipy/special/_logsumexp.py,sha256=g2kOu7XvVWDMswNNmK0b8M73xy3gkf3qekUfywF5NQM,9334
scipy/special/_mptestutils.py,sha256=L5x7loWaUvCVsexqUH130bhphXN9DH9OTFHcIWu2tMo,14894
scipy/special/_orthogonal.py,sha256=jlqsJK2xVS_RElIVwA22-K61JPBBx2zEarYq0GjyVic,77163
scipy/special/_orthogonal.pyi,sha256=coR_kB8pOaLo7ZLlFEBnpwqQTHR8WFAKTqVhRKFeXSQ,8635
scipy/special/_precompute/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/_precompute/cosine_cdf.py,sha256=7Fjlx721WrFcUMSUh6Vv40NMiR1QX71963X1tHOd55M,371
scipy/special/_precompute/expn_asy.py,sha256=trfFT7O7EweO7DfQHIHCzkwWNuqm7sTtCo-HjsUQthY,1319
scipy/special/_precompute/gammainc_asy.py,sha256=8XBo6RBUzdxUhH7GrVeVTzsMJgSnGb9NpknFAdHwcBY,2618
scipy/special/_precompute/gammainc_data.py,sha256=w3vsqOEvC6a7s58voRucZdWFXpO3jIfSGrGwVTNEalY,4201
scipy/special/_precompute/lambertw.py,sha256=UsSir6v9vfyYrlu5F3W-Qn3MMW5RzqqftfZAC-kTH8I,2029
scipy/special/_precompute/loggamma.py,sha256=J2jz62nBO-7JEOA7nG4JQQt5BuXSfUPRik2r3pwTr_s,1137
scipy/special/_precompute/struve_convergence.py,sha256=f21NuMoJE1Di4MF2kZfd6b_wiwqml-uQPylWdpncK_Q,3755
scipy/special/_precompute/utils.py,sha256=tJpjcNS6WaXzqP7cLac7yFyXfb_pfe3AKvDCa0mwi9A,925
scipy/special/_precompute/wright_bessel.py,sha256=4IHODoYYrAFKw8nlJWAPzrOduiHIMUILTJaqtA4jbq4,13210
scipy/special/_precompute/wright_bessel_data.py,sha256=TIwEyxKLTl2DqbLvppxdVWB1jJu-hL157M3JMjFK6eI,5799
scipy/special/_precompute/wrightomega.py,sha256=hAKXz3Kk9L-5JOBA86U6-yC_tsF8gOz0p8Y1vmWcAzE,996
scipy/special/_precompute/zetac.py,sha256=cZMVkubEXtildiIM9p5ldrV1sEoy240MckKPO3JhM6A,618
scipy/special/_sf_error.py,sha256=hDbymj1eSiPjl828PR-Kidkcd8RiVDpJEB-fGA9ltHM,390
scipy/special/_specfun.cp39-win_amd64.dll.a,sha256=c3exEuHkuq2tZ8CGSu0wy-6Rpw9MGCfjbP_QBqKjmRA,1548
scipy/special/_specfun.cp39-win_amd64.pyd,sha256=NRN7r4OT4cjXjLflIYW-HC9lP6nxdkJjD2QukmjrdTg,264704
scipy/special/_spfun_stats.py,sha256=Zb72pfmi5hzvkRXD2QXhVnJMmYyIjoqrg6tVxaCcoSM,3885
scipy/special/_spherical_bessel.py,sha256=o3RiJ46ravdMI1pF0HOLhahs8RLf-63WlUXNCi628jU,10774
scipy/special/_support_alternative_backends.py,sha256=MubLY0QPBdeTaWn4NlemUIOgwM_C0NQcaAY7UPcV0aA,2369
scipy/special/_test_internal.cp39-win_amd64.dll.a,sha256=MRFYArsoXjQbA9Zb_QJNVr1g-v1fjbBXgSrlUi2FyQM,1620
scipy/special/_test_internal.cp39-win_amd64.pyd,sha256=V5tqHDaILE_DoYc0HBq02_y8_oNBiN4KI0HHuBl1LmI,271360
scipy/special/_test_internal.pyi,sha256=SLu2iMKH7AWdVCbKlF0SpGFEdu2i-opkrFoCTq6zMaY,347
scipy/special/_testutils.py,sha256=_yhL5TN4pQxabMRt9NLqoOh6hWI3GuX4MzkShaNA084,12348
scipy/special/_ufuncs.cp39-win_amd64.dll.a,sha256=8zZJc8ITGJhIIttrtdMB0paIHLbYnTwYMI9gjlbpx34,1540
scipy/special/_ufuncs.cp39-win_amd64.pyd,sha256=DTmIDx792qOR6clozmMfa7h5Db5Go9uVrjQPU4BjLd0,1363456
scipy/special/_ufuncs.pyi,sha256=nVIdCRISPiuQ-bOKKC-HTv6vlcha0HpTJTV1G7qB5jA,9463
scipy/special/_ufuncs.pyx,sha256=7JAIiFaPwQpH92jQtqWV23NYB6Pczi9gFOxYKJJcAJw,912771
scipy/special/_ufuncs_cxx.cp39-win_amd64.dll.a,sha256=VP9SgOjtFBG5StXSu-EOCNA6rRUgLr0ayg02fPr9-nc,1588
scipy/special/_ufuncs_cxx.cp39-win_amd64.pyd,sha256=vndzfmsQiUNdkFeRhZ-5Py5Of_g5ehfhJw8jweXAUfg,1460224
scipy/special/_ufuncs_cxx.pxd,sha256=HtTIgBAmJwf9ZLdhBIIPVzjq6tHvs4XwRzIf6XWwmVM,2010
scipy/special/_ufuncs_cxx.pyx,sha256=p5M7pj4ge6WvpVYhlNrMbTcbqjAZfQ9_bZu7kPgocnI,11120
scipy/special/_ufuncs_cxx_defs.h,sha256=AE4bVbw2MQ66w37JdfOtLOoz0Bs48D8uDi8MY0j1my4,3040
scipy/special/_ufuncs_defs.h,sha256=VxyQL0BA3_egVUwCrZrMshKQzOKREeCW4JPqLCUh2NI,9401
scipy/special/add_newdocs.py,sha256=zw7pZMfX7zr-kH6MdBFhrmXBqqjj3tlfGfuL1o7drmI,484
scipy/special/basic.py,sha256=UVJGOHZCijP6H5Kiexi5U2R-CVqfUSCo4Rhv6sxb4bU,1669
scipy/special/cython_special.cp39-win_amd64.dll.a,sha256=Kb0IQw1mqR_Mp2TqYs4icCUyaiKkRgxXUh0pManWRDo,1620
scipy/special/cython_special.cp39-win_amd64.pyd,sha256=NtqXGvtyi_6QTOpuKv60uIOeHToMSn0UFhX6ACvaBb8,2892288
scipy/special/cython_special.pxd,sha256=kE7_hERcNcRglyTZ6ET1kEvYXKBlMrReFqPoL2c_F48,16609
scipy/special/cython_special.pyi,sha256=sC6nE-1ffPYfFKVvopszCin-ilDqBWkDW2O6s0and2M,61
scipy/special/cython_special.pyx,sha256=IyKuq23FJexdhLSaPmAAkWvw1VNvEE7PlVVBWk1LpZI,146428
scipy/special/orthogonal.py,sha256=coBKuqdBycBq4inCpauKEa40NAcd4hlH43kznUXgaJk,1877
scipy/special/sf_error.py,sha256=OhEWgiU5kk4QblZIhTAfAc0eU-mUKK_nTA3H-MHlJ68,593
scipy/special/specfun.py,sha256=UO9V0FqZmF1cpc3zUJhLFSm03-3Ri2hfuIgnDcuhuPs,869
scipy/special/special/binom.h,sha256=sd2_-BYuzi-6gKlVtZS7_2Ru5zkLCpP9-seg1KNMDYY,2444
scipy/special/special/cephes/beta.h,sha256=i5GDczceZRXCeo_zHuT8uN539WhuBGv-FHqgRHaP3Nw,7267
scipy/special/special/cephes/const.h,sha256=8830HDSHga6iqFAI5A8voKPjLAwurpDZfyUa8MXsaYI,2676
scipy/special/special/cephes/gamma.h,sha256=Fp-GJqOvtjlEhvEu7v3Wep26-L6XXS1I6snhVpDP9KM,10680
scipy/special/special/cephes/polevl.h,sha256=8ncKIH7oLTWnhE36ks58-dVBbR9kRqPfenEfyeFbMB8,4190
scipy/special/special/cephes/psi.h,sha256=GogtnrbO47yZflv3r19dUkamjtpPbkqrZzS1ikGqKH8,6517
scipy/special/special/cephes/trig.h,sha256=iAhCSWCBO-ZoCK4LsxYcA2cmQIPkckrUohfc2ARMp2o,1360
scipy/special/special/cephes/zeta.h,sha256=Y_Y-eGQUlTlIuuCYbq8A9n18-zeVCXv9rseCIu0D_GE,4558
scipy/special/special/config.h,sha256=Hy_mdrNZlNoMuU-Zg7Ih18PLZlt2EJHgh6PEQ8r5LzY,4473
scipy/special/special/digamma.h,sha256=sbsbK3L9FMdDsfkFJgTQum01b8OLSGa4GGlUDLBeK78,7501
scipy/special/special/error.h,sha256=_SaNUHWAu_UmmH24KRRbphf7lILxVtabVrmrCuMUCwQ,1233
scipy/special/special/evalpoly.h,sha256=BIBKLyczCRZ7fKZSlybuQptKN-zcL_PbkL-sgGEJnKI,1178
scipy/special/special/lambertw.h,sha256=hcXRu5ZtuOXosamjgEi112nfN8fi_zoStRxn-0FmfuI,5350
scipy/special/special/loggamma.h,sha256=ZVcKldZHVzENIatDY2_ot5sAEHz1VLqaPEaG78xnFNQ,6158
scipy/special/special/trig.h,sha256=itx9TcOcBv1goLPpem-miejoV9PX_T1uq39yV_7oB08,3215
scipy/special/special/zlog1.h,sha256=Qa_Am2ENtaj474Ty3q9zjbt3BTL5pPLtk4TiKZtBvEk,1012
scipy/special/spfun_stats.py,sha256=V6hWZ3XlGmCqBydaXtgF2cEsmRBSEqvAn-TDJ2MWSPk,562
scipy/special/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/data/boost.npz,sha256=tvIhwaNPzSr0JsZWSicUq2ceplhM8qFp0B3ZNiX8SQI,1270643
scipy/special/tests/data/gsl.npz,sha256=8oHuiW1OU8rYzfn2jv1ER_-SikJFilrLkDutNAg5-OE,51433
scipy/special/tests/data/local.npz,sha256=QXoYcTheO_idOFOTCoMT1wvCh21bj8SbhpR5qK6p4tI,203438
scipy/special/tests/test_basic.py,sha256=q8AOcQzYX4tV9yULCZdWPlVr_l4TZ4UAJcCCxaVmKnQ,176072
scipy/special/tests/test_bdtr.py,sha256=ZPWchkB2hVjJ7MALAN9K4QWI8PeGSxcqduER3_myJRk,3343
scipy/special/tests/test_boxcox.py,sha256=K3BgEIIMADJLcnrzi9Nq7SWErOIiLQP36t6Bz6aoRMk,2778
scipy/special/tests/test_cdflib.py,sha256=ZeHdSFK8VocclJo2fPtZjj2O37f2wBwaCJM8U7aVi2o,17968
scipy/special/tests/test_cdft_asymptotic.py,sha256=dsIl7h0WEtzX7Y3vCZSMEKpwb4EZoHvwL4j3BvRd8Ek,1490
scipy/special/tests/test_cosine_distr.py,sha256=ENyfDI4OabouefeuzVJQO7UsBE3Eaorv39Z4GQoBc7E,2773
scipy/special/tests/test_cython_special.py,sha256=OYxUpC4Kxs6OcxFUpk8LNtTrw-tsjOsVcC514VGypYI,19201
scipy/special/tests/test_data.py,sha256=wOXl57xV0qwgsg7HGH1nIR8MZ7N_Rv3gKdrIRCVLKN4,30994
scipy/special/tests/test_dd.py,sha256=3tsB8dnU22c5CEG5l-NJBxDmra5iQGhvUVKn7POdqvs,2020
scipy/special/tests/test_digamma.py,sha256=vBfs2G9WcIEP8VGpGaCsFlTD5g3XODAP7gsUEmAq6Ho,1427
scipy/special/tests/test_ellip_harm.py,sha256=qOVC4b0N6y7ubJNYa4K54W4R5oiHdWghdhy_rO5_gac,9918
scipy/special/tests/test_erfinv.py,sha256=JMvb2hWNQ63uPevk3kAL6KypAtOMJzg8n1G3Om9A15c,3148
scipy/special/tests/test_exponential_integrals.py,sha256=cPxuyehzPSgv_o3Q1Cfb2ON5NNxgBgyAi8dCDILIQe0,3805
scipy/special/tests/test_faddeeva.py,sha256=2BXe8JR2d_QBMvjX-bmHkP0TDzy4JgZhM6XvSlqO1HA,2653
scipy/special/tests/test_gamma.py,sha256=-u9wzNPSPsJTs_DJL6VZAXXIcIqJsxOeQy__MdRTGxY,270
scipy/special/tests/test_gammainc.py,sha256=Rrv_OnC7U6EtXSvrbGQpcPb7sccWVPIgIAzjs6DBIxM,3951
scipy/special/tests/test_hyp2f1.py,sha256=2MhaEiVjxEz-8AGRnT-AwHw7LjAUFHIpzPnDcftCjyk,80729
scipy/special/tests/test_hypergeometric.py,sha256=XDky5ALnj27_BLHO6VghO_iFcGsUVklAuR0lFb9tWQQ,5736
scipy/special/tests/test_kolmogorov.py,sha256=tumJSXhfuCXak-8qGtK9xD-qFK4Yt350fVN8ezZFqWI,19905
scipy/special/tests/test_lambertw.py,sha256=AoYbanXWmx5twRL-HBARQx4RPSfHMPboSEXl0GXwNfQ,4669
scipy/special/tests/test_log_softmax.py,sha256=0VhKfjYbv5-bV_d4tWyrG6OawlRjCUi_O8mRSgW0Mn8,3524
scipy/special/tests/test_loggamma.py,sha256=SdxYiItvixR4NabOKLyBHu-iEP7R52Rew8N8bZugH2s,2062
scipy/special/tests/test_logit.py,sha256=eJfxl4AYJF2VRFebHTw5NS7eF7Q_R82C42x3cou7uwE,5685
scipy/special/tests/test_logsumexp.py,sha256=2HQoXPVNxPHk1vWl6vaw9MNC8FhgKccugIrXNr19A6E,6752
scipy/special/tests/test_mpmath.py,sha256=9gFGLgpPaUx1riEWjNZsBO5Uu-uNipQYWcoYZ-N0Jcg,74937
scipy/special/tests/test_nan_inputs.py,sha256=Zgaxcg14kZegSoO4t_vIIFs1c9ZYiTU3gNoHgGVYJgM,1895
scipy/special/tests/test_ndtr.py,sha256=n_Y8wG9MYVmZOXPgJoiYjPGlUULdiPoDQFsuR6HzPWg,2757
scipy/special/tests/test_ndtri_exp.py,sha256=b4vFFkhdzIDWNgmKOEy5UjRCYtHsdkoaasRi_L9lzIM,3802
scipy/special/tests/test_orthogonal.py,sha256=jGOpJz_awB9KOkMMvu3denxMmxILvY6Z57d-3R2VU5s,32342
scipy/special/tests/test_orthogonal_eval.py,sha256=dAZ-_881U5ax9iS2cKVgReH_yvCYiLUtNWjYEy7CXfA,9763
scipy/special/tests/test_owens_t.py,sha256=sotiVxRrjoQ4lYjbbBx88nQKHEHhNowfJ7KPdIM-vNQ,1845
scipy/special/tests/test_pcf.py,sha256=VTEqviYLsm0DBnuu1Bpyn6rEocf9sZGSyY12ufwxaYo,688
scipy/special/tests/test_pdtr.py,sha256=YpZQ7ssQ5z0yL2lKss4lDooRlz_3n-aY2q4a8aVXXB8,1332
scipy/special/tests/test_powm1.py,sha256=AG5D4ex4jCS97xN7RlyumVc09OUTuczNxS2IYwt9OAg,2341
scipy/special/tests/test_precompute_expn_asy.py,sha256=DFW5CiFXjHNUkWgZrehKUxPNTd09Cllp1nZ0_FU172g,607
scipy/special/tests/test_precompute_gammainc.py,sha256=sY-xFU6MwT-bzjlXXYqu2_spch0XGJO0sZR6pQHMgPs,4567
scipy/special/tests/test_precompute_utils.py,sha256=EWXMuVz5WDUQISPzh87i02u1im9TcLbDBxyk-BO6iZY,1201
scipy/special/tests/test_round.py,sha256=aySiXp52xcim56HDkpumtSnFYgRTGzZFQ2USLNmcECI,437
scipy/special/tests/test_sf_error.py,sha256=wR_JCAz-j5NCRlFNNhP0jZw2ORLNFxiPKogS9emG2So,4036
scipy/special/tests/test_sici.py,sha256=REYntiqC0T4Pb-66ESBmjEmIoTJDbjYs786XNwUkzIw,1263
scipy/special/tests/test_specfun.py,sha256=ib_pE1PN-D3E2TXYF2_VG6v5bi97UxU2e-Mj7AWvrqM,1232
scipy/special/tests/test_spence.py,sha256=r4chXBi_nKK1uSfPse7owNhvcC93aBvcMBZf_8wTlF8,1131
scipy/special/tests/test_spfun_stats.py,sha256=ArofuQVYCm6RevEx0gP2AJ1_6ARrf4Qs5jL3j0LEPKU,2058
scipy/special/tests/test_sph_harm.py,sha256=jUhoguaBjEo2L6660qg41qaLxOxhbMZkMBsa0e5Afmo,1143
scipy/special/tests/test_spherical_bessel.py,sha256=_h08oRpVTjaMs5csuUavDQn5EBsjd3_kG10RbAM3dIY,14763
scipy/special/tests/test_support_alternative_backends.py,sha256=6JqGzafOVWWkWpV-GyU7boO3t9Uj52SgKu6o2u-_Id0,2717
scipy/special/tests/test_trig.py,sha256=vJL6u-XkIOfMOAAqAUs_xfbcJT4CG3HwF6iZ3fMmgjI,2404
scipy/special/tests/test_wright_bessel.py,sha256=t15XtWFFnN9YapbVZyyA88I32j8X14ADbUhZzbMRbLU,4270
scipy/special/tests/test_wrightomega.py,sha256=-2-tzEj7HA21xTo_WtCU5nc6rJDT9Tr0Z9IjA2f39mM,3677
scipy/special/tests/test_zeta.py,sha256=FbhrcRuDXJ_ZrVMRdUvICiaGMp7DM5CeWcOwYSKhXvk,1416
scipy/stats/__init__.py,sha256=2y7pwILkDMmqhp2q9dldIZODLnXmfw2QzYowvvOVCVc,18806
scipy/stats/_ansari_swilk_statistics.cp39-win_amd64.dll.a,sha256=Sg4qv3IaUWHmHnp46zxVDnjtOrxc6uTiRl9A8DVBDVI,1740
scipy/stats/_ansari_swilk_statistics.cp39-win_amd64.pyd,sha256=L-Vp0v4-fVum1tuSx2cVRviXW-nKgkPpA-zFxSZp7lo,258560
scipy/stats/_axis_nan_policy.py,sha256=r-fFgutwESmTEd_MjC6AlwJX5DqsHXTj7uvO6Qa8LS8,29749
scipy/stats/_biasedurn.cp39-win_amd64.dll.a,sha256=SX1XdgRcED1G1q8eicSPuTd3EZHcItKVfJd1VPuzKJA,1572
scipy/stats/_biasedurn.cp39-win_amd64.pyd,sha256=C3NeIi1m1Mv1iOHPo4o0iEmLQXuf7vQk33fKZBHXOAY,399360
scipy/stats/_biasedurn.pxd,sha256=OHa5weTjmvbIdkvmgDnD021p44bVaS5mNvDIx562SLI,1073
scipy/stats/_binned_statistic.py,sha256=IDN2tbE4N5CAl0DvjUmF0ChKSe0bsYdzCOCGfgtgIbs,33507
scipy/stats/_binomtest.py,sha256=gXacpbMuFCBgpwG_1__1InZhMgZsPfDZ3FFETkPybQQ,13493
scipy/stats/_boost/__init__.py,sha256=TOjKqfv_jPF6l8By1mdPbQFrV_H-ZMLRCUFZ7mNfL8A,1812
scipy/stats/_boost/beta_ufunc.cp39-win_amd64.dll.a,sha256=7y42vKO5ce-VhiUDdfkJcdDsteKrEF1g3kK-2a1s138,1572
scipy/stats/_boost/beta_ufunc.cp39-win_amd64.pyd,sha256=70CIphRXCFcMgUoxgk5Gz6vrCZPKQiEc8lv8V7j-66M,1060352
scipy/stats/_boost/binom_ufunc.cp39-win_amd64.dll.a,sha256=7K2qKC6abwWIvE112SfsCaoCaA6dTu_qzHhxK91GRpQ,1588
scipy/stats/_boost/binom_ufunc.cp39-win_amd64.pyd,sha256=DRyTu451vajb5gxYSL9JzBEY3tV8UE372S29-5hZVoo,1036288
scipy/stats/_boost/hypergeom_ufunc.cp39-win_amd64.dll.a,sha256=FndckZv3F55lmMzjIWMR219qFaCx-bfE3uIEmtVPeg8,1636
scipy/stats/_boost/hypergeom_ufunc.cp39-win_amd64.pyd,sha256=XqZjVRtHayCkozskzgSG_zg487Ap_1s3VzsRlIquJw4,1002496
scipy/stats/_boost/invgauss_ufunc.cp39-win_amd64.dll.a,sha256=Vnjd1WiFTaNy3_-hGq1hL40NkujKKBb0y30ahW1u0Tc,1620
scipy/stats/_boost/invgauss_ufunc.cp39-win_amd64.pyd,sha256=G7waUp89SUeCwaEOJO8he22GzV5LmRUnmoVPTXHftos,1029632
scipy/stats/_boost/nbinom_ufunc.cp39-win_amd64.dll.a,sha256=2OV9NXRGQLHrLUCWEBGyT1mEowi3M7rasbyXF75qxAY,1596
scipy/stats/_boost/nbinom_ufunc.cp39-win_amd64.pyd,sha256=Ublw1e4AA8qDQ9ErATYUaWJ6StgmYgntrWaFRkdgH4E,1037824
scipy/stats/_boost/ncf_ufunc.cp39-win_amd64.dll.a,sha256=fIbxL9-0jpCgGS1OZW-vGkL0LGzEZtABCY6hsSpQ2ZY,1564
scipy/stats/_boost/ncf_ufunc.cp39-win_amd64.pyd,sha256=RvYu-HMcBR0vzKHgzusmiJDX-43_qDvsHbOs3OoO3Kw,1036288
scipy/stats/_boost/nct_ufunc.cp39-win_amd64.dll.a,sha256=s3dPYCvMiaetnqvCfZi49K5vLsRROnZFjtIVEwWRVh0,1564
scipy/stats/_boost/nct_ufunc.cp39-win_amd64.pyd,sha256=SVaV3qO7sjEZNPQxj_tdqwo_PCSuJuY5XUm7KmGL510,1071104
scipy/stats/_boost/ncx2_ufunc.cp39-win_amd64.dll.a,sha256=SytpckwXT8l3_e4WzuhKywyJslATNr5bUKQUAzVCGQ8,1572
scipy/stats/_boost/ncx2_ufunc.cp39-win_amd64.pyd,sha256=Io5ryfqnPFFwcl_j-USlbP_pqo4Y7fHRyQZ9rcjPviw,1036800
scipy/stats/_boost/skewnorm_ufunc.cp39-win_amd64.dll.a,sha256=dGX9h5kAh6k_pg9bT2HPKCv3w2Okva-E4TFr7wyWlwk,1620
scipy/stats/_boost/skewnorm_ufunc.cp39-win_amd64.pyd,sha256=w-EzNhP79QOLvTfQ-asSGCE6Gf0Mwfe_fjifUhr2dkw,239104
scipy/stats/_bws_test.py,sha256=h-6Ra_vazN0T6FA1kHZkG1AkEQ6JiRkFzJwVxD5ghS8,7239
scipy/stats/_censored_data.py,sha256=-mOWeG-hGYqCz8sjbELiBeBOE216wS8PsvmD99YNVxA,18765
scipy/stats/_common.py,sha256=PUBtrtKESxYoaVx3tlq8ZYImEYCIuYslFYikUS7uwdU,177
scipy/stats/_constants.py,sha256=AZugmM-2GSPX31Ppxo60H41VzwhlMFuanU8tpXAKFLM,1001
scipy/stats/_continuous_distns.py,sha256=YvsXNSMp1VAssuI9cUh_Mopz2YYynyiZH00_94gER3s,398132
scipy/stats/_covariance.py,sha256=rkMxmsNHMtL7LZP0XmWgmev_rWKNiNrkik92ZWILC3s,23160
scipy/stats/_crosstab.py,sha256=dHftHq4CGFdfo87PDQFbNprSM7-qgfhtIUzUhTfWa8A,7559
scipy/stats/_discrete_distns.py,sha256=EgAYnCkCZTk8uG6NEr4gPYa-TORIU1sXTgSYFxzkduc,61373
scipy/stats/_distn_infrastructure.py,sha256=tO4xr1W3QqGR2RJRyAsIID99D2MXk38R-DG1LtU79yY,152404
scipy/stats/_distr_params.py,sha256=8qTv8a_M9W8vCMy7YervVGUwjP60srMgOaJBYLHu2T0,9020
scipy/stats/_entropy.py,sha256=ze_2es1LYIvwG3ALmiV4KOq_Smq46WS1cWnxrywqtdo,15655
scipy/stats/_fit.py,sha256=2Dg5X5LBvH8pYnzeLKdIzfe2gPp-6JDgtXIgrR7ewnc,61122
scipy/stats/_generate_pyx.py,sha256=AMw8HjQxWB23m-MZYvyNEV6TPpytc1SfYwB41IHiEcY,856
scipy/stats/_hypotests.py,sha256=RebRBUIaXWqpmobeNreLHua90U75eLj_jL6zPK9EpI4,80873
scipy/stats/_kde.py,sha256=42pdDE_15sTMT6HTtZmNOcf_phHbopW9RjgcByHFk-I,25866
scipy/stats/_ksstats.py,sha256=_buNl_2SqBDhPu318J-5zf58HNFvLsJkgjLINC6tWts,20716
scipy/stats/_levy_stable/__init__.py,sha256=XV3OS5J44tPRYHO6O7KPyx1P3BfEpKoN9tGVHXbgWeA,46765
scipy/stats/_levy_stable/levyst.cp39-win_amd64.dll.a,sha256=a3ZHjdChQAnXLE-0Onbj7La6gEOE_QDSRmN0PUcoH5U,1524
scipy/stats/_levy_stable/levyst.cp39-win_amd64.pyd,sha256=JdO2hHVKdvSpOIWoG2AiXbdoK_gJU_qR6n5OFP3RnTg,57344
scipy/stats/_mannwhitneyu.py,sha256=aGoEi4fpg-GQ4WkGJPhJN7NuyJHqc4xp1hZL3WEMJ7s,21041
scipy/stats/_morestats.py,sha256=1xpEt9Vba0RvlMlkE075VqU03_lLwMrRr9E2dp_Jmis,191500
scipy/stats/_mstats_basic.py,sha256=yXp8hGCq_op5dWxlF_CdtFU5KjOv-y2IBpgJZh6Gzmc,122927
scipy/stats/_mstats_extras.py,sha256=mH5y3bwAEO6XhyvIAsfitMwxmqsdD1hKbvQl8EhqMuY,16891
scipy/stats/_multicomp.py,sha256=a0QNETPUdVz-muYize7NnHIB50LpMGkXIfNiBbyBjhM,17741
scipy/stats/_multivariate.py,sha256=n9t--CSA5jGCmj4p0m6dhGV-IsQFWiR5u8OcYOYrT34,244828
scipy/stats/_mvn.cp39-win_amd64.dll.a,sha256=1spc3kLkw8r6ps-UswMe8jDNCqIimNpzuLPiRyDdb8U,1500
scipy/stats/_mvn.cp39-win_amd64.pyd,sha256=n8wMJ2Hgmj9LAvYiuvSisRe2Mt1vaCI6kj83ajw9cYc,105984
scipy/stats/_odds_ratio.py,sha256=Ovn36hoWLRYjV1AwnBiRj5ovUWsYingtY1BhClzPUSY,18340
scipy/stats/_page_trend_test.py,sha256=dvnRV0v6KZvrZyLT3w7nkp02dkv5T54C4ZcTDFt-iy0,19466
scipy/stats/_qmc.py,sha256=Aa60a7ALgflaBj2X2QDw24oGHOUeQw51EEW5UadsPfM,102151
scipy/stats/_qmc_cy.cp39-win_amd64.dll.a,sha256=bpVdkIhhxGHTbVvxKUwP_-8mi1UyXZZ1H3TUqZnL-w4,1540
scipy/stats/_qmc_cy.cp39-win_amd64.pyd,sha256=zfBlkYVGMM6t9Le-wIHvIbJCebgwR1stU7JkJMk8yYw,408576
scipy/stats/_qmc_cy.pyi,sha256=l2173UiDribq_G9S47QozpcKo8nO9D3rojwKLEd8m8k,1188
scipy/stats/_qmvnt.py,sha256=nlJZeatM_ZrfvGrZ5NKGPc4pP-g7TbdMj0aDdvVY6Z8,19300
scipy/stats/_rcont/__init__.py,sha256=xtM2CGKxJ2uUfF6Pfyoi_0em8i5pa6njGRaaGSrvFkA,88
scipy/stats/_rcont/rcont.cp39-win_amd64.dll.a,sha256=92Sth3NJq5YE-_8zIx4LhL6MmFRY00wadlVZ3xdU368,1516
scipy/stats/_rcont/rcont.cp39-win_amd64.pyd,sha256=_blhxyxuRccRzzPlaEwc5O01wmxOaZRleuhy7CudJHM,281088
scipy/stats/_relative_risk.py,sha256=txlXt8-p2wLydMIrKEOwdAt5giral_X51T2itd7VDDw,9834
scipy/stats/_resampling.py,sha256=4cUXlkMeBwOsv84NXuJEShiTGxTuMeF73DHpf9ksAhc,83343
scipy/stats/_result_classes.py,sha256=904WSGrKwzuWHdd3MaS-HUwTJzLm8iF5aNzh0wZq-TM,1125
scipy/stats/_rvs_sampling.py,sha256=2wz3p8laZ60p2ow9H_G5hnj5ZFlulaMynnFE4laJYRc,2289
scipy/stats/_sampling.py,sha256=6pcrRPLvRFI-VRM4gRuw8Fub2l8Ot8WeyC-o_AEM3KA,47722
scipy/stats/_sensitivity_analysis.py,sha256=ZU5scZRNF5evHDM2ViyRKeRIeJskKXgpmObkEctohpw,25457
scipy/stats/_sobol.cp39-win_amd64.dll.a,sha256=3E3pLyny1_UmTBTXEPiKjkKkw7geJO3XfcIs4w0mr-8,1524
scipy/stats/_sobol.cp39-win_amd64.pyd,sha256=9gUEu8AMWLEm1m-jXob2Anmm3gYk93OH-pUDF-Hy-qA,373248
scipy/stats/_sobol.pyi,sha256=tskim056aVi6WIymUMEMdWHaLk8QMqWa2kCAbF2ZTPo,1025
scipy/stats/_sobol_direction_numbers.npz,sha256=SFmTEUfULORluGBcsnf5V9mLg50DGU_fBleTV5BtGTs,589334
scipy/stats/_stats.cp39-win_amd64.dll.a,sha256=QT85AIBLAhrXv3QbBbex1mBV0yAfREmm8kCeHYvRiKo,1524
scipy/stats/_stats.cp39-win_amd64.pyd,sha256=DOv9TmrB0rEqWfwbKEHmJ_kSfpywAEIcH7I2MR26EZ0,704000
scipy/stats/_stats.pxd,sha256=iR5uHEVtvhVTh99viNHee7PBYW-qF2mlo0zkhO8TGOw,717
scipy/stats/_stats_mstats_common.py,sha256=G__dCnKFTUj-wViCifNAHAVwL_b_0WjRaazV7MaM2D4,19092
scipy/stats/_stats_py.py,sha256=ltR5NgFMfWiS4favhO6mB-ZOWN33dtoFKUKY6hkDhVY,434646
scipy/stats/_stats_pythran.cp39-win_amd64.dll.a,sha256=xCfLU3yok9hr1q_bPRtHVXA1-8Wh6GIQFB6QmJgJD9A,1620
scipy/stats/_stats_pythran.cp39-win_amd64.pyd,sha256=S8mmqxii2PnaujteLZB_J7FpJb3BGQ6C0lme-LBIE6A,1064448
scipy/stats/_survival.py,sha256=wf7KADgVjLnET6cibWFXGAY4EgJy17kgQ4JgVY0G1fc,26691
scipy/stats/_tukeylambda_stats.py,sha256=RtXsm72WCNJ8kQSYkL6x5XEsRopLVkY_wUSS_32NtmY,7070
scipy/stats/_unuran/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/_unuran/unuran_wrapper.cp39-win_amd64.dll.a,sha256=6d3YtUZk5qve9HrvIO4hV9RLKoL8lKfZ8UpVZCw3wSA,1620
scipy/stats/_unuran/unuran_wrapper.cp39-win_amd64.pyd,sha256=3w7NVC1REdnyCp4scLjOMDzdKmQvJ_sqFWGb0djj-Xg,1481728
scipy/stats/_unuran/unuran_wrapper.pyi,sha256=aLU9IcfR08wTYyFdrL55ilePBemIa94wScMls0YkvHI,5730
scipy/stats/_variation.py,sha256=Vzve--tEjfZJnekfriYdBCdjlb6bJiHvFCFzH7639nc,4496
scipy/stats/_warnings_errors.py,sha256=f_Hxg3GzZJW2U1B608HFABSkV2w9Xjv1qtsND2AuGKA,1234
scipy/stats/_wilcoxon.py,sha256=hC33yqM1Z6qFftB9rMg0wCmadDAv8Y-olAsBtwJXP04,8237
scipy/stats/biasedurn.py,sha256=31bvmg8wJmaIy2rs4CkSRnhG9jEbV9rn7fJ0RoRJafQ,549
scipy/stats/contingency.py,sha256=SxMGmqxWVIHJIRRJC2tI_aeU0mE-JnMx1PXoQLLtPQQ,16743
scipy/stats/distributions.py,sha256=_nRpDudL-PbnWvNm3fxRQ-00zEtCleSqhTNk1evZvq8,883
scipy/stats/kde.py,sha256=CceQ4q97WSElMimCGPjC5vEGx8QLuI8iQFH4KbltWN0,743
scipy/stats/morestats.py,sha256=FvoVSzbXNuhW4Pc4wAgL3sPAIwP0i8CgRqI2WxhtrTI,1425
scipy/stats/mstats.py,sha256=jzvELjOG5FulzAORL52iNL6VwQhA3N27RAr_-8fSu1Y,2606
scipy/stats/mstats_basic.py,sha256=RP6xgbhPD9MeyiMRgxHiGQSWJZXu3-Ay0Z9jc7l8uyQ,1938
scipy/stats/mstats_extras.py,sha256=6a3gBy_Tm578z4B3VXTn3siLMsRnLwYEL3hL8r5M-pU,811
scipy/stats/mvn.py,sha256=M0SeLZu6j1NULP0wMDLtWffsmGWgYS-pIsuQebBwPLg,588
scipy/stats/qmc.py,sha256=zzfKfqpVjNowjVqddBTmZdp1PvvmoVxnaQ0XAYJN6-w,11898
scipy/stats/sampling.py,sha256=IZYmnsnXtcwXxxHfGGNCE1nyIzRYIzWz-BuH1pSsRv0,1751
scipy/stats/stats.py,sha256=CBHVLbIYFiOYJMK8HfjZR9nIVOmqnwctIPuJfSEiIsc,2192
scipy/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/tests/common_tests.py,sha256=ahg4xq4lUSsJVlpPZOI9ub8AePgNbKtrGDqbijtwzRE,12639
scipy/stats/tests/data/_mvt.py,sha256=FhbZqSVqMei58-awIxMScB_gSDj_1Fy-RKRkJwuBBns,7076
scipy/stats/tests/data/fisher_exact_results_from_r.py,sha256=AqKIctwYj-6FUc6XhwZIfrPCEw073oMKhSWrDhuz00I,27956
scipy/stats/tests/data/jf_skew_t_gamlss_pdf_data.npy,sha256=JU0t7kpNVHuTMcYCQ8b8_K_9JsixBNCNT2BFp2RbO7o,4064
scipy/stats/tests/data/levy_stable/stable-Z1-cdf-sample-data.npy,sha256=zxjB8tZaIyvyxxISgt8xvyqL6Cevr8TtgQ7TdFfuiYo,183728
scipy/stats/tests/data/levy_stable/stable-Z1-pdf-sample-data.npy,sha256=_umVErq0zMZWm0e5JOSwNOHNurViT6_H4SBki9X3oSg,183688
scipy/stats/tests/data/levy_stable/stable-loc-scale-sample-data.npy,sha256=88cZ7dVDH7nnuey20Z48p6kJUpi9GfImaFsPykDwwHM,9328
scipy/stats/tests/data/nist_anova/AtmWtAg.dat,sha256=WhCDyQYtOWbyRL_6N2_JsDMkSr-GoNDdAWrznNcfuKc,3171
scipy/stats/tests/data/nist_anova/SiRstv.dat,sha256=Wk8C1_JW4Yww3K0bgeAmC7gQtawRP2EH4G3uFo0HZj4,2032
scipy/stats/tests/data/nist_anova/SmLs01.dat,sha256=rSjKwNT7ef_qJSK1U0kIO96hDxKm-MRs-6vMzbTWuJY,6304
scipy/stats/tests/data/nist_anova/SmLs02.dat,sha256=cI4y6vNnaavnUz59zRttxnCRtg35ON03xPDmVmDvews,48430
scipy/stats/tests/data/nist_anova/SmLs03.dat,sha256=4YQE0GKLeGcFgLIwo6O2zZl9lZ0A-Soog6TalCsZN3o,469635
scipy/stats/tests/data/nist_anova/SmLs04.dat,sha256=EwjrFYHyjObJ3kypUg_Mcp-RbDTx-d7hD7bmWB5bTOc,7064
scipy/stats/tests/data/nist_anova/SmLs05.dat,sha256=exMz5uVs7XQUUJbfd0z7H2MFITyyj5qCIsQYyoXQwpY,55668
scipy/stats/tests/data/nist_anova/SmLs06.dat,sha256=DA8Jot8F8unbUySmqu7YVkdlHodOn1w4krjyn8SiAPc,541674
scipy/stats/tests/data/nist_anova/SmLs07.dat,sha256=_ZXFEKqLPOf2fd0yhGzOrX2auuRBeQM2xvsPywrjVY8,7630
scipy/stats/tests/data/nist_anova/SmLs08.dat,sha256=h9043__rZdOdvj6LpzvRZkT8OWwarQP8ApUiXzumrvk,61097
scipy/stats/tests/data/nist_anova/SmLs09.dat,sha256=9GiMWg1WkPyG-ifgxaJl7vCyRkHnSvJ6py2WFQhN0T8,595702
scipy/stats/tests/data/nist_linregress/Norris.dat,sha256=WraQbGipxLLrFmHAzX0qCfWkaabU4FtraR8ZD197lE8,2688
scipy/stats/tests/data/rel_breitwigner_pdf_sample_data_ROOT.npy,sha256=7vTccC3YxuMcGMdOH4EoTD6coqtQKC3jnJrTC3u4520,38624
scipy/stats/tests/data/studentized_range_mpmath_ref.json,sha256=ZIARw6MmSmQkPpmLKGurnF_WxcZqQxUJmgoVsooTJIU,30737
scipy/stats/tests/test_axis_nan_policy.py,sha256=n2N12DmC5KTzKgfid54z0f89YAgPhanBevvMV9yenMQ,52666
scipy/stats/tests/test_binned_statistic.py,sha256=Zx8gdkQGNDoF6gUXLbi3PJdXsN-4ZmVrB_4HnCcYYYY,19382
scipy/stats/tests/test_boost_ufuncs.py,sha256=APd62wRVk3ipERc5RlOW-z0Nyx4hn9vwI1QCQoAvYbg,1872
scipy/stats/tests/test_censored_data.py,sha256=LtTBWL6HY-0m7HzbZkYDLO1NH3Z0h_D7I2XZ27CwuWY,7087
scipy/stats/tests/test_contingency.py,sha256=IXGuId-yDBKkt3aYl-Qj16Ojl_hFKQ0NEkLjfFCzlZ8,7947
scipy/stats/tests/test_continuous_basic.py,sha256=IU8OIsdMbfYKW9z_7LbPl0zgoSkaNuExmyscb5dlK_s,42809
scipy/stats/tests/test_continuous_fit_censored.py,sha256=PtKzuvJPtFTF__pr209v-i34pcYOXSCB1vMfz8Uez3M,24871
scipy/stats/tests/test_crosstab.py,sha256=8NEB6tQ-kVkPYhGvW_nH0WQbWzTruHGWzlrZdTyXPcc,3997
scipy/stats/tests/test_discrete_basic.py,sha256=lYPd-l9Ci7bWkHkAz6OU5Bqrjc6pTXxCcmSGn_EuACU,20546
scipy/stats/tests/test_discrete_distns.py,sha256=FqKg-bWb7JoSw0QkmgTbjKPqE7YwS593FgOZ07rnht0,23881
scipy/stats/tests/test_distributions.py,sha256=_-PPtwBmV9fLiG74Uuy42A584lLMU1uITBjkv3PhDJQ,393927
scipy/stats/tests/test_entropy.py,sha256=8WzBCUvPd0SHijC0pyNnK7b3RcjvSi93sJrnV2Wv4EA,11567
scipy/stats/tests/test_fast_gen_inversion.py,sha256=XX3949wQHNSieAp5sP26ANcFcRSVu2TWgVHzggNOU3E,16319
scipy/stats/tests/test_fit.py,sha256=P9oyItx00b_ydSRzWKmM3Dxu2H8GbD-XGnCdgfvklYo,46689
scipy/stats/tests/test_hypotests.py,sha256=3Er6eBwDQhqoYAgXK1VNMFxTpmWVfy09NCPY2faMlxo,82181
scipy/stats/tests/test_kdeoth.py,sha256=ctUd9YlnageW1tUrlZKbCfJVXVqeul_IiNokEnF9Jmg,21078
scipy/stats/tests/test_morestats.py,sha256=t7_G4SJfHdcwHjDd9qB6Npq3c8HOpviYXZCOAkAUb0M,131440
scipy/stats/tests/test_mstats_basic.py,sha256=9cSrIA7KXnPC_Evj2sc0b4YuLzQkmpAq-q8hlGQ8n50,88460
scipy/stats/tests/test_mstats_extras.py,sha256=ae8Qn_-iLAI70UaMDbl_r78eeX2xVShuNrRXa81jMxA,7469
scipy/stats/tests/test_multicomp.py,sha256=zabamhTMhUvYbxWS7vHQEPXuFJv6FAg5czBhVVYbchs,18230
scipy/stats/tests/test_multivariate.py,sha256=zne6CGX2dfqjXRbDbatfiDk3fYntsbOVg58x4RVHdk4,157172
scipy/stats/tests/test_odds_ratio.py,sha256=J5A_AiTlmnMM4ccKOl2cNrv9oBBRFyI5krfJB1F0GMo,6852
scipy/stats/tests/test_qmc.py,sha256=J4ce-tnYOEDchvb1zge0Xx2LtVkLZNgsqnfIewX8O-4,56055
scipy/stats/tests/test_rank.py,sha256=dGqwkEQK8_F_7AbdOQ6AdDlT9ni_sAcF8uVlmXp3fSc,12057
scipy/stats/tests/test_relative_risk.py,sha256=_JizDcNuNxqEQ5j9fNqbvhu0Xxa8YkDUlBivVTsykhY,3741
scipy/stats/tests/test_resampling.py,sha256=8HPX3vszyRh-2841U60AefdRlVe2JSGVyVJMjQuaG1c,73514
scipy/stats/tests/test_sampling.py,sha256=2tvh2caj9CAxAfetG_6Ut3lL00yfyqKTaQkPWqc74mI,55958
scipy/stats/tests/test_sensitivity_analysis.py,sha256=VnSlauhmxG2qbYY8h1b6SlOFD935mLcRVA3Es6xmPpU,10434
scipy/stats/tests/test_stats.py,sha256=PWA2VVf-c3HeG_VwdNlwJuHwsDivVqCPGFH93J24i8o,368856
scipy/stats/tests/test_survival.py,sha256=CyFbtihuBA5nj5-aXl_-4Ox67ehTLThNO__MJwlJ9SY,22735
scipy/stats/tests/test_tukeylambda_stats.py,sha256=AmqMknbKki17oYdfP5H_IaP6WyuvWRXNyUrWflzGLAE,3316
scipy/stats/tests/test_variation.py,sha256=pCy3hECRpUp_zYuV5oINSLQPmT9lQZcmrIsLr0o_1MU,6451
scipy/version.py,sha256=UEkIBSCL4qRhuonlGCpUPidK-nBsbGWzdBKEHbitGe8,276
