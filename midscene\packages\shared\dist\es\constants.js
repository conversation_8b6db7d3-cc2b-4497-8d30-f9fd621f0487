// src/constants/index.ts
var TEXT_SIZE_THRESHOLD = 9;
var TEXT_MAX_SIZE = 40;
var CONTAINER_MINI_HEIGHT = 3;
var CONTAINER_MINI_WIDTH = 3;
var NodeType = /* @__PURE__ */ ((NodeType2) => {
  NodeType2["CONTAINER"] = "CONTAINER Node";
  NodeType2["FORM_ITEM"] = "FORM_ITEM Node";
  NodeType2["BUTTON"] = "BUTTON Node";
  NodeType2["A"] = "Anchor Node";
  NodeType2["IMG"] = "IMG Node";
  NodeType2["TEXT"] = "TEXT Node";
  NodeType2["POSITION"] = "POSITION Node";
  return NodeType2;
})(NodeType || {});
var PLAYGROUND_SERVER_PORT = 5800;
var SCRCPY_SERVER_PORT = 5700;
var DEFAULT_WAIT_FOR_NAVIGATION_TIMEOUT = 5e3;
var DEFAULT_WAIT_FOR_NETWORK_IDLE_TIMEOUT = 2e3;
var DEFAULT_WAIT_FOR_NETWORK_IDLE_TIME = 300;
var DEFAULT_WAIT_FOR_NETWORK_IDLE_CONCURRENCY = 2;
export {
  CONTAINER_MINI_HEIGHT,
  CONTAINER_MINI_WIDTH,
  DEFAULT_WAIT_FOR_NAVIGATION_TIMEOUT,
  DEFAULT_WAIT_FOR_NETWORK_IDLE_CONCURRENCY,
  DEFAULT_WAIT_FOR_NETWORK_IDLE_TIME,
  DEFAULT_WAIT_FOR_NETWORK_IDLE_TIMEOUT,
  NodeType,
  PLAYGROUND_SERVER_PORT,
  SCRCPY_SERVER_PORT,
  TEXT_MAX_SIZE,
  TEXT_SIZE_THRESHOLD
};
