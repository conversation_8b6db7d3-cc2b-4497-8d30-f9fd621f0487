declare const defaultRunDirName = "midscene_run";
declare const isNodeEnv: boolean;
declare const getMidsceneRunDir: () => string;
declare const getMidsceneRunBaseDir: () => string;
/**
 * Get the path to the midscene_run directory or a subdirectory within it.
 * Creates the directory if it doesn't exist.
 *
 * @param subdir - Optional subdirectory name (e.g., 'log', 'report')
 * @returns The absolute path to the requested directory
 */
declare const getMidsceneRunSubDir: (subdir: "dump" | "cache" | "report" | "tmp" | "log" | "output") => string;

export { defaultRunDirName, getMidsceneRunBaseDir, getMidsceneRunDir, getMidsceneRunSubDir, isNodeEnv };
