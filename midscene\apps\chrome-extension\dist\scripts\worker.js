(()=>{"use strict";let e={SAVE_CONTEXT:"save-context",GET_CONTEXT:"get-context"},t=globalThis.console;chrome.sidePanel.setPanelBehavior({openPanelOnActionClick:!0}).catch(e=>t.error(e));let o=()=>Math.random().toString(36).substring(2,15),r=new Map;chrome.runtime.onMessage.addListener((n,s,a)=>{switch(t.log("Message received in service worker:",n),n.type){case e.SAVE_CONTEXT:{let{context:e}=n.payload,t=o();r.set(t,e),a({id:t});break}case e.GET_CONTEXT:{let{id:e}=n.payload,t=r.get(e);a(t?{context:t}:{error:"Screenshot not found"});break}default:t.log("sending response"),a({error:"Unknown message type"})}}),module.exports={}})();
//# sourceMappingURL=worker.js.map