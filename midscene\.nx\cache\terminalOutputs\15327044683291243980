
> chrome-extension@0.12.4 build E:\development\level-up\midscene\apps\chrome-extension
> rsbuild build && npm run pack-extension

[1m[38;2;189;255;243m [39m[38;2;189;255;243m [39m[38;2;180;250;236mR[39m[38;2;171;246;229ms[39m[38;2;162;241;222mb[39m[38;2;154;236;216mu[39m[38;2;145;232;209mi[39m[38;2;136;227;202ml[39m[38;2;127;222;195md[39m[38;2;127;222;195m [39m[38;2;118;217;188mv[39m[38;2;109;213;181m1[39m[38;2;101;208;175m.[39m[38;2;92;203;168m3[39m[38;2;83;199;161m.[39m[38;2;74;194;154m1[39m[38;2;74;194;154m
[39m[22m
[1m[32mready  [39m[22m built in [1m7.18[22m s[90m (web)[39m
[1m[32mready  [39m[22m built in [1m1.06[22m s[90m (node)[39m

[34m  File (node)                                                         Size        [39m
  [2mdist\scripts\[22m[36mstop-water-flow.js[39m                                     0.15 kB 
  [2mdist\[22m[35mmanifest.json[39m                                                  0.61 kB 
  [2mdist\scripts\[22m[36mworker.js[39m                                              0.67 kB 
  [2mdist\fonts\open-sans\open-sans-8-black\[22m[35mopen-sans-8-black.png[39m        4.7 kB  
  [2mdist\scripts\[22m[36mwater-flow.js[39m                                          4.8 kB  
  [2mdist\fonts\open-sans\open-sans-10-black\[22m[35mopen-sans-10-black.png[39m      4.9 kB  
  [2mdist\fonts\open-sans\open-sans-12-black\[22m[35mopen-sans-12-black.png[39m      5.8 kB  
  [2mdist\fonts\open-sans\open-sans-8-white\[22m[35mopen-sans-8-white.png[39m        5.8 kB  
  [2mdist\fonts\open-sans\open-sans-14-black\[22m[35mopen-sans-14-black.png[39m      6.8 kB  
  [2mdist\fonts\open-sans\[22m[35mApache License.txt[39m                             11.5 kB 
  [2mdist\fonts\open-sans\open-sans-16-black\[22m[35mopen-sans-16-black.png[39m      12.2 kB 
  [2mdist\fonts\open-sans\open-sans-10-black\[22m[35mopen-sans-10-black.fnt[39m      14.1 kB 
  [2mdist\fonts\open-sans\open-sans-16-white\[22m[35mopen-sans-16-white.png[39m      14.8 kB 
  [2mdist\fonts\open-sans\open-sans-12-black\[22m[35mopen-sans-12-black.fnt[39m      15.4 kB 
  [2mdist\fonts\open-sans\open-sans-14-black\[22m[35mopen-sans-14-black.fnt[39m      17.1 kB 
  [2mdist\scripts\[22m[36mhtmlElementDebug.js[39m                                    19.6 kB 
  [2mdist\scripts\[22m[36mhtmlElement.js[39m                                         22.9 kB 
  [2mdist\[22m[35micon128.png[39m                                                    25.9 kB 
  [2mdist\fonts\open-sans\open-sans-8-black\[22m[35mopen-sans-8-black.fnt[39m        26.7 kB 
  [2mdist\fonts\open-sans\open-sans-8-white\[22m[35mopen-sans-8-white.fnt[39m        26.7 kB 
  [2mdist\fonts\open-sans\open-sans-16-black\[22m[35mopen-sans-16-black.fnt[39m      27.1 kB 
  [2mdist\fonts\open-sans\open-sans-16-white\[22m[35mopen-sans-16-white.fnt[39m      27.1 kB 
  [2mdist\fonts\open-sans\open-sans-32-black\[22m[35mopen-sans-32-black.fnt[39m      27.5 kB 
  [2mdist\fonts\open-sans\open-sans-32-white\[22m[35mopen-sans-32-white.fnt[39m      27.5 kB 
  [2mdist\fonts\open-sans\open-sans-64-black\[22m[35mopen-sans-64-black.fnt[39m      27.8 kB 
  [2mdist\fonts\open-sans\open-sans-64-white\[22m[35mopen-sans-64-white.fnt[39m      27.8 kB 
  [2mdist\fonts\open-sans\open-sans-128-black\[22m[35mopen-sans-128-black.fnt[39m    28.1 kB 
  [2mdist\fonts\open-sans\open-sans-128-white\[22m[35mopen-sans-128-white.fnt[39m    28.1 kB 
  [2mdist\fonts\open-sans\open-sans-32-black\[22m[35mopen-sans-32-black.png[39m      29.5 kB 
  [2mdist\fonts\open-sans\open-sans-32-white\[22m[35mopen-sans-32-white.png[39m      36.0 kB 
  [2mdist\fonts\open-sans\open-sans-64-black\[22m[35mopen-sans-64-black.png[39m      72.0 kB 
  [2mdist\fonts\open-sans\open-sans-64-white\[22m[35mopen-sans-64-white.png[39m      85.7 kB 
  [2mdist\fonts\open-sans\open-sans-128-black\[22m[35mopen-sans-128-black.png[39m    164.0 kB
  [2mdist\fonts\open-sans\open-sans-128-white\[22m[35mopen-sans-128-white.png[39m    185.8 kB

  [34mTotal:[39m 1035.4 kB

[2m  -----[22m

[34m  File (web)                                                          Size         Gzip    [39m
  [2mdist\[22m[32mpopup.html[39m                                                     0.49 kB      [32m0.26 kB[39m
  [2mdist\[22m[32mindex.html[39m                                                     0.55 kB      [32m0.27 kB[39m
  [2mdist\[22m[35mmanifest.json[39m                                                  0.61 kB      [32m0.34 kB[39m
  [2mdist\fonts\open-sans\open-sans-8-black\[22m[35mopen-sans-8-black.png[39m        4.7 kB   
  [2mdist\fonts\open-sans\open-sans-10-black\[22m[35mopen-sans-10-black.png[39m      4.9 kB   
  [2mdist\static\js\async\[22m[36m450.41f8b458.js[39m                                5.1 kB       [32m1.5 kB[39m
  [2mdist\fonts\open-sans\open-sans-12-black\[22m[35mopen-sans-12-black.png[39m      5.8 kB   
  [2mdist\fonts\open-sans\open-sans-8-white\[22m[35mopen-sans-8-white.png[39m        5.8 kB   
  [2mdist\fonts\open-sans\open-sans-14-black\[22m[35mopen-sans-14-black.png[39m      6.8 kB   
  [2mdist\fonts\open-sans\[22m[35mApache License.txt[39m                             11.5 kB      [32m3.9 kB[39m
  [2mdist\fonts\open-sans\open-sans-16-black\[22m[35mopen-sans-16-black.png[39m      12.2 kB  
  [2mdist\fonts\open-sans\open-sans-10-black\[22m[35mopen-sans-10-black.fnt[39m      14.1 kB  
  [2mdist\fonts\open-sans\open-sans-16-white\[22m[35mopen-sans-16-white.png[39m      14.8 kB  
  [2mdist\fonts\open-sans\open-sans-12-black\[22m[35mopen-sans-12-black.fnt[39m      15.4 kB  
  [2mdist\fonts\open-sans\open-sans-14-black\[22m[35mopen-sans-14-black.fnt[39m      17.1 kB  
  [2mdist\scripts\[22m[36mhtmlElementDebug.js[39m                                    19.6 kB      [32m7.2 kB[39m
  [2mdist\scripts\[22m[36mhtmlElement.js[39m                                         22.9 kB      [32m8.5 kB[39m
  [2mdist\[22m[35micon128.png[39m                                                    25.9 kB  
  [2mdist\fonts\open-sans\open-sans-8-black\[22m[35mopen-sans-8-black.fnt[39m        26.7 kB  
  [2mdist\fonts\open-sans\open-sans-8-white\[22m[35mopen-sans-8-white.fnt[39m        26.7 kB  
  [2mdist\fonts\open-sans\open-sans-16-black\[22m[35mopen-sans-16-black.fnt[39m      27.1 kB  
  [2mdist\fonts\open-sans\open-sans-16-white\[22m[35mopen-sans-16-white.fnt[39m      27.1 kB  
  [2mdist\fonts\open-sans\open-sans-32-black\[22m[35mopen-sans-32-black.fnt[39m      27.5 kB  
  [2mdist\fonts\open-sans\open-sans-32-white\[22m[35mopen-sans-32-white.fnt[39m      27.5 kB  
  [2mdist\fonts\open-sans\open-sans-64-black\[22m[35mopen-sans-64-black.fnt[39m      27.8 kB  
  [2mdist\fonts\open-sans\open-sans-64-white\[22m[35mopen-sans-64-white.fnt[39m      27.8 kB  
  [2mdist\fonts\open-sans\open-sans-128-black\[22m[35mopen-sans-128-black.fnt[39m    28.1 kB  
  [2mdist\fonts\open-sans\open-sans-128-white\[22m[35mopen-sans-128-white.fnt[39m    28.1 kB  
  [2mdist\fonts\open-sans\open-sans-32-black\[22m[35mopen-sans-32-black.png[39m      29.5 kB  
  [2mdist\fonts\open-sans\open-sans-32-white\[22m[35mopen-sans-32-white.png[39m      36.0 kB  
  [2mdist\fonts\open-sans\open-sans-64-black\[22m[35mopen-sans-64-black.png[39m      72.0 kB  
  [2mdist\static\js\[22m[36mpopup.6311459a.js[39m                                    80.1 kB      [32m24.5 kB[39m
  [2mdist\fonts\open-sans\open-sans-64-white\[22m[35mopen-sans-64-white.png[39m      85.7 kB  
  [2mdist\static\js\[22m[36mlib-react.8cf83146.js[39m                                140.0 kB     [32m45.1 kB[39m
  [2mdist\static\js\[22m[36mlib-polyfill.85ea229d.js[39m                             147.6 kB     [32m46.7 kB[39m
  [2mdist\fonts\open-sans\open-sans-128-black\[22m[35mopen-sans-128-black.png[39m    164.0 kB 
  [2mdist\fonts\open-sans\open-sans-128-white\[22m[35mopen-sans-128-white.png[39m    185.8 kB 
  [2mdist\static\js\async\[22m[36m18.f4dc3b35.js[39m                                 274.6 kB     [32m66.2 kB[39m
  [2mdist\static\js\async\[22m[36m251.dd1be928.js[39m                                584.7 kB     [33m180.9 kB[39m
  [2mdist\static\js\[22m[36m337.d775a0c6.js[39m                                      1530.0 kB    [31m383.1 kB[39m
  [2mdist\static\js\[22m[36m341.7bcedadf.js[39m                                      2290.7 kB    [31m625.2 kB[39m
  [2mdist\static\js\[22m[36mindex.ee14982c.js[39m                                    4696.2 kB    [31m1271.1 kB[39m
  [2mdist\static\js\[22m[36m487.56518971.js[39m                                      7827.7 kB    [31m2142.5 kB[39m

  [34mTotal:[39m 18607.5 kB[2m (gzip: 5782.2 kB)[22m


> chrome-extension@0.12.4 pack-extension
> node scripts/pack-extension.js

Extension packed successfully: midscene-extension-v0.12.4.zip (15662741 total bytes saved in extension directory)
