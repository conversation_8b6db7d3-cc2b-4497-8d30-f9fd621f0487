"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// modern.config.ts
var modern_config_exports = {};
__export(modern_config_exports, {
  default: () => modern_config_default
});
module.exports = __toCommonJS(modern_config_exports);
var import_module_tools = require("@modern-js/module-tools");
var modern_config_default = (0, import_module_tools.defineConfig)({
  plugins: [(0, import_module_tools.moduleTools)()],
  buildPreset: "npm-library",
  buildConfig: {
    input: {
      index: "./src/index.ts",
      img: "./src/img/index.ts",
      constants: "./src/constants/index.ts",
      extractor: "./src/extractor/index.ts",
      "extractor-debug": "./src/extractor/debug.ts",
      fs: "./src/node/fs.ts",
      utils: "./src/utils.ts",
      logger: "./src/logger.ts",
      common: "./src/common.ts",
      "us-keyboard-layout": "./src/us-keyboard-layout.ts",
      env: "./src/env.ts",
      types: "./src/types/index.ts"
    },
    target: "es2020",
    dts: {
      respectExternal: true
    }
  }
});
