@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\development\level-up\midscene\packages\web-integration\bin\node_modules;E:\development\level-up\midscene\packages\web-integration\node_modules;E:\development\level-up\midscene\packages\node_modules;E:\development\level-up\midscene\node_modules;E:\development\level-up\node_modules;E:\development\node_modules;E:\node_modules;E:\development\level-up\midscene\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\development\level-up\midscene\packages\web-integration\bin\node_modules;E:\development\level-up\midscene\packages\web-integration\node_modules;E:\development\level-up\midscene\packages\node_modules;E:\development\level-up\midscene\node_modules;E:\development\level-up\node_modules;E:\development\node_modules;E:\node_modules;E:\development\level-up\midscene\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@midscene\web\bin\midscene-playground" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@midscene\web\bin\midscene-playground" %*
)
