"use strict";(self.webpackChunkchrome_extension=self.webpackChunkchrome_extension||[]).push([["450"],{32317:function(e,t,a){a.d(t,{FewShotPromptTemplate:()=>u});var r=a(9548),i=a(603),l=a(49068);a(89590);var s=a(7546);a(42407),a(16899),a(76937),a(40549),a(94509);var p=a(58746);s.ku,a(49156);var o=a(44282),n=a(13247);class m extends n.d{static lc_name(){return"ImagePromptTemplate"}constructor(e){if(super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompts","image"]}),Object.defineProperty(this,"template",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"templateFormat",{enumerable:!0,configurable:!0,writable:!0,value:"f-string"}),Object.defineProperty(this,"validateTemplate",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"additionalContentFields",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.template=e.template,this.templateFormat=e.templateFormat??this.templateFormat,this.validateTemplate=e.validateTemplate??this.validateTemplate,this.additionalContentFields=e.additionalContentFields,this.validateTemplate){let e=this.inputVariables;this.partialVariables&&(e=e.concat(Object.keys(this.partialVariables))),(0,i.af)([{type:"image_url",image_url:this.template}],this.templateFormat,e)}}_getPromptType(){return"prompt"}async partial(e){let t=this.inputVariables.filter(t=>!(t in e)),a={...this.partialVariables??{},...e};return new m({...this,inputVariables:t,partialVariables:a})}async format(e){let t={};for(let[a,r]of Object.entries(this.template))"string"==typeof r?t[a]=(0,i.SM)(r,this.templateFormat,e):t[a]=r;let a=e.url||t.url,r=e.detail||t.detail;if(!a)throw Error("Must provide either an image URL.");if("string"!=typeof a)throw Error("url must be a string.");let l={url:a};return r&&(l.detail=r),l}async formatPromptValue(e){let t=await this.format(e);return new o.Nn(t)}}a(18567),p.eq,n.d;class u extends r.A{constructor(e){if(super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"examples",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"exampleSelector",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"examplePrompt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"suffix",{enumerable:!0,configurable:!0,writable:!0,value:""}),Object.defineProperty(this,"exampleSeparator",{enumerable:!0,configurable:!0,writable:!0,value:"\n\n"}),Object.defineProperty(this,"prefix",{enumerable:!0,configurable:!0,writable:!0,value:""}),Object.defineProperty(this,"templateFormat",{enumerable:!0,configurable:!0,writable:!0,value:"f-string"}),Object.defineProperty(this,"validateTemplate",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.assign(this,e),void 0!==this.examples&&void 0!==this.exampleSelector)throw Error("Only one of 'examples' and 'example_selector' should be provided");if(void 0===this.examples&&void 0===this.exampleSelector)throw Error("One of 'examples' and 'example_selector' should be provided");if(this.validateTemplate){let e=this.inputVariables;this.partialVariables&&(e=e.concat(Object.keys(this.partialVariables))),(0,i.af)(this.prefix+this.suffix,this.templateFormat,e)}}_getPromptType(){return"few_shot"}static lc_name(){return"FewShotPromptTemplate"}async getExamples(e){if(void 0!==this.examples)return this.examples;if(void 0!==this.exampleSelector)return this.exampleSelector.selectExamples(e);throw Error("One of 'examples' and 'example_selector' should be provided")}async partial(e){let t=this.inputVariables.filter(t=>!(t in e)),a={...this.partialVariables??{},...e};return new u({...this,inputVariables:t,partialVariables:a})}async format(e){let t=await this.mergePartialAndUserVariables(e),a=await this.getExamples(t),r=await Promise.all(a.map(e=>this.examplePrompt.format(e))),l=[this.prefix,...r,this.suffix].join(this.exampleSeparator);return(0,i.SM)(l,this.templateFormat,t)}serialize(){if(this.exampleSelector||!this.examples)throw Error("Serializing an example selector is not currently supported");if(void 0!==this.outputParser)throw Error("Serializing an output parser is not currently supported");return{_type:this._getPromptType(),input_variables:this.inputVariables,example_prompt:this.examplePrompt.serialize(),example_separator:this.exampleSeparator,suffix:this.suffix,prefix:this.prefix,template_format:this.templateFormat,examples:this.examples}}static async deserialize(e){let t,{example_prompt:a}=e;if(!a)throw Error("Missing example prompt");let r=await l.PromptTemplate.deserialize(a);if(Array.isArray(e.examples))t=e.examples;else throw Error("Invalid examples format. Only list or string are supported.");return new u({inputVariables:e.input_variables,examplePrompt:r,examples:t,exampleSeparator:e.example_separator,prefix:e.prefix,suffix:e.suffix,templateFormat:e.template_format})}}}}]);
//# sourceMappingURL=450.41f8b458.js.map