#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/development/level-up/midscene/packages/web-integration/bin/node_modules:/mnt/e/development/level-up/midscene/packages/web-integration/node_modules:/mnt/e/development/level-up/midscene/packages/node_modules:/mnt/e/development/level-up/midscene/node_modules:/mnt/e/development/level-up/node_modules:/mnt/e/development/node_modules:/mnt/e/node_modules:/mnt/e/development/level-up/midscene/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/development/level-up/midscene/packages/web-integration/bin/node_modules:/mnt/e/development/level-up/midscene/packages/web-integration/node_modules:/mnt/e/development/level-up/midscene/packages/node_modules:/mnt/e/development/level-up/midscene/node_modules:/mnt/e/development/level-up/node_modules:/mnt/e/development/node_modules:/mnt/e/node_modules:/mnt/e/development/level-up/midscene/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@midscene/web/bin/midscene-playground" "$@"
else
  exec node  "$basedir/../@midscene/web/bin/midscene-playground" "$@"
fi
