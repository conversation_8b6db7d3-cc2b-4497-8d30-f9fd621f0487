// src/utils.ts
import { sha256 } from "js-sha256";
var ifInBrowser = typeof window !== "undefined";
function uuid() {
  return Math.random().toString(36).substring(2, 15);
}
var hashMap = {};
function generateHashId(rect, content = "") {
  const combined = JSON.stringify({
    content,
    rect
  });
  let sliceLength = 5;
  let slicedHash = "";
  const hashHex = sha256.create().update(combined).hex();
  const toLetters = (hex) => {
    return hex.split("").map((char) => {
      const code = Number.parseInt(char, 16);
      return String.fromCharCode(97 + code % 26);
    }).join("");
  };
  const hashLetters = toLetters(hashHex);
  while (sliceLength < hashLetters.length - 1) {
    slicedHash = hashLetters.slice(0, sliceLength);
    if (hashMap[slicedHash] && hashMap[slicedHash] !== combined) {
      sliceLength++;
      continue;
    }
    hashMap[slicedHash] = combined;
    break;
  }
  return slicedHash;
}
function assert(condition, message) {
  if (!condition) {
    throw new Error(message || "Assertion failed");
  }
}
function getGlobalScope() {
  if (typeof window !== "undefined") {
    return window;
  }
  if (typeof globalThis !== "undefined") {
    return globalThis;
  }
  if (typeof self !== "undefined") {
    return self;
  }
  return void 0;
}
var isMcp = false;
function setIsMcp(value) {
  isMcp = value;
}
function logMsg(...message) {
  if (!isMcp) {
    console.log(...message);
  }
}
async function repeat(times, fn) {
  for (let i = 0; i < times; i++) {
    await fn(i);
  }
}
var REGEXP_LT = /</g;
var REGEXP_GT = />/g;
var REGEXP_LT_ESCAPE = "__midscene_lt__";
var REGEXP_GT_ESCAPE = "__midscene_gt__";
var escapeHtml = (html) => {
  return html.replace(REGEXP_LT, REGEXP_LT_ESCAPE).replace(REGEXP_GT, REGEXP_GT_ESCAPE);
};
var antiEscapeHtml = (html) => {
  const REGEXP_LT2 = new RegExp(REGEXP_LT_ESCAPE, "g");
  const REGEXP_GT2 = new RegExp(REGEXP_GT_ESCAPE, "g");
  return html.replace(REGEXP_LT2, "<").replace(REGEXP_GT2, ">");
};
export {
  antiEscapeHtml,
  assert,
  escapeHtml,
  generateHashId,
  getGlobalScope,
  ifInBrowser,
  logMsg,
  repeat,
  setIsMcp,
  uuid
};
